<template>
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Jadual Waktu Men<PERSON>jar
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Paparan mingguan jadual kelas mengikut waktu
                </p>
            </div>

            <!-- Desktop: Buttons right-aligned, icons+text -->
            <div class="hidden lg:flex items-center space-x-2 md:space-x-3">
                <div class="relative inline-block">
                    <Button :variant="editMode ? 'primary' : 'outline'" size="sm"
                        :prepend-icon="editMode ? 'mdi:pencil-off' : 'mdi:pencil'" @click="$emit('toggle-edit')"
                        :title="editMode ? 'Berhenti Sunting' : 'Sunting Jadual'">
                        <span class="hidden md:inline">{{ editMode ? 'Be<PERSON><PERSON><PERSON>' : 'Sunting Jadual' }}</span>
                    </Button>
                    <span v-if="timetableEntries.length === 0 && !editMode"
                        class="absolute -top-1 -right-1 flex size-3">
                        <span
                            class="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
                        <span class="relative inline-flex size-3 rounded-full bg-red-500"></span>
                    </span>
                </div>
                <Button v-if="timetableEntries.length > 0" variant="outline" size="sm" prepend-icon="mdi:delete-sweep"
                    class="text-red-600 dark:text-red-400 border-red-300 dark:border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                    @click="$emit('open-clear-all')">
                    <span class="hidden md:inline">Kosongkan Semua</span>
                    <span class="md:hidden">Kosong</span>
                </Button>
                <Button variant="primary" size="sm" prepend-icon="mdi:account-school"
                    @click="$emit('open-class-subject')">
                    <span class="hidden lg:inline">Urus Kelas & Subjek</span>
                    <span class="lg:hidden">Urus</span>
                </Button>
            </div>
        </div>
        <!-- Mobile & Tablet: Buttons full-width, icon-only, in a row, no prepend-icon, center icons -->
        <div class="mt-4 flex lg:hidden w-full gap-2">

            <Button :variant="editMode ? 'primary' : 'outline'" size="sm"
                class="flex-1 flex items-center justify-center h-12 bg-white dark:bg-gray-800"
                @click="$emit('toggle-edit')" :aria-label="editMode ? 'Berhenti Sunting' : 'Sunting Jadual'">
                <span class="flex items-center justify-center w-full h-full">
                    <Icon :name="editMode ? 'mdi:pencil-off' : 'mdi:pencil'" class="h-5 w-5" />
                </span>
            </Button>
            <span v-if="timetableEntries.length === 0 && !editMode" class="absolute -top-1 -right-1 flex size-3">
                <span class="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
                <span class="relative inline-flex size-3 rounded-full bg-red-500"></span>
            </span>

            <Button v-if="timetableEntries.length > 0" variant="outline" size="sm"
                class="flex-1 flex items-center justify-center h-12 text-red-600 dark:text-red-400 border-red-300 dark:border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                @click="$emit('open-clear-all')" :aria-label="'Kosongkan Semua'">
                <span class="flex items-center justify-center w-full h-full">
                    <Icon name="mdi:delete-sweep" class="h-5 w-5" />
                </span>
            </Button>
            <Button variant="primary" size="sm" class="flex-1 flex items-center justify-center h-12"
                @click="$emit('open-class-subject')" aria-label="Urus Kelas & Subjek">
                <span class="flex items-center justify-center w-full h-full">
                    <Icon name="mdi:account-school" class="h-5 w-5" />
                </span>
            </Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'

defineProps<{
    editMode: boolean
    timetableEntries: any[]
}>()

defineEmits<{
    'toggle-edit': []
    'open-clear-all': []
    'open-class-subject': []
}>()
</script>