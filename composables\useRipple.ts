import { ref } from "vue";

interface Ripple {
  id: number;
  top: number;
  left: number;
  size: number;
}

export function useRipple() {
  const ripples = ref<Ripple[]>([]);

  const createRipple = (event: MouseEvent) => {
    const target = event.currentTarget as HTMLElement | null;
    if (!target) return;

    const rect = target.getBoundingClientRect();
    const diameter = Math.max(target.clientWidth, target.clientHeight) * 2; // Make ripple larger
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newRipple: Ripple = {
      id: Date.now(),
      top: y - diameter / 2,
      left: x - diameter / 2,
      size: diameter,
    };

    ripples.value.push(newRipple);

    setTimeout(() => {
      ripples.value = ripples.value.filter((r) => r.id !== newRipple.id);
    }, 600); // Corresponds to animation duration
  };

  return {
    ripples,
    createRipple,
  };
}
