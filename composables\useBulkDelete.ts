import { ref, computed, type Ref } from "vue";
import { useRphWeeks } from "./useRphWeeks";
import type { RphWeek } from "~/types/rph";

export const useBulkDelete = (
  selectedWeeksForDeletion: Ref<Set<string>>,
  weeks: Ref<RphWeek[]>
) => {
  const { deleteWeek } = useRphWeeks();

  const isDeletingWeeks = ref(false);
  const bulkDeleteError = ref<string | null>(null);

  // Computed properties
  const canBulkDelete = computed(() => selectedWeeksForDeletion.value.size > 0);
  const selectedWeekCount = computed(() => selectedWeeksForDeletion.value.size);

  // Get selected week names for confirmation display
  const selectedWeekNames = computed(() => {
    const selectedIds = Array.from(selectedWeeksForDeletion.value);
    return selectedIds
      .map(
        (id) =>
          weeks.value.find((w) => w.id === id)?.name || "Minggu Tidak Dikenali"
      )
      .sort();
  });

  // Clear selection
  const clearSelection = () => {
    selectedWeeksForDeletion.value = new Set();
  };

  // Execute bulk delete operation
  const executeBulkDelete = async () => {
    if (!canBulkDelete.value || isDeletingWeeks.value)
      return { success: false, error: "Invalid state" };

    isDeletingWeeks.value = true;
    bulkDeleteError.value = null;

    try {
      const idsToDelete = Array.from(selectedWeeksForDeletion.value);

      // Delete weeks sequentially to avoid overwhelming the server
      for (const weekId of idsToDelete) {
        await deleteWeek(weekId);
      }

      // Clear selection after successful deletion
      clearSelection();

      return { success: true };
    } catch (error) {
      console.error("Bulk delete error:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Ralat berlaku semasa memadam minggu";
      bulkDeleteError.value = errorMessage;
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      isDeletingWeeks.value = false;
    }
  };

  // Reset error state
  const resetError = () => {
    bulkDeleteError.value = null;
  };

  return {
    // States
    isDeletingWeeks,
    bulkDeleteError,

    // Computed
    canBulkDelete,
    selectedWeekCount,
    selectedWeekNames,

    // Actions
    executeBulkDelete,
    clearSelection,
    resetError,
  };
};
