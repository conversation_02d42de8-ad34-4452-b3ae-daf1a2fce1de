<template>
    <div class="space-y-2">

        <!-- Single-select using existing component -->
        <SingleSelect :key="`tidak-${options.length}`" :model-value="selectedValue"
            @update:model-value="selectedValue = $event" :options="formattedOptions" option-label="label"
            option-value="value" label="Tidak Terlaksana" placeholder="Pilih sebab tidak terlaksana..."
            :loading="loading" clearable variant="standard" />
        <div class="flex justify-end">
            <Button @click="showOptionsModal = true" size="sm" variant="outline" class="text-xs" type="button">
                <Icon name="heroicons:cog-6-tooth" class="h-3 w-3 mr-1" />
                <PERSON><PERSON> Pilihan
            </Button>
        </div>

        <!-- Options Management Modal -->
        <ReflectionOptionsModal :show="showOptionsModal" title="Tidak Terlaksana" :options="options"
            :loading="optionsLoading" @close="showOptionsModal = false" @add-option="handleAddOption"
            @update-option="handleUpdateOption" @delete-option="handleDeleteOption" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import SingleSelect from '~/components/ui/base/SingleSelect.vue'
import ReflectionOptionsModal from './ReflectionOptionsModal.vue'
import { useReflectionOptions } from '~/composables/useReflectionOptions'

interface Props {
    modelValue: string | null
}

interface Emits {
    (e: 'update:modelValue', value: string | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const {
    tidakTerlaksanaOptions: options,
    loading,
    fetchTidakTerlaksanaOptions,
    createTidakTerlaksanaOption,
    updateTidakTerlaksanaOption,
    deleteTidakTerlaksanaOption
} = useReflectionOptions()

// State
const showOptionsModal = ref(false)
const optionsLoading = ref(false)

// Computed
const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

// Format options for SingleSelect component
const formattedOptions = computed(() => {
    return options.value.map(option => ({
        value: option.id,
        label: option.option_text + (option.is_default ? ' (Lalai)' : '')
    }))
})

const handleAddOption = async (text: string) => {
    try {
        optionsLoading.value = true
        await createTidakTerlaksanaOption(text)
    } catch (error) {
        console.error('Error adding option:', error)
    } finally {
        optionsLoading.value = false
    }
}

const handleUpdateOption = async (id: string, text: string) => {
    try {
        optionsLoading.value = true
        await updateTidakTerlaksanaOption(id, text)
    } catch (error) {
        console.error('Error updating option:', error)
    } finally {
        optionsLoading.value = false
    }
}

const handleDeleteOption = async (id: string) => {
    try {
        optionsLoading.value = true
        await deleteTidakTerlaksanaOption(id)
        // Clear selection if the deleted option was selected
        if (selectedValue.value === id) {
            selectedValue.value = null
        }
    } catch (error) {
        console.error('Error deleting option:', error)
    } finally {
        optionsLoading.value = false
    }
}

// Lifecycle
onMounted(async () => {
    await fetchTidakTerlaksanaOptions()
})

// Watch for modelValue changes and ensure options are loaded
watch(() => props.modelValue, async (newValue) => {
    if (newValue && options.value.length === 0) {
        // If we have a selected value but no options loaded, fetch them
        await fetchTidakTerlaksanaOptions()
    }
}, { immediate: true })
</script>
