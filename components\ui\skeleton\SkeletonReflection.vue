<template>
  <div class="space-y-8">
    <!-- <PERSON>er -->
    <SkeletonPageHeader :title-width="'15rem'" :subtitle-width="'30rem'" :action-count="2" />

    <div class="space-y-6">
      <!-- Mode Selector -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div class="flex items-center space-x-4">
          <SkeletonBox height="2.5rem" width="6rem" class="rounded-md" />
          <SkeletonBox height="2.5rem" width="8rem" class="rounded-md" />
        </div>
      </div>

      <!-- Navigation Header (for detailed mode) -->
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-4">
        <!-- Day Tabs -->
        <div class="mb-4">
          <SkeletonBox height="1rem" width="4rem" class="mb-3" />
          <div class="flex space-x-2">
            <SkeletonBox v-for="day in 7" :key="`day-${day}`" height="2.5rem" width="5rem" class="rounded-md" />
          </div>
        </div>

        <!-- Class Subject Selector -->
        <div>
          <SkeletonBox height="1rem" width="6rem" class="mb-2" />
          <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
        </div>
      </div>

      <!-- Quick Copy Section -->
      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800 p-4">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-2">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <SkeletonBox height="1rem" width="10rem" />
          </div>
          <SkeletonBox height="2rem" width="2rem" class="rounded" />
        </div>
      </div>

      <!-- Main Form Content -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <!-- Quick Fields Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <!-- Overall Rating -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="8rem" />
            <div class="flex space-x-1">
              <SkeletonBox v-for="star in 5" :key="`star-${star}`" height="1.5rem" width="1.5rem" class="rounded" />
            </div>
          </div>

          <!-- Objectives Achieved -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="10rem" />
            <div class="flex space-x-4">
              <div class="flex items-center space-x-2">
                <SkeletonBox height="1rem" width="1rem" class="rounded-full" />
                <SkeletonBox height="0.875rem" width="3rem" />
              </div>
              <div class="flex items-center space-x-2">
                <SkeletonBox height="1rem" width="1rem" class="rounded-full" />
                <SkeletonBox height="0.875rem" width="4rem" />
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Fields -->
        <div class="space-y-6">
          <!-- Activity Effectiveness -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="12rem" />
            <div class="flex space-x-1">
              <SkeletonBox v-for="star in 5" :key="`activity-${star}`" height="1.5rem" width="1.5rem" class="rounded" />
            </div>
          </div>

          <!-- Time Management -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="10rem" />
            <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          </div>

          <!-- Student Engagement -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="11rem" />
            <div class="flex space-x-1">
              <SkeletonBox v-for="star in 5" :key="`engagement-${star}`" height="1.5rem" width="1.5rem"
                class="rounded" />
            </div>
          </div>

          <!-- Resource Adequacy -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="9rem" />
            <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          </div>

          <!-- Student Count -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="15rem" />
            <div class="flex items-center space-x-3">
              <SkeletonBox height="2.5rem" width="2.5rem" class="rounded" />
              <SkeletonBox height="2.5rem" width="4rem" class="rounded-md" />
              <SkeletonBox height="2.5rem" width="2.5rem" class="rounded" />
            </div>
          </div>

          <!-- Text Areas -->
          <div v-for="field in 4" :key="`textarea-${field}`" class="space-y-2">
            <SkeletonBox height="1rem" :width="getTextAreaLabelWidth(field)" />
            <SkeletonBox height="6rem" width="100%" class="rounded-md" />
          </div>

          <!-- Multi-select Fields -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="8rem" />
            <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          </div>

          <!-- Single Select -->
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="7rem" />
            <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-end space-x-3">
        <SkeletonBox height="2.5rem" width="5rem" class="rounded-md" />
        <SkeletonBox height="2.5rem" width="8rem" class="rounded-md" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'

const getTextAreaLabelWidth = (field: number): string => {
  const widths = ['12rem', '10rem', '14rem', '9rem']
  return widths[(field - 1) % widths.length]
}
</script>
