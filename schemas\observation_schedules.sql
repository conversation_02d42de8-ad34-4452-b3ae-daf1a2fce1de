create table public.observation_schedules (
  id uuid not null default gen_random_uuid (),
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  user_id uuid not null,
  observer_name text not null,
  observer_position text not null,
  observation_date date not null,
  class_subject_id text not null,
  status text not null default 'dijadualkan'::text,
  notes text null,
  constraint observation_schedules_pkey primary key (id),
  constraint observation_schedules_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint observation_schedules_class_subject_id_check check ((char_length(class_subject_id) > 0)),
  constraint observation_schedules_observer_name_check check ((char_length(observer_name) > 0)),
  constraint observation_schedules_observer_position_check check ((char_length(observer_position) > 0)),
  constraint observation_schedules_status_check check (
    (
      status = any (
        array[
          'dijadualkan'::text,
          'selesai'::text,
          'dibatalkan'::text,
          'dijadualkan semula'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_observation_schedules_user_id on public.observation_schedules using btree (user_id, observation_date desc) TABLESPACE pg_default;

create index IF not exists idx_observation_schedules_date on public.observation_schedules using btree (observation_date) TABLESPACE pg_default;

create index IF not exists idx_observation_schedules_status on public.observation_schedules using btree (status) TABLESPACE pg_default;

create index IF not exists idx_observation_schedules_user_status on public.observation_schedules using btree (user_id, status) TABLESPACE pg_default;

create trigger trigger_observation_schedules_updated_at BEFORE
update on observation_schedules for EACH row
execute FUNCTION update_observation_schedules_updated_at ();