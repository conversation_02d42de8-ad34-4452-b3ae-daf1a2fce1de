#!/usr/bin/env node

/**
 * RPHMate SaaS Development Environment Setup Script
 * Phase 4: Development Environment Setup
 * 
 * This script sets up the local development environment for testing
 * the multi-tenant SaaS platform with subdomain support.
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 RPHMate SaaS Development Environment Setup')
console.log('============================================\n')

// Configuration
const config = {
  baseDomain: 'localhost:3000',
  sampleSchools: [], // No hardcoded demo schools - production ready
  requiredEnvVars: [
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SECRET_KEY', 
    'STRIPE_WEBHOOK_SECRET',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
}

/**
 * Check if running on Windows
 */
function isWindows() {
  return process.platform === 'win32'
}

/**
 * Check environment variables
 */
function checkEnvironmentVariables() {
  console.log('📋 Checking environment variables...')
  
  const envPath = path.join(process.cwd(), '.env.local')
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found!')
    console.log('Please create .env.local with the required environment variables.')
    process.exit(1)
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8')
  const missingVars = []
  
  config.requiredEnvVars.forEach(varName => {
    if (!envContent.includes(`${varName}=`) || envContent.includes(`${varName}=your-`) || envContent.includes(`${varName}=...`)) {
      missingVars.push(varName)
    }
  })
  
  if (missingVars.length > 0) {
    console.error('❌ Missing or incomplete environment variables:')
    missingVars.forEach(varName => console.error(`   - ${varName}`))
    console.log('\nPlease update your .env.local file with actual values.')
    process.exit(1)
  }
  
  console.log('✅ Environment variables configured correctly\n')
}

/**
 * Setup local subdomain resolution
 */
function setupSubdomainResolution() {
  console.log('🌐 Setting up local subdomain resolution...')
  
  const hostsFile = isWindows() 
    ? 'C:\\Windows\\System32\\drivers\\etc\\hosts'
    : '/etc/hosts'
  
  try {
    // Read current hosts file
    let hostsContent = ''
    if (fs.existsSync(hostsFile)) {
      hostsContent = fs.readFileSync(hostsFile, 'utf8')
    }
    
    // Check if RPHMate entries already exist
    if (hostsContent.includes('# RPHMate SaaS Development')) {
      console.log('✅ Subdomain entries already exist in hosts file\n')
      return
    }
    
    // Prepare new entries
    const newEntries = [
      '\n# RPHMate SaaS Development',
      '127.0.0.1 localhost'
    ]
    
    config.sampleSchools.forEach(school => {
      newEntries.push(`127.0.0.1 ${school}.localhost`)
    })
    
    newEntries.push('# End RPHMate SaaS Development\n')
    
    console.log('📝 Hosts file entries to add:')
    newEntries.forEach(entry => {
      if (!entry.startsWith('#') && entry.trim()) {
        console.log(`   ${entry}`)
      }
    })
    
    console.log('\n⚠️  Manual Action Required:')
    console.log(`Please add the above entries to your hosts file: ${hostsFile}`)
    
    if (isWindows()) {
      console.log('💡 Windows: Run as Administrator and edit the hosts file')
    } else {
      console.log('💡 macOS/Linux: Use sudo to edit the hosts file')
      console.log('   Example: sudo nano /etc/hosts')
    }
    
    console.log('\n✅ Subdomain resolution setup instructions provided\n')
    
  } catch (error) {
    console.error('❌ Error setting up subdomain resolution:', error.message)
    console.log('Please manually add subdomain entries to your hosts file.\n')
  }
}

/**
 * Check dependencies
 */
function checkDependencies() {
  console.log('📦 Checking dependencies...')
  
  try {
    // Check if node_modules exists
    if (!fs.existsSync(path.join(process.cwd(), 'node_modules'))) {
      console.log('Installing dependencies...')
      execSync('npm install', { stdio: 'inherit' })
    }
    
    // Check for Stripe CLI
    try {
      execSync('stripe --version', { stdio: 'pipe' })
      console.log('✅ Stripe CLI is installed')
    } catch (error) {
      console.log('⚠️  Stripe CLI not found. Install it for webhook testing:')
      console.log('   Windows: choco install stripe-cli')
      console.log('   macOS: brew install stripe/stripe-cli/stripe')
      console.log('   Or download from: https://github.com/stripe/stripe-cli/releases')
    }
    
    console.log('✅ Dependencies checked\n')
    
  } catch (error) {
    console.error('❌ Error checking dependencies:', error.message)
    process.exit(1)
  }
}

/**
 * Create development scripts
 */
function createDevelopmentScripts() {
  console.log('📜 Creating development scripts...')
  
  // Create start script
  const startScript = `#!/bin/bash
# RPHMate SaaS Development Start Script

echo "🚀 Starting RPHMate SaaS Development Environment"
echo "=============================================="

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "❌ .env.local file not found!"
    echo "Please run: node scripts/setup-dev-environment.js"
    exit 1
fi

# Start the development server
echo "📡 Starting Nuxt development server..."
npm run dev &

# Wait a moment for server to start
sleep 3

echo ""
echo "✅ Development environment ready!"
echo ""
echo "🌐 Available URLs:"
echo "   Main Domain: http://localhost:3000"
echo "   Test School: http://xba1224.localhost:3000"
echo ""
echo "📋 Next Steps:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Test the complete registration flow"
echo "   3. Use Stripe test cards for payment testing"
echo ""
echo "🔧 For webhook testing, run in another terminal:"
echo "   stripe listen --forward-to localhost:3000/api/webhooks/stripe"
echo ""
`
  
  fs.writeFileSync('scripts/start-dev.sh', startScript)
  
  // Create Windows batch file
  const startBatch = `@echo off
REM RPHMate SaaS Development Start Script

echo 🚀 Starting RPHMate SaaS Development Environment
echo ==============================================

REM Check if .env.local exists
if not exist .env.local (
    echo ❌ .env.local file not found!
    echo Please run: node scripts/setup-dev-environment.js
    exit /b 1
)

REM Start the development server
echo 📡 Starting Nuxt development server...
start cmd /k npm run dev

timeout /t 3 /nobreak > nul

echo.
echo ✅ Development environment ready!
echo.
echo 🌐 Available URLs:
echo    Main Domain: http://localhost:3000
echo    Test School: http://xba1224.localhost:3000
echo.
echo 📋 Next Steps:
echo    1. Open http://localhost:3000 in your browser
echo    2. Test the complete registration flow
echo    3. Use Stripe test cards for payment testing
echo.
echo 🔧 For webhook testing, run in another terminal:
echo    stripe listen --forward-to localhost:3000/api/webhooks/stripe
echo.
pause
`
  
  fs.writeFileSync('scripts/start-dev.bat', startBatch)
  
  console.log('✅ Development scripts created')
  console.log('   - scripts/start-dev.sh (Unix/macOS)')
  console.log('   - scripts/start-dev.bat (Windows)\n')
}

/**
 * Display testing information
 */
function displayTestingInfo() {
  console.log('🧪 Testing Information')
  console.log('=====================\n')
  
  console.log('📋 Test URLs:')
  console.log('   Landing Page: http://localhost:3000')
  console.log('   Pricing Page: http://localhost:3000/pricing')
  console.log('   Registration: http://localhost:3000/billing')
  console.log('   Admin Login:  http://localhost:3000/login')
  console.log('   Test School:  http://xba1224.localhost:3000')
  console.log('   School Admin: http://xba1224.localhost:3000/admin\n')
  
  console.log('💳 Stripe Test Cards:')
  console.log('   Success: 4242 4242 4242 4242')
  console.log('   Decline: 4000 0000 0000 0002')
  console.log('   Require Authentication: 4000 0025 0000 3155\n')
  
  console.log('🔧 Development Commands:')
  console.log('   Start Dev Server: npm run dev')
  console.log('   TypeScript Check: npm run typecheck')
  console.log('   Stripe Webhooks:  stripe listen --forward-to localhost:3000/api/webhooks/stripe\n')
}

/**
 * Main setup function
 */
function main() {
  try {
    checkEnvironmentVariables()
    setupSubdomainResolution()
    checkDependencies()
    createDevelopmentScripts()
    displayTestingInfo()
    
    console.log('🎉 Development environment setup complete!')
    console.log('You can now start testing the RPHMate SaaS platform.\n')
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    process.exit(1)
  }
}

// Run the setup
main()
