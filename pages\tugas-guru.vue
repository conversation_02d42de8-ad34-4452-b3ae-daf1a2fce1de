<template>
  <!-- Page Loading State -->
  <SkeletonLoader v-if="loading" variant="tugas-guru" />

  <div v-if="!loading" class="space-y-8">
    <!-- Page Header -->
    <UiCompositePageHeader title="Tugas Guru" subtitle="Urus dan pantau semua tugasan dan tanggungjawab anda"
      icon="heroicons:clipboard-document-list-solid">
      <template #actions>
        <UiBaseButton variant="primary" size="sm" sm:size="md" prepend-icon="heroicons:printer-solid"
          @click="handlePrint" class="flex-1 sm:flex-none">
          <span class="hidden sm:inline">Cetak</span>
          <span class="sm:hidden">Cetak</span>
        </UiBaseButton>
      </template>
    </UiCompositePageHeader>

    <!-- 1. <PERSON>gas Kurikulum (Auto-generated from Timetable) -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center space-x-2">
          <UiBaseIcon name="heroicons:academic-cap-solid" class="w-5 h-5 text-primary" />
          <h2 class="text-xl font-semibold">Tugas Kurikulum</h2>
        </div>
      </template>
      <template #default>
        <!-- Empty State for Timetable -->
        <div v-if="curriculumTasks.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
          <UiBaseIcon name="heroicons:calendar-days-solid" class="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p class="mb-2">Tiada data kurikulum dijumpai.</p>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">Sila lengkapkan Jadual Mengajar anda terlebih dahulu.
          </p>
          <NuxtLink to="/jadual-mengajar">
            <UiBaseButton variant="primary" size="sm">
              <Icon name="mdi:calendar-plus" class="w-4 h-4 mr-2" />
              Pergi ke Jadual Mengajar
            </UiBaseButton>
          </NuxtLink>
        </div>

        <!-- Curriculum Tasks Table -->
        <div v-else>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Kelas & Subjek
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Jumlah Waktu
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Bil. Murid
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="task in curriculumTasks" :key="`${task.class_id}_${task.subject_id}`">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {{ task.class_name }} - {{ task.subject_name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ task.frequency }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ task.student_count || '-' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Info Text -->
          <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <div class="flex items-center">
              <UiBaseIcon name="heroicons:information-circle-solid" class="w-5 h-5 text-blue-500 mr-2" />
              <p class="text-sm text-blue-800 dark:text-blue-200">
                Data untuk bahagian ini dijana secara automatik daripada Jadual Mengajar.
              </p>
            </div>
          </div>
        </div>
      </template>
    </UiCompositeCard>

    <!-- 2. Tugas Khas/Pentadbiran -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:building-office-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Tugas Khas/Pentadbiran</h2>
          </div>
          <UiBaseButton type="button" @click="showAddTaskForm('pentadbiran')" variant="outline" size="sm"
            prepend-icon="heroicons:plus-solid">
            Tambah Tugas
          </UiBaseButton>
        </div>
      </template>
      <template #default>
        <TaskSection :tasks="administrativeTasks" :show-add-form="showAddForms.pentadbiran" category="pentadbiran"
          @add-task="addTask" @edit-task="editTask" @delete-task="deleteTaskHandler" @cancel-add="cancelAddTask" />
      </template>
    </UiCompositeCard>

    <!-- 3. Tugas Kokurikulum -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:trophy-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Tugas Kokurikulum</h2>
          </div>
          <UiBaseButton type="button" @click="showAddTaskForm('kokurikulum')" variant="outline" size="sm"
            prepend-icon="heroicons:plus-solid">
            Tambah Tugas
          </UiBaseButton>
        </div>
      </template>
      <template #default>
        <TaskSection :tasks="cocurricularTasks" :show-add-form="showAddForms.kokurikulum" category="kokurikulum"
          @add-task="addTask" @edit-task="editTask" @delete-task="deleteTaskHandler" @cancel-add="cancelAddTask" />
      </template>
    </UiCompositeCard>

    <!-- 4. Tugas Hal Ehwal Murid -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:users-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Tugas Hal Ehwal Murid</h2>
          </div>
          <UiBaseButton type="button" @click="showAddTaskForm('hal_ehwal_murid')" variant="outline" size="sm"
            prepend-icon="heroicons:plus-solid">
            Tambah Tugas
          </UiBaseButton>
        </div>
      </template>
      <template #default>
        <TaskSection :tasks="studentAffairsTasks" :show-add-form="showAddForms.hal_ehwal_murid"
          category="hal_ehwal_murid" @add-task="addTask" @edit-task="editTask" @delete-task="deleteTaskHandler"
          @cancel-add="cancelAddTask" />
      </template>
    </UiCompositeCard>

    <!-- 5. Kegiatan dan Sumbangan (Aktiviti Luar) -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center space-x-2">
          <UiBaseIcon name="heroicons:squares-plus-solid" class="w-5 h-5 text-primary" />
          <h2 class="text-xl font-semibold">Kegiatan dan Sumbangan (Aktiviti Luar)</h2>
        </div>
      </template>
      <template #default>
        <ActivitiesSection :activities="activities" :show-add-forms="showActivityAddForms" @add-activity="addActivity"
          @edit-activity="editActivity" @delete-activity="deleteActivityHandler" @show-add-form="showAddActivityForm"
          @cancel-add="cancelAddActivity" />
      </template>
    </UiCompositeCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSupabaseUser } from '#imports'
import { useToast } from '~/composables/useToast'
import { useCurriculumTasks } from '~/composables/useCurriculumTasks'
import { useTeacherTasks } from '~/composables/useTeacherTasks'
import { useTeacherActivities } from '~/composables/useTeacherActivities'
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue'
import UiCompositePageHeader from '~/components/ui/composite/PageHeader.vue'
import UiCompositeCard from '~/components/ui/composite/Card.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'
import UiBaseButton from '~/components/ui/base/Button.vue'

// Task management components
const TaskSection = defineAsyncComponent(() => import('~/components/tugas-guru/TaskSection.vue'))
const ActivitiesSection = defineAsyncComponent(() => import('~/components/tugas-guru/ActivitiesSection.vue'))

// =====================================================
// COMPOSABLES & STATE
// =====================================================

const user = useSupabaseUser()
const { success: showSuccessToast, error: showErrorToast } = useToast()
const {
  curriculumTasks,
  loading: curriculumLoading,
  hasTimetableEntries,
  loadCurriculumTasks
} = useCurriculumTasks()

const {
  administrativeTasks,
  cocurricularTasks,
  studentAffairsTasks,
  loading: tasksLoading,
  fetchTasks,
  createTask,
  updateTask,
  deleteTask
} = useTeacherTasks()

const {
  activitiesByCategory: activities,
  loading: activitiesLoading,
  fetchActivities,
  createActivity,
  updateActivity,
  deleteActivity
} = useTeacherActivities()

const loading = ref(true)

// Form states
const showAddForms = ref<Record<string, boolean>>({
  pentadbiran: false,
  kokurikulum: false,
  hal_ehwal_murid: false
})

const showActivityAddForms = ref<Record<string, boolean>>({
  sukan: false,
  pertubuhan: false,
  sumbangan: false
})

// Data states are now managed by composables

// =====================================================
// COMPUTED PROPERTIES
// =====================================================

// Computed properties will be added when composables are created

// =====================================================
// METHODS
// =====================================================

const showAddTaskForm = (category: string) => {
  showAddForms.value[category] = true
}

const cancelAddTask = (category: string) => {
  showAddForms.value[category] = false
}

const showAddActivityForm = (category: string) => {
  showActivityAddForms.value[category] = true
}

const cancelAddActivity = (category: string) => {
  showActivityAddForms.value[category] = false
}

// Task management methods
const addTask = async (category: string, description: string) => {
  try {
    const newTask = await createTask(category as any, description)
    if (newTask) {
      showSuccessToast('Tugas berjaya ditambah')
      showAddForms.value[category] = false
    }
  } catch (error) {
    console.error('Error adding task:', error)
    showErrorToast('Gagal menambah tugas. Sila cuba lagi.')
  }
}

const editTask = async (taskId: string, description: string) => {
  try {
    const updatedTask = await updateTask(taskId, { task_description: description })
    if (updatedTask) {
      showSuccessToast('Tugas berjaya dikemaskini')
    }
  } catch (error) {
    console.error('Error editing task:', error)
    showErrorToast('Gagal mengemaskini tugas. Sila cuba lagi.')
  }
}

const deleteTaskHandler = async (taskId: string) => {
  try {
    const success = await deleteTask(taskId)
    if (success) {
      showSuccessToast('Tugas berjaya dipadam')
    }
  } catch (error) {
    console.error('Error deleting task:', error)
    showErrorToast('Gagal memadam tugas. Sila cuba lagi.')
  }
}

// Activity management methods
const addActivity = async (category: string, description: string) => {
  try {
    const newActivity = await createActivity(category as any, description)
    if (newActivity) {
      showSuccessToast('Aktiviti berjaya ditambah')
      showActivityAddForms.value[category] = false
    }
  } catch (error) {
    console.error('Error adding activity:', error)
    showErrorToast('Gagal menambah aktiviti. Sila cuba lagi.')
  }
}

const editActivity = async (activityId: string, description: string) => {
  try {
    const updatedActivity = await updateActivity(activityId, { activity_description: description })
    if (updatedActivity) {
      showSuccessToast('Aktiviti berjaya dikemaskini')
    }
  } catch (error) {
    console.error('Error editing activity:', error)
    showErrorToast('Gagal mengemaskini aktiviti. Sila cuba lagi.')
  }
}

const deleteActivityHandler = async (activityId: string) => {
  try {
    const success = await deleteActivity(activityId)
    if (success) {
      showSuccessToast('Aktiviti berjaya dipadam')
    }
  } catch (error) {
    console.error('Error deleting activity:', error)
    showErrorToast('Gagal memadam aktiviti. Sila cuba lagi.')
  }
}

const loadData = async () => {
  try {
    loading.value = true

    // Load all data in parallel
    await Promise.all([
      loadCurriculumTasks(),
      fetchTasks(),
      fetchActivities()
    ])

  } catch (error) {
    console.error('Error loading data:', error)
    showErrorToast('Gagal memuat data. Sila cuba lagi.')
  } finally {
    loading.value = false
  }
}

// Print functionality
const handlePrint = () => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    showErrorToast('Gagal membuka tetingkap cetak. Sila cuba lagi.')
    return
  }

  const teacherName = user.value?.user_metadata?.full_name || user.value?.email?.split('@')[0] || 'Nama Guru'

  const printContent = generatePrintContent(teacherName)

  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
}

const generatePrintContent = (teacherName: string): string => {
  return `
    <!DOCTYPE html>
    <html lang="ms">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Tugas Guru - ${teacherName}</title>
      <style>
        @page {
          size: A4;
          margin: 2cm;
        }

        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
          margin: 0;
          padding: 0;
        }

        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 15px;
        }

        .header h1 {
          font-size: 24px;
          font-weight: bold;
          margin: 0 0 10px 0;
        }

        .teacher-name {
          font-size: 14px;
          margin: 10px 0;
        }

        .section {
          margin-bottom: 25px;
          page-break-inside: avoid;
        }

        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #2563eb;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 5px;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 15px;
        }

        th, td {
          border: 1px solid #d1d5db;
          padding: 8px;
          text-align: left;
          vertical-align: top;
        }

        th {
          background-color: #f3f4f6;
          font-weight: bold;
        }

        .numbered-list {
          margin: 0;
          padding-left: 20px;
        }

        .numbered-list li {
          margin-bottom: 5px;
        }

        .activities-table {
          margin-top: 10px;
        }

        .activities-table td:first-child {
          font-weight: bold;
          background-color: #f9fafb;
          width: 150px;
        }

        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }

          .section {
            page-break-inside: avoid;
          }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Tugas Guru</h1>
        <div class="teacher-name"><strong>Nama:</strong> ${teacherName}</div>
      </div>

      <!-- Tugas Kurikulum -->
      <div class="section">
        <div class="section-title">Tugas Kurikulum</div>
        ${generateCurriculumTable()}
      </div>

      <!-- Tugas Khas/Pentadbiran -->
      <div class="section">
        <div class="section-title">Tugas Khas/Pentadbiran</div>
        ${generateTasksList('pentadbiran')}
      </div>

      <!-- Tugas Kokurikulum -->
      <div class="section">
        <div class="section-title">Tugas Kokurikulum</div>
        ${generateTasksList('kokurikulum')}
      </div>

      <!-- Tugas Hal Ehwal Murid -->
      <div class="section">
        <div class="section-title">Tugas Hal Ehwal Murid</div>
        ${generateTasksList('hal_ehwal_murid')}
      </div>

      <!-- Kegiatan dan Sumbangan -->
      <div class="section">
        <div class="section-title">Kegiatan dan Sumbangan (Aktiviti Luar)</div>
        ${generateActivitiesTable()}
      </div>
    </body>
    </html>
  `
}

const generateCurriculumTable = (): string => {
  if (curriculumTasks.value.length === 0) {
    return '<p><em>Tiada data kurikulum dijumpai. Sila lengkapkan Jadual Mengajar terlebih dahulu.</em></p>'
  }

  let tableHTML = `
    <table>
      <thead>
        <tr>
          <th>Kelas & Subjek</th>
          <th>Jumlah Waktu</th>
          <th>Bil. Murid</th>
        </tr>
      </thead>
      <tbody>
  `

  curriculumTasks.value.forEach(task => {
    tableHTML += `
      <tr>
        <td>${task.class_name} - ${task.subject_name}</td>
        <td>${task.frequency}</td>
        <td>${task.student_count || '-'}</td>
      </tr>
    `
  })

  tableHTML += `
      </tbody>
    </table>
  `

  return tableHTML
}

const generateTasksList = (category: string): string => {
  let tasks: any[] = []

  if (category === 'pentadbiran') {
    tasks = administrativeTasks.value
  } else if (category === 'kokurikulum') {
    tasks = cocurricularTasks.value
  } else if (category === 'hal_ehwal_murid') {
    tasks = studentAffairsTasks.value
  }

  if (tasks.length === 0) {
    return '<p><em>Tiada tugas disimpan.</em></p>'
  }

  let listHTML = '<ol class="numbered-list">'
  tasks.forEach((task: any) => {
    listHTML += `<li>${task.task_description}</li>`
  })
  listHTML += '</ol>'

  return listHTML
}

const generateActivitiesTable = (): string => {
  const categories = [
    { key: 'sukan', label: 'Sukan' },
    { key: 'pertubuhan', label: 'Pertubuhan' },
    { key: 'sumbangan', label: 'Sumbangan' }
  ]

  let tableHTML = '<table class="activities-table">'

  categories.forEach(category => {
    const categoryActivities = (activities.value as any)[category.key] || []

    tableHTML += `
      <tr>
        <td rowspan="1">${category.label}</td>
        <td>
    `

    if (categoryActivities.length === 0) {
      tableHTML += '<em>Tiada aktiviti disimpan.</em>'
    } else {
      tableHTML += '<ol class="numbered-list">'
      categoryActivities.forEach((activity: any) => {
        tableHTML += `<li>${activity.activity_description}</li>`
      })
      tableHTML += '</ol>'
    }

    tableHTML += `
        </td>
      </tr>
    `
  })

  tableHTML += '</table>'
  return tableHTML
}

// =====================================================
// LIFECYCLE
// =====================================================

onMounted(() => {
  loadData()
})
</script>
