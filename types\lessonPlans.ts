// types/lessonPlans.ts
export interface LessonPlan {
  id: string;
  created_at: string;
  user_id: string;
  week_id: string; // Changed from week_label to week_id
  week_label: string; // Added week_label
  class_subject_ids: string[];
  days_selected: string[];
  file_name: string | null;
  storage_file_path: string | null;
  file_mime_type: string | null;
  file_size_bytes: number | null;
  public_url?: string; // Optional: For fetched public URL
  preview_type?:
    | "image"
    | "pdf"
    | "office"
    | "video"
    | "audio"
    | "text"
    | "other"; // Expanded preview types
}

export interface Week {
  id: string | number;
  name: string;
  user_id?: string;
}

// This type should align with UserClassSubjectEntry from schemas/userSchemas.ts
// if it's representing the same data structure passed from rph.vue
export interface ClassSubject {
  id: string | number; // Unique ID of the user's class_subject entry
  class_id: string; // Identifier for the class (e.g., '1-amanah')
  className: string; // Display name for the class (e.g., "1 Amanah")
  subject_id: number; // Identifier for the subject (FK to subjects table)
  // 'subject' (string name) will be resolved using subject_id and the useSubjects composable
  studentCount?: number | null;
}

export interface ProfileData {
  class_subjects: ClassSubject[] | null;
  // Add other profile fields if needed by RPH page logic directly
}

export interface DayOption {
  id: string;
  name: string;
}

export interface SubmissionStatus {
  type: "success" | "error";
  message: string;
}
