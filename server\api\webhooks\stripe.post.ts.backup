// Stripe webhook handler for payment processing
// Phase 3: Payment Integration

import Stripe from 'stripe'

// Initialize Stripe - will be done inside the handler to access runtime config

export default defineEventHandler(async (event) => {
  try {
    // Get Stripe keys from environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET

    if (!stripeSecretKey || !stripeWebhookSecret) {
      console.error('Stripe environment variables missing:', {
        secretKey: stripeSecretKey ? 'Present' : 'Missing',
        webhookSecret: stripeWebhookSecret ? 'Present' : 'Missing'
      })
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe configuration missing'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    const body = await readRawBody(event)
    const signature = getHeader(event, 'stripe-signature')

    if (!signature || !body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing signature or body'
      })
    }

    // Verify webhook signature
    let stripeEvent: Stripe.Event
    try {
      stripeEvent = stripe.webhooks.constructEvent(
        body,
        signature,
        stripeWebhookSecret
      )
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message)
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid signature'
      })
    }

    console.log('Received Stripe webhook:', stripeEvent.type)

    // Handle different event types
    switch (stripeEvent.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(stripeEvent.data.object as Stripe.Checkout.Session)
        break
        
      case 'customer.subscription.created':
        await handleSubscriptionCreated(stripeEvent.data.object as Stripe.Subscription)
        break
        
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(stripeEvent.data.object as Stripe.Subscription)
        break
        
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(stripeEvent.data.object as Stripe.Subscription)
        break
        
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(stripeEvent.data.object as Stripe.Invoice)
        break
        
      case 'invoice.payment_failed':
        await handlePaymentFailed(stripeEvent.data.object as Stripe.Invoice)
        break
        
      default:
        console.log(`Unhandled event type: ${stripeEvent.type}`)
    }

    return { received: true }

  } catch (error: any) {
    console.error('Webhook error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Webhook processing failed'
    })
  }
})

/**
 * Handle successful checkout completion
 */
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('Processing checkout completion:', session.id)
    
    // Get session metadata
    const metadata = session.metadata
    if (!metadata) {
      console.error('No metadata found in checkout session')
      return
    }

    const { schoolCode, schoolName, adminEmail, adminFirstName, adminLastName, selectedPlan } = metadata

    if (!schoolCode || !schoolName || !adminEmail) {
      console.error('Missing required metadata:', metadata)
      return
    }

    // Create school record with retry mechanism
    await createSchoolAccountWithRetry({
      schoolCode,
      schoolName,
      schoolAddress: metadata.schoolAddress || '',
      adminEmail,
      adminFirstName: adminFirstName || '',
      adminLastName: adminLastName || '',
      selectedPlan,
      stripeCustomerId: session.customer as string,
      stripeSessionId: session.id,
      subscriptionStatus: 'trialing', // Start with trial
      paymentAmount: session.amount_total || 0,
      currency: session.currency || 'myr'
    })

    console.log(`School account created successfully: ${schoolCode}`)

  } catch (error) {
    console.error('Error handling checkout completion:', error)
    // Error is already logged and queued for retry in createSchoolAccountWithRetry
  }
}

/**
 * Handle subscription creation
 */
async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription creation:', subscription.id)
    
    // Update school subscription status
    // TODO: Implement database update
    console.log('Subscription created for customer:', subscription.customer)
    
  } catch (error) {
    console.error('Error handling subscription creation:', error)
  }
}

/**
 * Handle subscription updates
 */
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription update:', subscription.id)
    
    // Update school subscription status
    // TODO: Implement database update
    console.log('Subscription updated for customer:', subscription.customer)
    
  } catch (error) {
    console.error('Error handling subscription update:', error)
  }
}

/**
 * Handle subscription deletion
 */
async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription deletion:', subscription.id)
    
    // Update school status to cancelled
    // TODO: Implement database update
    console.log('Subscription cancelled for customer:', subscription.customer)
    
  } catch (error) {
    console.error('Error handling subscription deletion:', error)
  }
}

/**
 * Handle successful payment
 */
async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('Processing successful payment:', invoice.id)
    
    // Update payment history
    // TODO: Implement database update
    console.log('Payment succeeded for customer:', invoice.customer)
    
  } catch (error) {
    console.error('Error handling payment success:', error)
  }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(invoice: Stripe.Invoice) {
  try {
    console.log('Processing failed payment:', invoice.id)
    
    // Handle failed payment (send notifications, etc.)
    // TODO: Implement database update and notifications
    console.log('Payment failed for customer:', invoice.customer)
    
  } catch (error) {
    console.error('Error handling payment failure:', error)
  }
}

/**
 * Create school account with retry mechanism
 */
async function createSchoolAccountWithRetry(data: {
  schoolCode: string
  schoolName: string
  schoolAddress: string
  adminEmail: string
  adminFirstName: string
  adminLastName: string
  selectedPlan: string
  stripeCustomerId: string
  stripeSessionId: string
  subscriptionStatus: string
  paymentAmount: number
  currency: string
}) {
  try {
    // Try to create school account
    return await createSchoolAccount(data)
  } catch (error: any) {
    console.error('School account creation failed, queuing for retry:', error)

    // Store failed payment for retry
    await storeFailedPayment({
      stripeSessionId: data.stripeSessionId,
      stripeCustomerId: data.stripeCustomerId,
      paymentAmount: data.paymentAmount,
      currency: data.currency,
      schoolData: {
        schoolCode: data.schoolCode,
        schoolName: data.schoolName,
        schoolAddress: data.schoolAddress,
        selectedPlan: data.selectedPlan
      },
      adminData: {
        adminEmail: data.adminEmail,
        adminFirstName: data.adminFirstName,
        adminLastName: data.adminLastName
      },
      errorDetails: {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
    })

    // Send support notification
    await sendSupportNotification(data, error)

    // Re-throw error to maintain webhook failure behavior
    throw error
  }
}

/**
 * Store failed payment for retry processing
 */
async function storeFailedPayment(data: {
  stripeSessionId: string
  stripeCustomerId: string
  paymentAmount: number
  currency: string
  schoolData: any
  adminData: any
  errorDetails: any
}) {
  try {
    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { error } = await supabase
      .from('failed_payments')
      .insert({
        stripe_session_id: data.stripeSessionId,
        stripe_customer_id: data.stripeCustomerId,
        payment_amount: data.paymentAmount,
        currency: data.currency,
        school_data: data.schoolData,
        admin_data: data.adminData,
        error_details: data.errorDetails,
        next_retry_at: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
      })

    if (error) {
      console.error('Failed to store failed payment:', error)
    } else {
      console.log('Failed payment stored for retry:', data.stripeSessionId)
    }
  } catch (error) {
    console.error('Error storing failed payment:', error)
  }
}

/**
 * Send support notification email
 */
async function sendSupportNotification(data: any, error: any) {
  try {
    // TODO: Implement email <NAME_EMAIL>
    console.log('🚨 SUPPORT ALERT: School creation failed')
    console.log('Session ID:', data.stripeSessionId)
    console.log('School Code:', data.schoolCode)
    console.log('Admin Email:', data.adminEmail)
    console.log('Error:', error.message)

    // For now, just log. In production, integrate with email service
  } catch (notificationError) {
    console.error('Failed to send support notification:', notificationError)
  }
}

/**
 * Create school account in database
 */
async function createSchoolAccount(data: {
  schoolCode: string
  schoolName: string
  schoolAddress: string
  adminEmail: string
  adminFirstName: string
  adminLastName: string
  selectedPlan: string
  stripeCustomerId: string
  stripeSessionId: string
  subscriptionStatus: string
}) {
  try {
    console.log('Creating school account:', data)

    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // 1. Create or get admin user account
    let adminUser = null

    // Try to find existing user by email using listUsers
    const { data: usersList, error: listError } = await supabase.auth.admin.listUsers()

    if (listError) {
      console.error('Error listing users:', listError)
      throw new Error('Failed to check existing users')
    }

    const existingUser = usersList.users?.find(user => user.email === data.adminEmail)

    if (existingUser) {
      adminUser = existingUser
      console.log('Using existing admin user:', adminUser.id)
    } else {
      // Create new admin user
      const { data: newUser, error: createUserError } = await supabase.auth.admin.createUser({
        email: data.adminEmail,
        email_confirm: true, // Auto-confirm email for paid users
        user_metadata: {
          full_name: `${data.adminFirstName} ${data.adminLastName}`.trim(),
          first_name: data.adminFirstName,
          last_name: data.adminLastName,
          is_school_admin: true
        }
      })

      if (createUserError || !newUser.user) {
        console.error('Error creating admin user:', createUserError)
        throw new Error('Failed to create admin user')
      }

      adminUser = newUser.user
      console.log('Created new admin user:', adminUser.id)
    }

    // 2. Create school record
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .insert({
        name: data.schoolName,
        code: data.schoolCode.toLowerCase(),
        admin_user_id: adminUser.id,
        address: data.schoolAddress || null,
        contact_email: data.adminEmail,
        subscription_status: data.subscriptionStatus,
        subscription_expires_at: data.subscriptionStatus === 'trialing'
          ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days trial
          : null,
        settings: {
          stripe_customer_id: data.stripeCustomerId,
          stripe_session_id: data.stripeSessionId,
          selected_plan: data.selectedPlan,
          created_via: 'stripe_checkout'
        }
      })
      .select()
      .single()

    if (schoolError || !school) {
      console.error('Error creating school:', schoolError)
      throw new Error('Failed to create school record')
    }

    console.log('Created school:', school.id)

    // 3. Create school membership for admin
    const { data: membership, error: membershipError } = await supabase
      .from('school_memberships')
      .insert({
        user_id: adminUser.id,
        school_id: school.id,
        role: 'admin',
        status: 'active'
      })
      .select()
      .single()

    if (membershipError) {
      console.error('Error creating school membership:', membershipError)
      // Don't throw here - school is created, membership can be fixed later
    }

    // 4. Update user profile with school admin flag and proper role
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: adminUser.id,
        full_name: `${data.adminFirstName} ${data.adminLastName}`.trim(),
        is_school_admin: true,
        school_id: school.id,
        role: { code: "as", label: "Admin Sekolah" },
        teaching_days_mode: "weekdays",
        is_profile_complete: true // Admin profile is complete after payment
      })

    if (profileError) {
      console.error('Error updating user profile:', profileError)
      // Don't throw here - school is created, profile can be fixed later
    }

    // 5. Clean up pre-billing data
    const { error: cleanupError } = await supabase
      .from('pre_billing')
      .delete()
      .eq('admin_email', data.adminEmail)
      .eq('billing_complete', false)

    if (cleanupError) {
      console.error('Error cleaning up pre-billing data:', cleanupError)
      // Don't throw here - school is created, cleanup can be done later
    } else {
      console.log('Pre-billing data cleaned up successfully')
    }

    console.log('School account creation completed successfully')

    return {
      success: true,
      schoolId: school.id,
      adminId: adminUser.id,
      schoolCode: school.code,
      schoolName: school.name
    }

  } catch (error: any) {
    console.error('Error in createSchoolAccount:', error)
    throw error
  }
}
