<template>
  <Transition name="toast" appear @enter="onEnter" @leave="onLeave">
    <div v-if="visible" :class="toastClasses" role="alert" :aria-live="toast.type === 'error' ? 'assertive' : 'polite'">
      <div class="flex items-start">
        <!-- Icon -->
        <div class="flex-shrink-0">
          <Icon :name="iconName" :class="iconClasses" />
        </div>

        <!-- Content -->
        <div class="ml-3 flex-1">
          <div v-if="toast.title" class="text-sm font-medium" :class="titleClasses">
            {{ toast.title }}
          </div>
          <div class="text-sm" :class="messageClasses">
            {{ toast.message }}
          </div>
        </div>

        <!-- Close button -->
        <div class="ml-4 flex-shrink-0 flex">
          <button @click="$emit('close')" :class="closeButtonClasses" aria-label="Tutup notifikasi">
            <Icon name="mdi:close" class="h-5 w-5" />
          </button>
        </div>
      </div>

      <!-- Progress bar for duration -->
      <div v-if="showProgressBar"
        class="absolute bottom-0 left-0 h-1 bg-current opacity-30 transition-all duration-100 ease-linear"
        :style="{ width: `${progressPercentage}%` }"></div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { Toast } from '~/composables/useToast'

interface Props {
  toast: Toast
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const visible = ref(true)
const progressPercentage = ref(100)
let progressInterval: ReturnType<typeof setInterval> | null = null

// Show progress bar only for non-persistent toasts with duration
const showProgressBar = computed(() =>
  !props.toast.persistent &&
  props.toast.duration &&
  props.toast.duration > 0
)

// Toast styling based on type
const toastClasses = computed(() => [
  'relative w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
  'p-4',
  {
    'ring-green-200 dark:ring-green-800': props.toast.type === 'success',
    'ring-red-200 dark:ring-red-800': props.toast.type === 'error',
    'ring-yellow-200 dark:ring-yellow-800': props.toast.type === 'warning',
    'ring-blue-200 dark:ring-blue-800': props.toast.type === 'info',
  }
])

const iconName = computed(() => {
  const icons = {
    success: 'mdi:check-circle',
    error: 'mdi:alert-circle',
    warning: 'mdi:alert',
    info: 'mdi:information'
  }
  return icons[props.toast.type]
})

const iconClasses = computed(() => [
  'h-6 w-6',
  {
    'text-green-400': props.toast.type === 'success',
    'text-red-400': props.toast.type === 'error',
    'text-yellow-400': props.toast.type === 'warning',
    'text-blue-400': props.toast.type === 'info',
  }
])

const titleClasses = computed(() => [
  {
    'text-green-800 dark:text-green-200': props.toast.type === 'success',
    'text-red-800 dark:text-red-200': props.toast.type === 'error',
    'text-yellow-800 dark:text-yellow-200': props.toast.type === 'warning',
    'text-blue-800 dark:text-blue-200': props.toast.type === 'info',
  }
])

const messageClasses = computed(() => [
  {
    'text-green-700 dark:text-green-300': props.toast.type === 'success',
    'text-red-700 dark:text-red-300': props.toast.type === 'error',
    'text-yellow-700 dark:text-yellow-300': props.toast.type === 'warning',
    'text-blue-700 dark:text-blue-300': props.toast.type === 'info',
  }
])

const closeButtonClasses = computed(() => [
  'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors',
  {
    'text-green-500 hover:bg-green-100 focus:ring-green-600 dark:text-green-400 dark:hover:bg-green-900': props.toast.type === 'success',
    'text-red-500 hover:bg-red-100 focus:ring-red-600 dark:text-red-400 dark:hover:bg-red-900': props.toast.type === 'error',
    'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600 dark:text-yellow-400 dark:hover:bg-yellow-900': props.toast.type === 'warning',
    'text-blue-500 hover:bg-blue-100 focus:ring-blue-600 dark:text-blue-400 dark:hover:bg-blue-900': props.toast.type === 'info',
  }
])

const startProgressBar = () => {
  if (!showProgressBar.value) return

  const duration = props.toast.duration!
  const interval = 50 // Update every 50ms for smooth animation
  const decrement = (interval / duration) * 100

  progressInterval = setInterval(() => {
    progressPercentage.value -= decrement
    if (progressPercentage.value <= 0) {
      progressPercentage.value = 0
      if (progressInterval) {
        clearInterval(progressInterval)
        progressInterval = null
      }
    }
  }, interval)
}

const onEnter = () => {
  startProgressBar()
}

const onLeave = () => {
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }
}

onMounted(() => {
  startProgressBar()
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }
})
</script>

<style scoped>
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.2s ease-in;
}

.toast-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}
</style>
