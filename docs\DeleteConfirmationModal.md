# DeleteConfirmationModal Component

A flexible, reusable confirmation modal for delete operations across the system.

## Features

- **Consistent UI**: Standardized delete confirmation experience
- **Flexible Content**: Customizable messages, icons, and styling
- **Impact Communication**: Show consequences of deletion with different severity levels
- **Extensible**: Slots for custom content and item details
- **Accessible**: Proper ARIA attributes and keyboard navigation
- **Loading States**: Built-in loading indicator for async operations

## Basic Usage

```vue
<template>
  <DeleteConfirmationModal
    :is-open="showDeleteModal"
    item-type="Rancangan Pengajaran"
    :item-name="planToDelete?.file_name"
    @confirm="confirmDelete"
    @cancel="showDeleteModal = false"
  />
</template>
```

## Advanced Usage

### With Custom Content and Impact

```vue
<template>
  <DeleteConfirmationModal
    :is-open="showDeleteModal"
    title="Padam Kelas & Subjek"
    item-type="kombinasi kelas-subjek"
    danger-level="high"
    impact-severity="high"
    :loading="isDeleting"
    @confirm="confirmDelete"
    @cancel="cancelDelete"
  >
    <!-- Custom item details -->
    <template #item-details>
      <div v-for="item in itemsToDelete" :key="item.id" class="flex items-center space-x-2">
        <Icon name="mdi:minus-circle" class="h-4 w-4 text-red-500" />
        <span>{{ item.className }} - {{ item.subjectName }}</span>
      </div>
    </template>

    <!-- Custom impact message -->
    <template #impact>
      <div>
        <p><strong>{{ affectedSlots.length }} slot jadual waktu</strong> akan dipadam secara automatik:</p>
        <ul class="mt-2 space-y-1 text-xs">
          <li v-for="slot in affectedSlots" :key="slot.id">
            • {{ slot.day }}, {{ slot.time }} - {{ slot.className }}
          </li>
        </ul>
      </div>
    </template>
  </DeleteConfirmationModal>
</template>
```

## Props

### Core Props
- `isOpen: boolean` - Controls modal visibility
- `title?: string` - Custom modal title (auto-generated if not provided)
- `zIndex?: number` - Modal z-index (default: 50)
- `loading?: boolean` - Shows loading state (default: false)

### Content Props
- `itemType?: string` - Type of item being deleted (e.g., 'Rancangan Pengajaran')
- `itemName?: string` - Name/identifier of the item
- `itemSubtitle?: string` - Additional info (date, location, etc.)
- `confirmationMessage?: string` - Custom confirmation message
- `warningMessage?: string` - Warning text (default: 'Tindakan ini tidak boleh dibatalkan.')

### Button Props
- `confirmText?: string` - Confirm button text (default: 'Padam')
- `cancelText?: string` - Cancel button text (default: 'Batal')
- `loadingText?: string` - Loading button text (default: 'Memadam...')
- `confirmIcon?: string` - Confirm button icon (default: 'mdi:delete')

### Styling Props
- `dangerLevel?: 'low' | 'medium' | 'high'` - Controls button color and overall severity (default: 'high')
- `alertIcon?: string` - Main alert icon (default: 'mdi:alert-circle')

### Item Details Props
- `showItemDetails?: boolean` - Show item details section (default: true)
- `itemDetailsLabel?: string` - Label for item details (default: 'Item yang akan dipadam')

### Impact Props
- `impactMessage?: string` - Impact/consequence message
- `impactSeverity?: 'low' | 'medium' | 'high'` - Impact section styling (default: 'medium')
- `impactIcon?: string` - Impact section icon (default: 'mdi:information')

## Events

- `@confirm` - Emitted when user confirms deletion
- `@cancel` - Emitted when user cancels
- `@update:is-open` - Emitted when modal should be closed

## Slots

### `item-details`
Custom content for the item details section:
```vue
<template #item-details>
  <div class="space-y-2">
    <p>{{ item.name }}</p>
    <p class="text-xs text-gray-500">{{ item.metadata }}</p>
  </div>
</template>
```

### `content`
Additional custom content between item details and impact:
```vue
<template #content>
  <div class="bg-yellow-50 p-3 rounded-lg">
    <p class="text-sm text-yellow-800">This action will also affect related items.</p>
  </div>
</template>
```

### `impact`
Custom impact/consequence content:
```vue
<template #impact>
  <div>
    <p><strong>Consequences:</strong></p>
    <ul class="mt-1 text-xs space-y-1">
      <li>• All related data will be removed</li>
      <li>• Backup will be created automatically</li>
    </ul>
  </div>
</template>
```

## Examples by Use Case

### 1. Simple Item Deletion
```vue
<DeleteConfirmationModal
  :is-open="showModal"
  item-type="fail"
  :item-name="file.name"
  @confirm="deleteFile"
  @cancel="closeModal"
/>
```

### 2. High-Impact Deletion with Consequences
```vue
<DeleteConfirmationModal
  :is-open="showModal"
  item-type="minggu"
  :item-name="week.name"
  danger-level="high"
  impact-severity="high"
  impact-message="<strong>Semua RPH, refleksi, dan fail</strong> untuk minggu ini akan dipadam secara kekal."
  @confirm="deleteWeek"
  @cancel="closeModal"
/>
```

### 3. Bulk Deletion
```vue
<DeleteConfirmationModal
  :is-open="showModal"
  :item-type="`${selectedItems.length} item`"
  item-name=""
  :confirmation-message="`Adakah anda pasti untuk memadam ${selectedItems.length} item yang dipilih?`"
  danger-level="high"
  @confirm="bulkDelete"
  @cancel="closeModal"
>
  <template #item-details>
    <div class="max-h-32 overflow-y-auto space-y-1">
      <div v-for="item in selectedItems" :key="item.id" class="text-sm">
        • {{ item.name }}
      </div>
    </div>
  </template>
</DeleteConfirmationModal>
```

## Styling Guidelines

### Danger Levels
- **Low**: Blue theme, informational
- **Medium**: Yellow theme, cautionary  
- **High**: Red theme, destructive (default)

### Impact Severity
- **Low**: Minimal consequences
- **Medium**: Some side effects
- **High**: Major consequences or irreversible actions

## Accessibility

The component includes:
- Proper ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader compatibility

## Migration from Existing Components

### From RPH DeleteConfirmationModal
```vue
<!-- Old -->
<DeleteConfirmationModal
  :is-open="showModal"
  item-type="Rancangan Pengajaran"
  :item-name-to-delete="plan.file_name"
  :week-label-to-delete="week.name"
  @confirm="deletePlan"
  @close="closeModal"
/>

<!-- New -->
<UiCompositeDeleteConfirmationModal
  :is-open="showModal"
  item-type="Rancangan Pengajaran"
  :item-name="plan.file_name"
  :item-subtitle="week.name"
  @confirm="deletePlan"
  @cancel="closeModal"
/>
```

### From Custom Confirm Dialogs
```vue
<!-- Old -->
<Modal :is-open="showModal">
  <div>Are you sure you want to delete this?</div>
  <template #footer>
    <Button @click="closeModal">Cancel</Button>
    <Button @click="confirm">Delete</Button>
  </template>
</Modal>

<!-- New -->
<UiCompositeDeleteConfirmationModal
  :is-open="showModal"
  item-type="item"
  :item-name="itemName"
  @confirm="confirm"
  @cancel="closeModal"
/>
```
