import { mount } from "@vue/test-utils";
import { describe, it, expect, vi, beforeEach, type Mock } from "vitest";

vi.stubGlobal("definePageMeta", vi.fn());

import ItemsPage from "../items.vue";
import { useDataStore } from "~/stores/data";
import type { SupabaseClient } from "@supabase/supabase-js";
import { ref } from "vue";
import { setActivePinia, createPinia } from "pinia";

// Define mocks for Nuxt composables
const mockUseFetch = vi.fn();
const mockUseRoute = vi.fn(() => ({ params: { id: "1" } }));
const mockUseRouter = vi.fn(() => ({ push: vi.fn() }));
const mockUseSupabaseClient = vi.fn();

vi.mock("#imports", () => ({
  useFetch: mockUseFetch,
  useRoute: mockUseRoute,
  useRouter: mockUseRouter,
  useSupabaseClient: mockUseSupabaseClient,
}));

// Mock local composables
let capturedSubscriptionHandler: ((payload: any) => void) | null = null;

const channelMock = {
  on: vi.fn(function (
    this: any,
    eventType: string,
    filter: any,
    callback: (payload: any) => void
  ) {
    if (eventType === "postgres_changes" && filter.table === "items") {
      capturedSubscriptionHandler = callback;
    }
    return this; // for chaining
  }),
  subscribe: vi.fn((callback?: (status: string, err?: any) => void) => {
    if (callback) {
      // Simulate async subscription confirmation
      Promise.resolve().then(() => callback("SUBSCRIBED"));
    }
  }),
};

const mockSupabaseClientInstance = {
  auth: {
    getSession: vi
      .fn()
      .mockResolvedValue({ data: { session: { user: { id: "123" } } } }),
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      order: vi.fn().mockResolvedValue({ data: [], error: null }), // Mock order method
    })),
    insert: vi.fn().mockResolvedValue({
      data: [{ id: 99, title: "Inserted Item", description: "Inserted Desc" }],
      error: null,
    }), // Mock insert method
  })),
  channel: vi.fn(() => channelMock),
} as unknown as SupabaseClient;

vi.mock("~/composables/useSupabase", () => ({
  useSupabase: vi.fn(() => ({ client: mockSupabaseClientInstance })),
}));

describe("Items Page", () => {
  let store: ReturnType<typeof useDataStore>;

  const mockSupabaseItems = [
    { id: 1, title: "Item 1 from Supabase", description: "Description 1" },
    { id: 2, title: "Item 2 from Supabase", description: "Description 2" },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    setActivePinia(createPinia());
    store = useDataStore();
    store.$patch({ items: [] });
    vi.spyOn(store, "addItem");
    capturedSubscriptionHandler = null; // Reset for each test

    mockUseFetch.mockReturnValue({
      data: ref(null),
      pending: ref(true),
      error: ref(null),
      refresh: vi.fn(),
    });

    // Reset Supabase client mock for 'from' to return the chained methods correctly for each test
    mockSupabaseClientInstance.from = vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi
          .fn()
          .mockResolvedValue({ data: [...mockSupabaseItems], error: null }),
      })),
      insert: vi.fn().mockResolvedValue({
        data: [
          { id: 99, title: "Inserted Item", description: "Inserted Desc" },
        ],
        error: null,
      }),
    })) as any;
    // Ensure useSupabaseClient itself returns the mocked instance
    mockUseSupabaseClient.mockReturnValue(mockSupabaseClientInstance);
  });

  it("renders loading state initially when pending is true", async () => {
    mockUseFetch.mockReturnValueOnce({
      data: ref(null),
      pending: ref(true),
      error: ref(null),
      refresh: vi.fn(),
    });

    const wrapper = mount(ItemsPage, {
      global: {
        stubs: {
          NuxtLink: true,
          UiCompositeCard: {
            // Renamed from Card
            template:
              '<div><slot name="header"></slot><slot></slot><slot name="footer"></slot></div>',
          },
          UiBaseButton: { template: "<button><slot></slot></button>" }, // Renamed from Button
        },
      },
    });
    // Check for a more specific loading element if possible, or a substring
    expect(wrapper.html()).toContain("Loading items...");
  });

  it("renders error message if fetching fails", async () => {
    const fetchError = new Error("Failed to fetch from Supabase"); // More specific error
    // Mock the select().order() chain to produce an error
    (mockSupabaseClientInstance.from as Mock).mockReturnValueOnce({
      select: vi.fn(() => ({
        order: vi.fn().mockResolvedValue({ data: null, error: fetchError }),
      })),
      insert: vi.fn(), // Keep insert available if needed by other parts of component setup
    });

    // Simulate useFetch also reflecting this error state if it's directly tied
    mockUseFetch.mockReturnValueOnce({
      data: ref(null),
      pending: ref(false),
      error: ref(fetchError), // useFetch's error reflects the Supabase error
      refresh: vi.fn(),
    });

    const wrapper = mount(ItemsPage, {
      global: {
        stubs: {
          NuxtLink: true,
          UiCompositeCard: {
            // Renamed from Card
            template:
              '<div><slot name="header"></slot><slot></slot><slot name="footer"></slot></div>',
          },
          UiBaseButton: { template: "<button><slot></slot></button>" }, // Renamed from Button
        },
      },
    });
    await wrapper.vm.$nextTick();
    await wrapper.vm.$nextTick(); // Additional tick
    expect(wrapper.html()).toContain(
      "Error loading items: Failed to fetch from Supabase"
    );
  });

  it("renders items from Supabase (via mocked Supabase client)", async () => {
    const supabaseItemsToRender = [
      { id: 3, title: "Supabase Item 3 Test", description: "Desc 3" },
      { id: 4, title: "Supabase Item 4 Test", description: "Desc 4" },
    ];

    // Mock Supabase success for initial fetch
    (mockSupabaseClientInstance.from as Mock).mockReturnValueOnce({
      select: vi.fn(() => ({
        order: vi
          .fn()
          .mockResolvedValue({ data: [...supabaseItemsToRender], error: null }),
      })),
      insert: vi.fn().mockResolvedValue({ data: [], error: null }), // Keep insert mock consistent
    });

    // This specific mockUseFetch is not strictly necessary if items.vue doesn't use useFetch for this
    mockUseFetch.mockReturnValueOnce({
      data: ref([...supabaseItemsToRender]), // Or ref(null) if component manages its own data state
      pending: ref(false),
      error: ref(null),
      refresh: vi.fn(),
    });

    const wrapper = mount(ItemsPage, {
      global: {
        stubs: {
          NuxtLink: true,
          UiCompositeCard: {
            // Renamed from Card
            template:
              '<div><slot name="header"></slot><slot></slot><slot name="footer"></slot></div>',
          },
          UiBaseButton: { template: "<button><slot></slot></button>" }, // Renamed from Button
        },
      },
    });

    await wrapper.vm.$nextTick(); // for onMounted
    await wrapper.vm.$nextTick(); // for fetchItems to complete and DOM to update

    // console.log(wrapper.html()); // For debugging

    supabaseItemsToRender.forEach((item) => {
      expect(wrapper.text()).toContain(item.title);
      // Optionally check for description if it's part of the default slot
      // expect(wrapper.text()).toContain(item.description);
    });
    // Removed Pinia specific checks here as they are not directly reflected without subscription
  });

  it("adds an item to the Pinia store when 'Add New Item' button is clicked", async () => {
    const initialSupabaseItems = [
      { id: 1, title: "Initial Item 1", description: "Desc 1" },
    ];
    const newItemFromSupabase = {
      id: 100,
      title: "New Item Added via Test",
      description: "Test Desc",
    };

    // Mock for initial fetch in onMounted
    (mockSupabaseClientInstance.from as Mock).mockImplementation((): any => {
      // This will be called for the initial fetch and then for the insert
      // We need to differentiate or make the insert mock more specific if `from` is called multiple times with different intentions.
      // For this test, let's assume the first call to `from().select().order()` is for initial load,
      // and the next call to `from().insert()` is for the new item.
      // A more robust way would be to have `mockSupabaseClientInstance.from` return different mocks based on chained calls.
      // However, given the structure, we can mock `insert` directly on the object returned by `from()`.

      const fromMockObject = {
        select: vi.fn(() => ({
          order: vi.fn().mockResolvedValue({
            data: [...initialSupabaseItems],
            error: null,
          }),
        })),
        insert: vi
          .fn()
          .mockResolvedValue({ data: [newItemFromSupabase], error: null }), // This will be called by addNewItem
      };
      return fromMockObject;
    });

    mockUseFetch.mockReturnValueOnce({
      // For initial load if any part of setup uses it
      data: ref([...initialSupabaseItems]),
      pending: ref(false),
      error: ref(null),
      refresh: vi.fn(),
    });

    store.$patch({ items: [] }); // Start with an empty store

    const wrapper = mount(ItemsPage, {
      global: {
        stubs: {
          NuxtLink: true,
          UiCompositeCard: {
            // Renamed from Card
            template:
              '<div><slot name="header"></slot><slot></slot><slot name="footer"></slot></div>',
          },
          UiBaseButton: {
            // Renamed from Button
            template:
              '<button data-testid="add-new-item-button" @click="$emit(\'click\')"><slot></slot></button>', // Matched data-testid
          },
        },
      },
    });

    await wrapper.vm.$nextTick(); // onMounted, initial fetch, and subscription setup
    await wrapper.vm.$nextTick(); // ensure all async operations from onMounted are processed

    const addButton = wrapper.find('button[data-testid="add-new-item-button"]'); // Matched data-testid
    expect(addButton.exists()).toBe(true);

    await addButton.trigger("click"); // Calls addNewItem, which calls client.insert

    // Wait for addNewItem's async insert call to be notionally complete
    // The actual store update happens via subscription
    await wrapper.vm.$nextTick();

    // Simulate the Supabase real-time event
    expect(capturedSubscriptionHandler).not.toBeNull();
    if (capturedSubscriptionHandler) {
      const mockPayload = {
        eventType: "INSERT",
        schema: "public",
        table: "items",
        new: newItemFromSupabase, // This is the data that store.addItem should receive
      };
      capturedSubscriptionHandler(mockPayload);
    }

    await wrapper.vm.$nextTick(); // Allow Pinia store update and component re-render

    expect(store.addItem).toHaveBeenCalledTimes(1);
    expect(store.addItem).toHaveBeenCalledWith(
      expect.objectContaining({
        title: "New Item Added via Test",
      })
    );
  });
});
