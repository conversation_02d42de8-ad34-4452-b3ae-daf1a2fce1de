-- Fix timetable_entries table to use correct data types
-- class_id should be text (like 't1', 'f1') not UUID
-- subject_id should remain UUID

-- Drop existing table if needed (be careful in production!)
-- DROP TABLE IF EXISTS public.timetable_entries;

-- Recreate table with correct data types
ALTER TABLE public.timetable_entries 
  ALTER COLUMN class_id TYPE text;

-- Update the unique constraint to reflect the new data type
-- First drop the existing constraint
ALTER TABLE public.timetable_entries 
  DROP CONSTRAINT IF EXISTS timetable_entries_user_day_time_unique;

-- Recreate the constraint with the new data type
ALTER TABLE public.timetable_entries 
  ADD CONSTRAINT timetable_entries_user_day_time_unique 
  UNIQUE (user_id, day, time_slot_start, time_slot_end);

-- Update the index to reflect the new data type
DROP INDEX IF EXISTS idx_timetable_entries_class_subject;
CREATE INDEX idx_timetable_entries_class_subject 
  ON public.timetable_entries USING btree (class_id, subject_id);
