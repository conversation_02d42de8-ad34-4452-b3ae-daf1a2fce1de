#!/bin/bash

# Production Deployment Script for RPHMate
# Created: 2025-07-13
# Purpose: Deploy the multi-tenant SaaS application to production

set -e  # Exit on any error

# Configuration
APP_NAME="rphmate"
DOMAIN="yourdomain.com"
SERVER_USER="deploy"
SERVER_HOST="your-server.com"
DEPLOY_PATH="/var/www/$APP_NAME"
PM2_APP_NAME="$APP_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if required commands exist
    command -v npm >/dev/null 2>&1 || error "npm is required but not installed"
    command -v rsync >/dev/null 2>&1 || error "rsync is required but not installed"
    command -v ssh >/dev/null 2>&1 || error "ssh is required but not installed"
    command -v curl >/dev/null 2>&1 || error "curl is required but not installed"
    
    # Check if .env.production exists
    if [ ! -f ".env.production" ]; then
        error ".env.production file not found"
    fi
    
    # Check SSH connection
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" exit 2>/dev/null; then
        error "Cannot connect to server $SERVER_USER@$SERVER_HOST"
    fi
    
    success "Prerequisites check passed"
}

# Run tests
run_tests() {
    log "Running tests..."
    
    # Type checking
    if npm run typecheck; then
        success "TypeScript checks passed"
    else
        error "TypeScript checks failed"
    fi
    
    # Unit tests (if available)
    if npm run test:unit 2>/dev/null; then
        success "Unit tests passed"
    else
        warning "Unit tests not available or failed"
    fi
    
    # Lint checks
    if npm run lint 2>/dev/null; then
        success "Lint checks passed"
    else
        warning "Lint checks not available or failed"
    fi
}

# Build application
build_application() {
    log "Building application for production..."
    
    # Install dependencies
    npm ci --production=false
    
    # Build the application
    if npm run build; then
        success "Application built successfully"
    else
        error "Application build failed"
    fi
    
    # Check if .output directory exists
    if [ ! -d ".output" ]; then
        error "Build output directory not found"
    fi
}

# Create deployment package
create_deployment_package() {
    log "Creating deployment package..."
    
    # Create temporary directory
    TEMP_DIR=$(mktemp -d)
    PACKAGE_NAME="$APP_NAME-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # Copy build output
    cp -r .output/* "$TEMP_DIR/"
    
    # Copy additional files
    cp package.json "$TEMP_DIR/" 2>/dev/null || true
    cp .env.production "$TEMP_DIR/.env" 2>/dev/null || true
    
    # Create package
    tar -czf "$PACKAGE_NAME" -C "$TEMP_DIR" .
    
    # Cleanup
    rm -rf "$TEMP_DIR"
    
    echo "$PACKAGE_NAME"
    success "Deployment package created: $PACKAGE_NAME"
}

# Deploy to server
deploy_to_server() {
    local package_name=$1
    
    log "Deploying to server..."
    
    # Create backup of current deployment
    ssh "$SERVER_USER@$SERVER_HOST" "
        if [ -d '$DEPLOY_PATH' ]; then
            sudo cp -r '$DEPLOY_PATH' '$DEPLOY_PATH.backup.$(date +%Y%m%d-%H%M%S)'
            echo 'Backup created'
        fi
    "
    
    # Upload package
    scp "$package_name" "$SERVER_USER@$SERVER_HOST:/tmp/"
    
    # Extract and deploy
    ssh "$SERVER_USER@$SERVER_HOST" "
        sudo mkdir -p '$DEPLOY_PATH'
        cd '$DEPLOY_PATH'
        sudo tar -xzf '/tmp/$package_name'
        sudo chown -R $SERVER_USER:$SERVER_USER '$DEPLOY_PATH'
        rm '/tmp/$package_name'
        echo 'Package deployed'
    "
    
    # Install production dependencies if package.json exists
    ssh "$SERVER_USER@$SERVER_HOST" "
        if [ -f '$DEPLOY_PATH/package.json' ]; then
            cd '$DEPLOY_PATH'
            npm ci --production
            echo 'Dependencies installed'
        fi
    "
    
    success "Application deployed to server"
}

# Update server configuration
update_server_config() {
    log "Updating server configuration..."
    
    # Update Nginx configuration
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Backup current nginx config
        sudo cp /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-available/$APP_NAME.backup.$(date +%Y%m%d-%H%M%S) 2>/dev/null || true
        
        # Create/update nginx config
        sudo tee /etc/nginx/sites-available/$APP_NAME > /dev/null << 'EOF'
server {
    listen 443 ssl http2;
    server_name $DOMAIN *.$DOMAIN;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection \"1; mode=block\";
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\";
    
    # Proxy to Nuxt.js application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name $DOMAIN *.$DOMAIN;
    return 301 https://\$server_name\$request_uri;
}
EOF
        
        # Enable site
        sudo ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
        
        # Test nginx configuration
        sudo nginx -t
        
        echo 'Nginx configuration updated'
    "
    
    success "Server configuration updated"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # Restart application with PM2
        cd '$DEPLOY_PATH'
        
        # Stop existing process
        pm2 stop '$PM2_APP_NAME' 2>/dev/null || true
        pm2 delete '$PM2_APP_NAME' 2>/dev/null || true
        
        # Start new process
        pm2 start ecosystem.config.js --name '$PM2_APP_NAME' || pm2 start npm --name '$PM2_APP_NAME' -- start
        pm2 save
        
        # Restart nginx
        sudo systemctl reload nginx
        
        echo 'Services restarted'
    "
    
    success "Services restarted successfully"
}

# Test deployment
test_deployment() {
    log "Testing deployment..."
    
    # Wait for application to start
    sleep 10
    
    # Test main domain
    if curl -f -s "https://$DOMAIN/health" > /dev/null; then
        success "Main domain health check passed"
    else
        error "Main domain health check failed"
    fi
    
    # Test subdomain
    if curl -f -s "https://demo.$DOMAIN/health" > /dev/null; then
        success "Subdomain health check passed"
    else
        warning "Subdomain health check failed (may be expected if demo school doesn't exist)"
    fi
    
    # Test SSL
    if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" < /dev/null 2>/dev/null | grep -q "Verify return code: 0"; then
        success "SSL certificate verification passed"
    else
        warning "SSL certificate verification failed"
    fi
    
    success "Deployment testing completed"
}

# Cleanup
cleanup() {
    log "Cleaning up..."
    
    # Remove local package
    rm -f "$APP_NAME"-*.tar.gz
    
    # Clean old backups on server (keep last 5)
    ssh "$SERVER_USER@$SERVER_HOST" "
        cd '$DEPLOY_PATH'.backup.* 2>/dev/null && ls -t | tail -n +6 | xargs -r sudo rm -rf
    " 2>/dev/null || true
    
    success "Cleanup completed"
}

# Rollback function
rollback() {
    log "Rolling back deployment..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        LATEST_BACKUP=\$(ls -t '$DEPLOY_PATH'.backup.* 2>/dev/null | head -n 1)
        if [ -n \"\$LATEST_BACKUP\" ]; then
            sudo rm -rf '$DEPLOY_PATH'
            sudo mv \"\$LATEST_BACKUP\" '$DEPLOY_PATH'
            cd '$DEPLOY_PATH'
            pm2 restart '$PM2_APP_NAME'
            echo 'Rollback completed'
        else
            echo 'No backup found for rollback'
            exit 1
        fi
    "
    
    success "Rollback completed"
}

# Main deployment function
main() {
    echo -e "${BLUE}"
    echo "🚀 RPHMate Production Deployment"
    echo "===================================="
    echo -e "${NC}"
    
    # Parse command line arguments
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            run_tests
            build_application
            PACKAGE_NAME=$(create_deployment_package)
            deploy_to_server "$PACKAGE_NAME"
            update_server_config
            restart_services
            test_deployment
            cleanup
            
            echo -e "${GREEN}"
            echo "🎉 Deployment completed successfully!"
            echo "🌐 Main site: https://$DOMAIN"
            echo "🏫 Demo school: https://demo.$DOMAIN"
            echo -e "${NC}"
            ;;
        "rollback")
            rollback
            ;;
        "test")
            test_deployment
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [deploy|rollback|test|help]"
            echo ""
            echo "Commands:"
            echo "  deploy   - Deploy application to production (default)"
            echo "  rollback - Rollback to previous deployment"
            echo "  test     - Test current deployment"
            echo "  help     - Show this help message"
            ;;
        *)
            error "Unknown command: $1. Use 'help' for usage information."
            ;;
    esac
}

# Trap errors and provide rollback option
trap 'error "Deployment failed! Run \"$0 rollback\" to revert changes."' ERR

# Run main function
main "$@"
