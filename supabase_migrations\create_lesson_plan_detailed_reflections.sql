-- Create lesson_plan_detailed_reflections table for individual kelas-subjek-day reflections
CREATE TABLE IF NOT EXISTS lesson_plan_detailed_reflections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Foreign key to lesson plan
    lesson_plan_id UUID REFERENCES lesson_plans(id) ON DELETE CASCADE NOT NULL,
    
    -- Specific kelas-subjek combination (e.g., "f1_d21dcaa8-d036-4e0e-8717-bc587bed7825")
    class_subject_id TEXT NOT NULL,
    
    -- Day in Malay (e.g., "ahad", "isnin", "selasa", etc.)
    day TEXT NOT NULL,
    
    -- Quick mode fields (from existing reflection schema)
    overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
    objectives_achieved BOOLEAN NOT NULL DEFAULT false,
    challenges_faced TEXT NOT NULL,
    
    -- Detailed mode fields
    activity_effectiveness INTEGER CHECK (activity_effectiveness >= 1 AND activity_effectiveness <= 5),
    time_management TEXT CHECK (time_management IN ('on_time', 'early', 'late')),
    student_engagement INTEGER CHECK (student_engagement >= 1 AND student_engagement <= 5),
    resource_adequacy TEXT CHECK (resource_adequacy IN ('inadequate', 'adequate', 'excellent')),
    improvements_needed TEXT,
    successful_strategies TEXT,
    action_items JSONB,
    additional_notes TEXT
);

-- Create indexes for better query performance
CREATE INDEX idx_lesson_plan_detailed_reflections_lesson_plan_id ON lesson_plan_detailed_reflections(lesson_plan_id);
CREATE INDEX idx_lesson_plan_detailed_reflections_class_subject ON lesson_plan_detailed_reflections(class_subject_id);
CREATE INDEX idx_lesson_plan_detailed_reflections_day ON lesson_plan_detailed_reflections(day);
CREATE INDEX idx_lesson_plan_detailed_reflections_composite ON lesson_plan_detailed_reflections(lesson_plan_id, class_subject_id, day);

-- Add Row Level Security (RLS)
ALTER TABLE lesson_plan_detailed_reflections ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only access their own detailed reflections
CREATE POLICY "Users can only access their own detailed reflections" ON lesson_plan_detailed_reflections
    FOR ALL USING (
        lesson_plan_id IN (
            SELECT id FROM lesson_plans WHERE user_id = auth.uid()
        )
    );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_lesson_plan_detailed_reflections_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_lesson_plan_detailed_reflections_updated_at
    BEFORE UPDATE ON lesson_plan_detailed_reflections
    FOR EACH ROW
    EXECUTE FUNCTION update_lesson_plan_detailed_reflections_updated_at();

-- Add comments for documentation
COMMENT ON TABLE lesson_plan_detailed_reflections IS 'Individual reflections for specific kelas-subjek and day combinations within a lesson plan';
COMMENT ON COLUMN lesson_plan_detailed_reflections.class_subject_id IS 'Class-subject combination ID from lesson plan class_subject_ids array';
COMMENT ON COLUMN lesson_plan_detailed_reflections.day IS 'Day in Malay from lesson plan days_selected array';
COMMENT ON COLUMN lesson_plan_detailed_reflections.action_items IS 'Array of action items as JSON';
