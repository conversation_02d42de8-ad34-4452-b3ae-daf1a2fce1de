<template>
  <!-- Hero Section -->
  <section class="relative bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 overflow-hidden">
    <!-- Background Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-20 -right-20 w-80 h-80 bg-blue-100 dark:bg-blue-900/20 rounded-full animate-pulse"></div>
      <div class="absolute -bottom-20 -left-20 w-96 h-96 bg-blue-50 dark:bg-blue-900/10 rounded-full animate-pulse delay-1000"></div>
      <div class="absolute top-1/3 left-1/4 w-32 h-32 bg-blue-200 dark:bg-blue-800/20 rounded-full animate-pulse delay-500"></div>
    </div>

    <!-- Hero Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
      <div class="text-center">
        <!-- Badge -->
        <div class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-800 dark:text-blue-200 text-sm font-medium mb-8">
          <UiBaseIcon name="heroicons:sparkles" class="w-4 h-4 mr-2" />
          Streamline Your Teaching Journey
        </div>

        <!-- Main Heading -->
        <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white leading-tight mb-6">
          Transform Your
          <span class="text-blue-600 dark:text-blue-400">Educational</span>
          Experience
        </h1>

        <!-- Subtitle -->
        <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto mb-10">
          RPHMate is the comprehensive SaaS platform that empowers schools and teachers to manage lesson plans, 
          track student progress, and enhance teaching effectiveness with modern tools and insights.
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-16">
          <NuxtLink to="/pricing" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors shadow-lg hover:shadow-xl">
            Start Free Trial
          </NuxtLink>
          <a href="#features" class="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
            Learn More
          </a>
        </div>

        <!-- Trust Indicators -->
        <div class="text-center">
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">Trusted by educators worldwide</p>
          <div class="flex items-center justify-center space-x-8 opacity-60">
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-400">1000+</div>
              <div class="text-sm text-gray-500">Schools</div>
            </div>
            <div class="w-px h-8 bg-gray-300"></div>
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-400">10k+</div>
              <div class="text-sm text-gray-500">Teachers</div>
            </div>
            <div class="w-px h-8 bg-gray-300"></div>
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-400">100k+</div>
              <div class="text-sm text-gray-500">Lesson Plans</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Everything You Need to Excel
        </h2>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Comprehensive tools designed specifically for modern educational institutions and dedicated teachers.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Feature 1 -->
        <div class="text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-lg transition-shadow">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UiBaseIcon name="heroicons:document-text" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Lesson Planning</h3>
          <p class="text-gray-600 dark:text-gray-300">Create, organize, and manage comprehensive lesson plans with templates and resources.</p>
        </div>

        <!-- Feature 2 -->
        <div class="text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-lg transition-shadow">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UiBaseIcon name="heroicons:chart-bar" class="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Progress Tracking</h3>
          <p class="text-gray-600 dark:text-gray-300">Monitor student progress and performance with detailed analytics and insights.</p>
        </div>

        <!-- Feature 3 -->
        <div class="text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-lg transition-shadow">
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UiBaseIcon name="heroicons:users" class="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Collaboration</h3>
          <p class="text-gray-600 dark:text-gray-300">Connect and collaborate with fellow educators to share resources and best practices.</p>
        </div>

        <!-- Feature 4 -->
        <div class="text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-lg transition-shadow">
          <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UiBaseIcon name="heroicons:calendar-days" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Scheduling</h3>
          <p class="text-gray-600 dark:text-gray-300">Manage timetables, assignments, and important dates with integrated calendar tools.</p>
        </div>

        <!-- Feature 5 -->
        <div class="text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-lg transition-shadow">
          <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UiBaseIcon name="heroicons:clipboard-document-check" class="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Assessment Tools</h3>
          <p class="text-gray-600 dark:text-gray-300">Create and manage assessments with automated grading and feedback systems.</p>
        </div>

        <!-- Feature 6 -->
        <div class="text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-lg transition-shadow">
          <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center mx-auto mb-4">
            <UiBaseIcon name="heroicons:cloud-arrow-up" class="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
          </div>
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Cloud Storage</h3>
          <p class="text-gray-600 dark:text-gray-300">Secure cloud storage for all your educational materials and student data.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-blue-600 dark:bg-blue-800">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
        Ready to Transform Your Teaching?
      </h2>
      <p class="text-xl text-blue-100 mb-8">
        Join thousands of educators who are already using RPHMate to enhance their teaching experience.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <NuxtLink to="/pricing" class="bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
          Start Free Trial
        </NuxtLink>
        <NuxtLink to="/login" class="border border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
          Sign In
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Set page head
useHead({
  title: 'RPHMate - Transform Your Educational Experience',
  meta: [
    {
      name: 'description',
      content: 'RPHMate is the comprehensive SaaS platform that empowers schools and teachers to manage lesson plans, track student progress, and enhance teaching effectiveness with modern tools and insights.'
    },
    {
      property: 'og:title',
      content: 'RPHMate - Transform Your Educational Experience'
    },
    {
      property: 'og:description',
      content: 'Comprehensive educational platform for lesson planning, progress tracking, and enhanced teaching experiences.'
    }
  ]
})
</script>

<style scoped>
/* Custom animations */
@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
</style>
