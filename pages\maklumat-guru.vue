<template>
  <!-- Page Loading State -->
  <SkeletonLoader v-if="loading" variant="profile" />

  <div v-if="!loading" class="space-y-8">
    <!-- Page Header -->
    <UiCompositePageHeader title="Profil Pengguna" subtitle="Urus maklumat peribadi dan profesional anda"
      icon="heroicons:user-solid" />



    <!-- Profile Form -->
    <form @submit.prevent="handleSubmit" class="space-y-8">
      <!-- 1. Maklumat Peribadi -->
      <UiCompositeCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:identification-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Maklumat Peribadi</h2>
          </div>
        </template>
        <template #default>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Input id="ic-number" v-model="formData.ic_number" placeholder="No. Kad <PERSON>"
                @blur="validateField('ic_number')" :aria-invalid="!!formErrors?.ic_number" />
              <span v-if="formErrors?.ic_number" class="text-alert-error text-sm mt-1">
                {{ formErrors.ic_number.join(', ') }}
              </span>
            </div>

            <div>
              <SingleSelect id="teacher-type" :model-value="formData.teacher_type || null"
                @update:model-value="(val: string | null) => { formData.teacher_type = val; validateField('teacher_type'); }"
                :options="teacherTypeOptions" placeholder="Jenis Guru" :aria-invalid="!!formErrors?.teacher_type" />
              <span v-if="formErrors?.teacher_type" class="text-alert-error text-sm mt-1">
                {{ formErrors.teacher_type.join(', ') }}
              </span>
            </div>

            <div>
              <SingleSelect id="religion" :model-value="formData.religion || null"
                @update:model-value="(val: string | null) => { formData.religion = val; validateField('religion'); }"
                :options="religionOptions" placeholder="Agama" :aria-invalid="!!formErrors?.religion" />
              <span v-if="formErrors?.religion" class="text-alert-error text-sm mt-1">
                {{ formErrors.religion.join(', ') }}
              </span>
            </div>

            <div>
              <SingleSelect id="gender" :model-value="formData.gender || null"
                @update:model-value="(val: string | null) => { formData.gender = val; validateField('gender'); }"
                :options="genderOptions" placeholder="Jantina" :aria-invalid="!!formErrors?.gender" />
              <span v-if="formErrors?.gender" class="text-alert-error text-sm mt-1">
                {{ formErrors.gender.join(', ') }}
              </span>
            </div>

            <div class="md:col-span-2">
              <DatePicker id="date-of-birth" v-model="formData.date_of_birth" placeholder="Tarikh Lahir"
                @blur="validateField('date_of_birth')" :aria-invalid="!!formErrors?.date_of_birth" />
              <span v-if="formErrors?.date_of_birth" class="text-alert-error text-sm mt-1">
                {{ formErrors.date_of_birth.join(', ') }}
              </span>
            </div>
          </div>
        </template>
      </UiCompositeCard>

      <!-- 2. Opsyen -->
      <UiCompositeCard>
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <UiBaseIcon name="heroicons:cog-6-tooth-solid" class="w-5 h-5 text-primary" />
              <h2 class="text-xl font-semibold">Opsyen</h2>
            </div>
            <UiBaseButton type="button" @click="profileForm.addOption" variant="outline" size="sm"
              prepend-icon="heroicons:plus-solid">
              Tambah Opsyen
            </UiBaseButton>
          </div>
        </template>
        <template #default>
          <!-- Add new option form (appears when button clicked) -->
          <div v-if="profileForm.showAddOptionForm.value" class="mb-4">
            <div
              class="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
              <div class="flex-1">
                <Input v-model="profileForm.newOptionData.value.name" placeholder="Nama Opsyen" />
              </div>
              <div class="flex space-x-2">
                <UiBaseButton @click="() => profileForm.saveNewOption(formData)" variant="primary" size="sm"
                  :disabled="isOptionSimpanDisabled">
                  Simpan
                </UiBaseButton>
                <UiBaseButton @click="profileForm.cancelNewOption" variant="outline" size="sm">
                  Batal
                </UiBaseButton>
              </div>
            </div>
          </div>

          <!-- Separator -->
          <div v-if="profileForm.showAddOptionForm.value && formData.options.length > 0"
            class="border-t border-gray-200 dark:border-gray-700 my-4"></div>

          <!-- List of saved options -->
          <div v-if="formData.options.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            <UiBaseIcon name="heroicons:document-text-solid" class="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Tiada opsyen disimpan. Klik "Tambah Opsyen" untuk menambah opsyen baru.</p>
          </div>
          <div v-else class="space-y-3">
            <div v-for="option in formData.options" :key="option.id"
              class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">

              <!-- Edit mode -->
              <div v-if="profileForm.editingOptionId.value === option.id" class="flex items-center space-x-3 flex-1">
                <Input v-model="profileForm.tempOptionData.value.name" placeholder="Nama Opsyen" class="flex-1" />
                <div class="flex space-x-2">
                  <UiBaseButton @click="() => profileForm.saveEditOption(formData)" variant="primary" size="sm">
                    Simpan
                  </UiBaseButton>
                  <UiBaseButton @click="profileForm.cancelEditOption" variant="outline" size="sm">
                    Batal
                  </UiBaseButton>
                </div>
              </div>

              <!-- View mode -->
              <div v-else class="flex items-center justify-between w-full">
                <span class="font-medium">{{ option.name }}</span>
                <div class="flex space-x-2">
                  <button @click="profileForm.startEditingOption(option)"
                    class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                    Edit
                  </button>
                  <span class="text-gray-300 dark:text-gray-600">|</span>
                  <button @click="() => profileForm.deleteOption(option.id, formData)"
                    class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm">
                    Padam
                  </button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </UiCompositeCard>

      <!-- 3. Kelulusan Akademik -->
      <UiCompositeCard>
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <UiBaseIcon name="heroicons:academic-cap-solid" class="w-5 h-5 text-primary" />
              <h2 class="text-xl font-semibold">Kelulusan Akademik / Ikhtisas</h2>
            </div>
            <UiBaseButton type="button" @click="profileForm.addQualification" variant="outline" size="sm"
              prepend-icon="heroicons:plus-solid">
              Tambah Kelulusan
            </UiBaseButton>
          </div>
        </template>
        <template #default>
          <!-- Add new qualification form (appears when button clicked) -->
          <div v-if="profileForm.showAddQualificationForm.value" class="mb-4">
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
              <div class="flex items-start justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Kelulusan Baru</h3>
                <div class="flex space-x-2">
                  <UiBaseButton @click="() => profileForm.saveNewQualification(formData)" variant="primary" size="sm"
                    :disabled="isQualificationSimpanDisabled">
                    Simpan
                  </UiBaseButton>
                  <UiBaseButton @click="profileForm.cancelNewQualification" variant="outline" size="sm">
                    Batal
                  </UiBaseButton>
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Input v-model="profileForm.newQualificationData.value.name" placeholder="Nama Kelulusan" />
                </div>
                <div>
                  <Input v-model="profileForm.newQualificationData.value.institution" placeholder="Institusi" />
                </div>
                <div>
                  <Input v-model="profileForm.newQualificationData.value.year" placeholder="Tahun" />
                </div>
              </div>
            </div>
          </div>

          <!-- Separator -->
          <div v-if="profileForm.showAddQualificationForm.value && formData.academic_qualifications.length > 0"
            class="border-t border-gray-200 dark:border-gray-700 my-4"></div>

          <!-- List of saved qualifications -->
          <div v-if="formData.academic_qualifications.length === 0"
            class="text-center py-8 text-gray-500 dark:text-gray-400">
            <UiBaseIcon name="heroicons:academic-cap-solid" class="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>Tiada kelulusan akademik disimpan. Klik "Tambah Kelulusan" untuk menambah kelulusan baru.</p>
          </div>
          <div v-else class="space-y-3">
            <div v-for="qualification in formData.academic_qualifications" :key="qualification.id"
              class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">

              <!-- Edit mode -->
              <div v-if="profileForm.editingQualificationId.value === qualification.id"
                class="flex items-center space-x-3 flex-1">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 flex-1">
                  <Input v-model="profileForm.tempQualificationData.value.name" placeholder="Nama Kelulusan" />
                  <Input v-model="profileForm.tempQualificationData.value.institution" placeholder="Institusi" />
                  <Input v-model="profileForm.tempQualificationData.value.year" placeholder="Tahun" />
                </div>
                <div class="flex space-x-2 ml-3">
                  <UiBaseButton @click="() => profileForm.saveEditQualification(formData)" variant="primary" size="sm">
                    Simpan
                  </UiBaseButton>
                  <UiBaseButton @click="profileForm.cancelEditQualification" variant="outline" size="sm">
                    Batal
                  </UiBaseButton>
                </div>
              </div>

              <!-- View mode -->
              <div v-else class="flex items-center justify-between w-full">
                <div class="flex-1">
                  <div class="font-medium">{{ qualification.name }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ qualification.institution }} • {{
                    qualification.year }}</div>
                </div>
                <div class="flex space-x-2">
                  <button @click="profileForm.startEditingQualification(qualification)"
                    class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                    Edit
                  </button>
                  <span class="text-gray-300 dark:text-gray-600">|</span>
                  <button @click="() => profileForm.deleteQualification(qualification.id, formData)"
                    class="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm">
                    Padam
                  </button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </UiCompositeCard>

      <!-- 4. Nombor Rujukan -->
      <UiCompositeCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:hashtag-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Nombor Rujukan</h2>
          </div>
        </template>
        <template #default>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Input id="file-number" v-model="formData.file_number" placeholder="No. Fail"
                @blur="validateField('file_number')" :aria-invalid="!!formErrors?.file_number" />
              <span v-if="formErrors?.file_number" class="text-alert-error text-sm mt-1">
                {{ formErrors.file_number.join(', ') }}
              </span>
            </div>

            <div>
              <Input id="spp-reference-number" v-model="formData.spp_reference_number" placeholder="No. Rujukan SPP"
                @blur="validateField('spp_reference_number')" :aria-invalid="!!formErrors?.spp_reference_number" />
              <span v-if="formErrors?.spp_reference_number" class="text-alert-error text-sm mt-1">
                {{ formErrors.spp_reference_number.join(', ') }}
              </span>
            </div>

            <div>
              <Input id="salary-number" v-model="formData.salary_number" placeholder="No. Gaji"
                @blur="validateField('salary_number')" :aria-invalid="!!formErrors?.salary_number" />
              <span v-if="formErrors?.salary_number" class="text-alert-error text-sm mt-1">
                {{ formErrors.salary_number.join(', ') }}
              </span>
            </div>

            <div>
              <Input id="epf-number" v-model="formData.epf_number" placeholder="No. KWSP"
                @blur="validateField('epf_number')" :aria-invalid="!!formErrors?.epf_number" />
              <span v-if="formErrors?.epf_number" class="text-alert-error text-sm mt-1">
                {{ formErrors.epf_number.join(', ') }}
              </span>
            </div>

            <div class="md:col-span-2">
              <Input id="income-tax-number" v-model="formData.income_tax_number" placeholder="No. Cukai Pendapatan"
                @blur="validateField('income_tax_number')" :aria-invalid="!!formErrors?.income_tax_number" />
              <span v-if="formErrors?.income_tax_number" class="text-alert-error text-sm mt-1">
                {{ formErrors.income_tax_number.join(', ') }}
              </span>
            </div>
          </div>
        </template>
      </UiCompositeCard>

      <!-- 5. Maklumat Pelantikan -->
      <UiCompositeCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:briefcase-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Maklumat Pelantikan</h2>
          </div>
        </template>
        <template #default>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <DatePicker id="appointment-date" v-model="formData.appointment_date" placeholder="Tarikh Pelantikan"
                @blur="validateField('appointment_date')" :aria-invalid="!!formErrors?.appointment_date" />
              <span v-if="formErrors?.appointment_date" class="text-alert-error text-sm mt-1">
                {{ formErrors.appointment_date.join(', ') }}
              </span>
            </div>

            <div>
              <DatePicker id="position-confirmation-date" v-model="formData.position_confirmation_date"
                placeholder="Tarikh Pengesahan Jawatan" @blur="validateField('position_confirmation_date')"
                :aria-invalid="!!formErrors?.position_confirmation_date" />
              <span v-if="formErrors?.position_confirmation_date" class="text-alert-error text-sm mt-1">
                {{ formErrors.position_confirmation_date.join(', ') }}
              </span>
            </div>

            <div>
              <DatePicker id="pensionable-position-date" v-model="formData.pensionable_position_date"
                placeholder="Tarikh Masuk ke Jawatan Berpencen" @blur="validateField('pensionable_position_date')"
                :aria-invalid="!!formErrors?.pensionable_position_date" />
              <span v-if="formErrors?.pensionable_position_date" class="text-alert-error text-sm mt-1">
                {{ formErrors.pensionable_position_date.join(', ') }}
              </span>
            </div>

            <div>
              <DatePicker id="retirement-date" v-model="formData.retirement_date" placeholder="Tarikh Pencen"
                @blur="validateField('retirement_date')" :aria-invalid="!!formErrors?.retirement_date" />
              <span v-if="formErrors?.retirement_date" class="text-alert-error text-sm mt-1">
                {{ formErrors.retirement_date.join(', ') }}
              </span>
            </div>
          </div>
        </template>
      </UiCompositeCard>

      <!-- Submit Button -->
      <div class="flex justify-end space-x-4">
        <UiBaseButton type="button" variant="outline" @click="resetForm" :disabled="saving">
          Reset
        </UiBaseButton>
        <UiBaseButton type="submit" variant="primary" :disabled="saving" :loading="saving">
          {{ saving ? 'Menyimpan...' : 'Simpan' }}
        </UiBaseButton>
      </div>
    </form>

    <!-- Page Leave Warning Modal -->
    <UiCompositeModal :is-open="pageLeaveWarning.showLeaveWarningModal.value" title="Perubahan Belum Disimpan"
      @update:is-open="pageLeaveWarning.showLeaveWarningModal.value = $event">
      <template #default>
        <div class="space-y-4">
          <p class="text-gray-700 dark:text-gray-300">
            Anda mempunyai perubahan yang belum disimpan. Adakah anda pasti mahu meninggalkan halaman ini?
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Semua perubahan yang belum disimpan akan hilang.
          </p>
        </div>
      </template>
      <template #footer>
        <div class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3">
          <UiBaseButton @click="pageLeaveWarning.cancelLeave" variant="outline" class="w-full sm:w-auto">
            Batal
          </UiBaseButton>
          <UiBaseButton @click="handleConfirmLeave" variant="primary" class="w-full sm:w-auto">
            Tinggalkan Halaman
          </UiBaseButton>
        </div>
      </template>
    </UiCompositeModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useSupabaseUser, useSupabaseClient } from '#imports';
import { useProfileForm } from '~/composables/useProfileForm';
import { usePageLeaveWarning } from '~/composables/usePageLeaveWarning';
import {
  type ExtendedProfile,
  RELIGION_OPTIONS,
  getTeacherTypeOptions
} from '~/schemas/profileSchemas';
import Input from '~/components/ui/base/Input.vue';
import SingleSelect from '~/components/ui/base/SingleSelect.vue';
import DatePicker from '~/components/ui/base/CustomDatePicker.vue';

import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue';
import UiCompositeModal from '~/components/ui/composite/Modal.vue';

// =====================================================
// PAGE META
// =====================================================

useHead({
  title: 'Profil Pengguna - RPHMate',
  meta: [
    { name: 'description', content: 'Urus maklumat peribadi dan profesional anda.' }
  ]
});

// =====================================================
// COMPOSABLES
// =====================================================

const user = useSupabaseUser();
const client = useSupabaseClient();
const { success: showSuccessToast, error: showErrorToast } = useToast();

// =====================================================
// STATE
// =====================================================

const loading = ref(true);
const saving = ref(false);

// Use composables for form management and page leave warning
const profileForm = useProfileForm();
const pageLeaveWarning = usePageLeaveWarning();

// Form change tracking
const originalFormData = ref<ExtendedProfile & { gender: string | null } | null>(null);
const hasFormChanges = ref<boolean>(false);

// Form data
const formData = ref<ExtendedProfile & { gender: string | null }>({
  ic_number: null,
  teacher_type: null,
  religion: null,
  date_of_birth: null,
  gender: null, // Add gender field from existing profile
  options: [],
  academic_qualifications: [],
  file_number: null,
  spp_reference_number: null,
  salary_number: null,
  epf_number: null,
  income_tax_number: null,
  appointment_date: null,
  position_confirmation_date: null,
  pensionable_position_date: null,
  retirement_date: null,
});

// Validation errors
const formErrors = ref<Record<string, string[]>>({});
const optionErrors = ref<Record<number, Record<string, string[]>>>({});
const qualificationErrors = ref<Record<number, Record<string, string[]>>>({});

// User role data for teacher type options
const userRoleData = ref<{ code: string; label: string } | null>(null);

// =====================================================
// COMPUTED PROPERTIES
// =====================================================

const religionOptions = computed(() => [...RELIGION_OPTIONS]);

const genderOptions = computed(() => [
  { value: 'lelaki', label: 'Lelaki' },
  { value: 'perempuan', label: 'Perempuan' },
]);

const teacherTypeOptions = computed(() => {
  return getTeacherTypeOptions(userRoleData.value || undefined);
});

// Destructure computed properties from composable
const { isOptionSimpanDisabled, isQualificationSimpanDisabled } = profileForm;

// Check for unsaved changes
const hasUnsavedChanges = computed(() => {
  // Check if there are unsaved add forms
  const hasUnsavedAddForms = profileForm.showAddOptionForm.value || profileForm.showAddQualificationForm.value;

  // Check if form data has changed from original
  const hasDataChanges = hasFormChanges.value;

  return hasUnsavedAddForms || hasDataChanges;
});

// =====================================================
// WATCHERS
// =====================================================

// Watch for form data changes
watch(formData, (newData) => {
  if (originalFormData.value) {
    const currentDataString = JSON.stringify(newData);
    const originalDataString = JSON.stringify(originalFormData.value);
    hasFormChanges.value = currentDataString !== originalDataString;
  }
}, { deep: true });

// =====================================================
// METHODS
// =====================================================

// Load profile data
const loadProfile = async () => {
  if (!user.value) return;

  try {
    loading.value = true;

    const { data, error: fetchError } = await client
      .from('profiles')
      .select('*')
      .eq('id', user.value.id)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    if (data) {
      // Map database data to form data (using any type since migration hasn't been run)
      const profileData = data as any;
      formData.value = {
        ic_number: profileData.ic_number || null,
        teacher_type: profileData.teacher_type || null,
        religion: profileData.religion || null,
        date_of_birth: profileData.date_of_birth || null,
        gender: profileData.gender || null,
        options: profileData.options || [],
        academic_qualifications: profileData.academic_qualifications || [],
        file_number: profileData.file_number || null,
        spp_reference_number: profileData.spp_reference_number || null,
        salary_number: profileData.salary_number || null,
        epf_number: profileData.epf_number || null,
        income_tax_number: profileData.income_tax_number || null,
        appointment_date: profileData.appointment_date || null,
        position_confirmation_date: profileData.position_confirmation_date || null,
        pensionable_position_date: profileData.pensionable_position_date || null,
        retirement_date: profileData.retirement_date || null,
      };

      // Extract role data for teacher type options
      if (profileData.role && typeof profileData.role === 'object') {
        userRoleData.value = profileData.role as { code: string; label: string };

        // If teacher_type is not set but role exists, use role as default
        if (!formData.value.teacher_type && userRoleData.value.code) {
          formData.value.teacher_type = userRoleData.value.code;
        }
      }

      // Store original data for change detection
      originalFormData.value = JSON.parse(JSON.stringify(formData.value));
      hasFormChanges.value = false;
    }
  } catch (err) {
    console.error('Error loading profile:', err);
    showErrorToast('Gagal memuat data profil. Sila cuba lagi.');
  } finally {
    loading.value = false;
  }
};

// Validation methods
const validateField = (fieldName: string) => {
  try {
    // Clear error if validation passes
    if (formErrors.value[fieldName]) {
      delete formErrors.value[fieldName];
    }
  } catch (err: any) {
    if (err.errors) {
      formErrors.value[fieldName] = err.errors.map((e: any) => e.message);
    }
  }
};

// Validation and form methods are handled by composables

// Page leave warning methods are now handled by the composable

// Form submission
const handleSubmit = async () => {
  if (!user.value) return;

  try {
    saving.value = true;

    // Basic validation - check required fields for options and qualifications
    let hasErrors = false;

    // Validate options
    formData.value.options.forEach((option, index) => {
      if (!option.name.trim()) {
        if (!optionErrors.value[index]) {
          optionErrors.value[index] = {};
        }
        optionErrors.value[index].name = ['Nama opsyen diperlukan'];
        hasErrors = true;
      }
    });

    // Validate qualifications
    formData.value.academic_qualifications.forEach((qualification, index) => {
      if (!qualification.name.trim()) {
        if (!qualificationErrors.value[index]) {
          qualificationErrors.value[index] = {};
        }
        qualificationErrors.value[index].name = ['Nama kelulusan diperlukan'];
        hasErrors = true;
      }
    });

    if (hasErrors) {
      showErrorToast('Sila betulkan ralat dalam borang sebelum menyimpan.');
      return;
    }

    // Prepare data for database - only include fields that have values
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    // Add fields only if they have values or are explicitly null
    if (formData.value.ic_number !== undefined) updateData.ic_number = formData.value.ic_number;
    if (formData.value.teacher_type !== undefined) updateData.teacher_type = formData.value.teacher_type;
    if (formData.value.religion !== undefined) updateData.religion = formData.value.religion;
    if (formData.value.date_of_birth !== undefined) updateData.date_of_birth = formData.value.date_of_birth;
    if (formData.value.gender !== undefined) updateData.gender = formData.value.gender;
    if (formData.value.options !== undefined) updateData.options = formData.value.options;
    if (formData.value.academic_qualifications !== undefined) updateData.academic_qualifications = formData.value.academic_qualifications;
    if (formData.value.file_number !== undefined) updateData.file_number = formData.value.file_number;
    if (formData.value.spp_reference_number !== undefined) updateData.spp_reference_number = formData.value.spp_reference_number;
    if (formData.value.salary_number !== undefined) updateData.salary_number = formData.value.salary_number;
    if (formData.value.epf_number !== undefined) updateData.epf_number = formData.value.epf_number;
    if (formData.value.income_tax_number !== undefined) updateData.income_tax_number = formData.value.income_tax_number;
    if (formData.value.appointment_date !== undefined) updateData.appointment_date = formData.value.appointment_date;
    if (formData.value.position_confirmation_date !== undefined) updateData.position_confirmation_date = formData.value.position_confirmation_date;
    if (formData.value.pensionable_position_date !== undefined) updateData.pensionable_position_date = formData.value.pensionable_position_date;
    if (formData.value.retirement_date !== undefined) updateData.retirement_date = formData.value.retirement_date;

    const { error: updateError } = await (client
      .from('profiles') as any)
      .update(updateData) // Cast the client method since new fields may not be in types yet
      .eq('id', user.value.id);

    if (updateError) {
      throw updateError;
    }

    // Show success message
    await nextTick();
    // Clear any existing errors
    formErrors.value = {};

    // Update original data to reflect saved state
    originalFormData.value = JSON.parse(JSON.stringify(formData.value));
    hasFormChanges.value = false;

    // Show success toast notification
    showSuccessToast('Profil berjaya dikemaskini!');

  } catch (err) {
    console.error('Error saving profile:', err);
    showErrorToast('Gagal menyimpan profil. Sila cuba lagi.');
  } finally {
    saving.value = false;
  }
};

// Reset form
const resetForm = async () => {
  formErrors.value = {};
  optionErrors.value = {};
  qualificationErrors.value = {};
  await loadProfile();
};

// =====================================================
// LIFECYCLE
// =====================================================

onMounted(() => {
  loadProfile();
});

// Create wrapper function for confirmLeave
const handleConfirmLeave = () => {
  pageLeaveWarning.confirmLeave(hasFormChanges);
};

// Setup page leave warning
pageLeaveWarning.setupPageLeaveWarning(() => hasUnsavedChanges.value, hasFormChanges);
</script>