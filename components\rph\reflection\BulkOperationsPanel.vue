<template>
  <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-2">
        <Icon name="heroicons:squares-plus" class="h-5 w-5 text-amber-600 dark:text-amber-400" />
        <h3 class="text-lg font-semibold text-amber-900 dark:text-amber-100">
          Operasi Pukal
        </h3>
      </div>
      <button
        @click="$emit('close')"
        class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-200 transition-colors"
        type="button"
      >
        <Icon name="heroicons:x-mark" class="h-5 w-5" />
      </button>
    </div>
    
    <!-- Description -->
    <p class="text-sm text-amber-800 dark:text-amber-200 mb-4">
      Pilih beberapa waktu untuk menandakan sebagai "Tidak Terlaksana" sekaligus.
    </p>
    
    <!-- Period Selection -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-amber-900 dark:text-amber-100 mb-3">
        Pilih Waktu ({{ selectedPeriods.length }} dipilih)
      </h4>
      
      <!-- Select All/None -->
      <div class="flex items-center space-x-4 mb-3">
        <button
          @click="selectAllPeriods"
          class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
          type="button"
        >
          Pilih Semua
        </button>
        <button
          @click="clearSelection"
          class="text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          type="button"
        >
          Kosongkan Pilihan
        </button>
      </div>
      
      <!-- Period Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        <label
          v-for="period in availablePeriods"
          :key="period.id"
          class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          :class="{
            'bg-blue-50 border-blue-300 dark:bg-blue-900/20 dark:border-blue-600': selectedPeriods.includes(period.id),
            'opacity-50': period.alreadyTidakTerlaksana
          }"
        >
          <input
            type="checkbox"
            :value="period.id"
            v-model="selectedPeriods"
            :disabled="period.alreadyTidakTerlaksana"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 mr-3"
          />
          <div class="flex-1">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ period.dayLabel }} - {{ period.classSubjectLabel }}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ period.timeSlot }}
            </div>
            <div v-if="period.alreadyTidakTerlaksana" class="text-xs text-red-600 dark:text-red-400 mt-1">
              Sudah ditandakan tidak terlaksana
            </div>
          </div>
        </label>
      </div>
    </div>
    
    <!-- Reason Selection -->
    <div v-if="selectedPeriods.length > 0" class="mb-6">
      <h4 class="text-sm font-medium text-amber-900 dark:text-amber-100 mb-3">
        Pilih Sebab Tidak Terlaksana
      </h4>
      <TidakTerlaksanaSelect
        v-model="selectedReason"
        :show-label="false"
        placeholder="Pilih sebab untuk semua waktu yang dipilih..."
      />
    </div>
    
    <!-- Actions -->
    <div class="flex items-center justify-between pt-4 border-t border-amber-200 dark:border-amber-700">
      <div class="text-sm text-amber-700 dark:text-amber-300">
        {{ selectedPeriods.length }} waktu akan ditandakan sebagai tidak terlaksana
      </div>
      <div class="flex items-center space-x-3">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors"
          type="button"
        >
          Batal
        </button>
        <button
          @click="applyBulkOperation"
          :disabled="selectedPeriods.length === 0 || !selectedReason || loading"
          class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          type="button"
        >
          <Icon v-if="loading" name="heroicons:arrow-path" class="h-4 w-4 mr-2 animate-spin" />
          {{ loading ? 'Memproses...' : 'Terapkan' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Icon from '~/components/ui/base/Icon.vue'
import TidakTerlaksanaSelect from './TidakTerlaksanaSelect.vue'

interface Period {
  id: string
  dayLabel: string
  classSubjectLabel: string
  timeSlot: string
  alreadyTidakTerlaksana: boolean
  classSubjectId: string
  day: string
}

interface Props {
  availablePeriods: Period[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  close: []
  apply: [periods: string[], reason: string]
}>()

// State
const selectedPeriods = ref<string[]>([])
const selectedReason = ref<string | null>(null)

// Computed
const selectablePeriods = computed(() => 
  props.availablePeriods.filter(period => !period.alreadyTidakTerlaksana)
)

// Methods
const selectAllPeriods = () => {
  selectedPeriods.value = selectablePeriods.value.map(period => period.id)
}

const clearSelection = () => {
  selectedPeriods.value = []
}

const applyBulkOperation = () => {
  if (selectedPeriods.value.length > 0 && selectedReason.value) {
    emit('apply', selectedPeriods.value, selectedReason.value)
  }
}

// Reset when component is shown
const reset = () => {
  selectedPeriods.value = []
  selectedReason.value = null
}

// Expose reset method
defineExpose({
  reset
})
</script>
