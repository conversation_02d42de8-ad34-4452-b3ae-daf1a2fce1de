// Get school members API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')
    const schoolId = getRouterParam(event, 'schoolId')

    // Get query parameters
    const query = getQuery(event)
    const { status = 'active', role, page = 1, limit = 50 } = query

    if (!schoolId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School ID is required'
      })
    }

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Check if the requesting user has access to this school
    const { data: requesterMembership, error: membershipError } = await supabase
      .from('school_memberships')
      .select('role')
      .eq('user_id', user.id)
      .eq('school_id', schoolId)
      .eq('status', 'active')
      .single()

    // Also check if user is the school admin
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .select('admin_user_id, name, code')
      .eq('id', schoolId)
      .single()

    if (schoolError || !school) {
      throw createError({
        statusCode: 404,
        statusMessage: 'School not found'
      })
    }

    const isSchoolAdmin = school.admin_user_id === user.id
    const hasAccess = isSchoolAdmin || !!requesterMembership

    if (!hasAccess) {
      throw createError({
        statusCode: 403,
        statusMessage: 'No access to this school'
      })
    }

    // Build query for members (simplified without auth.users joins)
    let membersQuery = supabase
      .from('school_memberships')
      .select('*')
      .eq('school_id', schoolId)

    // Apply filters
    if (status && status !== 'all') {
      membersQuery = membersQuery.eq('status', status)
    }

    if (role && role !== 'all') {
      membersQuery = membersQuery.eq('role', role)
    }

    // Apply pagination
    const pageNum = parseInt(page as string) || 1
    const limitNum = Math.min(parseInt(limit as string) || 50, 100) // Max 100 per page
    const offset = (pageNum - 1) * limitNum

    membersQuery = membersQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limitNum - 1)

    const { data: members, error: membersError } = await membersQuery

    if (membersError) {
      console.error('Error fetching school members:', membersError)
      throw createError({
        statusCode: 500,
        statusMessage: 'Error fetching school members'
      })
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('school_memberships')
      .select('*', { count: 'exact', head: true })
      .eq('school_id', schoolId)
      .eq('status', status === 'all' ? undefined : status)
      .eq('role', role === 'all' ? undefined : role)

    if (countError) {
      console.error('Error counting school members:', countError)
    }

    // Transform the data (simplified without user joins)
    const transformedMembers = members?.map(member => ({
      id: member.id,
      role: member.role,
      status: member.status,
      joined_at: member.joined_at,
      created_at: member.created_at,
      updated_at: member.updated_at,
      invitation_expires_at: member.invitation_expires_at,
      notes: member.notes,
      permissions: member.permissions,
      user_id: member.user_id,
      invited_by: member.invited_by
    })) || []

    return {
      success: true,
      members: transformedMembers,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limitNum)
      },
      school: {
        id: schoolId,
        name: school.name,
        code: school.code
      }
    }

  } catch (error: any) {
    console.error('Get school members error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
