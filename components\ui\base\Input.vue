<template>
  <div class="relative">
    <template v-if="props.variant === 'floating'">
      <FloatingLabel :for-input="componentId" :label="placeholder" :is-floated="isFloated" :is-focused="isFocused" />
    </template>
    <template v-if="props.variant === 'normal'">
      <label :for="componentId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ props.label }}
      </label>
    </template>
    <template v-else>
      <label :for="componentId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {{ props.label }}
      </label>
    </template>
    <input ref="inputEl" :type="props.type" :id="componentId" :value="props.modelValue" @input="onInput"
      @focus="handleFocus" @blur="handleBlur" :placeholder="props.variant === 'normal' ? props.placeholder : ''"
      :disabled="props.disabled" :required="props.required" :aria-label="props.placeholder"
      :aria-describedby="props.ariaDescribedby" :autocomplete="props.autocomplete"
      class="form-input block pb-2.5 pt-5 peer" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, useId, watch, onMounted, onUnmounted, nextTick } from 'vue';
import FloatingLabel from './FloatingLabel.vue';

const props = withDefaults(defineProps<{
  modelValue?: string | number | null; // Allow null for modelValue
  type?: "text" | "email" | "password" | "number" | "time"; // Added "time" type
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  ariaDescribedby?: string;
  autocomplete?: string;
  variant?: "floating" | "normal"; // Added variant prop
  label?: string; // Added label prop
}>(), {
  type: 'text',
  placeholder: '',
  disabled: false,
  required: false,
  autocomplete: 'off',
  variant: 'floating', // Default variant is floating
});

const emit = defineEmits(['update:modelValue']);

const componentId = useId();
const inputEl = ref<HTMLInputElement | null>(null); // Added back inputEl

const isFocused = ref(false);
// isFloated is now a computed property based on focus, modelValue, and inputEl.value.value
const isFloated = computed(() => {
  const hasModelValue = props.modelValue !== undefined && props.modelValue !== null && props.modelValue.toString().length > 0;
  const hasElementValue = inputEl.value ? inputEl.value.value.length > 0 : false;
  return isFocused.value || hasModelValue || hasElementValue;
});

const onInput = (event: Event) => { // Renamed from handleInputEvent
  const target = event.target as HTMLInputElement;
  let value: string | number | null = target.value;

  if (props.type === 'number') {
    if (target.value === '') {
      // When input type is number and value is empty string, emit null.
      // This is important for v-model.number to correctly set the bound variable to null.
      emit('update:modelValue', null);
      return;
    } else {
      // v-model.number will attempt to parse this to a number.
      // If it can't, it will result in NaN, which is fine for validation purposes.
      // We emit the string value, and v-model.number handles the conversion.
      value = target.value;
    }
  } else {
    // For non-number types, emit the string value directly.
    value = target.value;
  }
  emit('update:modelValue', value);
};

const handleFocus = () => {
  isFocused.value = true;
};

const handleBlur = () => {
  isFocused.value = false;
};

onMounted(() => {
  nextTick(() => {
    // Initial check in case of pre-filled values (e.g. browser restoring form state)
    // The computed isFloated should handle this if modelValue is already set.
    // If not, and the element has a value (e.g. from non-Vue autofill), we might need to force an update.
    if (inputEl.value && inputEl.value.value && !props.modelValue) {
      // This scenario is less common with v-model but can happen.
      // Forcing isFocused temporarily can trigger the label to float if needed.
      // However, this might have unintended side effects. The computed isFloated is preferred.
    }
  });
});

</script>

<style scoped>
input {
  /* Adjust padding to vertically center text */
  padding-top: 0.8rem;
  padding-bottom: 0.8rem;
}

/* Responsive placeholder text size for normal variant */
input::placeholder {
  font-size: 0.875rem;
  /* Default size for large screens */
}

/* Medium screens and below */
@media (max-width: 768px) {
  input::placeholder {
    font-size: 0.875rem;
    /* 14px - smaller for medium screens */
  }
}

/* Small screens and below */
@media (max-width: 640px) {
  input::placeholder {
    font-size: 0.8125rem;
    /* 13px - even smaller for small screens */
  }
}
</style>
