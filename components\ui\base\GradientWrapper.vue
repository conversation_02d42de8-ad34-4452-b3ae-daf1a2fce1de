<template>
    <div class="relative p-[2px] overflow-hidden rounded-md">
        <div class="absolute top-1/2 left-1/2 w-[500%] h-[500%] rounded-md" :class="{ 'animate-spin-slow': animate }"
            style="background: conic-gradient(rgba(100, 149, 237, 0.2) 0deg, rgba(192, 132, 252, 1) 60deg, transparent 20deg);">
        </div>
        <div class="relative">
            <slot></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps({
    animate: {
        type: Boolean,
        default: true
    }
})
</script>