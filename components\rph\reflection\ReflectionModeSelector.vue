<template>
    <!-- Mode Selection -->
    <div class="flex space-x-2">
        <Button @click="$emit('modeChanged', 'quick')" :variant="currentMode === 'quick' ? 'primary' : 'outline'"
            type="button" class="flex-1" prepend-icon="mdi:flash">
            <PERSON><PERSON><PERSON><PERSON>
        </Button>
        <Button @click="$emit('modeChanged', 'detailed')" :variant="currentMode === 'detailed' ? 'primary' : 'outline'"
            type="button" class="flex-1" prepend-icon="mdi:clipboard-text">
            Terperinci
        </Button>
    </div>
</template>

<script setup lang="ts">
import Button from '~/components/ui/base/Button.vue'
import type { ReflectionMode } from '~/types/reflections'

interface Props {
    currentMode: ReflectionMode
}

defineProps<Props>()

defineEmits<{
    modeChanged: [mode: ReflectionMode]
}>()
</script>
