<template>
  <div class="container mx-auto py-8">
    <div v-if="pending" class="text-center">Loading item...</div>
    <div v-else-if="error" class="text-center text-red-500">Error loading item: {{ error.message }}</div>
    <div v-else-if="item">
      <Card>
        <template #header>
          <h1 class="text-3xl font-bold">{{ item.title }}</h1>
        </template>
        <p class="text-gray-700 mb-2"><strong>ID:</strong> {{ item.id }}</p>
        <p class="text-gray-700">{{ item.description }}</p>
        <template #footer>
          <NuxtLink to="/items">
            <Button variant="secondary">Back to Items</Button>
          </NuxtLink>
        </template>
      </Card>
    </div>
    <div v-else class="text-center">Item not found.</div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useSupabase } from '~/composables/useSupabase';
import { ref, computed, onMounted } from 'vue';

// Define page meta to apply auth middleware
definePageMeta({});

interface Item {
  id: number;
  title: string;
  description: string;
}

const { client } = useSupabase();
const route = useRoute();
const itemIdString = computed(() => {
  const idParam = route.params.id;
  return Array.isArray(idParam) ? String(idParam[0]) : String(idParam);
});

const itemIdNumber = computed(() => {
  const idParam = route.params.id;
  return Array.isArray(idParam) ? Number(idParam[0]) : Number(idParam);
});

const item = ref<Item | null>(null);
const pending = ref(true);
const error = ref<any>(null);

async function fetchItem() {
  if (isNaN(itemIdNumber.value)) {
    console.error('Invalid item ID');
    error.value = { message: 'Invalid item ID' };
    pending.value = false;
    return;
  }
  pending.value = true;
  error.value = null;
  try {
    const { data, error: fetchError } = await client
      .from('items')
      .select('id, title, description')
      .eq('id', itemIdString.value) // Use string version of ID for .eq()
      .single<Omit<Item, 'id'> & { id: string | number }>(); // Adjust type for single()

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        item.value = null;
      } else {
        throw fetchError;
      }
    } else if (data) {
      item.value = {
        ...data,
        id: Number(data.id) // Convert id from string/number to number
      };
    } else {
      item.value = null;
    }
  } catch (e) {
    console.error("Error fetching item:", e);
    error.value = e;
    item.value = null;
  } finally {
    pending.value = false;
  }
}

onMounted(() => {
  fetchItem();
});

</script>
