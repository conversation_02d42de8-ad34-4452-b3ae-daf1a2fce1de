import { ref, computed } from 'vue';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import type { Database } from '~/types/supabase';
import type {
  ReflectionTemplate,
  UserReflectionTemplatePreference,
  ReflectionTemplateWithPreference,
  TemplateOption,
  TemplateApplicationResult,
  ReflectionTemplateFormData,
  TemplateUsageStats,
  ReflectionTemplateCategory,
  DetailedReflectionFormData
} from '~/types/reflections';
import { useTemplatePreferences } from './useTemplatePreferences';

export const useReflectionTemplates = () => {
  const supabase = useSupabaseClient<Database>();
  const user = useSupabaseUser();
  const { preferences: templatePreferences, getSortedTemplates } = useTemplatePreferences();

  // State
  const templates = ref<ReflectionTemplateWithPreference[]>([]);
  const systemTemplates = ref<ReflectionTemplate[]>([]);
  const userTemplates = ref<ReflectionTemplate[]>([]);
  const userPreferences = ref<UserReflectionTemplatePreference[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Cache for performance
  const templatesCache = new Map<string, ReflectionTemplate>();
  const preferencesCache = new Map<string, UserReflectionTemplatePreference>();

  // =====================================================
  // FETCH OPERATIONS
  // =====================================================

  const fetchSystemTemplates = async (): Promise<ReflectionTemplate[]> => {
    try {
      const { data, error: fetchError } = await (supabase as any)
        .from('reflection_templates')
        .select('*')
        .eq('is_system_template', true)
        .order('category', { ascending: true })
        .order('name', { ascending: true });

      if (fetchError) throw fetchError;

      const templates = data as ReflectionTemplate[];
      systemTemplates.value = templates;
      
      // Update cache
      templates.forEach(template => {
        templatesCache.set(template.id, template);
      });

      return templates;
    } catch (err) {
      console.error('Error fetching system templates:', err);
      throw err;
    }
  };

  const fetchUserTemplates = async (): Promise<ReflectionTemplate[]> => {
    if (!user.value) return [];

    try {
      const { data, error: fetchError } = await (supabase as any)
        .from('reflection_templates')
        .select('*')
        .eq('is_system_template', false)
        .eq('created_by', user.value.id)
        .order('name', { ascending: true });

      if (fetchError) throw fetchError;

      const templates = data as ReflectionTemplate[];
      userTemplates.value = templates;
      
      // Update cache
      templates.forEach(template => {
        templatesCache.set(template.id, template);
      });

      return templates;
    } catch (err) {
      console.error('Error fetching user templates:', err);
      throw err;
    }
  };

  const fetchUserPreferences = async (): Promise<UserReflectionTemplatePreference[]> => {
    if (!user.value) return [];

    try {
      const { data, error: fetchError } = await (supabase as any)
        .from('user_reflection_template_preferences')
        .select('*')
        .eq('user_id', user.value.id);

      if (fetchError) throw fetchError;

      const preferences = data as UserReflectionTemplatePreference[];
      userPreferences.value = preferences;
      
      // Update cache
      preferences.forEach(pref => {
        preferencesCache.set(pref.template_id, pref);
      });

      return preferences;
    } catch (err) {
      console.error('Error fetching user preferences:', err);
      throw err;
    }
  };

  const fetchAllTemplates = async (): Promise<ReflectionTemplateWithPreference[]> => {
    try {
      loading.value = true;
      error.value = null;

      // Fetch all data in parallel
      const [systemTemplatesData, userTemplatesData, userPreferencesData] = await Promise.all([
        fetchSystemTemplates(),
        fetchUserTemplates(),
        fetchUserPreferences()
      ]);

      // Combine templates with user preferences
      const allTemplates = [...systemTemplatesData, ...userTemplatesData];
      const templatesWithPreferences: ReflectionTemplateWithPreference[] = allTemplates.map(template => {
        const preference = userPreferencesData.find(p => p.template_id === template.id);
        return {
          ...template,
          user_preference: preference,
          is_favorite: preference?.is_favorite || false,
          user_usage_count: preference?.usage_count || 0,
          last_used: preference?.last_used || null
        };
      });

      templates.value = templatesWithPreferences;
      return templatesWithPreferences;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch templates';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // =====================================================
  // TEMPLATE OPERATIONS
  // =====================================================

  const createUserTemplate = async (templateData: ReflectionTemplateFormData): Promise<ReflectionTemplate> => {
    if (!user.value) throw new Error('User not authenticated');

    try {
      loading.value = true;
      error.value = null;

      const { data, error: createError } = await (supabase as any)
        .from('reflection_templates')
        .insert({
          name: templateData.name,
          description: templateData.description,
          category: templateData.category,
          prompts: templateData.prompts,
          default_values: templateData.default_values,
          is_system_template: false,
          created_by: user.value.id
        })
        .select()
        .single();

      if (createError) throw createError;

      const newTemplate = data as ReflectionTemplate;
      
      // Update cache and state
      templatesCache.set(newTemplate.id, newTemplate);
      userTemplates.value.push(newTemplate);
      
      // Refresh templates list
      await fetchAllTemplates();

      return newTemplate;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create template';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateUserTemplate = async (templateId: string, templateData: Partial<ReflectionTemplateFormData>): Promise<ReflectionTemplate> => {
    if (!user.value) throw new Error('User not authenticated');

    try {
      loading.value = true;
      error.value = null;

      const { data, error: updateError } = await (supabase as any)
        .from('reflection_templates')
        .update(templateData)
        .eq('id', templateId)
        .eq('created_by', user.value.id)
        .eq('is_system_template', false)
        .select()
        .single();

      if (updateError) throw updateError;

      const updatedTemplate = data as ReflectionTemplate;
      
      // Update cache and state
      templatesCache.set(updatedTemplate.id, updatedTemplate);
      const index = userTemplates.value.findIndex(t => t.id === templateId);
      if (index !== -1) {
        userTemplates.value[index] = updatedTemplate;
      }
      
      // Refresh templates list
      await fetchAllTemplates();

      return updatedTemplate;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update template';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteUserTemplate = async (templateId: string): Promise<void> => {
    if (!user.value) throw new Error('User not authenticated');

    try {
      loading.value = true;
      error.value = null;

      const { error: deleteError } = await (supabase as any)
        .from('reflection_templates')
        .delete()
        .eq('id', templateId)
        .eq('created_by', user.value.id)
        .eq('is_system_template', false);

      if (deleteError) throw deleteError;

      // Update cache and state
      templatesCache.delete(templateId);
      userTemplates.value = userTemplates.value.filter(t => t.id !== templateId);
      
      // Refresh templates list
      await fetchAllTemplates();
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete template';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // =====================================================
  // USER PREFERENCES OPERATIONS
  // =====================================================

  const updateTemplatePreference = async (
    templateId: string,
    updates: Partial<Omit<UserReflectionTemplatePreference, 'id' | 'user_id' | 'template_id' | 'created_at' | 'updated_at'>>
  ): Promise<UserReflectionTemplatePreference> => {
    if (!user.value) throw new Error('User not authenticated');

    try {
      // First, try to get existing preference
      const { data: existingData } = await (supabase as any)
        .from('user_reflection_template_preferences')
        .select('*')
        .eq('user_id', user.value.id)
        .eq('template_id', templateId)
        .maybeSingle();

      let preference: UserReflectionTemplatePreference;

      if (existingData) {
        // Update existing preference
        const { data, error: updateError } = await (supabase as any)
          .from('user_reflection_template_preferences')
          .update(updates)
          .eq('user_id', user.value.id)
          .eq('template_id', templateId)
          .select()
          .single();

        if (updateError) throw updateError;
        preference = data as UserReflectionTemplatePreference;
      } else {
        // Create new preference
        const { data, error: insertError } = await (supabase as any)
          .from('user_reflection_template_preferences')
          .insert({
            user_id: user.value.id,
            template_id: templateId,
            ...updates
          })
          .select()
          .single();

        if (insertError) throw insertError;
        preference = data as UserReflectionTemplatePreference;
      }

      // Update cache
      preferencesCache.set(templateId, preference);

      // Update state
      const index = userPreferences.value.findIndex(p => p.template_id === templateId);
      if (index !== -1) {
        userPreferences.value[index] = preference;
      } else {
        userPreferences.value.push(preference);
      }

      return preference;
    } catch (err) {
      console.error('Error updating template preference:', err);
      throw err;
    }
  };

  const toggleTemplateFavorite = async (templateId: string): Promise<void> => {
    try {
      const currentPreference = preferencesCache.get(templateId);
      const newFavoriteStatus = !currentPreference?.is_favorite;

      await updateTemplatePreference(templateId, {
        is_favorite: newFavoriteStatus,
        // Set default values if this is the first preference record
        ...(currentPreference ? {} : {
          usage_count: 0,
          customizations: {}
        })
      });

      // Refresh templates to update UI
      await fetchAllTemplates();
    } catch (err) {
      console.error('Error toggling template favorite:', err);
      throw err;
    }
  };

  const incrementTemplateUsage = async (templateId: string): Promise<void> => {
    try {
      // Get current preference or default values
      const currentPreference = preferencesCache.get(templateId);
      const currentUsage = currentPreference?.usage_count || 0;

      await updateTemplatePreference(templateId, {
        usage_count: currentUsage + 1,
        last_used: new Date().toISOString(),
        // Set default values if this is the first time using the template
        ...(currentPreference ? {} : {
          is_favorite: false,
          customizations: {}
        })
      });
    } catch (err) {
      console.error('Error incrementing template usage:', err);
      // Don't throw here as this is not critical for template application
    }

    // Also increment global usage count
    try {
      // Get current usage count first
      const { data: currentTemplate } = await (supabase as any)
        .from('reflection_templates')
        .select('usage_count')
        .eq('id', templateId)
        .single();

      if (currentTemplate) {
        await (supabase as any)
          .from('reflection_templates')
          .update({
            usage_count: (currentTemplate.usage_count || 0) + 1
          })
          .eq('id', templateId);
      }
    } catch (err) {
      console.error('Error incrementing global template usage:', err);
    }
  };

  // =====================================================
  // TEMPLATE APPLICATION
  // =====================================================

  const applyTemplate = async (templateId: string): Promise<TemplateApplicationResult> => {
    const template = templatesCache.get(templateId) || 
                    templates.value.find(t => t.id === templateId);

    if (!template) {
      throw new Error('Template not found');
    }

    // Increment usage count
    await incrementTemplateUsage(templateId);

    return {
      applied_prompts: template.prompts,
      applied_defaults: template.default_values,
      template_id: template.id,
      template_name: template.name
    };
  };

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  const templateOptions = computed((): TemplateOption[] => {
    return templates.value.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      is_system_template: template.is_system_template,
      is_favorite: template.is_favorite,
      usage_count: template.user_usage_count,
      last_used: template.last_used
    }));
  });

  const favoriteTemplates = computed(() => {
    return templates.value.filter(t => t.is_favorite);
  });

  const recentlyUsedTemplates = computed(() => {
    return templates.value
      .filter(t => t.last_used)
      .sort((a, b) => new Date(b.last_used!).getTime() - new Date(a.last_used!).getTime())
      .slice(0, 5);
  });

  const templatesByCategory = computed(() => {
    const categories: Record<ReflectionTemplateCategory, ReflectionTemplateWithPreference[]> = {
      lesson_type: [],
      assessment: [],
      behavior: [],
      technology: [],
      general: []
    };

    templates.value.forEach(template => {
      categories[template.category].push(template);
    });

    return categories;
  });

  // =====================================================
  // TEMPLATE PREFERENCES INTEGRATION
  // =====================================================

  const getDefaultTemplate = async (): Promise<ReflectionTemplate | null> => {
    const defaultTemplateId = templatePreferences.value.default_template_id;
    if (!defaultTemplateId) return null;

    return templates.value.find(t => t.id === defaultTemplateId) || null;
  };

  const getSortedTemplatesWithPreferences = (): ReflectionTemplateWithPreference[] => {
    return getSortedTemplates(templates.value);
  };

  const getFilteredTemplates = (category?: ReflectionTemplateCategory): ReflectionTemplateWithPreference[] => {
    let filtered = templates.value;

    // Filter by category if specified
    if (category) {
      filtered = filtered.filter(t => t.category === category);
    }

    // Filter by user preferences
    if (!templatePreferences.value.show_system_templates) {
      filtered = filtered.filter(t => !t.is_system_template);
    }

    // Apply preferred categories filter if any are set
    if (templatePreferences.value.preferred_categories.length > 0) {
      const preferredTemplates = filtered.filter(t =>
        templatePreferences.value.preferred_categories.includes(t.category)
      );
      const otherTemplates = filtered.filter(t =>
        !templatePreferences.value.preferred_categories.includes(t.category)
      );
      filtered = [...preferredTemplates, ...otherTemplates];
    }

    return getSortedTemplates(filtered);
  };

  const shouldAutoApplyDefault = (): boolean => {
    return templatePreferences.value.auto_apply_default &&
           !!templatePreferences.value.default_template_id;
  };

  return {
    // State
    templates,
    systemTemplates,
    userTemplates,
    userPreferences,
    loading,
    error,

    // Computed
    templateOptions,
    favoriteTemplates,
    recentlyUsedTemplates,
    templatesByCategory,

    // Methods
    fetchAllTemplates,
    fetchSystemTemplates,
    fetchUserTemplates,
    fetchUserPreferences,
    createUserTemplate,
    updateUserTemplate,
    deleteUserTemplate,
    updateTemplatePreference,
    toggleTemplateFavorite,
    incrementTemplateUsage,
    applyTemplate,

    // Template preferences integration
    getDefaultTemplate,
    getSortedTemplatesWithPreferences,
    getFilteredTemplates,
    shouldAutoApplyDefault
  };
};
