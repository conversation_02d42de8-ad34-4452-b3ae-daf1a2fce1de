<template>
  <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
    <h3 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">
      <PERSON><PERSON><PERSON><PERSON>
    </h3>

    <!-- Overall Rating -->
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        <PERSON><PERSON><PERSON> (1-5 bintang)
      </label>
      <div class="flex space-x-1">
        <button v-for="star in 5" :key="star" type="button" @click="setRating(star)"
          class="text-2xl focus:outline-none transition-colors duration-200 hover:scale-110" :class="[
            star <= modelValue.overall_rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600',
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
          ]" :disabled="disabled" :aria-label="`Rate ${star} star${star > 1 ? 's' : ''}`">
          <Icon name="mdi:star" class="w-6 h-6" />
        </button>
      </div>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
        Penilaian: {{ modelValue.overall_rating }} daripada 5 bintang
      </p>
      <span v-if="errors?.overall_rating" class="text-red-500 text-sm mt-1">
        {{ errors.overall_rating[0] }}
      </span>
    </div>

    <!-- Objectives Achieved -->
    <div class="mb-4">
      <label class="flex items-center space-x-3">
        <Checkbox id="objectives-achieved" :model-value="modelValue.objectives_achieved"
          @update:model-value="updateField('objectives_achieved', $event)" :aria-invalid="!!errors?.objectives_achieved"
          :disabled="disabled" />
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
          Objektif pembelajaran tercapai
        </span>
      </label>
      <span v-if="errors?.objectives_achieved" class="text-red-500 text-sm mt-1">
        {{ errors.objectives_achieved[0] }}
      </span>
    </div> <!-- Challenges Faced -->
    <div>
      <label for="challenges-faced" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Cabaran yang dihadapi <span class="text-gray-400 text-xs">(pilihan)</span>
      </label>
      <textarea id="challenges-faced" :value="modelValue.challenges_faced"
        @input="updateField('challenges_faced', ($event.target as HTMLTextAreaElement).value)" rows="3"
        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
        :aria-invalid="!!errors?.challenges_faced" aria-describedby="challenges-error" :disabled="disabled"
        placeholder="Nyatakan cabaran yang dihadapi dalam pengajaran ini (pilihan)...">
      </textarea>
      <span v-if="errors?.challenges_faced" id="challenges-error" class="text-red-500 text-sm mt-1">
        {{ errors.challenges_faced[0] }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import Icon from '~/components/ui/base/Icon.vue';
import Checkbox from '~/components/ui/base/Checkbox.vue';

interface QuickFieldsData {
  overall_rating: number;
  objectives_achieved: boolean;
  challenges_faced: string;
}

interface Props {
  modelValue: QuickFieldsData;
  errors?: Record<string, string[]> | null;
  disabled?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: QuickFieldsData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const setRating = (rating: number) => {
  if (!props.disabled) {
    updateField('overall_rating', rating);
  }
};

const updateField = (field: keyof QuickFieldsData, value: any) => {
  const updated = { ...props.modelValue, [field]: value };
  emit('update:modelValue', updated);
};
</script>
