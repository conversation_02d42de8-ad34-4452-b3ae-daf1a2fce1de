-- Migration: Create pre_billing and failed_payments tables for 3-phase registration
-- Created: 2025-07-16
-- Description: Support new registration flow with auth → info → payment phases

BEGIN;

-- =====================================================
-- PRE_BILLING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS pre_billing (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Session management
    session_token UUID NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (timezone('utc'::text, now()) + INTERVAL '1 day') NOT NULL,
    
    -- Phase 1: Authentication data
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    admin_email TEXT NOT NULL,
    is_google_signup BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    
    -- Phase 2: School & Admin information
    school_name TEXT,
    school_code TEXT,
    school_address TEXT,
    admin_full_name TEXT,
    
    -- Phase 3: Billing information
    selected_plan TEXT, -- from pricing page
    billing_complete BOOLEAN DEFAULT FALSE,
    
    -- Progress tracking
    phase_completed INTEGER DEFAULT 0 CHECK (phase_completed >= 0 AND phase_completed <= 3),
    
    -- Constraints
    CONSTRAINT pre_billing_email_format CHECK (admin_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT pre_billing_school_code_format CHECK (school_code IS NULL OR school_code ~ '^[a-zA-Z0-9]+$'),
    CONSTRAINT pre_billing_selected_plan_valid CHECK (selected_plan IS NULL OR selected_plan IN ('basic', 'professional', 'enterprise'))
);

-- =====================================================
-- FAILED_PAYMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS failed_payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Payment information
    stripe_session_id TEXT NOT NULL UNIQUE,
    stripe_customer_id TEXT,
    payment_amount INTEGER NOT NULL,
    currency TEXT DEFAULT 'myr',
    
    -- School data that failed to create
    school_data JSONB NOT NULL,
    admin_data JSONB NOT NULL,
    
    -- Retry tracking
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 10,
    last_retry_at TIMESTAMP WITH TIME ZONE,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    
    -- Status management
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'retrying', 'manual_review', 'resolved', 'failed')),
    error_details JSONB DEFAULT '{}'::jsonb,
    
    -- Resolution tracking
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by TEXT,
    resolution_notes TEXT,
    
    -- Constraints
    CONSTRAINT failed_payments_retry_count_valid CHECK (retry_count >= 0 AND retry_count <= max_retries),
    CONSTRAINT failed_payments_amount_positive CHECK (payment_amount > 0)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Pre-billing indexes
CREATE INDEX idx_pre_billing_session_token ON pre_billing(session_token);
CREATE INDEX idx_pre_billing_user_id ON pre_billing(user_id);
CREATE INDEX idx_pre_billing_email ON pre_billing(admin_email);
CREATE INDEX idx_pre_billing_school_code ON pre_billing(school_code);
CREATE INDEX idx_pre_billing_expires_at ON pre_billing(expires_at);
CREATE INDEX idx_pre_billing_phase_completed ON pre_billing(phase_completed);

-- Failed payments indexes
CREATE INDEX idx_failed_payments_stripe_session ON failed_payments(stripe_session_id);
CREATE INDEX idx_failed_payments_status ON failed_payments(status);
CREATE INDEX idx_failed_payments_next_retry ON failed_payments(next_retry_at) WHERE status = 'retrying';
CREATE INDEX idx_failed_payments_created_at ON failed_payments(created_at);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE pre_billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE failed_payments ENABLE ROW LEVEL SECURITY;

-- Pre-billing policies (users can only access their own records)
CREATE POLICY "Users can view their own pre-billing records" ON pre_billing
    FOR SELECT
    USING (user_id = auth.uid() OR admin_email = auth.email());

CREATE POLICY "Users can update their own pre-billing records" ON pre_billing
    FOR UPDATE
    USING (user_id = auth.uid() OR admin_email = auth.email())
    WITH CHECK (user_id = auth.uid() OR admin_email = auth.email());

CREATE POLICY "Users can insert their own pre-billing records" ON pre_billing
    FOR INSERT
    WITH CHECK (user_id = auth.uid() OR admin_email = auth.email());

-- Failed payments policies (admin only)
CREATE POLICY "Only service role can access failed payments" ON failed_payments
    FOR ALL
    USING (auth.role() = 'service_role');

-- =====================================================
-- FUNCTIONS FOR CLEANUP
-- =====================================================

-- Function to clean up expired pre-billing records
CREATE OR REPLACE FUNCTION cleanup_expired_pre_billing()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM pre_billing 
    WHERE expires_at < timezone('utc'::text, now())
    AND billing_complete = FALSE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update retry schedule for failed payments
CREATE OR REPLACE FUNCTION schedule_payment_retry(payment_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    current_retry_count INTEGER;
    delay_minutes INTEGER;
BEGIN
    -- Get current retry count
    SELECT retry_count INTO current_retry_count
    FROM failed_payments
    WHERE id = payment_id;
    
    -- Calculate exponential backoff delay (5min * 2^retry_count)
    delay_minutes := 5 * POWER(2, current_retry_count);
    
    -- Update retry schedule
    UPDATE failed_payments
    SET 
        retry_count = retry_count + 1,
        last_retry_at = timezone('utc'::text, now()),
        next_retry_at = timezone('utc'::text, now()) + (delay_minutes || ' minutes')::INTERVAL,
        status = CASE 
            WHEN retry_count + 1 >= max_retries THEN 'manual_review'
            ELSE 'retrying'
        END,
        updated_at = timezone('utc'::text, now())
    WHERE id = payment_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE pre_billing IS 'Temporary storage for 3-phase school registration process';
COMMENT ON COLUMN pre_billing.session_token IS 'Unique token to track user session across registration phases';
COMMENT ON COLUMN pre_billing.phase_completed IS '0=none, 1=auth, 2=info, 3=payment';
COMMENT ON COLUMN pre_billing.expires_at IS 'Automatic cleanup after 24 hours';

COMMENT ON TABLE failed_payments IS 'Tracking and retry system for failed school creation after successful payment';
COMMENT ON COLUMN failed_payments.retry_count IS 'Number of automatic retry attempts made';
COMMENT ON COLUMN failed_payments.next_retry_at IS 'When the next automatic retry should occur';

COMMIT;
