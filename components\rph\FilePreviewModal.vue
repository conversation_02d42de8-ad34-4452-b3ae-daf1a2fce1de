'''<template>
  <UiCompositeModal :is-open="isOpen" :title="title" @close="emits('close')"
    :size="previewType === 'office' ? '4xl' : 'xl'">
    <div class="flex flex-col" :class="previewType === 'office' ? 'h-[80vh]' : 'max-h-[80vh]'">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center flex-1 p-4">
        <div class="text-center">
          <Icon name="svg-spinners:270-ring-with-bg" class="h-12 w-12 text-primary" />
          <p class="mt-2 text-gray-600 dark:text-gray-300">Memuatkan pratonton...</p>
        </div>
      </div>
      <!-- Content -->
      <template v-else>
        <div v-if="previewType === 'office' && previewUrl" class="flex-1 p-1 sm:p-2 md:p-4">
          <iframe :src="previewUrl" width="100%" height="100%" frameborder="0" class="rounded-md">
            Loading office preview...
          </iframe>
        </div>
        <div v-else-if="previewType === 'image' && previewUrl"
          class="flex-1 p-1 sm:p-2 md:p-4 flex items-center justify-center">
          <img :src="previewUrl" alt="Image Preview"
            class="max-w-full max-h-full object-contain rounded-md shadow-md" />
        </div>
        <div v-else class="flex-1 p-4 flex items-center justify-center">
          <p class="text-center text-gray-500">Gagal memuatkan pratonton. Sila cuba muat turun fail itu.</p>
        </div>
      </template>
    </div>
    <!-- Footer with Open in New Tab button -->
    <div v-if="!isLoading && rawFileUrl" class="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
      <a :href="rawFileUrl" target="_blank" rel="noopener noreferrer">
        <UiBaseButton variant="outline" :prepend-icon="'heroicons:arrow-top-right-on-square'">
          Buka di Tab Baharu
        </UiBaseButton>
      </a>
    </div>
  </UiCompositeModal>
</template>

<script setup lang="ts">
interface Props {
  isOpen: boolean;
  title?: string;
  previewUrl?: string | null;
  previewType?: 'image' | 'office' | null;
  isLoading?: boolean;
  rawFileUrl?: string | null;
}

withDefaults(defineProps<Props>(), {
  title: 'Preview File',
  previewUrl: null,
  previewType: null,
  isLoading: false,
  rawFileUrl: null,
});

const emits = defineEmits<{ (e: 'close'): void }>();
</script>
'''
