-- Migration: Create new JSONB-based lesson plan reflections table
-- This replaces the multi-row lesson_plan_detailed_reflections approach
-- with a single-row JSONB approach for better performance and simplicity

BEGIN;

-- =====================================================
-- CREATE NEW JSONB-BASED REFLECTIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS lesson_plan_reflections_jsonb (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Foreign keys
    lesson_plan_id UUID REFERENCES lesson_plans(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- All reflections stored as JSONB
    -- Structure: { "class_subject_id_day": { reflection_data }, ... }
    -- Example: { 
    --   "f1_math_isnin": { 
    --     "overall_rating": 5, 
    --     "objectives_achieved": true,
    --     "challenges_faced": "",
    --     "activity_effectiveness": 5,
    --     "time_management": "on_time",
    --     "student_engagement": 5,
    --     "resource_adequacy": "adequate",
    --     "improvements_needed": null,
    --     "successful_strategies": null,
    --     "action_items": [],
    --     "additional_notes": null,
    --     "jumlah_murid_mencapai_objektif": 0,
    --     "tindakan_susulan": [],
    --     "tidak_terlaksana": null
    --   }
    -- }
    reflections JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    -- Calculated overall rating (average of all period ratings)
    overall_rating DECIMAL(3,1) NOT NULL DEFAULT 5.0 CHECK (overall_rating >= 1.0 AND overall_rating <= 5.0),
    
    -- Metadata
    total_periods INTEGER NOT NULL DEFAULT 0,
    periods_with_custom_data INTEGER NOT NULL DEFAULT 0
);

-- =====================================================
-- ADD CONSTRAINTS AND INDEXES
-- =====================================================

-- Unique constraint: one reflection record per lesson plan
ALTER TABLE lesson_plan_reflections_jsonb 
ADD CONSTRAINT unique_lesson_plan_reflection 
UNIQUE (lesson_plan_id);

-- Index for fast lookups by lesson plan
CREATE INDEX IF NOT EXISTS idx_lesson_plan_reflections_jsonb_lesson_plan_id 
ON lesson_plan_reflections_jsonb (lesson_plan_id);

-- Index for user-based queries
CREATE INDEX IF NOT EXISTS idx_lesson_plan_reflections_jsonb_user_id 
ON lesson_plan_reflections_jsonb (user_id);

-- GIN index for JSONB queries
CREATE INDEX IF NOT EXISTS idx_lesson_plan_reflections_jsonb_reflections 
ON lesson_plan_reflections_jsonb USING GIN (reflections);

-- Index for rating-based queries
CREATE INDEX IF NOT EXISTS idx_lesson_plan_reflections_jsonb_overall_rating 
ON lesson_plan_reflections_jsonb (overall_rating);

-- =====================================================
-- ADD RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE lesson_plan_reflections_jsonb ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own reflections
CREATE POLICY "Users can only access their own reflections" 
ON lesson_plan_reflections_jsonb
FOR ALL USING (user_id = auth.uid());

-- =====================================================
-- CREATE TRIGGER FOR UPDATED_AT
-- =====================================================

CREATE OR REPLACE FUNCTION update_lesson_plan_reflections_jsonb_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_lesson_plan_reflections_jsonb_updated_at
    BEFORE UPDATE ON lesson_plan_reflections_jsonb
    FOR EACH ROW
    EXECUTE FUNCTION update_lesson_plan_reflections_jsonb_updated_at();

-- =====================================================
-- CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to calculate overall rating from JSONB reflections
CREATE OR REPLACE FUNCTION calculate_overall_rating_from_jsonb(reflections_data JSONB)
RETURNS DECIMAL(3,1) AS $$
DECLARE
    total_rating DECIMAL := 0;
    period_count INTEGER := 0;
    period_key TEXT;
    period_data JSONB;
    period_rating INTEGER;
BEGIN
    -- Iterate through all periods in the JSONB
    FOR period_key, period_data IN SELECT * FROM jsonb_each(reflections_data)
    LOOP
        -- Check if period has overall_rating
        IF period_data ? 'overall_rating' THEN
            -- Check if lesson was not delivered (tidak_terlaksana is not null)
            IF period_data ? 'tidak_terlaksana' AND
               period_data->>'tidak_terlaksana' IS NOT NULL AND
               period_data->>'tidak_terlaksana' != '' AND
               period_data->>'tidak_terlaksana' != 'null' THEN
                -- Lesson not delivered - count as 0 stars
                period_rating := 0;
            ELSE
                -- Normal lesson - use actual rating
                period_rating := (period_data->>'overall_rating')::INTEGER;
            END IF;

            total_rating := total_rating + period_rating;
            period_count := period_count + 1;
        END IF;
    END LOOP;

    -- Return average rating, default to 5.0 if no periods
    IF period_count = 0 THEN
        RETURN 5.0;
    ELSE
        RETURN ROUND(total_rating / period_count, 1);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update overall rating when reflections change
CREATE OR REPLACE FUNCTION update_overall_rating_trigger()
RETURNS TRIGGER AS $$
BEGIN
    NEW.overall_rating := calculate_overall_rating_from_jsonb(NEW.reflections);
    NEW.total_periods := jsonb_object_keys(NEW.reflections)::TEXT[] |> array_length(#, 1);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update overall rating
CREATE TRIGGER calculate_overall_rating_on_change
    BEFORE INSERT OR UPDATE ON lesson_plan_reflections_jsonb
    FOR EACH ROW
    EXECUTE FUNCTION update_overall_rating_trigger();

-- =====================================================
-- CREATE HELPER FUNCTIONS FOR EASY QUERYING
-- =====================================================

-- Function to get reflection for specific period
CREATE OR REPLACE FUNCTION get_period_reflection(
    p_lesson_plan_id UUID,
    p_class_subject_id TEXT,
    p_day TEXT
)
RETURNS JSONB AS $$
DECLARE
    period_key TEXT;
    result JSONB;
BEGIN
    period_key := p_class_subject_id || '_' || p_day;
    
    SELECT reflections->period_key INTO result
    FROM lesson_plan_reflections_jsonb
    WHERE lesson_plan_id = p_lesson_plan_id;
    
    RETURN COALESCE(result, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to update reflection for specific period
CREATE OR REPLACE FUNCTION update_period_reflection(
    p_lesson_plan_id UUID,
    p_class_subject_id TEXT,
    p_day TEXT,
    p_reflection_data JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    period_key TEXT;
BEGIN
    period_key := p_class_subject_id || '_' || p_day;
    
    UPDATE lesson_plan_reflections_jsonb
    SET reflections = reflections || jsonb_build_object(period_key, p_reflection_data)
    WHERE lesson_plan_id = p_lesson_plan_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

COMMIT;

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

/*
-- Create initial reflections for a lesson plan
INSERT INTO lesson_plan_reflections_jsonb (lesson_plan_id, user_id, reflections)
VALUES (
    'lesson-plan-uuid',
    'user-uuid',
    '{
        "f1_math_isnin": {
            "overall_rating": 5,
            "objectives_achieved": true,
            "challenges_faced": "",
            "activity_effectiveness": 5,
            "time_management": "on_time",
            "student_engagement": 5,
            "resource_adequacy": "adequate",
            "improvements_needed": null,
            "successful_strategies": null,
            "action_items": [],
            "additional_notes": null,
            "jumlah_murid_mencapai_objektif": 0,
            "tindakan_susulan": [],
            "tidak_terlaksana": null
        },
        "f1_math_selasa": {
            "overall_rating": 5,
            "objectives_achieved": true,
            "challenges_faced": "",
            "activity_effectiveness": 5,
            "time_management": "on_time",
            "student_engagement": 5,
            "resource_adequacy": "adequate",
            "improvements_needed": null,
            "successful_strategies": null,
            "action_items": [],
            "additional_notes": null,
            "jumlah_murid_mencapai_objektif": 0,
            "tindakan_susulan": [],
            "tidak_terlaksana": null
        }
    }'::jsonb
);

-- Update a specific period's reflection
SELECT update_period_reflection(
    'lesson-plan-uuid',
    'f1_math',
    'isnin',
    '{
        "overall_rating": 4,
        "objectives_achieved": true,
        "challenges_faced": "Some students struggled with fractions",
        "activity_effectiveness": 4,
        "time_management": "on_time",
        "student_engagement": 4,
        "resource_adequacy": "adequate"
    }'::jsonb
);

-- Get reflection for specific period
SELECT get_period_reflection('lesson-plan-uuid', 'f1_math', 'isnin');

-- Get all reflections for a lesson plan
SELECT reflections, overall_rating, total_periods
FROM lesson_plan_reflections_jsonb
WHERE lesson_plan_id = 'lesson-plan-uuid';
*/
