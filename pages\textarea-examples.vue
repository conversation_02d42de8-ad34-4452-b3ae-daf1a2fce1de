<template>
    <div class="max-w-4xl mx-auto p-8 space-y-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">
                Textarea Component Examples
            </h1>

            <!-- Floating Label Variant -->
            <div class="space-y-6">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                    Floating Label Variant
                </h2>

                <!-- Basic Floating -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Basic Floating</h3>
                    <UiBaseTextarea v-model="floatingBasic" variant="floating" placeholder="Enter your message here..."
                        :rows="3" />
                </div>

                <!-- Floating with Auto-resize -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Floating with Auto-resize</h3>
                    <UiBaseTextarea v-model="floatingAutoResize" variant="floating"
                        placeholder="This textarea will grow as you type..." :rows="3" auto-resize />
                </div>

                <!-- Floating with Error -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Floating with Error</h3>
                    <UiBaseTextarea v-model="floatingError" variant="floating"
                        placeholder="Enter required information..." :rows="3"
                        :error="floatingError.length === 0 ? 'This field is required' : ''" required />
                </div>

                <!-- Floating with Vertical Resize -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Floating with Vertical Resize</h3>
                    <UiBaseTextarea v-model="floatingResize" variant="floating"
                        placeholder="You can resize this textarea vertically..." :rows="3" resize="vertical" />
                </div>
            </div>

            <!-- Normal Variant -->
            <div class="space-y-6 mt-12">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                    Normal Variant
                </h2>

                <!-- Basic Normal -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Basic Normal</h3>
                    <UiBaseTextarea v-model="normalBasic" variant="normal" label="Description"
                        placeholder="Enter your description here..." :rows="3" />
                </div>

                <!-- Normal with Auto-resize -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Normal with Auto-resize</h3>
                    <UiBaseTextarea v-model="normalAutoResize" variant="normal" label="Comments"
                        placeholder="This textarea will expand as needed..." :rows="3" auto-resize />
                </div>

                <!-- Normal with Error -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Normal with Error</h3>
                    <UiBaseTextarea v-model="normalError" variant="normal" label="Required Field"
                        placeholder="Enter required text..." :rows="3"
                        :error="normalError.length === 0 ? 'This field is required' : ''" required />
                </div>

                <!-- Normal Disabled -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Normal Disabled</h3>
                    <UiBaseTextarea v-model="normalDisabled" variant="normal" label="Disabled Field"
                        placeholder="This field is disabled..." :rows="3" disabled />
                </div>

                <!-- Normal with Both Resize -->
                <div class="space-y-4">
                    <h3 class="text-md font-medium text-gray-700 dark:text-gray-300">Normal with Both Resize</h3>
                    <UiBaseTextarea v-model="normalBothResize" variant="normal" label="Resizable Field"
                        placeholder="You can resize this textarea in both directions..." :rows="3" resize="both" />
                </div>
            </div>

            <!-- Values Display -->
            <div class="mt-12 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Current Values:</h3>
                <div class="space-y-2 text-sm">
                    <div><strong>Floating Basic:</strong> {{ floatingBasic || 'Empty' }}</div>
                    <div><strong>Floating Auto-resize:</strong> {{ floatingAutoResize || 'Empty' }}</div>
                    <div><strong>Floating Error:</strong> {{ floatingError || 'Empty' }}</div>
                    <div><strong>Floating Resize:</strong> {{ floatingResize || 'Empty' }}</div>
                    <div><strong>Normal Basic:</strong> {{ normalBasic || 'Empty' }}</div>
                    <div><strong>Normal Auto-resize:</strong> {{ normalAutoResize || 'Empty' }}</div>
                    <div><strong>Normal Error:</strong> {{ normalError || 'Empty' }}</div>
                    <div><strong>Normal Disabled:</strong> {{ normalDisabled || 'Empty' }}</div>
                    <div><strong>Normal Both Resize:</strong> {{ normalBothResize || 'Empty' }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Floating variant examples
const floatingBasic = ref('')
const floatingAutoResize = ref('')
const floatingError = ref('')
const floatingResize = ref('')

// Normal variant examples
const normalBasic = ref('')
const normalAutoResize = ref('')
const normalError = ref('')
const normalDisabled = ref('This field is disabled and cannot be edited')
const normalBothResize = ref('')
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
