// Get user schools API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')

    // Initialize Supabase client with the user's token
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Check if user is a school admin by checking the is_school_admin flag in profiles
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_school_admin')
      .eq('id', user.id)
      .single()

    // If profile doesn't exist or is_school_admin is not true, user is not a school admin
    if (profileError || !profile || profile.is_school_admin !== true) {
      return {
        success: false,
        schools: [],
        total: 0,
        message: 'User is not a school administrator'
      }
    }

    // Try to get user's school memberships with school data
    let schools: any[] = []

    try {
      const { data: memberships, error: membershipError } = await supabase
        .from('school_memberships')
        .select(`
          *,
          school:schools(*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .order('joined_at', { ascending: false })

      if (!membershipError && memberships) {
        // Transform the data
        schools = memberships.map(membership => ({
          ...membership.school,
          membership: {
            id: membership.id,
            role: membership.role,
            status: membership.status,
            joined_at: membership.joined_at,
            permissions: membership.permissions
          }
        }))
      }
    } catch (error) {
      console.log('School memberships table not available, checking admin schools only')
    }

    // Get admin schools (schools where user is the admin_user_id)
    try {
      const { data: adminSchools, error: adminError } = await supabase
        .from('schools')
        .select('*')
        .eq('admin_user_id', user.id)

      if (!adminError && adminSchools) {
        // Add admin schools that might not have memberships
        for (const adminSchool of adminSchools) {
          const existingSchool = schools.find(s => s.id === adminSchool.id)
          if (!existingSchool) {
            schools.push({
              ...adminSchool,
              membership: {
                id: null,
                role: 'admin',
                status: 'active',
                joined_at: adminSchool.created_at,
                permissions: {}
              }
            })
          }
        }
      }
    } catch (error) {
      console.log('Schools table not available, using development fallback')
    }

    // No development fallback - only real schools for school admins

    return {
      success: true,
      schools,
      total: schools.length
    }

  } catch (error: any) {
    console.error('User schools API error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
