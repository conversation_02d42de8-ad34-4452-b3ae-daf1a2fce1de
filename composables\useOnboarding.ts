import { ref, computed } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'
import type { Database } from '~/types/supabase'

interface OnboardingStep {
  id: string
  title: string
  content: string
  icon?: string
  target?: string
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  persistent?: boolean
}

interface OnboardingTour {
  id: string
  name: string
  steps: OnboardingStep[]
  autoStart?: boolean
  showOnce?: boolean
}

interface UserOnboardingState {
  completedTours: string[]
  skippedTours: string[]
  currentTour?: string
  currentStep?: number
  showTooltips: boolean
  lastUpdated: string
}

export const useOnboarding = () => {
  const supabase = useSupabaseClient<Database>()
  const user = useSupabaseUser()
  
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const userState = ref<UserOnboardingState>({
    completedTours: [],
    skippedTours: [],
    showTooltips: true,
    lastUpdated: new Date().toISOString()
  })
  
  const currentTour = ref<OnboardingTour | null>(null)
  const currentStepIndex = ref(0)
  const isActive = ref(false)
  
  // Predefined tours
  const tours: OnboardingTour[] = [
    {
      id: 'reflection-modes',
      name: 'Reflection Modes',
      autoStart: true,
      showOnce: true,
      steps: [
        {
          id: 'quick-mode',
          title: 'Quick Mode',
          content: 'Quick mode shows auto-calculated reflections based on your detailed entries. Perfect for getting an overview of your lesson plan performance.',
          icon: 'heroicons:bolt',
          target: '[data-onboarding="quick-mode"]',
          position: 'bottom'
        },
        {
          id: 'detailed-mode',
          title: 'Detailed Mode',
          content: 'Detailed mode lets you add specific reflections for each class and day. Use this when you want to record detailed feedback about your lessons.',
          icon: 'heroicons:document-text',
          target: '[data-onboarding="detailed-mode"]',
          position: 'bottom'
        },
        {
          id: 'mode-switching',
          title: 'Switch Between Modes',
          content: 'You can easily switch between Quick and Detailed modes using these tabs. Your data is automatically saved and synchronized.',
          icon: 'heroicons:arrow-path',
          target: '[data-onboarding="mode-tabs"]',
          position: 'bottom'
        }
      ]
    },
    {
      id: 'detailed-reflection',
      name: 'Detailed Reflection Features',
      autoStart: false,
      showOnce: false,
      steps: [
        {
          id: 'class-subject-selection',
          title: 'Select Class & Subject',
          content: 'Choose the specific class and subject combination you want to reflect on. Only combinations from your timetable will be shown.',
          icon: 'heroicons:academic-cap',
          target: '[data-onboarding="class-subject-selector"]',
          position: 'bottom'
        },
        {
          id: 'day-navigation',
          title: 'Navigate Between Days',
          content: 'Use these tabs to switch between different days of the week. Each day can have different reflections for the same class-subject.',
          icon: 'heroicons:calendar-days',
          target: '[data-onboarding="day-tabs"]',
          position: 'bottom'
        },
        {
          id: 'quick-copy',
          title: 'Copy from Other Days',
          content: 'Save time by copying reflections from other days when lessons are similar. You can always modify the copied data.',
          icon: 'heroicons:document-duplicate',
          target: '[data-onboarding="quick-copy"]',
          position: 'right'
        },
        {
          id: 'tidak-terlaksana',
          title: 'Mark as Not Delivered',
          content: 'If a lesson wasn\'t delivered, use the "Tidak Terlaksana" option. This will automatically adjust ratings and provide appropriate follow-up options.',
          icon: 'heroicons:x-circle',
          target: '[data-onboarding="tidak-terlaksana"]',
          position: 'left'
        }
      ]
    }
  ]
  
  // Computed
  const currentStep = computed(() => {
    if (!currentTour.value || currentStepIndex.value >= currentTour.value.steps.length) {
      return null
    }
    return currentTour.value.steps[currentStepIndex.value]
  })
  
  const isLastStep = computed(() => {
    return currentTour.value ? currentStepIndex.value >= currentTour.value.steps.length - 1 : false
  })
  
  const isFirstStep = computed(() => currentStepIndex.value === 0)
  
  const shouldShowTooltips = computed(() => userState.value.showTooltips)
  
  // Methods
  const loadUserState = async () => {
    if (!user.value) return
    
    try {
      loading.value = true
      error.value = null
      
      const { data, error: fetchError } = await supabase
        .from('user_preferences')
        .select('onboarding_state')
        .eq('user_id', user.value.id)
        .maybeSingle()
      
      if (fetchError) {
        throw fetchError
      }
      
      if (data?.onboarding_state && typeof data.onboarding_state === 'object' && !Array.isArray(data.onboarding_state)) {
        const onboardingState = data.onboarding_state as Record<string, any>
        userState.value = {
          ...userState.value,
          completedTours: onboardingState.completedTours || [],
          skippedTours: onboardingState.skippedTours || [],
          currentTour: onboardingState.currentTour,
          currentStep: onboardingState.currentStep,
          showTooltips: onboardingState.showTooltips ?? true,
          lastUpdated: onboardingState.lastUpdated || new Date().toISOString()
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load onboarding state'
      console.error('Error loading onboarding state:', err)
    } finally {
      loading.value = false
    }
  }
  
  const saveUserState = async () => {
    if (!user.value) return
    
    try {
      userState.value.lastUpdated = new Date().toISOString()
      
      const { error: upsertError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.value.id,
          onboarding_state: userState.value
        })
      
      if (upsertError) throw upsertError
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to save onboarding state'
      console.error('Error saving onboarding state:', err)
    }
  }
  
  const startTour = (tourId: string) => {
    const tour = tours.find(t => t.id === tourId)
    if (!tour) return false
    
    // Check if tour should be shown
    if (tour.showOnce && userState.value.completedTours.includes(tourId)) {
      return false
    }
    
    if (userState.value.skippedTours.includes(tourId) && tour.showOnce) {
      return false
    }
    
    currentTour.value = tour
    currentStepIndex.value = 0
    isActive.value = true
    
    return true
  }
  
  const nextStep = () => {
    if (!currentTour.value) return
    
    if (isLastStep.value) {
      completeTour()
    } else {
      currentStepIndex.value++
    }
  }
  
  const previousStep = () => {
    if (currentStepIndex.value > 0) {
      currentStepIndex.value--
    }
  }
  
  const skipTour = () => {
    if (!currentTour.value) return
    
    userState.value.skippedTours.push(currentTour.value.id)
    endTour()
    saveUserState()
  }
  
  const completeTour = () => {
    if (!currentTour.value) return
    
    userState.value.completedTours.push(currentTour.value.id)
    endTour()
    saveUserState()
  }
  
  const endTour = () => {
    currentTour.value = null
    currentStepIndex.value = 0
    isActive.value = false
  }
  
  const resetTour = (tourId: string) => {
    userState.value.completedTours = userState.value.completedTours.filter(id => id !== tourId)
    userState.value.skippedTours = userState.value.skippedTours.filter(id => id !== tourId)
    saveUserState()
  }
  
  const toggleTooltips = (enabled: boolean) => {
    userState.value.showTooltips = enabled
    saveUserState()
  }
  
  const checkAutoStartTours = () => {
    if (!shouldShowTooltips.value) return
    
    const autoStartTours = tours.filter(tour => 
      tour.autoStart && 
      !userState.value.completedTours.includes(tour.id) &&
      !userState.value.skippedTours.includes(tour.id)
    )
    
    if (autoStartTours.length > 0) {
      startTour(autoStartTours[0].id)
    }
  }
  
  const getTourProgress = (tourId: string) => {
    const tour = tours.find(t => t.id === tourId)
    if (!tour) return null
    
    return {
      completed: userState.value.completedTours.includes(tourId),
      skipped: userState.value.skippedTours.includes(tourId),
      totalSteps: tour.steps.length
    }
  }
  
  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    userState: computed(() => userState.value),
    currentTour: computed(() => currentTour.value),
    currentStep,
    currentStepIndex: computed(() => currentStepIndex.value),
    isActive: computed(() => isActive.value),
    isLastStep,
    isFirstStep,
    shouldShowTooltips,
    tours,
    
    // Methods
    loadUserState,
    saveUserState,
    startTour,
    nextStep,
    previousStep,
    skipTour,
    completeTour,
    endTour,
    resetTour,
    toggleTooltips,
    checkAutoStartTours,
    getTourProgress
  }
}
