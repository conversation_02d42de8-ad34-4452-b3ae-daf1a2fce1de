import { ref, computed, onMounted, onUnmounted, type Ref } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'

export function usePageLeaveWarning() {
  // Page leave warning states
  const showLeaveWarningModal = ref(false)
  const pendingNavigation = ref<Function | null>(null)

  // Page leave warning methods
  const handleBeforeUnload = (event: BeforeUnloadEvent, hasUnsavedChanges: boolean) => {
    if (hasUnsavedChanges) {
      event.preventDefault()
      event.returnValue = ''
      return ''
    }
  }

  const confirmLeave = (hasFormChanges: Ref<boolean>) => {
    showLeaveWarningModal.value = false

    // If there's a pending navigation (from router guard), proceed with it
    if (pendingNavigation.value) {
      pendingNavigation.value()
      pendingNavigation.value = null
    }

    // Reset form changes flag to prevent further warnings
    hasFormChanges.value = false
  }

  const cancelLeave = () => {
    showLeaveWarningModal.value = false

    // If there's a pending navigation (from router guard), cancel it
    if (pendingNavigation.value) {
      pendingNavigation.value(false) // Cancel navigation
      pendingNavigation.value = null
    }
  }

  const setupPageLeaveWarning = (hasUnsavedChanges: () => boolean, hasFormChanges: Ref<boolean>) => {
    // Browser navigation guard
    const beforeUnloadHandler = (event: BeforeUnloadEvent) => {
      handleBeforeUnload(event, hasUnsavedChanges())
    }

    // Router navigation guard for SPA navigation
    onBeforeRouteLeave((_to, _from, next) => {
      if (hasUnsavedChanges()) {
        showLeaveWarningModal.value = true
        // Store the navigation callback for later use
        pendingNavigation.value = next
      } else {
        next()
      }
    })

    onMounted(() => {
      // Add beforeunload event listener for page leave warning (browser navigation)
      window.addEventListener('beforeunload', beforeUnloadHandler)
    })

    // Cleanup on unmount
    onUnmounted(() => {
      window.removeEventListener('beforeunload', beforeUnloadHandler)
    })
  }

  return {
    showLeaveWarningModal,
    confirmLeave,
    cancelLeave,
    setupPageLeaveWarning
  }
}
