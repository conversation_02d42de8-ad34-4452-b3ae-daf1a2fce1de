BEGIN;

-- Add new columns to parent table
ALTER TABLE teacher_schedules
  ADD COLUMN lesson_plan_id UUID,
  ADD COLUMN schedule_details JSONB NOT NULL DEFAULT '{}'::jsonb;

-- Update constraints
ALTER TABLE teacher_schedules
  DROP CONSTRAINT unique_class_subject_per_creation;

ALTER TABLE teacher_schedules
  ADD CONSTRAINT unique_lesson_plan_per_creation 
  UNIQUE (user_id, lesson_plan_id, created_at); -- Keep created_at for partitioning key

-- Add foreign key constraint (assuming lesson_plans table exists)
ALTER TABLE teacher_schedules
  ADD CONSTRAINT teacher_schedules_lesson_plan_fkey
  FOREIGN KEY (lesson_plan_id) 
  REFERENCES lesson_plans(id)
  ON DELETE CASCADE; -- Changed to CASCADE as per user's request (if lesson plan deleted, delete schedule)

-- Remove obsolete columns
ALTER TABLE teacher_schedules
  DROP COLUMN IF EXISTS class_id,
  DROP COLUMN IF EXISTS subject_id,
  DROP COLUMN IF EXISTS days_scheduled,
  DROP COLUMN IF EXISTS total_periods;

-- Apply changes to partitions

-- Create new partition for 2026
CREATE TABLE teacher_schedules_2026
PARTITION OF teacher_schedules
FOR VALUES FROM ('2026-01-01') TO ('2027-01-01');

-- Verify foreign keys (example for timetable_entries)
-- No changes needed as primary key (id, created_at) remains

COMMIT;