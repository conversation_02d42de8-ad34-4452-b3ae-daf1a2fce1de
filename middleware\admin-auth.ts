// Admin authentication middleware
// Created: 2025-07-13

import { navigateTo, defineNuxtRouteMiddleware } from "#app"

export default defineNuxtRouteMiddleware(async (to, from) => {
  // This middleware is specifically for admin routes
  // It should only run on admin dashboard routes
  
  if (!to.path.startsWith('/admin')) {
    return
  }

  // Skip auth check for login and register pages
  if (to.path === '/admin/login' || to.path === '/admin/register') {
    return
  }

  const supabase = useSupabaseClient()

  try {
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return navigateTo('/admin/login')
    }

    // Check if user has admin privileges by fetching their schools
    try {
      const response = await $fetch('/api/schools/user-schools', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      }) as any

      if (!response.success || response.schools.length === 0) {
        // User doesn't have any schools, so no admin access
        return navigateTo('/')
      }
    } catch (error) {
      console.error('Error checking admin access:', error)
      return navigateTo('/')
    }

    // Check for specific admin routes that require super admin access
    if (to.path.startsWith('/admin/coupons') || to.path.startsWith('/admin/system')) {
      const isSuperAdmin = await checkUserSuperAdminAccess(session.user.id)
      
      if (!isSuperAdmin) {
        return navigateTo('/admin/dashboard')
      }
    }
    
  } catch (error) {
    console.error('Error in admin auth middleware:', error)
    return navigateTo('/admin/login')
  }
})

/**
 * Check if user has super admin access
 * Super admins can manage coupons, system settings, etc.
 */
async function checkUserSuperAdminAccess(userId: string): Promise<boolean> {
  const supabase = useSupabaseClient()

  try {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) return false

    // Hardcoded super admin emails (replace with your actual admin emails)
    const superAdminEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    return superAdminEmails.includes(user.email || '')
  } catch (error) {
    console.error('Error checking super admin access:', error)
    return false
  }
}
