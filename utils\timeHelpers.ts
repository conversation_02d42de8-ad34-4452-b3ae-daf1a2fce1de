// Time utility functions for timetable and schedule management

/**
 * Formats a time string to 12-hour format with AM/PM
 * @param time - Time string in HH:MM or HH:MM:SS format
 * @returns Formatted time string (e.g., "2:30 PM")
 */
export function formatTime(time: string): string {
  if (!time) return ''

  // Handle both HH:MM and HH:MM:SS formats
  const timeParts = time.split(':')
  const hours = parseInt(timeParts[0])
  const minutes = parseInt(timeParts[1])

  const period = hours >= 12 ? 'PM' : 'AM'
  const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`
}

/**
 * Normalizes time format for consistent comparison
 * @param time - Time string in HH:MM or HH:MM:SS format
 * @returns Normalized time string in HH:MM format
 */
export function normalizeTimeFormat(time: string): string {
  if (!time) return ''
  
  // If time already has seconds (HH:MM:SS), return as is for storage
  if (time.length === 8 && time.split(':').length === 3) {
    return time
  }
  
  // If time is HH:MM format, add :00 seconds for comparison consistency
  if (time.length === 5 && time.split(':').length === 2) {
    return `${time}:00`
  }
  
  return time
}

/**
 * Creates a time range label
 * @param startTime - Start time in HH:MM format
 * @param endTime - End time in HH:MM format
 * @returns Formatted time range (e.g., "8:00 AM - 9:00 AM")
 */
export function formatTimeRange(startTime: string, endTime: string): string {
  if (!startTime || !endTime) return ''
  
  const start = formatTime(startTime)
  const end = formatTime(endTime)
  return `${start} - ${end}`
}

/**
 * Calculates duration between two times in minutes
 * @param startTime - Start time in HH:MM format
 * @param endTime - End time in HH:MM format
 * @returns Duration in minutes
 */
export function calculateDurationMinutes(startTime: string, endTime: string): number {
  if (!startTime || !endTime) return 0

  const [startHours, startMinutes] = startTime.split(':').map(Number)
  const [endHours, endMinutes] = endTime.split(':').map(Number)

  const startTotalMinutes = startHours * 60 + startMinutes
  const endTotalMinutes = endHours * 60 + endMinutes

  return Math.max(0, endTotalMinutes - startTotalMinutes)
}

/**
 * Adds duration in minutes to a time string
 * @param time - Start time in HH:MM format
 * @param durationMinutes - Duration to add in minutes
 * @returns New time in HH:MM format
 */
export function addDurationToTime(time: string, durationMinutes: number): string {
  if (!time) return ''

  const [hours, minutes] = time.split(':').map(Number)
  const totalMinutes = hours * 60 + minutes + durationMinutes

  const newHours = Math.floor(totalMinutes / 60)
  const newMinutes = totalMinutes % 60

  return `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}`
}

/**
 * Checks if a time is within a time range (inclusive)
 * @param time - Time to check in HH:MM format
 * @param startTime - Range start time in HH:MM format
 * @param endTime - Range end time in HH:MM format
 * @returns True if time is within the range
 */
export function isTimeInRange(time: string, startTime: string, endTime: string): boolean {
  if (!time || !startTime || !endTime) return false

  const [timeHours, timeMinutes] = time.split(':').map(Number)
  const [startHours, startMinutes] = startTime.split(':').map(Number)
  const [endHours, endMinutes] = endTime.split(':').map(Number)

  const timeTotal = timeHours * 60 + timeMinutes
  const startTotal = startHours * 60 + startMinutes
  const endTotal = endHours * 60 + endMinutes

  return timeTotal >= startTotal && timeTotal <= endTotal
}

/**
 * Validates a time string format
 * @param time - Time string to validate
 * @returns True if time format is valid (HH:MM or HH:MM:SS)
 */
export function isValidTimeFormat(time: string): boolean {
  if (!time) return false

  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/
  return timeRegex.test(time)
}

/**
 * Converts 24-hour time to minutes since midnight
 * @param time - Time string in HH:MM format
 * @returns Minutes since midnight
 */
export function timeToMinutes(time: string): number {
  if (!time) return 0

  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

/**
 * Converts minutes since midnight to HH:MM format
 * @param minutes - Minutes since midnight
 * @returns Time string in HH:MM format
 */
export function minutesToTime(minutes: number): string {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

/**
 * Common duration presets for quick time slot setup
 */
export const DURATION_PRESETS = [
  { label: '15 min', minutes: 15 },
  { label: '30 min', minutes: 30 },
  { label: '45 min', minutes: 45 },
  { label: '60 min', minutes: 60 }
] as const

/**
 * Common school time periods for Malaysian schools
 */
export const COMMON_SCHOOL_TIMES = {
  MORNING_START: '07:30',
  MORNING_ASSEMBLY: '07:45',
  FIRST_PERIOD: '08:00',
  FIRST_BREAK: '10:00',
  FIRST_BREAK_END: '10:20',
  LUNCH_BREAK: '12:20',
  LUNCH_BREAK_END: '13:00',
  AFTERNOON_END: '15:00'
} as const
