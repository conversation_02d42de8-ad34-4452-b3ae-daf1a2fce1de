-- Migration: Add school_id to existing tables for multi-tenant data isolation
-- Created: 2025-07-13
-- Description: Add school_id foreign key to user-data tables while keeping global tables unchanged

BEGIN;

-- =====================================================
-- TABLES THAT NEED SCHOOL_ID (USER-SPECIFIC DATA)
-- =====================================================

-- 1. profiles - User profiles are school-specific
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 2. lesson_plans - Lesson plans are school-specific
ALTER TABLE lesson_plans 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 3. lesson_plan_detailed_reflections - Reflections are school-specific
ALTER TABLE lesson_plan_detailed_reflections 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 4. teacher_schedules - Schedules are school-specific
ALTER TABLE teacher_schedules 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 5. timetable_entries - Timetables are school-specific
ALTER TABLE timetable_entries 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 6. observation_schedules - Observations are school-specific
ALTER TABLE observation_schedules 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 7. teacher_activities - Activities are school-specific
ALTER TABLE teacher_activities 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 8. teacher_tasks - Tasks are school-specific
ALTER TABLE teacher_tasks 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 9. teacher_observer_assignments - Observer assignments are school-specific
ALTER TABLE teacher_observer_assignments 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 10. rph_weeks - RPH weeks are school-specific
ALTER TABLE rph_weeks 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 11. user_week_submissions - Week submissions are school-specific
ALTER TABLE user_week_submissions 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 12. academic_calendar_documents - School-specific documents
ALTER TABLE academic_calendar_documents 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 13. annual_calendar_events - School-specific events
ALTER TABLE annual_calendar_events 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 14. dskp_documents - School-specific DSKP documents
ALTER TABLE dskp_documents 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 15. rpt_documents - School-specific RPT documents
ALTER TABLE rpt_documents 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 16. items - School-specific items
ALTER TABLE items 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 17. jadual_pencerapan - School-specific observation schedules
ALTER TABLE jadual_pencerapan 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 18. tidak_terlaksana - School-specific records
ALTER TABLE tidak_terlaksana 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 19. tindakan_susulan - School-specific follow-up actions
ALTER TABLE tindakan_susulan 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 20. user_preferences - User preferences are school-specific
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- 21. user_reflection_template_preferences - School-specific template preferences
ALTER TABLE user_reflection_template_preferences 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- =====================================================
-- TABLES THAT REMAIN GLOBAL (NO SCHOOL_ID)
-- =====================================================

-- subjects - Global subjects shared across all schools (with option for school-specific)
-- Note: subjects table will have school_id as nullable for school-specific subjects
ALTER TABLE subjects 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- reflection_templates - Global templates shared across all schools (with option for school-specific)
-- Note: reflection_templates table will have school_id as nullable for school-specific templates
ALTER TABLE reflection_templates 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- global_settings - Remains completely global (no school_id)
-- This table contains system-wide settings

-- =====================================================
-- CREATE INDEXES FOR SCHOOL_ID COLUMNS
-- =====================================================

-- Create indexes for all school_id columns for better query performance
CREATE INDEX IF NOT EXISTS idx_profiles_school_id ON profiles (school_id);
CREATE INDEX IF NOT EXISTS idx_lesson_plans_school_id ON lesson_plans (school_id);
CREATE INDEX IF NOT EXISTS idx_lesson_plan_detailed_reflections_school_id ON lesson_plan_detailed_reflections (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_schedules_school_id ON teacher_schedules (school_id);
CREATE INDEX IF NOT EXISTS idx_timetable_entries_school_id ON timetable_entries (school_id);
CREATE INDEX IF NOT EXISTS idx_observation_schedules_school_id ON observation_schedules (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_activities_school_id ON teacher_activities (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_school_id ON teacher_tasks (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_observer_assignments_school_id ON teacher_observer_assignments (school_id);
CREATE INDEX IF NOT EXISTS idx_rph_weeks_school_id ON rph_weeks (school_id);
CREATE INDEX IF NOT EXISTS idx_user_week_submissions_school_id ON user_week_submissions (school_id);
CREATE INDEX IF NOT EXISTS idx_academic_calendar_documents_school_id ON academic_calendar_documents (school_id);
CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_school_id ON annual_calendar_events (school_id);
CREATE INDEX IF NOT EXISTS idx_dskp_documents_school_id ON dskp_documents (school_id);
CREATE INDEX IF NOT EXISTS idx_rpt_documents_school_id ON rpt_documents (school_id);
CREATE INDEX IF NOT EXISTS idx_items_school_id ON items (school_id);
CREATE INDEX IF NOT EXISTS idx_jadual_pencerapan_school_id ON jadual_pencerapan (school_id);
CREATE INDEX IF NOT EXISTS idx_tidak_terlaksana_school_id ON tidak_terlaksana (school_id);
CREATE INDEX IF NOT EXISTS idx_tindakan_susulan_school_id ON tindakan_susulan (school_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_school_id ON user_preferences (school_id);
CREATE INDEX IF NOT EXISTS idx_user_reflection_template_preferences_school_id ON user_reflection_template_preferences (school_id);

-- Indexes for nullable school_id columns (global/school-specific data)
CREATE INDEX IF NOT EXISTS idx_subjects_school_id ON subjects (school_id);
CREATE INDEX IF NOT EXISTS idx_reflection_templates_school_id ON reflection_templates (school_id);

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN profiles.school_id IS 'Reference to school - user profiles are school-specific';
COMMENT ON COLUMN lesson_plans.school_id IS 'Reference to school - lesson plans are school-specific';
COMMENT ON COLUMN teacher_schedules.school_id IS 'Reference to school - schedules are school-specific';
COMMENT ON COLUMN timetable_entries.school_id IS 'Reference to school - timetables are school-specific';
COMMENT ON COLUMN subjects.school_id IS 'Reference to school - NULL for global subjects, set for school-specific subjects';
COMMENT ON COLUMN reflection_templates.school_id IS 'Reference to school - NULL for global templates, set for school-specific templates';

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify school_id columns were added
SELECT 'School ID columns added successfully' as status;

-- Show which tables now have school_id
SELECT 
    table_name,
    column_name,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE column_name = 'school_id' 
AND table_schema = 'public'
ORDER BY table_name;
