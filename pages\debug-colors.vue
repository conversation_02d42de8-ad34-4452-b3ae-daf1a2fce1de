<template>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                            Subject Color Diagnostic Tool
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">
                            Debug and test subject color mappings for all subjects in the database
                        </p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <Button @click="refreshData" :disabled="loading">
                            <Icon name="mdi:refresh" class="mr-2 h-4 w-4" />
                            Refresh Data
                        </Button>
                        <Button @click="testAllMappings" variant="primary">
                            <Icon name="mdi:test-tube" class="mr-2 h-4 w-4" />
                            Test All Mappings
                        </Button>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <Icon name="mdi:database" class="h-8 w-8 text-blue-500 mr-3" />
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Total Subjects</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ subjects.length }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <Icon name="mdi:palette" class="h-8 w-8 text-green-500 mr-3" />
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Mapped Colors</p>
                            <p class="text-2xl font-bold text-green-900 dark:text-green-100">
                                {{ mappedCount }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <Icon name="mdi:alert" class="h-8 w-8 text-red-500 mr-3" />
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Fallback Colors</p>
                            <p class="text-2xl font-bold text-red-900 dark:text-red-100">
                                {{ fallbackCount }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <div class="flex items-center">
                        <Icon name="mdi:bug" class="h-8 w-8 text-orange-500 mr-3" />
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Issues Found</p>
                            <p class="text-2xl font-bold text-orange-900 dark:text-orange-100">
                                {{ issuesCount }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12">
                <div class="flex items-center justify-center space-x-2">
                    <Icon name="mdi:loading" class="h-6 w-6 animate-spin text-blue-500" />
                    <span class="text-gray-600 dark:text-gray-400">Loading subjects...</span>
                </div>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <Alert type="error" :message="error?.message || 'An error occurred'" />
            </div>

            <!-- Subject Color Testing Grid -->
            <div v-else class="space-y-6">
                <!-- Filter Controls -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <div class="flex flex-wrap items-center gap-4">
                        <div class="flex-1 min-w-64">
                            <Input v-model="searchQuery" placeholder="Search subjects..." prepend-icon="mdi:magnify" />
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</label>
                            <select v-model="filterType"
                                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="all">All Subjects</option>
                                <option value="mapped">Mapped Colors</option>
                                <option value="fallback">Fallback Colors</option>
                                <option value="issues">Issues Only</option>
                                <option value="problematic">Problematic Subjects</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Subject Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div v-for="result in filteredResults" :key="result.subject.id"
                        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                        <!-- Subject Header -->
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">
                                        {{ result.subject.name }}
                                    </h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Code: {{ result.subject.code }} | ID: {{ result.subject.id.substring(0, 8) }}...
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <!-- Status Indicator -->
                                    <div :class="[
                                        'w-3 h-3 rounded-full',
                                        result.colorResult.isPredefined ? 'bg-green-500' : 'bg-red-500'
                                    ]"
                                        :title="result.colorResult.isPredefined ? 'Predefined Color' : 'Fallback Color'">
                                    </div>
                                    <!-- Issue Indicator -->
                                    <Icon v-if="result.issues.length > 0" name="mdi:alert-circle"
                                        class="h-4 w-4 text-orange-500"
                                        :title="`${result.issues.length} issues found`" />
                                </div>
                            </div>
                        </div>

                        <!-- Color Preview -->
                        <div class="p-4">
                            <div :class="[
                                'w-full h-20 rounded-lg flex items-center justify-center mb-4 border-2',
                                result.colorResult.color.bg_color || 'bg-gray-100 dark:bg-gray-700',
                                result.colorResult.color.bg_color ? 'border-transparent' : 'border-red-300 dark:border-red-600 border-dashed'
                            ]">
                                <span :class="[
                                    'text-sm font-medium',
                                    result.colorResult.color.text_color || 'text-gray-500 dark:text-gray-400'
                                ]">
                                    {{ result.colorResult.color.subject_name }}
                                </span>
                            </div>

                            <!-- Color Details -->
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between">
                                    <span class="text-gray-500 dark:text-gray-400">Color Key:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">
                                        {{ result.colorResult.colorKey || 'hash-generated' }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500 dark:text-gray-400">Background:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">
                                        {{ result.colorResult.color.bg_color || 'MISSING' }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500 dark:text-gray-400">Text Color:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">
                                        {{ result.colorResult.color.text_color || 'MISSING' }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500 dark:text-gray-400">Border:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">
                                        {{ result.colorResult.color.color || 'MISSING' }}
                                    </span>
                                </div>
                            </div>

                            <!-- Matching Keywords -->
                            <div v-if="result.colorResult.matchedKeywords.length > 0" class="mt-4">
                                <span class="text-xs text-gray-500 dark:text-gray-400 mb-2 block">Matched
                                    Keywords:</span>
                                <div class="flex flex-wrap gap-1">
                                    <span v-for="keyword in result.colorResult.matchedKeywords" :key="keyword"
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                        {{ keyword }}
                                    </span>
                                </div>
                            </div>

                            <!-- Issues -->
                            <div v-if="result.issues.length > 0" class="mt-4">
                                <span class="text-xs text-red-600 dark:text-red-400 mb-2 block font-medium">Issues
                                    Found:</span>
                                <div class="space-y-1">
                                    <div v-for="issue in result.issues" :key="issue"
                                        class="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded">
                                        {{ issue }}
                                    </div>
                                </div>
                            </div>

                            <!-- Debug Info -->
                            <details class="mt-4">
                                <summary
                                    class="text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                                    Debug Info
                                </summary>
                                <div class="mt-2 text-xs space-y-1 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                    <div><strong>Normalized Name:</strong> {{ result.debugInfo.normalizedName }}</div>
                                    <div><strong>Search Attempts:</strong> {{ result.debugInfo.searchAttempts.join(', ')
                                        }}</div>
                                    <div><strong>Resolution Method:</strong> {{ result.debugInfo.resolutionMethod }}
                                    </div>
                                </div>
                            </details>
                        </div>
                    </div>
                </div>

                <!-- No Results -->
                <div v-if="filteredResults.length === 0"
                    class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 text-center">
                    <Icon name="mdi:filter-off" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        No subjects match your filter
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Try adjusting your search query or filter settings.
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import Input from '~/components/ui/base/Input.vue'
import Alert from '~/components/ui/base/Alert.vue'
import { useSubjects } from '~/composables/useSubjects'
import { SUBJECT_COLORS, DYNAMIC_COLORS } from '~/types/timetable'
import type { SubjectColor } from '~/types/timetable'

// Set page metadata
definePageMeta({
    layout: 'default'
})

// Composables
const { subjects, loading, error, fetchSubjects } = useSubjects()

// Local state
const searchQuery = ref('')
const filterType = ref('all')
const testResults = ref<SubjectTestResult[]>([])

// Types
interface SubjectTestResult {
    subject: {
        id: string
        name: string
        code: string
    }
    colorResult: {
        color: SubjectColor
        isPredefined: boolean
        colorKey?: string
        matchedKeywords: string[]
    }
    issues: string[]
    debugInfo: {
        normalizedName: string
        searchAttempts: string[]
        resolutionMethod: string
    }
}

// Enhanced subject name to color key mapping (same as TimetableView.vue but with debug info)
const SUBJECT_MAPPING = {
    // PHASE 1: EXACT MATCHES (Highest Priority)
    // STEM Sciences
    'matematik tambahan': 'additional_mathematics',
    'addmath': 'additional_mathematics',
    'add math': 'additional_mathematics',
    'matematik': 'mathematics',
    'math': 'mathematics',
    'fizik': 'physics',
    'physics': 'physics',
    'kimia': 'chemistry',
    'chemistry': 'chemistry',
    'biologi': 'biology',
    'biology': 'biology',
    'sains': 'science',
    'science': 'science',

    // Languages - Most specific first to prevent conflicts
    'bahasa melayu': 'malay',
    'melayu': 'malay',
    'bm': 'malay',
    'bahasa inggeris': 'english',
    'inggeris': 'english',
    'english': 'english',
    'bi': 'english',
    'bahasa arab': 'arabic',
    'arab': 'arabic',
    'ba': 'arabic',
    'bahasa cina': 'chinese',
    'cina': 'chinese',
    'chinese': 'chinese',
    'bc': 'chinese',
    'mandarin': 'chinese',
    'bahasa tamil': 'tamil',
    'tamil': 'tamil',
    'bt': 'tamil',
    'bahasa kadazandusun': 'kadazandusun',
    'kadazandusun': 'kadazandusun',
    'bkd': 'kadazandusun',
    'kadazan': 'kadazandusun',
    'dusun': 'kadazandusun',
    'bahasa iban': 'iban',
    'iban': 'iban',
    'bib': 'iban',
    'bahasa semai': 'semai',
    'semai': 'semai',
    'bs': 'semai',

    // Social Sciences
    'sejarah': 'history',
    'history': 'history',
    'geografi': 'geography',
    'geography': 'geography',
    'ekonomi': 'economics',
    'economics': 'economics',
    'pendidikan islam': 'islamic',
    'islam': 'islamic',
    'agama islam': 'islamic',
    'agama': 'islamic',
    'pi': 'islamic',

    // Arts & Physical - Specific before generic
    'pendidikan seni visual': 'visual_arts',
    'seni visual': 'visual_arts',
    'visual arts': 'visual_arts',
    'psv': 'visual_arts',
    'pendidikan muzik': 'music',
    'muzik': 'music',
    'music': 'music',
    'pendidikan jasmani dan kesihatan': 'physical',
    'pendidikan jasmani': 'physical',
    'jasmani dan kesihatan': 'physical',
    'jasmani': 'physical',
    'sukan': 'physical',
    'physical education': 'physical',
    'physical': 'physical',
    'pjk': 'physical',
    'pe': 'physical',
    'pendidikan moral': 'moral',
    'moral': 'moral',
    'pm': 'moral',

    // Business & Technology
    'perniagaan': 'business',
    'niaga': 'business',
    'business studies': 'business',
    'business': 'business',
    'prinsip perakaunan': 'accounting',
    'perakaunan': 'accounting',
    'akaun': 'accounting',
    'accounting': 'accounting',
    'principles of accounting': 'accounting',
    'reka bentuk dan teknologi': 'design_technology',
    'reka bentuk': 'design_technology',
    'design and technology': 'design_technology',
    'rbt': 'design_technology',
    'design': 'design_technology',
    'teknologi': 'design_technology',
    'dt': 'design_technology',

    // Legacy mappings (kept for backward compatibility)
    'seni': 'visual_arts',
    'art': 'visual_arts',
    'arts': 'visual_arts'
} as const

// Priority-ordered partial match keywords
const PARTIAL_MATCH_KEYWORDS = [
    { keyword: 'matematik tambahan', colorKey: 'additional_mathematics' },
    { keyword: 'pendidikan seni visual', colorKey: 'visual_arts' },
    { keyword: 'pendidikan jasmani dan kesihatan', colorKey: 'physical' },
    { keyword: 'reka bentuk dan teknologi', colorKey: 'design_technology' },
    { keyword: 'prinsip perakaunan', colorKey: 'accounting' },
    { keyword: 'pendidikan islam', colorKey: 'islamic' },
    { keyword: 'pendidikan moral', colorKey: 'moral' },
    { keyword: 'pendidikan muzik', colorKey: 'music' },
    { keyword: 'pendidikan jasmani', colorKey: 'physical' },
    { keyword: 'bahasa kadazandusun', colorKey: 'kadazandusun' },
    { keyword: 'bahasa inggeris', colorKey: 'english' },
    { keyword: 'bahasa melayu', colorKey: 'malay' },
    { keyword: 'bahasa tamil', colorKey: 'tamil' },
    { keyword: 'bahasa semai', colorKey: 'semai' },
    { keyword: 'bahasa cina', colorKey: 'chinese' },
    { keyword: 'bahasa iban', colorKey: 'iban' },
    { keyword: 'bahasa arab', colorKey: 'arabic' },
    { keyword: 'seni visual', colorKey: 'visual_arts' },
    { keyword: 'reka bentuk', colorKey: 'design_technology' },
    { keyword: 'jasmani dan kesihatan', colorKey: 'physical' },
    { keyword: 'kadazandusun', colorKey: 'kadazandusun' },
    { keyword: 'matematik', colorKey: 'mathematics' },
    { keyword: 'perakaunan', colorKey: 'accounting' },
    { keyword: 'perniagaan', colorKey: 'business' },
    { keyword: 'inggeris', colorKey: 'english' },
    { keyword: 'ekonomi', colorKey: 'economics' },
    { keyword: 'geografi', colorKey: 'geography' },
    { keyword: 'sejarah', colorKey: 'history' },
    { keyword: 'biologi', colorKey: 'biology' },
    { keyword: 'chemistry', colorKey: 'chemistry' },
    { keyword: 'physics', colorKey: 'physics' },
    { keyword: 'jasmani', colorKey: 'physical' },
    { keyword: 'teknologi', colorKey: 'design_technology' },
    { keyword: 'melayu', colorKey: 'malay' },
    { keyword: 'english', colorKey: 'english' },
    { keyword: 'chinese', colorKey: 'chinese' },
    { keyword: 'mandarin', colorKey: 'chinese' },
    { keyword: 'tamil', colorKey: 'tamil' },
    { keyword: 'arabic', colorKey: 'arabic' },
    { keyword: 'semai', colorKey: 'semai' },
    { keyword: 'iban', colorKey: 'iban' },
    { keyword: 'kimia', colorKey: 'chemistry' },
    { keyword: 'fizik', colorKey: 'physics' },
    { keyword: 'sains', colorKey: 'science' },
    { keyword: 'science', colorKey: 'science' },
    { keyword: 'history', colorKey: 'history' },
    { keyword: 'geography', colorKey: 'geography' },
    { keyword: 'economics', colorKey: 'economics' },
    { keyword: 'islam', colorKey: 'islamic' },
    { keyword: 'agama', colorKey: 'islamic' },
    { keyword: 'muzik', colorKey: 'music' },
    { keyword: 'music', colorKey: 'music' },
    { keyword: 'moral', colorKey: 'moral' },
    { keyword: 'business', colorKey: 'business' },
    { keyword: 'niaga', colorKey: 'business' },
    { keyword: 'accounting', colorKey: 'accounting' },
    { keyword: 'akaun', colorKey: 'accounting' },
    { keyword: 'design', colorKey: 'design_technology' },
    { keyword: 'sukan', colorKey: 'physical' },
    { keyword: 'physical', colorKey: 'physical' },
    { keyword: 'seni', colorKey: 'visual_arts' },
    { keyword: 'visual', colorKey: 'visual_arts' },
    { keyword: 'art', colorKey: 'visual_arts' },
    { keyword: 'bahasa', colorKey: 'malay' },
    { keyword: 'pendidikan', colorKey: 'default' }
] as const

// Enhanced getSubjectColor function with debug info
const getSubjectColorWithDebug = (subjectId: string, subjectName: string): {
    color: SubjectColor
    isPredefined: boolean
    colorKey?: string
    matchedKeywords: string[]
    debugInfo: {
        normalizedName: string
        searchAttempts: string[]
        resolutionMethod: string
    }
} => {
    const normalizedName = subjectName.toLowerCase().trim()
    const searchAttempts: string[] = []
    const matchedKeywords: string[] = []
    let resolutionMethod = 'unknown'

    // PHASE 1: EXACT MATCHES
    for (const [keyword, colorKey] of Object.entries(SUBJECT_MAPPING)) {
        searchAttempts.push(`exact:${keyword}`)
        if (normalizedName === keyword) {
            if (SUBJECT_COLORS[colorKey]) {
                resolutionMethod = 'exact_match'
                return {
                    color: SUBJECT_COLORS[colorKey],
                    isPredefined: true,
                    colorKey,
                    matchedKeywords: [keyword],
                    debugInfo: { normalizedName, searchAttempts, resolutionMethod }
                }
            }
        }
    }

    // PHASE 2: PARTIAL MATCHES
    for (const { keyword, colorKey } of PARTIAL_MATCH_KEYWORDS) {
        searchAttempts.push(`partial:${keyword}`)
        if (normalizedName.includes(keyword)) {
            // Conflict resolution logic
            if (keyword === 'sains' &&
                (normalizedName.includes('kimia') || normalizedName.includes('biologi') ||
                    normalizedName.includes('fizik') || normalizedName.includes('matematik') ||
                    normalizedName.includes('chemistry') || normalizedName.includes('biology') ||
                    normalizedName.includes('physics') || normalizedName.includes('math'))) {
                continue
            }

            if (keyword === 'bahasa' &&
                (normalizedName.includes('inggeris') || normalizedName.includes('arab') ||
                    normalizedName.includes('melayu') || normalizedName.includes('cina') ||
                    normalizedName.includes('tamil') || normalizedName.includes('kadazandusun') ||
                    normalizedName.includes('semai') || normalizedName.includes('iban') ||
                    normalizedName.includes('english') || normalizedName.includes('arabic') ||
                    normalizedName.includes('chinese') || normalizedName.includes('mandarin'))) {
                continue
            }

            if (keyword === 'seni' &&
                (normalizedName.includes('visual') || normalizedName.includes('muzik') ||
                    normalizedName.includes('music'))) {
                continue
            }

            if (keyword === 'pendidikan' &&
                (normalizedName.includes('jasmani') || normalizedName.includes('islam') ||
                    normalizedName.includes('moral') || normalizedName.includes('seni') ||
                    normalizedName.includes('muzik') || normalizedName.includes('physical') ||
                    normalizedName.includes('music') || normalizedName.includes('visual'))) {
                continue
            }

            if (SUBJECT_COLORS[colorKey]) {
                matchedKeywords.push(keyword)
                resolutionMethod = 'partial_match'
                return {
                    color: SUBJECT_COLORS[colorKey],
                    isPredefined: true,
                    colorKey,
                    matchedKeywords,
                    debugInfo: { normalizedName, searchAttempts, resolutionMethod }
                }
            }
        }
    }

    // PHASE 3: HASH-GENERATED COLOR
    resolutionMethod = 'hash_generated'
    let hash = 0
    for (let i = 0; i < subjectId.length; i++) {
        const char = subjectId.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash
    }

    const colorIndex = Math.abs(hash) % DYNAMIC_COLORS.length
    const dynamicColor = DYNAMIC_COLORS[colorIndex]

    return {
        color: {
            subject_id: subjectId,
            subject_name: subjectName,
            color: dynamicColor.color,
            bg_color: dynamicColor.bg_color,
            text_color: dynamicColor.text_color
        },
        isPredefined: false,
        matchedKeywords: [],
        debugInfo: { normalizedName, searchAttempts, resolutionMethod }
    }
}

// Methods
const refreshData = async () => {
    await fetchSubjects()
    testAllMappings()
}

// Test all subjects and generate results
const testAllMappings = () => {
    // Start with real subjects
    const realSubjectResults = subjects.value.map(subject => {
        const colorResult = getSubjectColorWithDebug(subject.id, subject.name)
        const issues: string[] = []

        // Check for issues
        if (!colorResult.color.bg_color) {
            issues.push('Missing background color')
        }
        if (!colorResult.color.text_color) {
            issues.push('Missing text color')
        }
        if (!colorResult.color.color) {
            issues.push('Missing border color')
        }
        if (colorResult.color.bg_color && colorResult.color.bg_color.includes('undefined')) {
            issues.push('Invalid background color class')
        }
        if (!colorResult.isPredefined) {
            issues.push('Using fallback hash-generated color')
        }

        return {
            subject: {
                id: subject.id,
                name: subject.name,
                code: subject.code
            },
            colorResult,
            issues,
            debugInfo: colorResult.debugInfo
        }
    })

    // Add example non-existing subjects to demonstrate fallback colors
    const exampleNonExistingSubjects = [
        { id: 'example-1', name: 'Robotik dan AI', code: 'RAI' },
        { id: 'example-2', name: 'Pengaturcaraan Komputer', code: 'PK' },
        { id: 'example-3', name: 'Sains Data', code: 'SD' },
        { id: 'example-4', name: 'Kejuruteraan Perisian', code: 'KP' },
        { id: 'example-5', name: 'Keselamatan Siber', code: 'KS' },
        { id: 'example-6', name: 'Bahasa Jepun', code: 'BJ' },
        { id: 'example-7', name: 'Psikologi Pendidikan', code: 'PP' },
        { id: 'example-8', name: 'Astronomi', code: 'AST' }
    ]

    const exampleResults = exampleNonExistingSubjects.map(subject => {
        const colorResult = getSubjectColorWithDebug(subject.id, subject.name)
        const issues: string[] = []

        // Check for issues
        if (!colorResult.color.bg_color) {
            issues.push('Missing background color')
        }
        if (!colorResult.color.text_color) {
            issues.push('Missing text color')
        }
        if (!colorResult.color.color) {
            issues.push('Missing border color')
        }
        if (colorResult.color.bg_color && colorResult.color.bg_color.includes('undefined')) {
            issues.push('Invalid background color class')
        }
        if (!colorResult.isPredefined) {
            issues.push('Using fallback hash-generated color (EXAMPLE)')
        }

        return {
            subject: {
                id: subject.id,
                name: `${subject.name} (EXAMPLE)`,
                code: subject.code
            },
            colorResult,
            issues,
            debugInfo: colorResult.debugInfo
        }
    })

    // Combine real subjects with examples
    testResults.value = [...realSubjectResults, ...exampleResults]
}

// Computed properties
const mappedCount = computed(() =>
    testResults.value.filter(r => r.colorResult.isPredefined).length
)

const fallbackCount = computed(() =>
    testResults.value.filter(r => !r.colorResult.isPredefined).length
)

const issuesCount = computed(() =>
    testResults.value.filter(r => r.issues.length > 0).length
)

const filteredResults = computed(() => {
    let filtered = testResults.value

    // Apply search filter
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(r =>
            r.subject.name.toLowerCase().includes(query) ||
            r.subject.code.toLowerCase().includes(query)
        )
    }

    // Apply type filter
    switch (filterType.value) {
        case 'mapped':
            filtered = filtered.filter(r => r.colorResult.isPredefined)
            break
        case 'fallback':
            filtered = filtered.filter(r => !r.colorResult.isPredefined)
            break
        case 'issues':
            filtered = filtered.filter(r => r.issues.length > 0)
            break
        case 'problematic':
            // Show subjects that are using fallback colors or have issues
            filtered = filtered.filter(r => !r.colorResult.isPredefined || r.issues.length > 0)
            break
    }

    return filtered
})

// Lifecycle
onMounted(async () => {
    await fetchSubjects()
    testAllMappings()
})
</script>