# Textarea Component

A flexible textarea component with two variants (floating label and normal) that matches the styling and behavior of the Input component.

## Features

- **Two Variants**: Floating label and normal variants
- **Auto-resize**: Optional auto-resize functionality that grows the textarea as content increases
- **Resize Control**: Configurable resize behavior (none, vertical, horizontal, both)
- **Error Handling**: Built-in error message display
- **Accessibility**: Proper ARIA attributes and labels
- **Responsive**: Responsive placeholder text sizing
- **Dark Mode**: Full dark mode support

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string \| null` | `undefined` | The v-model value |
| `placeholder` | `string` | `''` | Placeholder text |
| `disabled` | `boolean` | `false` | Whether the textarea is disabled |
| `required` | `boolean` | `false` | Whether the textarea is required |
| `ariaDescribedby` | `string` | `undefined` | ARIA describedby attribute |
| `variant` | `'floating' \| 'normal'` | `'floating'` | The variant style |
| `label` | `string` | `undefined` | Label text (used for normal variant) |
| `rows` | `number` | `3` | Number of rows |
| `resize` | `'none' \| 'vertical' \| 'horizontal' \| 'both'` | `'none'` | Resize behavior |
| `error` | `string` | `undefined` | Error message to display |
| `autoResize` | `boolean` | `false` | Enable auto-resize functionality |

## Usage Examples

### Floating Label Variant

```vue
<template>
  <!-- Basic floating label -->
  <UiBaseTextarea 
    v-model="description" 
    variant="floating" 
    placeholder="Enter your description..."
    :rows="3"
  />

  <!-- Floating with auto-resize -->
  <UiBaseTextarea 
    v-model="comments" 
    variant="floating" 
    placeholder="Comments will expand as you type..."
    :rows="3"
    auto-resize
  />

  <!-- Floating with error -->
  <UiBaseTextarea 
    v-model="requiredField" 
    variant="floating" 
    placeholder="This field is required..."
    :rows="3"
    :error="requiredField.length === 0 ? 'This field is required' : ''"
    required
  />
</template>
```

### Normal Variant

```vue
<template>
  <!-- Basic normal variant -->
  <UiBaseTextarea 
    v-model="description" 
    variant="normal" 
    label="Description"
    placeholder="Enter your description..."
    :rows="3"
  />

  <!-- Normal with vertical resize -->
  <UiBaseTextarea 
    v-model="notes" 
    variant="normal" 
    label="Notes"
    placeholder="Add your notes here..."
    :rows="4"
    resize="vertical"
  />

  <!-- Normal with error -->
  <UiBaseTextarea 
    v-model="feedback" 
    variant="normal" 
    label="Feedback"
    placeholder="Please provide your feedback..."
    :rows="3"
    :error="errors.feedback"
    required
  />
</template>
```

### Advanced Usage

```vue
<template>
  <!-- Auto-resize with minimum height -->
  <UiBaseTextarea 
    v-model="content" 
    variant="floating" 
    placeholder="Start typing and watch me grow..."
    :rows="2"
    auto-resize
    resize="none"
  />

  <!-- Disabled state -->
  <UiBaseTextarea 
    v-model="readOnlyContent" 
    variant="normal" 
    label="Read Only"
    placeholder="This content cannot be edited"
    :rows="3"
    disabled
  />

  <!-- With both resize directions -->
  <UiBaseTextarea 
    v-model="flexibleContent" 
    variant="normal" 
    label="Flexible Size"
    placeholder="Resize me in any direction..."
    :rows="4"
    resize="both"
  />
</template>
```

## Styling

The component uses the same `.form-input` CSS class as the Input component for consistent styling. It supports:

- Dark mode through CSS variables
- Responsive placeholder text sizing
- Focus states with ring effects
- Disabled states with appropriate opacity
- Error states with red border and text

## Auto-resize Behavior

When `autoResize` is enabled:
- The textarea automatically adjusts its height based on content
- Maintains a minimum height based on the `rows` prop
- Disables vertical scrolling (`overflow-y: hidden`)
- Recalculates height on content changes

## Error Handling

Error messages are displayed below the textarea when the `error` prop is provided:
- Red text color for visibility
- Proper spacing and typography
- Accessible through ARIA attributes

## Integration

The component is designed to work seamlessly with:
- Vue 3 Composition API
- Nuxt 3 auto-imports
- Tailwind CSS
- Dark mode toggle
- Form validation libraries

## Browser Support

Supports all modern browsers with proper fallbacks for older browsers.
