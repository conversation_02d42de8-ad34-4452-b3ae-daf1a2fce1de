-- Data Archival Strategy for eRPH System
-- Manages data lifecycle and maintains optimal performance

-- =====================================================
-- 1. ARCHIVE TABLES STRUCTURE
-- =====================================================

-- Create archive schema
CREATE SCHEMA IF NOT EXISTS archive;

-- Archive table for old lesson plans (2+ years old)
CREATE TABLE archive.lesson_plans_archive (
    LIKE lesson_plans INCLUDING ALL
);

-- Archive table for old reflections
CREATE TABLE archive.lesson_plan_reflections_archive (
    LIKE lesson_plan_reflections INCLUDING ALL
);

-- Archive table for old detailed reflections
CREATE TABLE archive.lesson_plan_detailed_reflections_archive (
    LIKE lesson_plan_detailed_reflections INCLUDING ALL
);

-- Archive table for old teacher schedules
CREATE TABLE archive.teacher_schedules_archive (
    LIKE teacher_schedules INCLUDING ALL
);

-- =====================================================
-- 2. ARCHIVAL FUNCTIONS
-- =====================================================

-- Function to archive old lesson plans and related data
CREATE OR REPLACE FUNCTION archive_old_data(cutoff_date DATE DEFAULT CURRENT_DATE - INTERVAL '2 years')
RETURNS TABLE(
    archived_lesson_plans INTEGER,
    archived_reflections INTEGER,
    archived_detailed_reflections INTEGER,
    archived_teacher_schedules INTEGER,
    freed_storage_mb NUMERIC
) AS $$
DECLARE
    lesson_plan_count INTEGER := 0;
    reflection_count INTEGER := 0;
    detailed_reflection_count INTEGER := 0;
    teacher_schedule_count INTEGER := 0;
    storage_freed NUMERIC := 0;
BEGIN
    -- Calculate storage before archival
    SELECT pg_total_relation_size('lesson_plans') + 
           pg_total_relation_size('lesson_plan_reflections') + 
           pg_total_relation_size('lesson_plan_detailed_reflections') + 
           pg_total_relation_size('teacher_schedules')
    INTO storage_freed;
    
    -- Archive lesson plans
    WITH archived_plans AS (
        INSERT INTO archive.lesson_plans_archive
        SELECT * FROM lesson_plans 
        WHERE created_at < cutoff_date
        RETURNING id
    )
    SELECT COUNT(*) INTO lesson_plan_count FROM archived_plans;
    
    -- Archive reflections
    WITH archived_reflections AS (
        INSERT INTO archive.lesson_plan_reflections_archive
        SELECT r.* FROM lesson_plan_reflections r
        JOIN archive.lesson_plans_archive a ON r.lesson_plan_id = a.id
        RETURNING id
    )
    SELECT COUNT(*) INTO reflection_count FROM archived_reflections;
    
    -- Archive detailed reflections
    WITH archived_detailed AS (
        INSERT INTO archive.lesson_plan_detailed_reflections_archive
        SELECT d.* FROM lesson_plan_detailed_reflections d
        JOIN archive.lesson_plans_archive a ON d.lesson_plan_id = a.id
        RETURNING id
    )
    SELECT COUNT(*) INTO detailed_reflection_count FROM archived_detailed;
    
    -- Archive teacher schedules
    WITH archived_schedules AS (
        INSERT INTO archive.teacher_schedules_archive
        SELECT s.* FROM teacher_schedules s
        JOIN archive.lesson_plans_archive a ON s.lesson_plan_id = a.id
        RETURNING id
    )
    SELECT COUNT(*) INTO teacher_schedule_count FROM archived_schedules;
    
    -- Delete from main tables (cascading will handle related records)
    DELETE FROM lesson_plans WHERE created_at < cutoff_date;
    
    -- Calculate freed storage
    storage_freed := storage_freed - (
        pg_total_relation_size('lesson_plans') + 
        pg_total_relation_size('lesson_plan_reflections') + 
        pg_total_relation_size('lesson_plan_detailed_reflections') + 
        pg_total_relation_size('teacher_schedules')
    );
    
    storage_freed := storage_freed / 1024 / 1024; -- Convert to MB
    
    RETURN QUERY SELECT 
        lesson_plan_count,
        reflection_count,
        detailed_reflection_count,
        teacher_schedule_count,
        storage_freed;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. PARTITION DROPPING STRATEGY
-- =====================================================

-- Function to drop old partitions (for partitioned tables)
CREATE OR REPLACE FUNCTION drop_old_partitions(months_to_keep INTEGER DEFAULT 24)
RETURNS TABLE(dropped_partition TEXT) AS $$
DECLARE
    partition_name TEXT;
    cutoff_date DATE;
BEGIN
    cutoff_date := CURRENT_DATE - (months_to_keep || ' months')::INTERVAL;
    
    -- Find and drop old lesson plan partitions
    FOR partition_name IN
        SELECT schemaname||'.'||tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'lesson_plans_%'
        AND tablename ~ '\d{4}_\d{2}$'
        AND to_date(substring(tablename from '\d{4}_\d{2}$'), 'YYYY_MM') < cutoff_date
    LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || partition_name || ' CASCADE';
        RETURN QUERY SELECT partition_name;
    END LOOP;
    
    -- Drop old reflection partitions
    FOR partition_name IN
        SELECT schemaname||'.'||tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'lesson_plan_reflections_%'
        AND tablename ~ '\d{4}_\d{2}$'
        AND to_date(substring(tablename from '\d{4}_\d{2}$'), 'YYYY_MM') < cutoff_date
    LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || partition_name || ' CASCADE';
        RETURN QUERY SELECT partition_name;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. FILE STORAGE CLEANUP
-- =====================================================

-- Function to identify orphaned files for cleanup
CREATE OR REPLACE FUNCTION find_orphaned_files()
RETURNS TABLE(
    storage_file_path TEXT,
    file_size_bytes BIGINT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lp.storage_file_path,
        lp.file_size_bytes,
        lp.created_at
    FROM lesson_plans lp
    WHERE lp.storage_file_path IS NOT NULL
    AND lp.created_at < CURRENT_DATE - INTERVAL '2 years'
    
    UNION ALL
    
    SELECT 
        alp.storage_file_path,
        alp.file_size_bytes,
        alp.created_at
    FROM archive.lesson_plans_archive alp
    WHERE alp.storage_file_path IS NOT NULL
    AND alp.created_at < CURRENT_DATE - INTERVAL '5 years';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. AUTOMATED MAINTENANCE SCHEDULE
-- =====================================================

-- Monthly archival (run on 1st of each month at 2 AM)
SELECT cron.schedule(
    'monthly-archival',
    '0 2 1 * *',
    'SELECT archive_old_data();'
);

-- Quarterly partition cleanup (run every 3 months)
SELECT cron.schedule(
    'quarterly-partition-cleanup',
    '0 3 1 */3 *',
    'SELECT drop_old_partitions(24);'
);

-- Weekly vacuum and analyze for archive tables
SELECT cron.schedule(
    'archive-maintenance',
    '0 4 * * 0',
    'VACUUM ANALYZE archive.lesson_plans_archive, archive.lesson_plan_reflections_archive;'
);

-- =====================================================
-- 6. MONITORING AND REPORTING
-- =====================================================

-- View for archival statistics
CREATE OR REPLACE VIEW archival_stats AS
SELECT 
    'lesson_plans' as table_name,
    COUNT(*) as active_records,
    (SELECT COUNT(*) FROM archive.lesson_plans_archive) as archived_records,
    pg_size_pretty(pg_total_relation_size('lesson_plans')) as active_size,
    pg_size_pretty(pg_total_relation_size('archive.lesson_plans_archive')) as archived_size
FROM lesson_plans
UNION ALL
SELECT 
    'lesson_plan_reflections',
    COUNT(*),
    (SELECT COUNT(*) FROM archive.lesson_plan_reflections_archive),
    pg_size_pretty(pg_total_relation_size('lesson_plan_reflections')),
    pg_size_pretty(pg_total_relation_size('archive.lesson_plan_reflections_archive'))
FROM lesson_plan_reflections;

-- Function to generate archival report
CREATE OR REPLACE FUNCTION generate_archival_report()
RETURNS TABLE(
    report_date DATE,
    total_active_records BIGINT,
    total_archived_records BIGINT,
    active_storage_size TEXT,
    archived_storage_size TEXT,
    archival_ratio NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CURRENT_DATE,
        (SELECT SUM(active_records) FROM archival_stats),
        (SELECT SUM(archived_records) FROM archival_stats),
        (SELECT pg_size_pretty(SUM(pg_total_relation_size(table_name::regclass))) 
         FROM (VALUES ('lesson_plans'), ('lesson_plan_reflections'), 
                      ('lesson_plan_detailed_reflections'), ('teacher_schedules')) AS t(table_name)),
        (SELECT pg_size_pretty(SUM(pg_total_relation_size(('archive.' || table_name)::regclass))) 
         FROM (VALUES ('lesson_plans_archive'), ('lesson_plan_reflections_archive'), 
                      ('lesson_plan_detailed_reflections_archive'), ('teacher_schedules_archive')) AS t(table_name)),
        ROUND(
            (SELECT SUM(archived_records)::NUMERIC FROM archival_stats) / 
            NULLIF((SELECT SUM(active_records + archived_records) FROM archival_stats), 0) * 100, 
            2
        );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. EMERGENCY RECOVERY PROCEDURES
-- =====================================================

-- Function to restore archived data if needed
CREATE OR REPLACE FUNCTION restore_archived_data(
    start_date DATE,
    end_date DATE
) RETURNS INTEGER AS $$
DECLARE
    restored_count INTEGER := 0;
BEGIN
    -- Restore lesson plans
    WITH restored AS (
        INSERT INTO lesson_plans
        SELECT * FROM archive.lesson_plans_archive
        WHERE created_at BETWEEN start_date AND end_date
        RETURNING id
    )
    SELECT COUNT(*) INTO restored_count FROM restored;
    
    -- Restore related data (reflections will be restored via foreign keys)
    INSERT INTO lesson_plan_reflections
    SELECT r.* FROM archive.lesson_plan_reflections_archive r
    JOIN lesson_plans lp ON r.lesson_plan_id = lp.id
    WHERE lp.created_at BETWEEN start_date AND end_date;
    
    RETURN restored_count;
END;
$$ LANGUAGE plpgsql;
