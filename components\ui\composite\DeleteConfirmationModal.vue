<template>
    <Modal :is-open="isOpen" :title="computedTitle" :z-index="zIndex" @update:is-open="handleModalClose">
        <div class="space-y-4">
            <!-- Alert Icon and Main Message -->
            <div class="flex items-start space-x-3">
                <Icon :name="alertIcon" :class="alertIconClass" />
                <div class="min-w-0 flex-1">
                    <!-- Primary confirmation message -->
                    <p class="text-gray-900 dark:text-white font-medium mb-2">
                        {{ confirmationMessage }}
                    </p>

                    <!-- Warning message -->
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {{ warningMessage }}
                    </p>
                </div>
            </div>

            <!-- Item Details (optional) -->
            <div v-if="showItemDetails" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {{ itemDetailsLabel }}:
                </p>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <slot name="item-details">
                        <!-- Single item -->
                        <div v-if="!itemCount || itemCount <= 1">
                            <p>{{ itemName }}</p>
                            <p v-if="itemSubtitle" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ itemSubtitle }}
                            </p>
                        </div>

                        <!-- Bulk items -->
                        <div v-else class="space-y-2">
                            <p class="font-medium">{{ itemCount }} {{ itemType }} dipilih:</p>
                            <div class="max-h-32 overflow-y-auto space-y-1">
                                <div v-for="item in itemList" :key="item" class="text-xs">
                                    <span class="font-medium">{{ item }}</span>
                                </div>
                            </div>
                        </div>
                    </slot>
                </div>
            </div>

            <!-- Additional Content (slot for custom content) -->
            <div v-if="$slots.content" class="space-y-3">
                <slot name="content"></slot>
            </div>

            <!-- Impact Warning (optional) -->
            <div v-if="impactMessage || $slots.impact" :class="[
                'flex items-start space-x-2 p-3 rounded-lg border',
                impactSeverity === 'high' ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800' :
                    impactSeverity === 'medium' ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800' :
                        'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
            ]">
                <Icon :name="impactIcon" :class="[
                    'h-4 w-4 mt-0.5',
                    impactSeverity === 'high' ? 'text-red-500' :
                        impactSeverity === 'medium' ? 'text-yellow-500' :
                            'text-blue-500'
                ]" />
                <div :class="[
                    'text-sm',
                    impactSeverity === 'high' ? 'text-red-800 dark:text-red-200' :
                        impactSeverity === 'medium' ? 'text-yellow-800 dark:text-yellow-200' :
                            'text-blue-800 dark:text-blue-200'
                ]">
                    <slot name="impact">
                        <div v-html="impactMessage"></div>
                    </slot>
                </div>
            </div>

            <!-- Error Display (optional) -->
            <div v-if="errorMessage && !loading"
                class="flex items-start space-x-2 p-3 rounded-lg border bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800">
                <Icon name="mdi:alert-circle" class="h-4 w-4 text-red-500 mt-0.5" />
                <div class="text-sm text-red-800 dark:text-red-200">
                    <div class="font-medium">Ralat</div>
                    <div>{{ errorMessage }}</div>
                </div>
            </div>

            <!-- Loading Display -->
            <div v-if="loading" class="flex items-center justify-center py-4">
                <div class="flex items-center space-x-3">
                    <Icon name="mdi:loading" class="h-5 w-5 animate-spin text-red-600" />
                    <span class="text-sm text-gray-600 dark:text-gray-400">{{ loadingText }}</span>
                </div>
            </div>
        </div>

        <template #footer>
            <div
                class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                <Button variant="outline" @click="handleCancel" :disabled="loading">
                    {{ cancelText }}
                </Button>
                <Button :variant="dangerLevel === 'high' ? 'delete' : 'primary'" @click="handleConfirm"
                    :disabled="loading" :prepend-icon="confirmIcon">
                    <Icon v-if="loading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                    {{ loading ? loadingText : (itemCount > 1 ? `${confirmText} ${itemCount} ${itemType}` : confirmText)
                    }}
                </Button>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Modal from './Modal.vue'
import Button from '../base/Button.vue'
import Icon from '../base/Icon.vue'

interface Props {
    // Core modal props
    isOpen: boolean
    title?: string
    zIndex?: number
    loading?: boolean

    // Content customization
    itemType?: string // e.g., 'Rancangan Pengajaran', 'Kelas', 'Minggu'
    itemName?: string // The name/identifier of the item being deleted
    itemSubtitle?: string // Additional info (e.g., date, location)

    // For bulk operations
    itemCount?: number // Number of items being deleted (for bulk operations)
    itemList?: string[] // List of item names for bulk operations

    // Confirmation message (auto-generated if not provided)
    confirmationMessage?: string
    warningMessage?: string

    // Button customization
    confirmText?: string
    cancelText?: string
    loadingText?: string
    confirmIcon?: string

    // Styling and behavior
    dangerLevel?: 'low' | 'medium' | 'high' // Controls button color and impact styling
    alertIcon?: string

    // Item details section
    showItemDetails?: boolean
    itemDetailsLabel?: string

    // Impact/consequence messaging
    impactMessage?: string
    impactSeverity?: 'low' | 'medium' | 'high'
    impactIcon?: string

    // Error handling
    errorMessage?: string
}

interface Emits {
    (e: 'update:is-open', value: boolean): void
    (e: 'confirm'): void
    (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
    title: '',
    zIndex: 50,
    loading: false,
    itemType: 'item',
    itemName: '',
    itemSubtitle: '',
    itemCount: 0,
    itemList: () => [],
    confirmationMessage: '',
    warningMessage: 'Tindakan ini tidak boleh dibatalkan.',
    confirmText: 'Padam',
    cancelText: 'Batal',
    loadingText: 'Memadam...',
    confirmIcon: 'mdi:delete',
    dangerLevel: 'high',
    alertIcon: 'mdi:alert-circle',
    showItemDetails: true,
    itemDetailsLabel: 'Item yang akan dipadam',
    impactMessage: '',
    impactSeverity: 'medium',
    impactIcon: 'mdi:information',
    errorMessage: ''
})

const emit = defineEmits<Emits>()

// Computed properties
const computedTitle = computed(() => {
    if (props.title) return props.title

    if (props.itemCount > 1) {
        return `Padam ${props.itemCount} ${props.itemType}`
    }

    return `Padam ${props.itemType}`
})

const alertIconClass = computed(() => {
    const baseClasses = 'w-6 h-6 flex-shrink-0 mt-0.5'
    const colorClass = props.dangerLevel === 'high' ? 'text-red-500' :
        props.dangerLevel === 'medium' ? 'text-yellow-500' : 'text-blue-500'
    return `${baseClasses} ${colorClass}`
})

const confirmationMessage = computed(() => {
    if (props.confirmationMessage) return props.confirmationMessage

    // Handle bulk operations
    if (props.itemCount > 1) {
        return `Adakah anda pasti untuk memadam ${props.itemCount} ${props.itemType} yang dipilih?`
    }

    // Handle single item
    const itemName = props.itemName ? `"${props.itemName}"` : 'item ini'
    return `Adakah anda pasti untuk memadam ${itemName}?`
})

// Event handlers
const handleConfirm = () => {
    emit('confirm')
}

const handleCancel = () => {
    emit('cancel')
    emit('update:is-open', false)
}

const handleModalClose = (value: boolean) => {
    if (!value) {
        // Modal is being closed (X button clicked)
        emit('cancel')
    }
    emit('update:is-open', value)
}
</script>
