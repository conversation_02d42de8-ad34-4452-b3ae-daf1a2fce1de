<template>
  <div class="flex items-center justify-between">
    <div class="flex items-center space-x-2">
      <UiBaseIcon name="heroicons:calendar-days-solid" class="w-5 h-5 text-primary" />
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON><PERSON></h2>
    </div>
    <div class="flex items-center space-x-2">
      <UiBaseButton @click="$emit('openAddWeekModal')" variant="primary" size="sm" prepend-icon="heroicons:plus-solid"
        class="hidden sm:flex">
        <PERSON><PERSON>gu
      </UiBaseButton>
      <UiBaseButton @click="$emit('openManageModal')" variant="outline" size="sm"
        prepend-icon="heroicons:cog-6-tooth-solid" class="hidden sm:flex">
        Urus
      </UiBaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import UiBaseButton from '~/components/ui/base/Button.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'

defineEmits<{
  openAddWeekModal: []
  openManageModal: []
}>()
</script>
