// Server-side plugin to ensure environment variables are loaded
import dotenv from 'dotenv'
import { existsSync } from 'fs'

export default defineNuxtPlugin(() => {
  // Load environment variables from .env.local if it exists
  if (existsSync('.env.local')) {
    dotenv.config({ path: '.env.local' })
  }

  // Also try .env
  if (existsSync('.env')) {
    dotenv.config({ path: '.env' })
  }

  // Only log in development mode and only if there are missing keys
  if (process.env.NODE_ENV === 'development') {
    const missingKeys = []
    if (!process.env.STRIPE_SECRET_KEY) missingKeys.push('STRIPE_SECRET_KEY')
    if (!process.env.STRIPE_WEBHOOK_SECRET) missingKeys.push('STRIPE_WEBHOOK_SECRET')
    if (!process.env.SUPABASE_URL) missingKeys.push('SUPABASE_URL')

    if (missingKeys.length > 0) {
      console.warn('⚠️ Missing environment variables:', missingKeys)
    }
  }
})
