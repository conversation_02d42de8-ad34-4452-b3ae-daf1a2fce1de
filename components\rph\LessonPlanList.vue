<template>

    <div v-if="isLoadingLessonPlans && lessonPlans.length === 0" class="text-center text-gray-500 py-8">
        <p class="text-xl">Memuatkan rancangan pengajaran...</p>
    </div>

    <UiCompositeCard class="overflow-hidden">
        <template #header>
            <div class="flex items-center space-x-2">
                <UiBaseIcon name="heroicons:document-plus-solid" class="w-5 h-5 text-primary" />
                <h2 v-if="currentSelectedWeek" class="text-xl font-semibold text-gray-900 dark:text-white">RPH
                    Terdahulu untuk
                    {{ currentSelectedWeek?.name || 'Minggu Dipilih' }}</h2>
            </div>
        </template>

        <div v-if="!isLoadingLessonPlans && lessonPlans.length === 0 && currentSelectedWeek"
            class="text-center text-gray-500 py-8">
            <p class="text-lg">Belum ada rancangan pengajaran dimuat naik untuk {{ currentSelectedWeek.name }}.
            </p>
            <p>Gunakan borang di atas untuk memuat naik rancangan pengajaran pertama anda untuk minggu ini.</p>
        </div>

        <LessonPlanCard v-else-if="lessonPlans.length > 0" v-for="plan in lessonPlans" :key="plan.id" :plan="plan"
            :user-profile-class-subjects="userClassSubjects" :weekLabel="currentSelectedWeek?.name || ''"
            @edit="handleEdit" @delete="handleDelete" @preview-office="handlePreviewOffice"
            @preview-image="handlePreviewImage" @download-plan="handleDownload" @add-reflection="handleAddReflection"
            @edit-reflection="handleEditReflection" />
    </UiCompositeCard>


    <div v-if="!currentSelectedWeek && !isLoadingRphWeeks && !isLoadingLessonPlans"
        class="text-center text-gray-500 py-8">
        <p class="text-xl">Sila pilih minggu untuk memuat naik RPH, melihat senarai RPH, dan status penyerahan.</p>
    </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import type { LessonPlan } from '~/types/lessonPlans';
import type { RphWeek } from '~/types/rph';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import LessonPlanCard from '~/components/rph/LessonPlanCard.vue';

const props = defineProps({
    lessonPlans: {
        type: Array as PropType<LessonPlan[]>,
        required: true,
    },
    currentSelectedWeek: {
        type: Object as PropType<RphWeek | null>,
        default: null,
    },
    userClassSubjects: {
        type: Array as PropType<UserClassSubjectEntry[]>,
        required: true,
    },
    isLoadingLessonPlans: {
        type: Boolean,
        default: false,
    },
    isLoadingRphWeeks: {
        type: Boolean,
        default: false,
    }
});

const emit = defineEmits([
    'edit-plan',
    'delete-plan',
    'preview-office-plan',
    'preview-image-plan',
    'download-plan',
    'add-reflection',
    'edit-reflection'
]);

const handleEdit = (plan: LessonPlan) => {
    emit('edit-plan', plan);
};

const handleDelete = (plan: LessonPlan) => {
    emit('delete-plan', plan);
};

const handlePreviewOffice = (plan: LessonPlan) => {
    emit('preview-office-plan', plan);
};

const handlePreviewImage = (plan: LessonPlan) => {
    emit('preview-image-plan', plan);
};

const handleDownload = (plan: LessonPlan) => {
    emit('download-plan', plan);
};

const handleAddReflection = (plan: LessonPlan) => {
    emit('add-reflection', plan);
};

const handleEditReflection = (plan: LessonPlan) => {
    emit('edit-reflection', plan);
};
</script>

<style scoped>
/* Add any specific styles for LessonPlanList.vue if needed */
</style>
