import { ref, computed } from 'vue';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import type { Database } from '~/types/supabase';

// Template preferences interface
export interface TemplatePreferences {
  default_template_id: string | null;
  auto_apply_default: boolean;
  show_template_suggestions: boolean;
  preferred_categories: string[];
  template_sort_order: 'recent' | 'alphabetical' | 'usage' | 'category';
  show_system_templates: boolean;
  show_usage_stats: boolean;
  auto_save_custom_templates: boolean;
  template_preview_mode: 'simple' | 'detailed';
}

// Default preferences
const DEFAULT_PREFERENCES: TemplatePreferences = {
  default_template_id: null,
  auto_apply_default: false,
  show_template_suggestions: true,
  preferred_categories: [],
  template_sort_order: 'recent',
  show_system_templates: true,
  show_usage_stats: true,
  auto_save_custom_templates: true,
  template_preview_mode: 'detailed'
};

// Global state
const preferences = ref<TemplatePreferences>({ ...DEFAULT_PREFERENCES });
const loading = ref(false);
const error = ref<string | null>(null);

export const useTemplatePreferences = () => {
  const supabase = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  // =====================================================
  // FETCH OPERATIONS
  // =====================================================

  const fetchPreferences = async (): Promise<TemplatePreferences> => {
    if (!user.value) {
      preferences.value = { ...DEFAULT_PREFERENCES };
      return preferences.value;
    }

    try {
      loading.value = true;
      error.value = null;

      const { data, error: fetchError } = await (supabase as any)
        .from('user_preferences')
        .select('template_preferences')
        .eq('user_id', user.value.id)
        .maybeSingle();

      if (fetchError) throw fetchError;

      if (data?.template_preferences) {
        // Merge with defaults to ensure all keys exist
        preferences.value = {
          ...DEFAULT_PREFERENCES,
          ...(data.template_preferences as Partial<TemplatePreferences>)
        };
      } else {
        preferences.value = { ...DEFAULT_PREFERENCES };
      }

      return preferences.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch template preferences';
      console.error('Error fetching template preferences:', err);
      preferences.value = { ...DEFAULT_PREFERENCES };
      return preferences.value;
    } finally {
      loading.value = false;
    }
  };

  // =====================================================
  // UPDATE OPERATIONS
  // =====================================================

  const updatePreferences = async (
    updates: Partial<TemplatePreferences>
  ): Promise<TemplatePreferences> => {
    if (!user.value) throw new Error('User not authenticated');

    try {
      loading.value = true;
      error.value = null;

      // Merge updates with current preferences
      const updatedPreferences = {
        ...preferences.value,
        ...updates
      };

      // First, try to get existing user preferences
      const { data: existingData } = await (supabase as any)
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.value.id)
        .maybeSingle();

      let result;
      if (existingData) {
        // Update existing record
        const { data, error: updateError } = await (supabase as any)
          .from('user_preferences')
          .update({
            template_preferences: updatedPreferences
          })
          .eq('user_id', user.value.id)
          .select('template_preferences')
          .single();

        if (updateError) throw updateError;
        result = data;
      } else {
        // Insert new record
        const { data, error: insertError } = await (supabase as any)
          .from('user_preferences')
          .insert({
            user_id: user.value.id,
            template_preferences: updatedPreferences
          })
          .select('template_preferences')
          .single();

        if (insertError) throw insertError;
        result = data;
      }

      if (result?.template_preferences) {
        preferences.value = {
          ...DEFAULT_PREFERENCES,
          ...(result.template_preferences as Partial<TemplatePreferences>)
        };
      }

      return preferences.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update template preferences';
      console.error('Error updating template preferences:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // =====================================================
  // SPECIFIC PREFERENCE UPDATES
  // =====================================================

  const setDefaultTemplate = async (templateId: string | null): Promise<void> => {
    await updatePreferences({ default_template_id: templateId });
  };

  const toggleAutoApplyDefault = async (): Promise<void> => {
    await updatePreferences({ auto_apply_default: !preferences.value.auto_apply_default });
  };

  const updateSortOrder = async (sortOrder: TemplatePreferences['template_sort_order']): Promise<void> => {
    await updatePreferences({ template_sort_order: sortOrder });
  };

  const updatePreferredCategories = async (categories: string[]): Promise<void> => {
    await updatePreferences({ preferred_categories: categories });
  };

  const toggleSystemTemplates = async (): Promise<void> => {
    await updatePreferences({ show_system_templates: !preferences.value.show_system_templates });
  };

  const toggleUsageStats = async (): Promise<void> => {
    await updatePreferences({ show_usage_stats: !preferences.value.show_usage_stats });
  };

  const toggleTemplateSuggestions = async (): Promise<void> => {
    await updatePreferences({ show_template_suggestions: !preferences.value.show_template_suggestions });
  };

  const updatePreviewMode = async (mode: TemplatePreferences['template_preview_mode']): Promise<void> => {
    await updatePreferences({ template_preview_mode: mode });
  };

  const toggleAutoSaveCustomTemplates = async (): Promise<void> => {
    await updatePreferences({ auto_save_custom_templates: !preferences.value.auto_save_custom_templates });
  };

  // =====================================================
  // RESET OPERATIONS
  // =====================================================

  const resetToDefaults = async (): Promise<void> => {
    await updatePreferences(DEFAULT_PREFERENCES);
  };

  // =====================================================
  // COMPUTED PROPERTIES
  // =====================================================

  const hasDefaultTemplate = computed(() => !!preferences.value.default_template_id);

  const shouldShowSystemTemplates = computed(() => preferences.value.show_system_templates);

  const shouldShowUsageStats = computed(() => preferences.value.show_usage_stats);

  const shouldShowSuggestions = computed(() => preferences.value.show_template_suggestions);

  const shouldAutoApplyDefault = computed(() => 
    preferences.value.auto_apply_default && hasDefaultTemplate.value
  );

  const currentSortOrder = computed(() => preferences.value.template_sort_order);

  const currentPreviewMode = computed(() => preferences.value.template_preview_mode);

  const preferredCategories = computed(() => preferences.value.preferred_categories);

  // =====================================================
  // UTILITY FUNCTIONS
  // =====================================================

  const isPreferredCategory = (category: string): boolean => {
    return preferences.value.preferred_categories.includes(category);
  };

  const getSortedTemplates = <T extends { name: string; usage_count?: number; last_used?: string | null; category: string }>(
    templates: T[]
  ): T[] => {
    const sortOrder = preferences.value.template_sort_order;
    
    switch (sortOrder) {
      case 'alphabetical':
        return [...templates].sort((a, b) => a.name.localeCompare(b.name));
      
      case 'usage':
        return [...templates].sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));
      
      case 'category':
        return [...templates].sort((a, b) => {
          if (a.category !== b.category) {
            return a.category.localeCompare(b.category);
          }
          return a.name.localeCompare(b.name);
        });
      
      case 'recent':
      default:
        return [...templates].sort((a, b) => {
          const aTime = a.last_used ? new Date(a.last_used).getTime() : 0;
          const bTime = b.last_used ? new Date(b.last_used).getTime() : 0;
          return bTime - aTime;
        });
    }
  };

  // =====================================================
  // EXPORT
  // =====================================================

  return {
    // State
    preferences: computed(() => preferences.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Computed properties
    hasDefaultTemplate,
    shouldShowSystemTemplates,
    shouldShowUsageStats,
    shouldShowSuggestions,
    shouldAutoApplyDefault,
    currentSortOrder,
    currentPreviewMode,
    preferredCategories,

    // Methods
    fetchPreferences,
    updatePreferences,
    setDefaultTemplate,
    toggleAutoApplyDefault,
    updateSortOrder,
    updatePreferredCategories,
    toggleSystemTemplates,
    toggleUsageStats,
    toggleTemplateSuggestions,
    updatePreviewMode,
    toggleAutoSaveCustomTemplates,
    resetToDefaults,

    // Utilities
    isPreferredCategory,
    getSortedTemplates
  };
};
