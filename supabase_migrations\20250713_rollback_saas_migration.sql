-- Rollback Script for SaaS Multi-tenant Migration
-- Created: 2025-07-13
-- Description: Rollback the complete SaaS transformation to restore single-tenant state
-- 
-- WARNING: This will remove all multi-tenant data and structure!
-- Make sure to backup your data before running this script.

-- =====================================================
-- ROLLBACK CONFIRMATION
-- =====================================================

-- Log rollback start
INSERT INTO migration_log (migration_name, status, notes) 
VALUES ('20250713_rollback_saas_migration', 'started', 'Starting rollback of SaaS transformation');

BEGIN;

-- =====================================================
-- STEP 1: DISABLE RLS ON ALL TABLES
-- =====================================================

-- Disable RLS on all tables that had it enabled
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plan_detailed_reflections DISABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_schedules DISABLE ROW LEVEL SECURITY;
ALTER TABLE timetable_entries DISABLE ROW LEVEL SECURITY;
ALTER TABLE observation_schedules DISABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_activities DISABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_observer_assignments DISABLE ROW LEVEL SECURITY;
ALTER TABLE rph_weeks DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_week_submissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE academic_calendar_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE annual_calendar_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE dskp_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE rpt_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE items DISABLE ROW LEVEL SECURITY;
ALTER TABLE jadual_pencerapan DISABLE ROW LEVEL SECURITY;
ALTER TABLE tidak_terlaksana DISABLE ROW LEVEL SECURITY;
ALTER TABLE tindakan_susulan DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_reflection_template_preferences DISABLE ROW LEVEL SECURITY;
ALTER TABLE subjects DISABLE ROW LEVEL SECURITY;
ALTER TABLE reflection_templates DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 2: DROP ALL RLS POLICIES
-- =====================================================

-- Drop all policies created during migration
-- Note: This will drop ALL policies, including any existing ones
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
                      policy_record.policyname, 
                      policy_record.schemaname, 
                      policy_record.tablename);
    END LOOP;
END $$;

-- =====================================================
-- STEP 3: REMOVE SCHOOL_ID COLUMNS FROM EXISTING TABLES
-- =====================================================

-- Remove school_id columns from all tables that had them added
ALTER TABLE profiles DROP COLUMN IF EXISTS school_id;
ALTER TABLE lesson_plans DROP COLUMN IF EXISTS school_id;
ALTER TABLE lesson_plan_detailed_reflections DROP COLUMN IF EXISTS school_id;
ALTER TABLE teacher_schedules DROP COLUMN IF EXISTS school_id;
ALTER TABLE timetable_entries DROP COLUMN IF EXISTS school_id;
ALTER TABLE observation_schedules DROP COLUMN IF EXISTS school_id;
ALTER TABLE teacher_activities DROP COLUMN IF EXISTS school_id;
ALTER TABLE teacher_tasks DROP COLUMN IF EXISTS school_id;
ALTER TABLE teacher_observer_assignments DROP COLUMN IF EXISTS school_id;
ALTER TABLE rph_weeks DROP COLUMN IF EXISTS school_id;
ALTER TABLE user_week_submissions DROP COLUMN IF EXISTS school_id;
ALTER TABLE academic_calendar_documents DROP COLUMN IF EXISTS school_id;
ALTER TABLE annual_calendar_events DROP COLUMN IF EXISTS school_id;
ALTER TABLE dskp_documents DROP COLUMN IF EXISTS school_id;
ALTER TABLE rpt_documents DROP COLUMN IF EXISTS school_id;
ALTER TABLE items DROP COLUMN IF EXISTS school_id;
ALTER TABLE jadual_pencerapan DROP COLUMN IF EXISTS school_id;
ALTER TABLE tidak_terlaksana DROP COLUMN IF EXISTS school_id;
ALTER TABLE tindakan_susulan DROP COLUMN IF EXISTS school_id;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS school_id;
ALTER TABLE user_reflection_template_preferences DROP COLUMN IF EXISTS school_id;
ALTER TABLE subjects DROP COLUMN IF EXISTS school_id;
ALTER TABLE reflection_templates DROP COLUMN IF EXISTS school_id;

-- =====================================================
-- STEP 4: DROP HELPER FUNCTIONS
-- =====================================================

-- Drop all helper functions created during migration
DROP FUNCTION IF EXISTS get_user_school_ids(UUID);
DROP FUNCTION IF EXISTS user_has_role_in_schools(TEXT, UUID);
DROP FUNCTION IF EXISTS is_coupon_valid(TEXT);
DROP FUNCTION IF EXISTS record_coupon_usage(TEXT, UUID, UUID, TEXT, TEXT, INET);

-- Drop trigger functions for coupons
DROP FUNCTION IF EXISTS increment_coupon_used_count();
DROP FUNCTION IF EXISTS decrement_coupon_used_count();

-- Drop update timestamp functions for new tables
DROP FUNCTION IF EXISTS update_schools_updated_at();
DROP FUNCTION IF EXISTS update_school_memberships_updated_at();
DROP FUNCTION IF EXISTS update_coupons_updated_at();

-- =====================================================
-- STEP 5: DROP NEW TABLES (IN REVERSE ORDER)
-- =====================================================

-- Drop tables in reverse order of creation to handle foreign key dependencies
DROP TABLE IF EXISTS coupon_usage CASCADE;
DROP TABLE IF EXISTS coupons CASCADE;
DROP TABLE IF EXISTS school_memberships CASCADE;
DROP TABLE IF EXISTS schools CASCADE;

-- =====================================================
-- STEP 6: DROP INDEXES THAT WERE CREATED
-- =====================================================

-- Drop indexes that were created for school_id columns
-- Note: Indexes on dropped columns are automatically dropped,
-- but we'll be explicit for documentation

-- Most school_id indexes will be automatically dropped when columns are dropped
-- This section is mainly for documentation of what was removed

-- =====================================================
-- STEP 7: CLEAN UP MIGRATION LOG
-- =====================================================

-- Mark all migration entries as rolled back
UPDATE migration_log 
SET status = 'rolled_back', 
    notes = COALESCE(notes, '') || ' - Rolled back on ' || NOW()::TEXT
WHERE migration_name LIKE '20250713_complete_saas_migration%';

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify that new tables are gone
SELECT 'Verifying table removal...' as status;

SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('schools', 'school_memberships', 'coupons', 'coupon_usage');

-- Verify that school_id columns are removed
SELECT 'Verifying school_id column removal...' as status;

SELECT table_name, column_name 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND column_name = 'school_id';

-- Verify that RLS is disabled
SELECT 'Verifying RLS is disabled...' as status;

SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;

-- Show migration log
SELECT 'Migration log:' as status;

SELECT migration_name, status, executed_at, notes 
FROM migration_log 
WHERE migration_name LIKE '20250713%' 
ORDER BY executed_at DESC;

-- Final confirmation
SELECT 'Rollback completed successfully. Single-tenant state restored.' as final_status;
