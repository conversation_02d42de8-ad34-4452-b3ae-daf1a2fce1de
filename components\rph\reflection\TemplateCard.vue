<template>
  <div
    class="template-card bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:border-primary hover:shadow-md transition-all cursor-pointer"
    @click="$emit('select', template.id)"
  >
    <!-- Template Head<PERSON> -->
    <div class="flex items-start justify-between mb-2">
      <div class="flex-1 min-w-0">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {{ template.name }}
        </h4>
        <p v-if="template.description" class="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
          {{ template.description }}
        </p>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center space-x-1 ml-2">
        <!-- Favorite Button -->
        <button
          @click.stop="$emit('toggle-favorite', template.id)"
          :class="[
            'p-1 rounded-full transition-colors',
            template.is_favorite
              ? 'text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
              : 'text-gray-400 hover:text-red-500 hover:bg-gray-50 dark:hover:bg-gray-800'
          ]"
          :title="template.is_favorite ? 'Buang dari kegemaran' : 'Tambah ke kegemaran'"
        >
          <Icon :name="template.is_favorite ? 'heroicons:heart-solid' : 'heroicons:heart'" class="w-4 h-4" />
        </button>
        
        <!-- Preview Button -->
        <button
          @click.stop="$emit('preview', template.id)"
          class="p-1 rounded-full text-gray-400 hover:text-blue-500 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          title="Pratonton template"
        >
          <Icon name="heroicons:eye" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Template Metadata -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <!-- Category Badge -->
        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {{ getCategoryLabel(template.category) }}
        </span>
        
        <!-- System Template Badge -->
        <span v-if="template.is_system_template" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          Sistem
        </span>
      </div>
      
      <!-- Usage Stats -->
      <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
        <!-- Usage Count -->
        <div v-if="template.user_usage_count && template.user_usage_count > 0" class="flex items-center space-x-1">
          <Icon name="heroicons:chart-bar" class="w-3 h-3" />
          <span>{{ template.user_usage_count }}</span>
        </div>
        
        <!-- Last Used -->
        <div v-if="template.last_used" class="flex items-center space-x-1">
          <Icon name="heroicons:clock" class="w-3 h-3" />
          <span>{{ formatLastUsed(template.last_used) }}</span>
        </div>
      </div>
    </div>

    <!-- Quick Preview of Prompts -->
    <div v-if="showQuickPreview && Object.keys(template.prompts).length > 0" class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
      <div class="text-xs text-gray-600 dark:text-gray-400">
        <div class="font-medium mb-1">Soalan panduan:</div>
        <div class="space-y-1">
          <div v-for="(prompt, field, index) in template.prompts" :key="field" class="truncate">
            <span v-if="index < 2" class="text-gray-500">{{ getFieldLabel(field) }}:</span>
            <span v-if="index < 2" class="ml-1">{{ prompt }}</span>
          </div>
          <div v-if="Object.keys(template.prompts).length > 2" class="text-gray-400 italic">
            +{{ Object.keys(template.prompts).length - 2 }} lagi...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import type { ReflectionTemplateWithPreference } from '~/types/reflections';

interface Props {
  template: ReflectionTemplateWithPreference;
  showQuickPreview?: boolean;
}

interface Emits {
  (e: 'select', templateId: string): void;
  (e: 'toggle-favorite', templateId: string): void;
  (e: 'preview', templateId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  showQuickPreview: false
});

const emit = defineEmits<Emits>();

// Field labels for display
const fieldLabels: Record<string, string> = {
  challenges_faced: 'Cabaran',
  successful_strategies: 'Strategi Berjaya',
  improvements_needed: 'Penambahbaikan',
  additional_notes: 'Catatan',
  overall_rating: 'Penilaian',
  objectives_achieved: 'Objektif',
  activity_effectiveness: 'Keberkesanan',
  time_management: 'Masa',
  student_engagement: 'Penglibatan',
  resource_adequacy: 'Sumber'
};

// Methods
const getCategoryLabel = (category: string): string => {
  return TEMPLATE_CATEGORY_LABELS[category as keyof typeof TEMPLATE_CATEGORY_LABELS] || category;
};

const getFieldLabel = (field: string): string => {
  return fieldLabels[field] || field;
};

const formatLastUsed = (lastUsed: string): string => {
  const date = new Date(lastUsed);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Baru sahaja';
  } else if (diffInHours < 24) {
    return `${diffInHours}j lalu`;
  } else if (diffInHours < 24 * 7) {
    const days = Math.floor(diffInHours / 24);
    return `${days}h lalu`;
  } else {
    return date.toLocaleDateString('ms-MY', { 
      day: 'numeric', 
      month: 'short' 
    });
  }
};
</script>

<style scoped>
.template-card:hover {
  transform: translateY(-1px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
