<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <Icon name="heroicons:academic-cap" class="mx-auto h-12 w-12 text-blue-600" />
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          Register Your School
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Start your free trial and transform your teaching experience
        </p>
      </div>

      <!-- Registration Form -->
      <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
        <!-- Step 1: Coupon Code -->
        <div v-if="currentStep === 1" class="space-y-4">
          <div>
            <label for="coupon-code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Coupon Code (Optional)
            </label>
            <div class="mt-1 relative">
              <input id="coupon-code" v-model="form.couponCode" type="text"
                placeholder="Enter coupon code for free access"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                :class="{ 'border-red-500': couponError }" />
              <button v-if="form.couponCode" type="button" @click="validateCoupon" :disabled="isValidatingCoupon"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <Icon :name="isValidatingCoupon ? 'heroicons:arrow-path' : 'heroicons:check-circle'" :class="[
                  'h-5 w-5',
                  isValidatingCoupon ? 'animate-spin text-gray-400' :
                    couponValid ? 'text-green-500' : 'text-gray-400'
                ]" />
              </button>
            </div>
            <p v-if="couponError" class="mt-1 text-sm text-red-600">{{ couponError }}</p>
            <p v-if="couponValid" class="mt-1 text-sm text-green-600">Coupon is valid! You'll get free access.</p>
          </div>

          <div class="flex justify-between">
            <button type="button" @click="skipCoupon" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
              Skip (Start Trial)
            </button>
            <button type="button" @click="nextStep" :disabled="!!(form.couponCode && !couponValid)"
              class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
              Continue
            </button>
          </div>
        </div>

        <!-- Step 2: School Information -->
        <div v-if="currentStep === 2" class="space-y-4">
          <div>
            <label for="school-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              School Name *
            </label>
            <input id="school-name" v-model="form.schoolName" type="text" required placeholder="Enter your school name"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>

          <div>
            <label for="school-code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              School Code *
            </label>
            <input id="school-code" v-model="form.schoolCode" type="text" required
              placeholder="e.g., XBA1224 (will be used in your school URL)"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              @input="validateSchoolCode" />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Your school will be accessible at: {{ form.schoolCode.toLowerCase() }}.yourdomain.com
            </p>
            <p v-if="schoolCodeError" class="mt-1 text-sm text-red-600">{{ schoolCodeError }}</p>
          </div>

          <div>
            <label for="contact-email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Contact Email
            </label>
            <input id="contact-email" v-model="form.contactEmail" type="email" placeholder="<EMAIL>"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Description
            </label>
            <textarea id="description" v-model="form.description" rows="3"
              placeholder="Brief description of your school"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
          </div>

          <div class="flex justify-between">
            <button type="button" @click="previousStep" class="text-gray-600 hover:text-gray-500 text-sm font-medium">
              Back
            </button>
            <button type="submit" :disabled="isSubmitting || !isFormValid"
              class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors flex items-center">
              <Icon v-if="isSubmitting" name="heroicons:arrow-path" class="animate-spin h-4 w-4 mr-2" />
              {{ isSubmitting ? 'Creating School...' : 'Create School' }}
            </button>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="submitError"
          class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <Icon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-400 mr-2" />
            <p class="text-sm text-red-800 dark:text-red-200">{{ submitError }}</p>
          </div>
        </div>
      </form>

      <!-- Sign In Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Already have an account?
          <NuxtLink to="/admin/login" class="font-medium text-blue-600 hover:text-blue-500">
            Sign in
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SchoolRegistrationForm } from '~/types/multiTenant'

// Use admin layout
definePageMeta({
  layout: 'landing' as any,
  middleware: 'guest' as any
})

// Form state
const currentStep = ref(1)
const form = ref({
  couponCode: '',
  schoolName: '',
  schoolCode: '',
  contactEmail: '',
  description: ''
})

// Validation state
const isValidatingCoupon = ref(false)
const couponValid = ref(false)
const couponError = ref('')
const schoolCodeError = ref('')
const submitError = ref('')
const isSubmitting = ref(false)

// Composables
const supabase = useSupabaseClient()
const user = useSupabaseUser()
const router = useRouter()

// Computed
const isFormValid = computed(() => {
  return form.value.schoolName.trim() &&
    form.value.schoolCode.trim() &&
    !schoolCodeError.value
})

// Methods
const validateCoupon = async () => {
  if (!form.value.couponCode.trim()) return

  isValidatingCoupon.value = true
  couponError.value = ''

  try {
    const data = await $fetch('/api/coupons/validate', {
      method: 'POST',
      body: { code: form.value.couponCode }
    }) as any

    if (data.success && data.isValid && data.canUse) {
      couponValid.value = true
      couponError.value = ''
    } else {
      couponValid.value = false
      couponError.value = data.error || 'Invalid coupon code'
    }
  } catch (error: any) {
    couponValid.value = false
    couponError.value = error.data?.message || error.message || 'Failed to validate coupon'
  } finally {
    isValidatingCoupon.value = false
  }
}

const validateSchoolCode = async () => {
  const code = form.value.schoolCode.trim()

  if (!code) {
    schoolCodeError.value = ''
    return
  }

  // Basic validation first
  if (code.length < 3) {
    schoolCodeError.value = 'School code must be at least 3 characters'
    return
  }

  if (!/^[a-zA-Z0-9]+$/.test(code)) {
    schoolCodeError.value = 'School code can only contain letters and numbers'
    return
  }

  // Check availability with API
  try {
    const data = await $fetch('/api/schools/validate-code', {
      method: 'POST',
      body: { code }
    }) as any

    if (data.isValid) {
      schoolCodeError.value = ''
      form.value.schoolCode = data.code // Use the normalized code
    } else {
      schoolCodeError.value = data.error || 'School code is not available'
    }
  } catch (error: any) {
    schoolCodeError.value = 'Error checking code availability'
  }
}

const skipCoupon = () => {
  form.value.couponCode = ''
  couponValid.value = false
  couponError.value = ''
  nextStep()
}

const nextStep = () => {
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value || !user.value) return

  isSubmitting.value = true
  submitError.value = ''

  try {
    // Prepare school data
    const schoolData = {
      name: form.value.schoolName.trim(),
      code: form.value.schoolCode.trim().toLowerCase(),
      description: form.value.description?.trim() || null,
      contactEmail: form.value.contactEmail?.trim() || null
    }

    // Get auth token
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('Authentication required')
    }

    // Register school
    const response = await $fetch('/api/schools/register', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`
      },
      body: {
        schoolData,
        couponCode: form.value.couponCode || null,
        adminUserId: user.value.id
      }
    }) as any

    if (response.success) {
      // Show success message and redirect
      await router.push('/admin/dashboard')
    } else {
      throw new Error(response.error || 'Failed to create school')
    }
  } catch (error: any) {
    submitError.value = error.data?.message || error.message || 'Failed to create school'
  } finally {
    isSubmitting.value = false
  }
}

// SEO
useHead({
  title: 'Register Your School - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Register your school for RPHMate and start your free trial today.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for registration page */
</style>
