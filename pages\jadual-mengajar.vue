<template>
    <!-- Page Loading State -->
    <SkeletonSchedulePage v-if="isPageLoading" />

    <div v-if="!isPageLoading" class="space-y-8">
        <!-- Page Header -->
        <UiCompositePageHeader title="Pengurusan Jadual Mengajar"
            subtitle="Urus jadual waktu anda - sistem akan menjana jadual mengajar secara automatik"
            icon="heroicons:calendar-days-solid">
            <template #actions>
                <UiBaseButton variant="primary" size="sm" sm:size="md" prepend-icon="heroicons:printer-solid"
                    @click="handlePrint" class="flex-1 sm:flex-none">
                    <span class="hidden sm:inline">Cetak</span>
                    <span class="sm:hidden">Cetak</span>
                </UiBaseButton>
            </template>
        </UiCompositePageHeader>

        <!-- Summary Stats -->
        <UiCompositeCard>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <div class="flex items-center">
                        <UiBaseIcon name="mdi:calendar-check" class="h-8 w-8 text-blue-500 mr-3" />
                        <div>
                            <p class="text-sm text-blue-600 dark:text-blue-400">Kombinasi Kelas-Subjek</p>
                            <p class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                                {{ schedulesWithDetails.length }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <div class="flex items-center">
                        <UiBaseIcon name="mdi:clock" class="h-8 w-8 text-green-500 mr-3" />
                        <div>
                            <p class="text-sm text-green-600 dark:text-green-400">Jumlah Waktu Mengajar</p>
                            <p class="text-lg font-semibold text-green-900 dark:text-green-100">
                                {{ totalPeriodsCount }} waktu
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <div class="flex items-center">
                        <UiBaseIcon name="mdi:book-open" class="h-8 w-8 text-purple-500 mr-3" />
                        <div>
                            <p class="text-sm text-purple-600 dark:text-purple-400">Jumlah Subjek</p>
                            <p class="text-lg font-semibold text-purple-900 dark:text-purple-100">
                                {{ uniqueSubjectsCount }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                    <div class="flex items-center">
                        <UiBaseIcon name="mdi:auto-fix" class="h-8 w-8 text-orange-500 mr-3" />
                        <div>
                            <p class="text-sm text-orange-600 dark:text-orange-400">Status Sistem</p>
                            <p class="text-lg font-semibold text-orange-900 dark:text-orange-100">
                                Auto-Generated
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </UiCompositeCard>
        <!-- View Toggle -->
        <UiCompositeCard>
            <div class="flex items-center justify-center sm:justify-end">
                <div class="w-full sm:w-auto">
                    <UiBaseViewToggle v-model="currentView" :options="[
                        {
                            value: 'timetable',
                            label: 'Jadual Waktu',
                            icon: 'heroicons:calendar-days-solid'
                        },
                        {
                            value: 'list',
                            label: 'Ringkasan Jadual',
                            icon: 'heroicons:list-bullet-solid'
                        }
                    ]" />
                </div>
            </div>
        </UiCompositeCard>
        <!-- Auto-Generated Schedules List View -->
        <div v-if="currentView === 'list'" class="bg-white dark:bg-gray-800 rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Ringkasan Jadual Mengajar
                        </h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Sistem menjana jadual mengajar daripada jadual waktu secara automatik. Tiada pengeditan
                            manual diperlukan.
                        </p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center space-x-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <Icon name="mdi:auto-fix" class="h-5 w-5 text-green-500" />
                            <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                Sentiasa Terkini
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div v-if="schedulesWithDetails.length === 0" class="p-12 text-center">
                <Icon name="mdi:calendar-blank" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Tiada Jadual Mengajar Dijana
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                    Jadual mengajar akan dijana secara automatik apabila anda menambah kelas ke dalam jadual waktu.
                    Sistem akan mengira semua kombinasi kelas-subjek dan mengira jumlah waktu untuk setiap kombinasi.
                </p>
                <UiBaseButton variant="primary" @click="currentView = 'timetable'">
                    <UiBaseIcon name="mdi:calendar-week" class="mr-2 h-4 w-4" />
                    Buka Jadual Waktu
                </UiBaseButton>
            </div>

            <!-- Schedules Table -->
            <div v-else class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th v-for="header in sortableHeaders" :key="header.key"
                                @click="toggleSort(header.key as 'class' | 'days' | 'periods')"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <div class="flex items-center">
                                    <span>{{ header.label }}</span>
                                    <Icon v-if="sortField === header.key"
                                        :name="sortDirection === 'asc' ? 'mdi:arrow-up' : 'mdi:arrow-down'"
                                        class="ml-1 h-4 w-4" />
                                </div>
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Status
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="schedule in sortedSchedules" :key="schedule.id"
                            class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 flex-shrink-0">
                                        <div
                                            class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                            <Icon name="mdi:book" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ schedule.class_name }} - {{ getFullSubjectName(schedule.subject_name) }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            Auto-generated dari jadual waktu
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    <span v-for="day in schedule.days_scheduled" :key="day"
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                        {{ getDayLabel(day) }}
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ schedule.total_periods }} waktu
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-col">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <Icon name="mdi:auto-fix" class="h-4 w-4 text-green-500" />
                                        <span class="text-sm text-green-600 dark:text-green-400 font-medium">
                                            Auto-generated
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        Sentiasa terkini dengan jadual waktu
                                    </span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div> <!-- Timetable View -->
        <TimetableView v-if="currentView === 'timetable'" :user-class-subjects="userClassSubjects"
            @update-class-subjects="loadUserClassSubjects" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useToast } from '~/composables/useToast'
import TimetableView from '~/components/schedule/TimetableView.vue'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'
import { useTimetableToSchedule } from '~/composables/useTimetableToSchedule'
import { useSubjects } from '~/composables/useSubjects'
import { useTimeSlots } from '~/composables/useTimeSlots'
import { useTimetable } from '~/composables/useTimetable'
import useSubjectColors from '~/composables/useSubjectColors'
import SkeletonSchedulePage from '~/components/ui/skeleton/SkeletonSchedulePage.vue'

// Set page metadata
definePageMeta({
    layout: 'school' as any
})

// CRITICAL: Authentication check - redirect if not logged in
const user = useSupabaseUser()
if (!user.value) {
    await navigateTo('/auth/login')
}

// Set page head
useHead({
    title: 'Jadual Mengajar - RPHMate',
    meta: [
        {
            name: 'description',
            content: 'Pengurusan jadual mengajar yang dijana secara automatik daripada jadual waktu untuk perancangan yang lebih baik.'
        }
    ]
})

// Page loading state (local control)
const isPageLoading = ref(true)

// Auto-generation composable (primary data source)
const {
    schedulesWithDetails,
    totalPeriodsCount,
    uniqueSubjectsCount,
    dayOptions,
    loadTimetableEntries
} = useTimetableToSchedule()

// Supabase for user data
const supabase = useSupabaseClient()
// user already declared above for auth check

// User profile data
const userProfile = ref<any>(null) // To store the full profile including class_subjects
const userName = ref<string>('')

// User class-subjects data (needed for TimetableView)
const userClassSubjects = ref<UserClassSubjectEntry[]>([])

// Subject data from useSubjects composable
const { subjects, fetchSubjects } = useSubjects()

// Timetable composables for print functionality
const { timeSlots, fetchTimeSlots } = useTimeSlots()
const { timetableEntries, fetchTimetableEntries } = useTimetable()
const { getEntryColor } = useSubjectColors()

// Map for quick lookup: subject_abbreviation -> subject_id
const subjectAbbreviationToIdMap = ref<Record<string, string>>({})

// Local state
const currentView = ref<'list' | 'timetable'>('timetable') // Default to timetable view
const sortField = ref<'class' | 'days' | 'periods'>('class')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Toast notifications
const { error: showErrorToast } = useToast()

// Error handling (currently timetable composable doesn't have errors)
const error = computed(() => null)

// Helper function to get full subject name from abbreviation
const getFullSubjectName = (abbreviation: string): string => {
    const subjectId = subjectAbbreviationToIdMap.value[abbreviation];
    if (subjectId) {
        const subject = subjects.value.find(s => s.id === subjectId);
        return subject?.name || abbreviation; // Return full name or fallback to abbreviation
    }
    return abbreviation; // If no mapping found, return original abbreviation
};

// Sortable headers configuration
const sortableHeaders = computed(() => [
    { key: 'class', label: 'Kelas & Subjek' },
    { key: 'days', label: 'Hari Mengajar' },
    { key: 'periods', label: 'Jumlah Waktu' },
]);

// Toggle sort direction
const toggleSort = (field: 'class' | 'days' | 'periods') => {
    if (sortField.value === field) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        sortField.value = field;
        sortDirection.value = 'asc';
    }
};

// Computed properties
const sortedSchedules = computed(() => {
    return [...schedulesWithDetails.value].sort((a, b) => {
        let comparison = 0;

        switch (sortField.value) {
            case 'class':
                comparison = a.class_name.localeCompare(b.class_name);
                if (comparison === 0) {
                    // If class names are the same, sort by subject name
                    comparison = getFullSubjectName(a.subject_name)
                        .localeCompare(getFullSubjectName(b.subject_name));
                }
                break;
            case 'days':
                // Sort by the number of days scheduled
                comparison = a.days_scheduled.length - b.days_scheduled.length;
                break;
            case 'periods':
                comparison = a.total_periods - b.total_periods;
                break;
        }

        return sortDirection.value === 'asc' ? comparison : -comparison;
    });
});

const availableClassSubjects = computed(() => {
    // Get unique class-subject combinations from current schedules
    const combinations = new Set<string>()
    schedulesWithDetails.value.forEach(schedule => {
        combinations.add(`${schedule.class_name} - ${getFullSubjectName(schedule.subject_name)}`)
    })
    return Array.from(combinations).map(combo => ({ label: combo, value: combo }))
})

// Helper functions
const getDayLabel = (day: string): string => {
    const dayOption = dayOptions.value.find(d => d.value === day)
    return dayOption ? dayOption.label : day
}

// Load user class subjects from profile and create subject abbreviation map
const loadUserClassSubjects = async () => {
    if (!user.value) {
        userClassSubjects.value = [];
        userProfile.value = null;
        userName.value = '';
        subjectAbbreviationToIdMap.value = {};
        return;
    }

    try {
        const { data: profile, error: fetchError } = await supabase
            .from('profiles')
            .select('class_subjects, full_name')
            .eq('id', user.value.id)
            .single();

        if (fetchError) {
            console.error('Error fetching profile:', fetchError);
            showErrorToast('Gagal memuatkan data profil. Sila cuba lagi.');
            userClassSubjects.value = [];
            userProfile.value = null;
            userName.value = '';
            subjectAbbreviationToIdMap.value = {};
            return;
        }

        userProfile.value = profile;
        userName.value = (profile as any)?.full_name || '';
        const classSubjects = ((profile as any)?.class_subjects as UserClassSubjectEntry[]) || [];
        userClassSubjects.value = classSubjects;

        // Populate the subject abbreviation to ID map
        const newMap: Record<string, string> = {};
        classSubjects.forEach(cs => {
            if (cs.subject_abbreviation && cs.subject_id) {
                newMap[cs.subject_abbreviation] = cs.subject_id;
            }
        });
        subjectAbbreviationToIdMap.value = newMap;

    } catch (err) {
        console.error('Error loading user class subjects:', err);
        showErrorToast('Gagal memuatkan data kelas dan subjek. Sila cuba lagi.');
        userClassSubjects.value = [];
        userProfile.value = null;
        userName.value = '';
        subjectAbbreviationToIdMap.value = {};
    }
}

// Print functionality
const handlePrint = async () => {
    try {
        // Switch to timetable view if not already
        if (currentView.value !== 'timetable') {
            currentView.value = 'timetable'
            await nextTick() // Wait for view to update
        }

        // Wait a bit more for the timetable to fully load
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Fetch fresh data to ensure we have the latest
        await Promise.all([
            fetchTimeSlots(),
            fetchTimetableEntries()
        ])

        // Wait a bit more for the data to be processed
        await nextTick()

        // Check if we have data
        if (timeSlots.value.length === 0) {
            showErrorToast('Tiada data slot waktu untuk dicetak. Sila tambah slot waktu terlebih dahulu.')
            return
        }

        // Create print content
        const printContent = generatePrintableContent()

        // Open print window
        const printWindow = window.open('', '_blank')
        if (!printWindow) {
            showErrorToast('Gagal membuka tetingkap cetak. Sila pastikan popup tidak disekat.')
            return
        }

        printWindow.document.write(printContent)
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
    } catch (error) {
        console.error('Print error:', error)
        showErrorToast('Gagal mencetak jadual. Sila cuba lagi.')
    }
}

const generatePrintableContent = (): string => {
    // Days configuration
    const days = [
        { value: 'ISNIN', label: 'Isnin' },
        { value: 'SELASA', label: 'Selasa' },
        { value: 'RABU', label: 'Rabu' },
        { value: 'KHAMIS', label: 'Khamis' },
        { value: 'JUMAAT', label: 'Jumaat' }
    ]

    // Helper function to normalize time format (remove seconds if present)
    const normalizeTime = (time: string): string => {
        return time.substring(0, 5) // Get HH:MM part only
    }

    // Helper function to get timetable entry for a specific slot and day
    const getTimetableEntry = (timeSlotId: string, day: string) => {
        // Find the time slot to get its start and end times
        const timeSlot = timeSlots.value.find(slot => slot.id === timeSlotId)
        if (!timeSlot) {
            return undefined
        }

        const result = timetableEntries.value.find(entry => {
            // Normalize times to HH:MM format for comparison
            const entryStart = normalizeTime(entry.time_slot_start)
            const entryEnd = normalizeTime(entry.time_slot_end)
            const slotStart = normalizeTime(timeSlot.start_time)
            const slotEnd = normalizeTime(timeSlot.end_time)

            const timeMatch = entryStart === slotStart && entryEnd === slotEnd
            const dayMatch = entry.day === day
            return timeMatch && dayMatch
        })
        return result
    }

    // Helper function to format time
    const formatTime = (time: string): string => {
        const [hours, minutes] = time.split(':')
        const hour = parseInt(hours)
        const ampm = hour >= 12 ? 'PM' : 'AM'
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
        return `${displayHour}:${minutes} ${ampm}`
    }

    // Generate table rows
    let tableRows = ''

    timeSlots.value.forEach((timeSlot) => {
        const timeLabel = timeSlot.period_number < 0
            ? timeSlot.label
            : `${formatTime(timeSlot.start_time)} - ${formatTime(timeSlot.end_time)}`

        tableRows += `
            <tr style="border-bottom: 1px solid #e5e7eb;">
                <td style="padding: 12px; border-right: 1px solid #e5e7eb; background-color: #f9fafb; font-weight: 600; text-align: center; font-size: 12px; ${timeSlot.period_number < 0 ? 'background-color: #fef3c7;' : ''}">
                    ${timeLabel}
                    ${timeSlot.period_number >= 0 ? `<br><small style="color: #6b7280; font-size: 10px;">Waktu ${timeSlot.period_number + 1}</small>` : ''}
                </td>`

        days.forEach(day => {
            const entry = getTimetableEntry(timeSlot.id, day.value)

            if (entry && entry.activity_type === 'CLASS') {
                const subjectColor = getEntryColor(entry)
                tableRows += `
                    <td style="padding: 8px; border-right: 1px solid #e5e7eb; text-align: center; background-color: ${subjectColor.bg_color}; color: ${subjectColor.text_color}; border-left: 4px solid ${subjectColor.color};">
                        <div style="font-weight: 600; font-size: 14px; text-align: left;">${entry.subject_name}</div>
                        <div style="font-size: 12px; margin-top: 2px; text-align: right;">${entry.class_name}</div>
                    </td>`
            } else if (entry) {
                // Non-class activity
                tableRows += `
                    <td style="padding: 8px; border-right: 1px solid #e5e7eb; text-align: center; background-color: #f3f4f6; color: #374151;">
                        <div style="font-weight: 600; font-size: 14px; text-align: left;">${entry.activity_title || 'Aktiviti'}</div>
                        ${entry.activity_description ? `<div style="font-size: 12px; margin-top: 2px; text-align: right;">${entry.activity_description}</div>` : ''}
                    </td>`
            } else if (timeSlot.period_number < 0) {
                // Break time
                tableRows += `
                    <td style="padding: 8px; border-right: 1px solid #e5e7eb; text-align: center; background-color: #fef3c7; color: #d97706;">
                        <div style="font-size: 20px;">☕</div>
                    </td>`
            } else {
                // Empty slot
                tableRows += `
                    <td style="padding: 8px; border-right: 1px solid #e5e7eb; background-color: #ffffff;">
                        &nbsp;
                    </td>`
            }
        })

        tableRows += '</tr>'
    })

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Jadual Waktu Persendirian</title>
            <style>
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    color: #000;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .title {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .name {
                    font-size: 18px;
                    margin-bottom: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    table-layout: fixed;
                }
                th {
                    background-color: #f9fafb;
                    padding: 12px;
                    border: 1px solid #e5e7eb;
                    font-weight: 600;
                    text-align: center;
                }
                td {
                    border: 1px solid #e5e7eb;
                    vertical-align: middle;
                    min-height: 60px;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">Jadual Waktu Persendirian</div>
                <div class="name">${userName.value || 'Nama Guru'}</div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th style="width: 120px;">Waktu</th>
                        ${days.map(day => `<th style="width: 17.6%;">${day.label}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>
        </body>
        </html>`
}

// Lifecycle
onMounted(async () => {
    try {
        await loadTimetableEntries(); // Await the timetable loading
        await fetchSubjects(); // Fetch all subjects on mount
        await loadUserClassSubjects(); // Load user class subjects and populate map
    } catch (error) {
        console.error('Error during schedule page initialization:', error);
        showErrorToast('Terdapat masalah semasa memuatkan halaman. Sila cuba lagi.');
    } finally {
        // Always set loading to false when initialization is complete
        isPageLoading.value = false;
    }

    // Watch for user changes and load their class-subjects
    watch(user, async () => {
        await loadUserClassSubjects();
    }, { immediate: true });
})
</script>
