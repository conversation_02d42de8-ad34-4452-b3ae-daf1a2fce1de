// Debug endpoint to check environment variables
// This should be removed in production

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()

  return {
    timestamp: new Date().toISOString(),
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      // Check if keys exist (don't expose actual values)
      stripe: {
        publishableKey: config.public.stripePublishableKey ? 'Present' : 'Missing',
        secretKey: config.stripeSecretKey ? 'Present' : 'Missing',
        webhookSecret: config.stripeWebhookSecret ? 'Present' : 'Missing',
        // Also check process.env directly
        processEnvSecretKey: process.env.STRIPE_SECRET_KEY ? 'Present' : 'Missing',
        processEnvWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? 'Present' : 'Missing',
        processEnvPublishableKey: process.env.STRIPE_PUBLISHABLE_KEY ? 'Present' : 'Missing'
      },
      supabase: {
        url: config.public.supabaseUrl ? 'Present' : 'Missing',
        anonKey: config.public.supabaseAnonKey ? 'Present' : 'Missing',
        serviceRoleKey: config.supabaseServiceRoleKey ? 'Present' : 'Missing',
        processEnvUrl: process.env.SUPABASE_URL ? 'Present' : 'Missing',
        processEnvAnonKey: process.env.SUPABASE_ANON_KEY ? 'Present' : 'Missing',
        processEnvServiceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Present' : 'Missing'
      },
      debug: {
        allEnvKeys: Object.keys(process.env).filter(key =>
          key.includes('STRIPE') || key.includes('SUPABASE')
        )
      }
    }
  }
})
