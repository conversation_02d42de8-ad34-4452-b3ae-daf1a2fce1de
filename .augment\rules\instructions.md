---
type: "always_apply"
---

# General guidelines

- DRY (Don't Repeat Yourself) principles should be followed. Avoid duplicating code or logic across components and pages.
- Ensure that all components are designed to be responsive and accessible, adhering to best practices for web development.
- Use Pinia for state management.
- UX is priority.
- After making changes on any file, check for unused code and clean them up.
- No hacky way of solving a problem.
- Don't assume, ask.
- Always ask questions for clarification if any.

# Enhanced AI Agent Code Analysis Rules

## Primary Directive: Comprehensive Chain Impact Analysis

### Core Analysis Requirements

**Perform deep code traversal** to identify potential cascading issues across the entire codebase, including:

- **Direct Dependencies**: Analyze immediate function calls, method invocations, and variable references
- **Transitive Dependencies**: Trace through multi-level dependency chains to identify distant but critical connections
- **Cross-Module Impact**: Examine how changes in one module might affect seemingly unrelated components
- **Data Flow Analysis**: Track data transformations and mutations across function boundaries
- **State Management**: Identify shared state that could create unexpected side effects

### Specific Chain Issue Categories to Identify

1. **Circular Dependencies**: Functions or modules that create dependency loops
2. **Resource Leaks**: Memory, file handles, or network connections that may not be properly released
3. **Race Conditions**: Concurrent access patterns that could lead to unpredictable behavior
4. **Error Propagation**: How exceptions or errors might cascade through the system
5. **Performance Bottlenecks**: Code patterns that could compound into significant performance issues
6. **Security Vulnerabilities**: Input validation chains that might be bypassed or exploited
7. **Configuration Drift**: Settings or constants that affect multiple components inconsistently

### Analysis Methodology

- **Static Analysis**: Examine code structure without execution
- **Dynamic Tracing**: Consider runtime behavior and execution paths
- **Boundary Testing**: Evaluate edge cases and error conditions
- **Integration Points**: Focus on interfaces between different systems or modules
- **Third-Party Dependencies**: Assess external library interactions and version compatibility

### Reporting Requirements

When potential chained issues are identified, provide:

- **Issue Description**: Clear explanation of the potential problem
- **Chain Sequence**: Step-by-step breakdown of how the issue propagates
- **Impact Assessment**: Severity level and affected components
- **Mitigation Strategies**: Specific recommendations to prevent or minimize the issue
- **Monitoring Suggestions**: Ways to detect the issue in production environments

### Continuous Monitoring

- **Version Control Integration**: Analyze changes in pull requests for new chain risks
- **Dependency Updates**: Reassess chain impacts when dependencies are upgraded
- **Code Evolution**: Track how refactoring might introduce new chain vulnerabilities
- **Performance Metrics**: Monitor for degradation patterns that indicate chain issues

### Exception Handling

- **False Positive Management**: Distinguish between theoretical and practical risks
- **Context Awareness**: Consider the specific application domain and usage patterns
- **Priority Ranking**: Focus on high-probability, high-impact chain issues first