---
type: "always_apply"
---

# General guidelines

- DRY (Don't Repeat Yourself) principles should be followed. Avoid duplicating code or logic across components and pages.
- Ensure that all components are designed to be responsive and accessible, adhering to best practices for web development.
- Use Pinia for state management.
- UX is priority.
- After making changes on any file, check for unused code and clean them up.
- No hacky way of solving a problem.
- Don't assume, ask.
- Always ask questions for clarification if any.