<template>
  <div class="w-full bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-4">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Progress Bar -->
      <div class="relative">
        <!-- Background Line -->
        <div class="absolute top-5 left-0 w-full h-0.5 bg-gray-200 dark:bg-gray-600"></div>
        
        <!-- Progress Line -->
        <div 
          class="absolute top-5 left-0 h-0.5 bg-blue-600 transition-all duration-500 ease-in-out"
          :style="{ width: progressWidth }"
        ></div>
        
        <!-- Steps -->
        <div class="relative flex justify-between">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="flex flex-col items-center"
          >
            <!-- Step Circle -->
            <div 
              class="w-10 h-10 rounded-full border-2 flex items-center justify-center text-sm font-semibold transition-all duration-300"
              :class="getStepClasses(index + 1)"
            >
              <Icon 
                v-if="currentStep > index + 1" 
                name="heroicons:check-20-solid" 
                class="w-5 h-5 text-white"
              />
              <span v-else class="text-sm font-semibold">{{ index + 1 }}</span>
            </div>
            
            <!-- Step Label -->
            <div class="mt-2 text-center">
              <div 
                class="text-sm font-medium transition-colors duration-300"
                :class="currentStep >= index + 1 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'"
              >
                {{ step.title }}
              </div>
              <div 
                class="text-xs mt-1 transition-colors duration-300"
                :class="currentStep >= index + 1 ? 'text-gray-600 dark:text-gray-300' : 'text-gray-400 dark:text-gray-500'"
              >
                {{ step.subtitle }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface RegistrationStep {
  title: string
  subtitle: string
}

interface Props {
  currentStep: number
}

const props = defineProps<Props>()

// Define the three registration steps
const steps: RegistrationStep[] = [
  {
    title: 'Pendaftaran',
    subtitle: 'Akaun & Pengesahan'
  },
  {
    title: 'Maklumat',
    subtitle: 'Sekolah & Pentadbir'
  },
  {
    title: 'Pembayaran',
    subtitle: 'Pelan & Bayaran'
  }
]

// Calculate progress width based on current step
const progressWidth = computed(() => {
  if (props.currentStep <= 1) return '0%'
  if (props.currentStep === 2) return '50%'
  if (props.currentStep >= 3) return '100%'
  return '0%'
})

// Get CSS classes for each step circle
const getStepClasses = (stepNumber: number) => {
  if (props.currentStep > stepNumber) {
    // Completed step
    return 'bg-blue-600 border-blue-600 text-white'
  } else if (props.currentStep === stepNumber) {
    // Current step
    return 'bg-blue-600 border-blue-600 text-white'
  } else {
    // Future step
    return 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400'
  }
}
</script>

<style scoped>
/* Additional animations for smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
