<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Teacher Management
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Manage teachers for {{ school?.name }}
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="showInviteModal = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-2" />
            Invite Teacher
          </button>
          <button
            @click="showBulkActions = !showBulkActions"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-colors',
              selectedTeachers.length > 0 
                ? 'bg-gray-600 hover:bg-gray-700 text-white' 
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            ]"
            :disabled="selectedTeachers.length === 0"
          >
            Bulk Actions ({{ selectedTeachers.length }})
          </button>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Teachers
          </label>
          <div class="relative">
            <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by name or email..."
              class="pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
          </div>
        </div>

        <!-- Role Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Role
          </label>
          <select
            v-model="roleFilter"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Roles</option>
            <option value="admin">Admin</option>
            <option value="supervisor">Supervisor</option>
            <option value="teacher">Teacher</option>
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status
          </label>
          <select
            v-model="statusFilter"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="pending">Pending</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Bulk Actions Panel -->
    <div v-if="showBulkActions && selectedTeachers.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <span class="text-sm font-medium text-yellow-800">
            {{ selectedTeachers.length }} teacher(s) selected
          </span>
          <div class="flex space-x-2">
            <button
              @click="bulkUpdateRole"
              class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
            >
              Change Role
            </button>
            <button
              @click="bulkUpdateStatus"
              class="text-sm bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded"
            >
              Change Status
            </button>
            <button
              @click="bulkRemove"
              class="text-sm bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded"
            >
              Remove
            </button>
          </div>
        </div>
        <button
          @click="clearSelection"
          class="text-sm text-gray-600 hover:text-gray-800"
        >
          Clear Selection
        </button>
      </div>
    </div>

    <!-- Teachers Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">
            Teachers ({{ filteredTeachers.length }})
          </h2>
          <div class="flex items-center space-x-2">
            <button
              @click="refreshTeachers"
              class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Icon name="heroicons:arrow-path" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="p-6">
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse flex items-center space-x-4">
            <div class="w-4 h-4 bg-gray-200 rounded"></div>
            <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-200 rounded w-1/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Teachers List -->
      <div v-else-if="filteredTeachers.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="toggleSelectAll"
                  class="rounded border-gray-300"
                >
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Teacher
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Role
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Joined
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Last Active
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="teacher in filteredTeachers" :key="teacher.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  :checked="selectedTeachers.includes(teacher.id)"
                  @change="toggleTeacherSelection(teacher.id)"
                  class="rounded border-gray-300"
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img
                      v-if="teacher.user?.avatar_url"
                      :src="teacher.user.avatar_url"
                      :alt="teacher.user.full_name"
                      class="h-10 w-10 rounded-full"
                    >
                    <div
                      v-else
                      class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"
                    >
                      <span class="text-sm font-medium text-gray-700">
                        {{ getInitials(teacher.user?.full_name || teacher.user?.email) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ teacher.user?.full_name || 'Pending Invitation' }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ teacher.user?.email || teacher.invitation_email }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getRoleBadgeClass(teacher.role)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ teacher.role }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(teacher.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ teacher.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(teacher.joined_at || teacher.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ teacher.last_active ? formatDate(teacher.last_active) : 'Never' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button
                    @click="editTeacher(teacher)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Edit
                  </button>
                  <button
                    v-if="teacher.status === 'pending'"
                    @click="resendInvitation(teacher)"
                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                  >
                    Resend
                  </button>
                  <button
                    @click="removeTeacher(teacher)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Remove
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-else class="p-12 text-center">
        <Icon name="heroicons:users" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No teachers found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Get started by inviting your first teacher.
        </p>
        <div class="mt-6">
          <button
            @click="showInviteModal = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-2" />
            Invite Teacher
          </button>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <InviteTeacherModal
      v-if="showInviteModal"
      :school-id="schoolId"
      @close="showInviteModal = false"
      @invited="handleTeacherInvited"
    />

    <EditTeacherModal
      v-if="showEditModal"
      :teacher="selectedTeacher"
      :school-id="schoolId"
      @close="showEditModal = false"
      @updated="handleTeacherUpdated"
    />
  </div>
</template>

<script setup lang="ts">
// Layout and meta
definePageMeta({
  layout: 'admin',
  middleware: 'admin-auth'
})

// Route params
const route = useRoute()
const schoolId = route.params.id as string

// Composables
const supabase = useSupabaseClient()

// State
const isLoading = ref(true)
const school = ref<any>(null)
const teachers = ref<any[]>([])
const selectedTeachers = ref<string[]>([])
const showInviteModal = ref(false)
const showEditModal = ref(false)
const showBulkActions = ref(false)
const selectedTeacher = ref<any>(null)

// Filters
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')

// Computed
const filteredTeachers = computed(() => {
  let filtered = teachers.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(teacher => 
      teacher.user?.full_name?.toLowerCase().includes(query) ||
      teacher.user?.email?.toLowerCase().includes(query) ||
      teacher.invitation_email?.toLowerCase().includes(query)
    )
  }

  if (roleFilter.value) {
    filtered = filtered.filter(teacher => teacher.role === roleFilter.value)
  }

  if (statusFilter.value) {
    filtered = filtered.filter(teacher => teacher.status === statusFilter.value)
  }

  return filtered
})

const isAllSelected = computed(() => {
  return filteredTeachers.value.length > 0 && 
         selectedTeachers.value.length === filteredTeachers.value.length
})

const isIndeterminate = computed(() => {
  return selectedTeachers.value.length > 0 && 
         selectedTeachers.value.length < filteredTeachers.value.length
})

// Methods
const fetchSchoolData = async () => {
  try {
    const { data, error } = await supabase
      .from('schools')
      .select('*')
      .eq('id', schoolId)
      .single()

    if (error) throw error
    school.value = data
  } catch (error) {
    console.error('Error fetching school:', error)
  }
}

const fetchTeachers = async () => {
  try {
    isLoading.value = true

    const { data, error } = await supabase
      .from('school_memberships')
      .select(`
        *,
        user:profiles(*)
      `)
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })

    if (error) throw error
    teachers.value = data || []
  } catch (error) {
    console.error('Error fetching teachers:', error)
  } finally {
    isLoading.value = false
  }
}

const refreshTeachers = () => {
  fetchTeachers()
}

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedTeachers.value = []
  } else {
    selectedTeachers.value = filteredTeachers.value.map(t => t.id)
  }
}

const toggleTeacherSelection = (teacherId: string) => {
  const index = selectedTeachers.value.indexOf(teacherId)
  if (index > -1) {
    selectedTeachers.value.splice(index, 1)
  } else {
    selectedTeachers.value.push(teacherId)
  }
}

const clearSelection = () => {
  selectedTeachers.value = []
  showBulkActions.value = false
}

const editTeacher = (teacher: any) => {
  selectedTeacher.value = teacher
  showEditModal.value = true
}

const removeTeacher = async (teacher: any) => {
  if (confirm(`Are you sure you want to remove ${teacher.user?.full_name || teacher.invitation_email}?`)) {
    try {
      const { error } = await (supabase as any)
        .from('school_memberships')
        .update({ status: 'removed' })
        .eq('id', teacher.id)

      if (error) throw error
      await fetchTeachers()
    } catch (error) {
      console.error('Error removing teacher:', error)
    }
  }
}

const resendInvitation = async (teacher: any) => {
  try {
    // TODO: Implement resend invitation API call
    console.log('Resending invitation to:', teacher.invitation_email)
  } catch (error) {
    console.error('Error resending invitation:', error)
  }
}

const bulkUpdateRole = () => {
  // TODO: Implement bulk role update
  console.log('Bulk update role for:', selectedTeachers.value)
}

const bulkUpdateStatus = () => {
  // TODO: Implement bulk status update
  console.log('Bulk update status for:', selectedTeachers.value)
}

const bulkRemove = () => {
  // TODO: Implement bulk remove
  console.log('Bulk remove:', selectedTeachers.value)
}

const handleTeacherInvited = () => {
  showInviteModal.value = false
  fetchTeachers()
}

const handleTeacherUpdated = () => {
  showEditModal.value = false
  fetchTeachers()
}

const getInitials = (name: string) => {
  return name?.split(' ').map(n => n[0]).join('').toUpperCase() || '?'
}

const getRoleBadgeClass = (role: string) => {
  switch (role) {
    case 'admin': return 'bg-red-100 text-red-800'
    case 'supervisor': return 'bg-blue-100 text-blue-800'
    case 'teacher': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800'
    case 'pending': return 'bg-yellow-100 text-yellow-800'
    case 'inactive': return 'bg-gray-100 text-gray-800'
    case 'suspended': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  fetchSchoolData()
  fetchTeachers()
})
</script>
