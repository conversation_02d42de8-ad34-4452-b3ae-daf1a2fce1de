// Coupon management composable
// Created: 2025-07-13

import { ref } from 'vue'
import type { 
  Coupon, 
  CouponValidationResult, 
  CouponUsage,
  CouponValidationForm 
} from '~/types/multiTenant'

export const useCoupons = () => {
  const supabase = useSupabaseClient()
  const isValidating = ref(false)
  const validationError = ref<string | null>(null)

  // Validate a coupon code
  const validateCoupon = async (couponCode: string): Promise<CouponValidationResult> => {
    try {
      isValidating.value = true
      validationError.value = null

      if (!couponCode || couponCode.trim().length === 0) {
        return {
          isValid: false,
          coupon: null,
          error: 'Coupon code is required',
          canUse: false,
          usageRemaining: null
        }
      }

      // Call the API to validate coupon
      const response = await $fetch('/api/coupons/validate', {
        method: 'POST',
        body: { code: couponCode }
      }) as any

      if (response.success && response.isValid) {
        // Calculate usage remaining
        const usageRemaining = response.coupon?.usage_limit
          ? response.coupon.usage_limit - response.coupon.used_count
          : null

        return {
          isValid: true,
          coupon: response.coupon,
          error: null,
          canUse: usageRemaining === null || usageRemaining > 0,
          usageRemaining
        }
      } else {
        return {
          isValid: false,
          coupon: null,
          error: response.error || 'Invalid or expired coupon code',
          canUse: false,
          usageRemaining: null
        }
      }

    } catch (error: any) {
      validationError.value = error.message || 'Failed to validate coupon'
      return {
        isValid: false,
        coupon: null,
        error: error.message || 'Failed to validate coupon',
        canUse: false,
        usageRemaining: null
      }
    } finally {
      isValidating.value = false
    }
  }

  // Record coupon usage
  const recordCouponUsage = async (
    couponCode: string,
    schoolId: string,
    usageType: 'school_registration' | 'subscription_renewal' | 'upgrade' = 'school_registration'
  ): Promise<{ success: boolean; error?: string; usageId?: string }> => {
    try {
      const user = useSupabaseUser()
      
      if (!user.value) {
        return { success: false, error: 'User not authenticated' }
      }

      // Get client information
      const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : null
      const ipAddress = null // IP address would need to be obtained from server-side

      // Call the database function to record usage
      const { data: usageId, error } = await (supabase as any)
        .rpc('record_coupon_usage', {
          p_coupon_code: couponCode.toUpperCase(),
          p_school_id: schoolId,
          p_used_by: user.value.id,
          p_usage_type: usageType,
          p_user_agent: userAgent,
          p_ip_address: ipAddress
        })

      if (error) {
        throw error
      }

      return { success: true, usageId }

    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Failed to record coupon usage' 
      }
    }
  }

  // Get coupon usage history for a school
  const getCouponUsageHistory = async (schoolId: string) => {
    try {
      const { data, error } = await supabase
        .from('coupon_usage')
        .select(`
          *,
          coupon:coupons(code, name, description),
          used_by_user:auth.users(email)
        `)
        .eq('school_id', schoolId)
        .order('used_at', { ascending: false })

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      return { 
        data: null, 
        error: error.message || 'Failed to fetch coupon usage history' 
      }
    }
  }

  // Admin functions for managing coupons
  const createCoupon = async (couponData: {
    code: string
    name?: string
    description?: string
    usage_limit?: number
    expires_at?: string
    discount_type?: 'free_registration' | 'percentage' | 'fixed_amount'
    discount_value?: number
  }) => {
    try {
      const user = useSupabaseUser()
      
      if (!user.value) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await (supabase as any)
        .from('coupons')
        .insert({
          ...couponData,
          code: couponData.code.toUpperCase(),
          created_by: user.value.id
        })
        .select()
        .single()

      if (error) throw error

      return { success: true, data, error: null }
    } catch (error: any) {
      return { 
        success: false, 
        data: null, 
        error: error.message || 'Failed to create coupon' 
      }
    }
  }

  const updateCoupon = async (couponId: string, updates: Partial<Coupon>) => {
    try {
      const { data, error } = await (supabase as any)
        .from('coupons')
        .update(updates)
        .eq('id', couponId)
        .select()
        .single()

      if (error) throw error

      return { success: true, data, error: null }
    } catch (error: any) {
      return { 
        success: false, 
        data: null, 
        error: error.message || 'Failed to update coupon' 
      }
    }
  }

  const deactivateCoupon = async (couponId: string, reason?: string) => {
    try {
      const user = useSupabaseUser()
      
      if (!user.value) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await (supabase as any)
        .from('coupons')
        .update({
          is_active: false,
          deactivated_by: user.value.id,
          deactivated_at: new Date().toISOString(),
          deactivation_reason: reason
        })
        .eq('id', couponId)
        .select()
        .single()

      if (error) throw error

      return { success: true, data, error: null }
    } catch (error: any) {
      return { 
        success: false, 
        data: null, 
        error: error.message || 'Failed to deactivate coupon' 
      }
    }
  }

  const getAllCoupons = async () => {
    try {
      const { data, error } = await supabase
        .from('coupons')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      return { 
        data: null, 
        error: error.message || 'Failed to fetch coupons' 
      }
    }
  }

  const getCouponAnalytics = async (couponId?: string) => {
    try {
      let query = supabase
        .from('coupon_usage')
        .select(`
          *,
          coupon:coupons(code, name),
          school:schools(name, code)
        `)

      if (couponId) {
        query = query.eq('coupon_id', couponId)
      }

      const { data, error } = await query
        .order('used_at', { ascending: false })

      if (error) throw error

      return { data, error: null }
    } catch (error: any) {
      return { 
        data: null, 
        error: error.message || 'Failed to fetch coupon analytics' 
      }
    }
  }

  return {
    // State
    isValidating: readonly(isValidating),
    validationError: readonly(validationError),

    // Methods
    validateCoupon,
    recordCouponUsage,
    getCouponUsageHistory,

    // Admin methods
    createCoupon,
    updateCoupon,
    deactivateCoupon,
    getAllCoupons,
    getCouponAnalytics
  }
}
