import { defineEventHand<PERSON>, readBody } from "h3";

export default defineEventHandler(async (event) => {
  if (event.node.req.method !== "POST") {
    event.node.res.statusCode = 405;
    return { error: "Method Not Allowed" };
  }

  // Simulate reading the body (though we won't process the file here)
  // In a real scenario, you'd handle multipart/form-data here
  // const body = await readBody(event);

  // Simulate upload processing time (e.g., 1 to 3 seconds)
  const delay = Math.random() * 2000 + 1000;
  await new Promise((resolve) => setTimeout(resolve, delay));

  // Simulate a successful response
  event.node.res.statusCode = 200;
  return {
    success: true,
    message: 'File "uploaded" successfully to mock endpoint!',
    // You could return a fake file URL if needed
    // fileUrl: `/uploads/mock-${Date.now()}.jpg`,
  };
});
