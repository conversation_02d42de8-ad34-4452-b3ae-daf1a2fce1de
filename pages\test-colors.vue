<template>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    Subject Color Mapping Test
                </h1>
                <p class="text-gray-600 dark:text-gray-400">
                    Testing comprehensive color mapping for all 25 Malaysian subjects
                </p>
                <div class="mt-4 flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Predefined Colors: {{ predefinedCount
                            }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Hash-Generated: {{ hashGeneratedCount
                            }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span class="text-sm text-gray-600 dark:text-gray-400">Coverage: {{ coveragePercentage
                            }}%</span>
                    </div>
                </div>
            </div>

            <!-- Color Mapping Results -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="subject in processedSubjects" :key="subject.id"
                    class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                    <div class="p-6">
                        <!-- Subject Header -->
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ subject.name }}
                            </h3>
                            <div class="flex items-center space-x-2">
                                <div
                                    :class="['w-4 h-4 rounded-full', subject.colorResult.isPredefined ? 'bg-green-500' : 'bg-red-500']">
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ subject.colorResult.isPredefined ? 'Predefined' : 'Hash-Generated' }}
                                </span>
                            </div>
                        </div>

                        <!-- Color Preview -->
                        <div class="mb-4">
                            <div
                                :class="['w-full h-16 rounded-lg flex items-center justify-center', subject.colorResult.color.bg_color]">
                                <span :class="['text-sm font-medium', subject.colorResult.color.text_color]">
                                    {{ subject.colorResult.color.subject_name }}
                                </span>
                            </div>
                        </div>

                        <!-- Color Details -->
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Color Key:</span>
                                <span class="text-gray-900 dark:text-white font-mono">{{ subject.colorResult.colorKey ||
                                    'hash-generated' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Background:</span>
                                <span class="text-gray-900 dark:text-white font-mono">{{
                                    subject.colorResult.color.bg_color }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Text:</span>
                                <span class="text-gray-900 dark:text-white font-mono">{{
                                    subject.colorResult.color.text_color }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Category:</span>
                                <span class="text-gray-900 dark:text-white">{{ subject.category }}</span>
                            </div>
                        </div>

                        <!-- Matching Keywords -->
                        <div v-if="subject.colorResult.matchedKeywords.length > 0" class="mt-4">
                            <span class="text-xs text-gray-500 dark:text-gray-400 mb-2 block">Matched Keywords:</span>
                            <div class="flex flex-wrap gap-1">
                                <span v-for="keyword in subject.colorResult.matchedKeywords" :key="keyword"
                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                    {{ keyword }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    Color Mapping Summary
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ testSubjects.length }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Total Subjects</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ predefinedCount }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Predefined Colors</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-red-600 dark:text-red-400">{{ hashGeneratedCount }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Hash-Generated</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ coveragePercentage }}%
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Coverage</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { SUBJECT_COLORS, DYNAMIC_COLORS } from '~/types/timetable'
import type { SubjectColor } from '~/types/timetable'

// Test subjects based on the 25 Malaysian subjects from the SQL data
const testSubjects = ref([
    { id: '1', name: 'Bahasa Melayu', category: 'Languages' },
    { id: '2', name: 'Bahasa Inggeris', category: 'Languages' },
    { id: '3', name: 'Matematik', category: 'STEM Sciences' },
    { id: '4', name: 'Matematik Tambahan', category: 'STEM Sciences' },
    { id: '5', name: 'Sejarah', category: 'Social Sciences' },
    { id: '6', name: 'Geografi', category: 'Social Sciences' },
    { id: '7', name: 'Ekonomi', category: 'Social Sciences' },
    { id: '8', name: 'Pendidikan Islam', category: 'Social Sciences' },
    { id: '9', name: 'Pendidikan Moral', category: 'Arts & Physical' },
    { id: '10', name: 'Sains', category: 'STEM Sciences' },
    { id: '11', name: 'Fizik', category: 'STEM Sciences' },
    { id: '12', name: 'Kimia', category: 'STEM Sciences' },
    { id: '13', name: 'Biologi', category: 'STEM Sciences' },
    { id: '14', name: 'Pendidikan Jasmani dan Kesihatan', category: 'Arts & Physical' },
    { id: '15', name: 'Pendidikan Seni Visual', category: 'Arts & Physical' },
    { id: '16', name: 'Pendidikan Muzik', category: 'Arts & Physical' },
    { id: '17', name: 'Bahasa Arab', category: 'Languages' },
    { id: '18', name: 'Bahasa Cina', category: 'Languages' },
    { id: '19', name: 'Bahasa Tamil', category: 'Languages' },
    { id: '20', name: 'Bahasa Kadazandusun', category: 'Languages' },
    { id: '21', name: 'Bahasa Iban', category: 'Languages' },
    { id: '22', name: 'Bahasa Semai', category: 'Languages' },
    { id: '23', name: 'Perniagaan', category: 'Business & Technology' },
    { id: '24', name: 'Prinsip Perakaunan', category: 'Business & Technology' },
    { id: '25', name: 'Reka Bentuk dan Teknologi', category: 'Business & Technology' }
])

// Enhanced subject name to color key mapping (same as in TimetableView.vue)
const SUBJECT_MAPPING = {
    // STEM Sciences - Exact matches first
    'matematik tambahan': 'additional_mathematics',
    'addmath': 'additional_mathematics',
    'matematik': 'mathematics',
    'math': 'mathematics',
    'fizik': 'physics',
    'physics': 'physics',
    'kimia': 'chemistry',
    'chemistry': 'chemistry',
    'biologi': 'biology',
    'biology': 'biology',
    'sains': 'science',
    'science': 'science',

    // Languages - Specific before generic to avoid conflicts
    'bahasa melayu': 'malay',
    'melayu': 'malay',
    'bahasa inggeris': 'english',
    'inggeris': 'english',
    'english': 'english',
    'bahasa arab': 'arabic',
    'arab': 'arabic',
    'bahasa kadazandusun': 'kadazandusun',
    'kadazandusun': 'kadazandusun',
    'bkd': 'kadazandusun',
    'bahasa semai': 'semai',
    'semai': 'semai',
    'bs': 'semai',
    'bahasa iban': 'iban',
    'iban': 'iban',
    'bib': 'iban',
    'bahasa cina': 'chinese',
    'cina': 'chinese',
    'bc': 'chinese',
    'bahasa tamil': 'tamil',
    'tamil': 'tamil',
    'bt': 'tamil',

    // Social Sciences
    'sejarah': 'history',
    'history': 'history',
    'geografi': 'geography',
    'geography': 'geography',
    'ekonomi': 'economics',
    'economics': 'economics',
    'pendidikan islam': 'islamic',
    'islam': 'islamic',
    'agama': 'islamic',
    'pi': 'islamic',

    // Arts & Physical - Specific before generic to resolve conflicts
    'pendidikan seni visual': 'visual_arts',
    'seni visual': 'visual_arts',
    'psv': 'visual_arts',
    'pendidikan muzik': 'music',
    'muzik': 'music',
    'music': 'music',
    'pendidikan jasmani dan kesihatan': 'physical',
    'pendidikan jasmani': 'physical',
    'jasmani': 'physical',
    'sukan': 'physical',
    'physical': 'physical',
    'pjk': 'physical',
    'pendidikan moral': 'moral',
    'moral': 'moral',
    'pm': 'moral',

    // Business & Technology
    'perniagaan': 'business',
    'niaga': 'business',
    'business': 'business',
    'prinsip perakaunan': 'accounting',
    'perakaunan': 'accounting',
    'akaun': 'accounting',
    'accounting': 'accounting',
    'reka bentuk dan teknologi': 'design_technology',
    'reka bentuk': 'design_technology',
    'rbt': 'design_technology',
    'design': 'design_technology',
    'teknologi': 'design_technology',

    // Legacy mappings (kept for backward compatibility)
    'seni': 'art',
    'art': 'art',
    'bahasa': 'malay' // Generic fallback - should be overridden by specific matches above
} as const

// Enhanced getSubjectColor function (same logic as in TimetableView.vue)
const getSubjectColor = (subjectId: string, subjectName: string): { color: SubjectColor, isPredefined: boolean, colorKey?: string, matchedKeywords: string[] } => {
    // Normalize subject name for consistent matching
    const normalizedName = subjectName.toLowerCase().trim()
    const matchedKeywords: string[] = []

    // PHASE 1: Exact matches first (highest priority)
    for (const [keyword, colorKey] of Object.entries(SUBJECT_MAPPING)) {
        if (normalizedName === keyword) {
            if (SUBJECT_COLORS[colorKey]) {
                return {
                    color: SUBJECT_COLORS[colorKey],
                    isPredefined: true,
                    colorKey,
                    matchedKeywords: [keyword]
                }
            }
        }
    }

    // PHASE 2: Priority-ordered partial matches (medium priority)
    const sortedKeywords = Object.entries(SUBJECT_MAPPING)
        .sort(([a], [b]) => b.length - a.length)

    for (const [keyword, colorKey] of sortedKeywords) {
        if (normalizedName.includes(keyword)) {
            // Enhanced conflict resolution logic

            // Skip generic 'sains' if specific sciences are present
            if (keyword === 'sains' &&
                (normalizedName.includes('kimia') || normalizedName.includes('biologi') ||
                    normalizedName.includes('fizik') || normalizedName.includes('matematik'))) {
                continue
            }

            // Skip generic 'bahasa' if specific languages are present
            if (keyword === 'bahasa' &&
                (normalizedName.includes('inggeris') || normalizedName.includes('arab') ||
                    normalizedName.includes('melayu') || normalizedName.includes('cina') ||
                    normalizedName.includes('tamil') || normalizedName.includes('kadazandusun') ||
                    normalizedName.includes('semai') || normalizedName.includes('iban'))) {
                continue
            }

            // Skip generic 'seni' if specific arts are present
            if (keyword === 'seni' &&
                (normalizedName.includes('visual') || normalizedName.includes('muzik'))) {
                continue
            }

            // Skip generic 'pendidikan' if more specific education subjects are present
            if (keyword === 'pendidikan' &&
                (normalizedName.includes('jasmani') || normalizedName.includes('islam') ||
                    normalizedName.includes('moral') || normalizedName.includes('seni') ||
                    normalizedName.includes('muzik'))) {
                continue
            }

            if (SUBJECT_COLORS[colorKey]) {
                matchedKeywords.push(keyword)
                return {
                    color: SUBJECT_COLORS[colorKey],
                    isPredefined: true,
                    colorKey,
                    matchedKeywords
                }
            }
        }
    }

    // PHASE 3: Fallback to hash-generated color
    const hashColor = generateUniqueColor(subjectId, subjectName)
    return {
        color: hashColor,
        isPredefined: false,
        matchedKeywords: []
    }
}

// Generate a unique but consistent color for subjects not in predefined categories
const generateUniqueColor = (subjectId: string, subjectName: string): SubjectColor => {
    // Create a simple hash from the subject ID for consistency
    let hash = 0
    for (let i = 0; i < subjectId.length; i++) {
        const char = subjectId.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // Convert to 32bit integer
    }

    // Use absolute value and modulo to get a color index
    const colorIndex = Math.abs(hash) % DYNAMIC_COLORS.length
    const dynamicColor = DYNAMIC_COLORS[colorIndex]

    return {
        subject_id: subjectId,
        subject_name: subjectName,
        color: dynamicColor.color,
        bg_color: dynamicColor.bg_color,
        text_color: dynamicColor.text_color
    }
}

// Process all test subjects and get their color mappings
const processedSubjects = computed(() => {
    return testSubjects.value.map(subject => ({
        ...subject,
        colorResult: getSubjectColor(subject.id, subject.name)
    }))
})

// Statistics
const predefinedCount = computed(() =>
    processedSubjects.value.filter(s => s.colorResult.isPredefined).length
)

const hashGeneratedCount = computed(() =>
    processedSubjects.value.filter(s => !s.colorResult.isPredefined).length
)

const coveragePercentage = computed(() =>
    Math.round((predefinedCount.value / testSubjects.value.length) * 100)
)

// Set page metadata
definePageMeta({
    layout: 'blank'
})
</script>