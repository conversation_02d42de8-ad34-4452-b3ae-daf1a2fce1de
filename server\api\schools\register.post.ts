// School registration API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'
import type { SchoolRegistrationForm, SchoolRegistrationResponse } from '~/types/multiTenant'

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event)
    const { schoolData, couponCode, adminUserId } = body

    // Validate required fields
    if (!schoolData?.name || !schoolData?.code || !adminUserId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: name, code, or adminUserId'
      })
    }

    // Initialize Supabase client with service role key for admin operations
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Start transaction-like operations
    let school = null
    let membership = null
    let couponUsage = null

    try {
      // 1. Validate coupon if provided
      if (couponCode) {
        const { data: isValid, error: couponError } = await supabase
          .rpc('is_coupon_valid', { coupon_code: couponCode.toUpperCase() })

        if (couponError || !isValid) {
          throw createError({
            statusCode: 400,
            statusMessage: 'Invalid or expired coupon code'
          })
        }
      }

      // 2. Check if school code is already taken
      const { data: existingSchool, error: checkError } = await supabase
        .from('schools')
        .select('id')
        .eq('code', schoolData.code.toLowerCase())
        .single()

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw createError({
          statusCode: 500,
          statusMessage: 'Error checking school code availability'
        })
      }

      if (existingSchool) {
        throw createError({
          statusCode: 409,
          statusMessage: 'School code is already taken'
        })
      }

      // 3. Create the school
      const { data: newSchool, error: schoolError } = await supabase
        .from('schools')
        .insert({
          name: schoolData.name,
          code: schoolData.code.toLowerCase(),
          admin_user_id: adminUserId,
          description: schoolData.description || null,
          contact_email: schoolData.contactEmail || null,
          contact_phone: schoolData.contactPhone || null,
          address: schoolData.address || null,
          subscription_status: couponCode ? 'active' : 'trial',
          subscription_expires_at: couponCode ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days trial
        })
        .select()
        .single()

      if (schoolError) {
        throw createError({
          statusCode: 500,
          statusMessage: `Failed to create school: ${schoolError.message}`
        })
      }

      school = newSchool

      // 4. Create admin membership
      const { data: newMembership, error: membershipError } = await supabase
        .from('school_memberships')
        .insert({
          user_id: adminUserId,
          school_id: school.id,
          role: 'admin',
          status: 'active'
        })
        .select()
        .single()

      if (membershipError) {
        // Rollback: Delete the school
        await supabase.from('schools').delete().eq('id', school.id)
        
        throw createError({
          statusCode: 500,
          statusMessage: `Failed to create admin membership: ${membershipError.message}`
        })
      }

      membership = newMembership

      // 5. Record coupon usage if coupon was provided
      if (couponCode) {
        try {
          const { data: usageId, error: usageError } = await supabase
            .rpc('record_coupon_usage', {
              p_coupon_code: couponCode.toUpperCase(),
              p_school_id: school.id,
              p_used_by: adminUserId,
              p_usage_type: 'school_registration'
            })

          if (usageError) {
            console.error('Failed to record coupon usage:', usageError)
            // Don't fail the registration for coupon usage errors
          } else {
            couponUsage = { id: usageId }
          }
        } catch (error) {
          console.error('Error recording coupon usage:', error)
          // Continue without failing
        }
      }

      // 6. Generate school URL
      const config = useRuntimeConfig()
      const schoolUrl = `${school.code}.${config.public.baseDomain}`

      // Return success response
      const response: SchoolRegistrationResponse = {
        success: true,
        school,
        membership,
        schoolUrl: `https://${schoolUrl}`,
        ...(couponUsage && { couponUsage })
      }

      return response

    } catch (error: any) {
      // If we created a school but failed later, clean up
      if (school?.id) {
        try {
          await supabase.from('schools').delete().eq('id', school.id)
        } catch (cleanupError) {
          console.error('Failed to cleanup school after error:', cleanupError)
        }
      }
      
      throw error
    }

  } catch (error: any) {
    console.error('School registration error:', error)
    
    // Handle different error types
    if (error.statusCode) {
      throw error // Re-throw HTTP errors
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Internal server error during school registration'
    })
  }
})
