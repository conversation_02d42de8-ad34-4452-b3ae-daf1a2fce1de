// DEPRECATED: This composable has been split into smaller, focused composables
// Use the new composables for better maintainability:
// - useReflectionPeriods: For period reflection CRUD operations
// - useReflectionScheduleOptions: For class-subject and day options
// - useReflectionSync: For reflection synchronization
// - useReflectionOptions: For tindakan susulan and tidak terlaksana options

import { computed } from 'vue';
import { useReflectionPeriods } from './useReflectionPeriods';
import { useReflectionScheduleOptions } from './useReflectionScheduleOptions';
import { useReflectionSync } from './useReflectionSync';
import type {
  DetailedReflectionFormData,
  ClassSubjectOption,
  DayOption
} from '~/types/reflections';

// Note: Types are now exported from useReflectionPeriods.ts
// Import them directly from there to avoid duplication warnings

/**
 * @deprecated This composable has been split into smaller, focused composables.
 * Use the new composables for better maintainability:
 * - useReflectionPeriods: For period reflection CRUD operations
 * - useReflectionScheduleOptions: For class-subject and day options
 * - useReflectionSync: For reflection synchronization
 * - useReflectionOptions: For tindakan susulan and tidak terlaksana options
 *
 * This composable is maintained for backward compatibility.
 */
export const useDetailedReflections = () => {
  // Use the new split composables
  const reflectionPeriods = useReflectionPeriods();
  const scheduleOptions = useReflectionScheduleOptions();
  const reflectionSync = useReflectionSync();

  // Backward compatibility: delegate to new composables
  return {
    // State - delegate to reflection periods composable
    loading: reflectionPeriods.loading,
    error: computed(() =>
      reflectionPeriods.error.value ||
      scheduleOptions.error.value ||
      reflectionSync.error.value
    ),

    // Methods - delegate to appropriate composables
    createDetailedReflection: reflectionSync.createDetailedReflection,
    fetchDetailedReflections: reflectionPeriods.fetchDetailedReflections,
    getPeriodReflection: reflectionPeriods.getPeriodReflection,
    updatePeriodReflection: reflectionPeriods.updatePeriodReflection,
    updateDetailedReflection: reflectionPeriods.updateDetailedReflection,
    getClassSubjectOptions: scheduleOptions.getClassSubjectOptions,
    getDayOptions: scheduleOptions.getDayOptions,
    syncReflectionsWithLessonPlan: reflectionSync.syncReflectionsWithLessonPlan,

    // Helpers - delegate to reflection periods composable
    createPeriodKey: reflectionPeriods.createPeriodKey,
    createDefaultReflectionData: reflectionPeriods.createDefaultReflectionData
  };
};
