-- Migration: Create coupons table for school registration discounts
-- Created: 2025-07-13
-- Description: Manage coupon codes for free school registration with usage tracking and expiry

BEGIN;

-- =====================================================
-- CREATE COUPONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS coupons (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Coupon basic information
    code TEXT NOT NULL,
    name TEXT, -- Display name for admin purposes
    description TEXT, -- Description of what this coupon provides
    
    -- Coupon status and limits
    is_active BOOLEAN DEFAULT true,
    usage_limit INTEGER DEFAULT NULL, -- NULL = unlimited usage
    used_count INTEGER DEFAULT 0,
    
    -- Validity period
    expires_at TIMESTAMP WITH TIME ZONE,
    starts_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
    
    -- Coupon value and type
    discount_type TEXT DEFAULT 'free_registration' CHECK (discount_type IN ('free_registration', 'percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) DEFAULT 0, -- For percentage or fixed amount discounts
    
    -- Admin management
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    deactivated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    deactivation_reason TEXT,
    
    -- Additional metadata
    notes TEXT, -- Admin notes
    metadata JSONB DEFAULT '{}'::jsonb, -- Additional coupon data
    
    -- Constraints
    CONSTRAINT coupons_code_unique UNIQUE (code),
    CONSTRAINT coupons_code_not_empty CHECK (char_length(code) > 0),
    CONSTRAINT coupons_code_format CHECK (code ~ '^[A-Z0-9]+$'), -- Only uppercase alphanumeric
    CONSTRAINT coupons_usage_limit_positive CHECK (usage_limit IS NULL OR usage_limit > 0),
    CONSTRAINT coupons_used_count_non_negative CHECK (used_count >= 0),
    CONSTRAINT coupons_discount_value_non_negative CHECK (discount_value >= 0)
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================

-- Index for coupon code lookups (most common query)
CREATE UNIQUE INDEX IF NOT EXISTS idx_coupons_code_upper ON coupons (UPPER(code));

-- Index for active coupon queries
CREATE INDEX IF NOT EXISTS idx_coupons_is_active ON coupons (is_active);

-- Index for expiry checks
CREATE INDEX IF NOT EXISTS idx_coupons_expires_at ON coupons (expires_at) 
WHERE expires_at IS NOT NULL;

-- Index for validity period queries
CREATE INDEX IF NOT EXISTS idx_coupons_validity_period ON coupons (starts_at, expires_at);

-- Index for usage tracking
CREATE INDEX IF NOT EXISTS idx_coupons_usage ON coupons (used_count, usage_limit);

-- Index for admin queries
CREATE INDEX IF NOT EXISTS idx_coupons_created_by ON coupons (created_by);

-- GIN index for metadata JSONB column
CREATE INDEX IF NOT EXISTS idx_coupons_metadata ON coupons USING GIN (metadata);

-- =====================================================
-- CREATE TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_coupons_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row changes
DROP TRIGGER IF EXISTS trigger_coupons_updated_at ON coupons;
CREATE TRIGGER trigger_coupons_updated_at
    BEFORE UPDATE ON coupons
    FOR EACH ROW
    EXECUTE FUNCTION update_coupons_updated_at();

-- =====================================================
-- CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to check if a coupon is valid
CREATE OR REPLACE FUNCTION is_coupon_valid(coupon_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    coupon_record RECORD;
    current_time TIMESTAMP WITH TIME ZONE := timezone('utc'::text, now());
BEGIN
    -- Get coupon details
    SELECT * INTO coupon_record 
    FROM coupons 
    WHERE UPPER(code) = UPPER(coupon_code);
    
    -- Check if coupon exists
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if coupon is active
    IF NOT coupon_record.is_active THEN
        RETURN FALSE;
    END IF;
    
    -- Check if coupon has started
    IF coupon_record.starts_at > current_time THEN
        RETURN FALSE;
    END IF;
    
    -- Check if coupon has expired
    IF coupon_record.expires_at IS NOT NULL AND coupon_record.expires_at < current_time THEN
        RETURN FALSE;
    END IF;
    
    -- Check usage limit
    IF coupon_record.usage_limit IS NOT NULL AND coupon_record.used_count >= coupon_record.usage_limit THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE coupons IS 'Coupon codes for school registration discounts and promotions';
COMMENT ON COLUMN coupons.id IS 'Unique identifier for the coupon';
COMMENT ON COLUMN coupons.code IS 'Unique coupon code (case-insensitive)';
COMMENT ON COLUMN coupons.name IS 'Display name for admin purposes';
COMMENT ON COLUMN coupons.is_active IS 'Whether the coupon is currently active';
COMMENT ON COLUMN coupons.usage_limit IS 'Maximum number of times this coupon can be used (NULL = unlimited)';
COMMENT ON COLUMN coupons.used_count IS 'Number of times this coupon has been used';
COMMENT ON COLUMN coupons.expires_at IS 'When the coupon expires (NULL = never expires)';
COMMENT ON COLUMN coupons.discount_type IS 'Type of discount (free_registration, percentage, fixed_amount)';
COMMENT ON COLUMN coupons.discount_value IS 'Discount value (percentage or fixed amount)';
COMMENT ON COLUMN coupons.metadata IS 'Additional coupon data in JSON format';

COMMENT ON FUNCTION is_coupon_valid(TEXT) IS 'Check if a coupon code is valid and can be used';

-- =====================================================
-- CREATE RLS POLICIES
-- =====================================================

-- Enable RLS on coupons table
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;

-- Policy: Only authenticated users can view active coupons (for validation)
CREATE POLICY "Users can view active coupons for validation" ON coupons
    FOR SELECT USING (is_active = true);

-- Policy: Only super admins can manage coupons (will be refined later with proper admin roles)
CREATE POLICY "Super admins can manage coupons" ON coupons
    FOR ALL USING (
        auth.uid() IN (
            SELECT id FROM auth.users 
            WHERE email IN ('<EMAIL>') -- Replace with actual admin emails
        )
    );

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 'Coupons table created successfully' as status;

-- Show table structure
\d coupons;

-- Test the validation function
SELECT 'Coupon validation function created' as status;
