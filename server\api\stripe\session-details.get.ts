// Get Stripe session details for success page
import Stripe from 'stripe'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const sessionId = query.session_id as string

    console.log('🔍 Session details API called with sessionId:', sessionId)

    if (!sessionId) {
      console.error('❌ No session ID provided')
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      })
    }

    // Initialize Stripe
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY
    if (!stripeSecretKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe configuration missing'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    console.log('📡 Retrieving Stripe session...')

    // Retrieve the checkout session
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['customer', 'subscription']
    })

    console.log('📦 Stripe session retrieved:', {
      id: session.id,
      status: session.status,
      payment_status: session.payment_status,
      metadata: session.metadata
    })

    if (!session) {
      console.error('❌ Session not found')
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found'
      })
    }

    // Extract school information from metadata
    const metadata = session.metadata || {}
    console.log('📋 Session metadata:', metadata)
    
    return {
      success: true,
      session: {
        id: session.id,
        status: session.status,
        payment_status: session.payment_status,
        customer_email: session.customer_details?.email,
        amount_total: session.amount_total,
        currency: session.currency
      },
      school: {
        name: metadata.schoolName || 'Unknown School',
        code: metadata.schoolCode || 'unknown',
        plan: metadata.selectedPlan || 'professional',
        adminEmail: metadata.adminEmail || session.customer_details?.email,
        adminName: `${metadata.adminFirstName || ''} ${metadata.adminLastName || ''}`.trim()
      },
      subscription: {
        status: session.subscription ? 'active' : 'trialing',
        trial_end: session.subscription ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    }

  } catch (error: any) {
    console.error('Error retrieving session details:', error)
    
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to retrieve session details'
    })
  }
})
