<template>
  <Modal
    :is-open="isOpen"
    :title="`Pratonton Template: ${template?.name || ''}`"
    @update:is-open="$emit('update:is-open', $event)"
    size="lg"
  >
    <div v-if="template" class="space-y-6">
      <!-- Template Information -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
              {{ template.name }}
            </h3>
            <p v-if="template.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {{ template.description }}
            </p>
            <div class="flex items-center space-x-3 mt-3">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {{ getCategoryLabel(template.category) }}
              </span>
              <span v-if="!template.is_system_template" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Template Peribadi
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Prompts Section -->
      <div>
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Soalan Panduan ({{ Object.keys(template.prompts).length }})
        </h4>
        
        <div v-if="Object.keys(template.prompts).length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <Icon name="heroicons:chat-bubble-left-right" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p class="text-gray-500 dark:text-gray-400">
            Tiada soalan panduan dalam template ini.
          </p>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="(prompt, field, index) in template.prompts"
            :key="field"
            class="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
          >
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-medium">
                {{ index + 1 }}
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {{ getFieldLabel(field) }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ prompt }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Default Values Section -->
      <div v-if="hasDefaultValues">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Nilai Lalai
        </h4>
        
        <div class="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="(value, field) in template.default_values" :key="field" class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {{ getFieldLabel(field) }}:
              </span>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ formatDefaultValue(field, value) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Usage Statistics (for user templates) -->
      <div v-if="!template.is_system_template" class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
          Statistik Penggunaan
        </h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ template.usage_count || 0 }}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              Kali Digunakan
            </div>
          </div>
          <div class="text-center">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ formatDate(template.created_at) }}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              Tarikh Dicipta
            </div>
          </div>
        </div>
      </div>

      <!-- Template Preview Form -->
      <div>
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Pratonton Borang
        </h4>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Ini adalah pratonton bagaimana template akan kelihatan dalam borang refleksi:
          </p>
          
          <!-- Mock form fields -->
          <div class="space-y-4">
            <div v-for="(prompt, field) in template.prompts" :key="field">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ getFieldLabel(field) }}
              </label>
              <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                {{ prompt }}
              </div>
              <textarea
                :placeholder="`Jawapan untuk ${getFieldLabel(field).toLowerCase()}...`"
                rows="2"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                disabled
              ></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <UiBaseButton
          @click="$emit('update:is-open', false)"
          variant="outline"
        >
          Tutup
        </UiBaseButton>
        <UiBaseButton
          v-if="template && !template.is_system_template"
          @click="editTemplate"
          variant="primary"
          prepend-icon="heroicons:pencil"
        >
          Edit Template
        </UiBaseButton>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import type { ReflectionTemplate } from '~/types/reflections';
import Modal from '~/components/ui/composite/Modal.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';

interface Props {
  isOpen: boolean;
  template?: ReflectionTemplate | null;
}

interface Emits {
  (e: 'update:is-open', value: boolean): void;
  (e: 'edit-template', template: ReflectionTemplate): void;
}

const props = withDefaults(defineProps<Props>(), {
  template: null
});

const emit = defineEmits<Emits>();

// Field labels for display
const fieldLabels: Record<string, string> = {
  challenges_faced: 'Cabaran Dihadapi',
  successful_strategies: 'Strategi Berjaya',
  improvements_needed: 'Penambahbaikan Diperlukan',
  additional_notes: 'Catatan Tambahan',
  overall_rating: 'Penilaian Keseluruhan',
  objectives_achieved: 'Objektif Tercapai',
  activity_effectiveness: 'Keberkesanan Aktiviti',
  time_management: 'Pengurusan Masa',
  student_engagement: 'Penglibatan Pelajar',
  resource_adequacy: 'Kecukupan Sumber'
};

// Computed
const hasDefaultValues = computed(() => {
  return props.template && Object.keys(props.template.default_values).length > 0;
});

// Methods
const getCategoryLabel = (category: string): string => {
  return TEMPLATE_CATEGORY_LABELS[category as keyof typeof TEMPLATE_CATEGORY_LABELS] || category;
};

const getFieldLabel = (field: string): string => {
  return fieldLabels[field] || field;
};

const formatDefaultValue = (field: string, value: any): string => {
  if (typeof value === 'boolean') {
    return value ? 'Ya' : 'Tidak';
  }
  if (typeof value === 'number') {
    return value.toString();
  }
  if (typeof value === 'string') {
    // Format specific field values
    if (field === 'time_management') {
      const timeLabels: Record<string, string> = {
        'on_time': 'Tepat Masa',
        'early': 'Awal',
        'late': 'Lewat',
        'not_applicable': 'Tidak Berkenaan'
      };
      return timeLabels[value] || value;
    }
    if (field === 'resource_adequacy') {
      const resourceLabels: Record<string, string> = {
        'inadequate': 'Tidak Mencukupi',
        'adequate': 'Mencukupi',
        'excellent': 'Sangat Baik',
        'not_applicable': 'Tidak Berkenaan'
      };
      return resourceLabels[value] || value;
    }
    return value;
  }
  return String(value);
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('ms-MY', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const editTemplate = () => {
  if (props.template) {
    emit('edit-template', props.template);
    emit('update:is-open', false);
  }
};
</script>
