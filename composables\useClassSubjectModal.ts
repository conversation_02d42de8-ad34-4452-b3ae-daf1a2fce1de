import { ref, computed, nextTick } from 'vue'
import { useSubjects } from '~/composables/useSubjects'

// Types
interface ClassSubjectFormData {
  id: number | null
  class: string | null
  className: string
  studentCount: number | null
  subject: string | null
  subjectAbbreviation: string
}

interface FormOption {
  label: string
  value: string
  code?: string
  disabled?: boolean
}

interface Subject {
  id: string
  name: string
  code: string
  category?: string
  level_type?: string
  sub_level?: string
  sort_order?: number
  is_active?: boolean
  is_custom?: boolean
}

export function useClassSubjectModal() {
  // Constants
  const ADD_NEW_SUBJECT_VALUE = 'add_new_subject_option_value'

  // Modal and Form State
  const isModalOpen = ref(false)
  const editingId = ref<number | null>(null)
  const isEditing = ref(false)
  const selectedLevelType = ref<'Tahun' | 'Tingkatan' | null>(null)

  const formData = ref<ClassSubjectFormData>({
    id: null,
    class: null,
    className: '',
    studentCount: null,
    subject: null,
    subjectAbbreviation: ''
  })

  const formErrors = ref<Record<string, string[]> | null>(null)
  const showNewSubjectInput = ref(false)
  const newSubjectNameFromInput = ref('')
  const selectedExistingClassName = ref<string | null>(null)

  // Store session-only subjects locally
  const sessionOnlySubjects = ref<Subject[]>([])

  // Get subjects from composable
  const { subjects, loading: subjectsLoading, fetchSubjects, addSubject: addSubjectToDb } = useSubjects()

  // Class options arrays (EXACT format from actual data)
  const tahunOptions = [
    { label: 'Tahun 1', value: 't1' },
    { label: 'Tahun 2', value: 't2' },
    { label: 'Tahun 3', value: 't3' },
    { label: 'Tahun 4', value: 't4' },
    { label: 'Tahun 5', value: 't5' },
    { label: 'Tahun 6', value: 't6' }
  ]

  const tingkatanOptions = [
    { label: 'Tingkatan 1', value: 'f1' },
    { label: 'Tingkatan 2', value: 'f2' },
    { label: 'Tingkatan 3', value: 'f3' },
    { label: 'Tingkatan 4', value: 'f4' },
    { label: 'Tingkatan 5', value: 'f5' }
  ]

  // Category labels for better UX
  const CATEGORY_LABELS: Record<string, string> = {
    'teras': 'Teras',
    'tambahan': 'Tambahan',
    'pilihan': 'Pilihan',
    'elektif_sains': 'Elektif Sains',
    'elektif_sastera': 'Elektif Sastera'
  }

  // Helper function to get class level from class value
  const getClassLevel = (classValue: string): string | null => {
    if (!classValue) return null
    if (classValue.startsWith('t')) return 'tahun'
    if (classValue.startsWith('f')) {
      const level = parseInt(classValue.substring(1))
      if (level >= 1 && level <= 3) return 'lower'
      if (level >= 4 && level <= 5) return 'upper'
    }
    return null
  }

  // Computed Properties
  const classOptions = computed<FormOption[]>(() => {
    if (selectedLevelType.value === 'Tahun') return tahunOptions
    if (selectedLevelType.value === 'Tingkatan') return tingkatanOptions
    return []
  })

  const computedSubjectOptions = computed<FormOption[]>(() => {
    // Combine subjects from DB and session-only subjects
    let combinedSubjects = [...subjects.value, ...sessionOnlySubjects.value]

    // Filter subjects based on selected level and class
    if (selectedLevelType.value && formData.value.class) {
      const levelType = selectedLevelType.value.toLowerCase()
      const classLevel = getClassLevel(formData.value.class)

      combinedSubjects = combinedSubjects.filter(subject => {
        // Include custom subjects
        if (subject.is_custom) return true

        // Filter by level_type
        if (subject.level_type !== levelType && subject.level_type !== 'both') return false

        // For tingkatan, also filter by sub_level
        if (levelType === 'tingkatan' && classLevel && subject.sub_level) {
          if (subject.sub_level !== 'all' && subject.sub_level !== classLevel) return false
        }

        return subject.is_active !== false // Include if is_active is true or undefined
      })
    }

    // Group subjects by category and sort
    const groupedSubjects = combinedSubjects.reduce((groups, subject) => {
      const category = subject.category || 'lain-lain'
      if (!groups[category]) groups[category] = []
      groups[category].push(subject)
      return groups
    }, {} as Record<string, typeof combinedSubjects>)

    // Sort subjects within each category by sort_order then name
    Object.keys(groupedSubjects).forEach(category => {
      groupedSubjects[category].sort((a, b) => {
        if (a.sort_order !== b.sort_order) {
          return (a.sort_order || 999) - (b.sort_order || 999)
        }
        return a.name.localeCompare(b.name)
      })
    })

    // Create options with category headers
    const options: FormOption[] = []
    const categoryOrder = ['teras', 'tambahan', 'pilihan', 'elektif_sains', 'elektif_sastera', 'lain-lain']

    categoryOrder.forEach(category => {
      if (groupedSubjects[category] && groupedSubjects[category].length > 0) {
        // Add category header (disabled option)
        options.push({
          value: `header_${category}`,
          label: `--- ${CATEGORY_LABELS[category] || category.toUpperCase()} ---`,
          code: '',
          disabled: true
        })

        // Add subjects in this category
        groupedSubjects[category].forEach(subject => {
          options.push({
            value: subject.id,
            label: subject.name,
            code: subject.code
          })
        })
      }
    })

    // Add "Tambah Subjek Baru..." option at the end
    if (options.length > 0) {
      options.push({ value: ADD_NEW_SUBJECT_VALUE, label: '+ Tambah Subjek Baru...', code: '' })
    }

    return options
  })

  const isFormDisabled = computed(() => !selectedLevelType.value || subjectsLoading.value)

  const modalTitle = computed(() => {
    if (!selectedLevelType.value) return 'Sila Pilih Tahun/Tingkatan Dahulu'
    return isEditing.value ? 'Kemaskini Kelas & Subjek' : 'Tambah Kelas & Subjek'
  })

  // Helper function to get existing class options
  const getExistingClassOptions = (classSubjects: any[]) => {
    if (!formData.value.class) return []
    const selectedClassValue = formData.value.class
    // Filter unique class names for the selected class level from the current list
    const uniqueClassNames = new Set(
      classSubjects
        .filter(item => item.class_id === selectedClassValue && item.className)
        .map(item => item.className)
    )
    return Array.from(uniqueClassNames).map(name => ({ value: name, label: name }))
  }

  // Level type selection
  const selectLevelType = (type: 'Tahun' | 'Tingkatan') => {
    if (selectedLevelType.value === type) { // Deselect if clicking the same button
      selectedLevelType.value = null
      formData.value.class = null
    } else {
      selectedLevelType.value = type
      formData.value.class = null // Reset class selection when type changes
    }
    // Reset other form fields
    formData.value.className = ''
    formData.value.subject = null
    formData.value.subjectAbbreviation = ''
    formData.value.studentCount = null
    formErrors.value = null
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
    selectedExistingClassName.value = null
  }

  // Edit function
  const openEditModal = (item: any) => {
    console.log('🔧 openEditModal called with item:', item)
    
    // EXACT same logic as ClassSubject handleEdit but with correct class format
    const isTahun = tahunOptions.some(opt => opt.value === item.class_id)
    const isTingkatan = tingkatanOptions.some(opt => opt.value === item.class_id)
    
    console.log('🔧 class_id:', item.class_id)
    console.log('🔧 isTahun:', isTahun)
    console.log('🔧 isTingkatan:', isTingkatan)
    
    // Set level type FIRST
    if (isTahun) {
      selectedLevelType.value = 'Tahun'
      console.log('🔧 Set selectedLevelType to Tahun')
    } else if (isTingkatan) {
      selectedLevelType.value = 'Tingkatan'
      console.log('🔧 Set selectedLevelType to Tingkatan')
    } else {
      selectedLevelType.value = null
      console.log('🔧 Set selectedLevelType to null')
    }
    
    console.log('🔧 selectedLevelType.value after setting:', selectedLevelType.value)
    
    // Use nextTick to ensure classOptions are updated before setting formData
    nextTick(() => {
      console.log('🔧 Inside nextTick, selectedLevelType.value:', selectedLevelType.value)
      
      formData.value = {
        id: Date.now(), // Internal ID for editing tracking
        class: item.class_id,
        className: item.className,
        studentCount: item.studentCount ?? null,
        subject: item.subject_id,
        subjectAbbreviation: item.subject_abbreviation || ''
      }
      isEditing.value = true
      editingId.value = Date.now() // Store internal ID of item being edited
      formErrors.value = null
      showNewSubjectInput.value = false
      newSubjectNameFromInput.value = ''
      selectedExistingClassName.value = item.className // Pre-select if className exists
      isModalOpen.value = true
      
      console.log('🔧 Modal opened, isEditing:', isEditing.value)
      console.log('🔧 Final selectedLevelType.value:', selectedLevelType.value)
    })
  }

  // Add new modal
  const openAddModal = () => {
    isEditing.value = false
    editingId.value = null
    resetForm()
    isModalOpen.value = true
  }

  // Reset form
  const resetForm = () => {
    const currentClassSelection = selectedLevelType.value ? formData.value.class : null
    formData.value = {
      id: null,
      class: currentClassSelection,
      className: '',
      studentCount: null,
      subject: null,
      subjectAbbreviation: ''
    }
    formErrors.value = null
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
    selectedExistingClassName.value = null
  }

  // Cancel modal
  const cancelModal = () => {
    isModalOpen.value = false
    resetForm()
  }

  return {
    // State
    isModalOpen,
    editingId,
    isEditing,
    selectedLevelType,
    formData,
    formErrors,
    showNewSubjectInput,
    newSubjectNameFromInput,
    selectedExistingClassName,
    sessionOnlySubjects,
    subjectsLoading,
    
    // Constants
    ADD_NEW_SUBJECT_VALUE,
    tahunOptions,
    tingkatanOptions,
    
    // Computed
    classOptions,
    computedSubjectOptions,
    isFormDisabled,
    modalTitle,
    
    // Functions
    selectLevelType,
    openEditModal,
    openAddModal,
    resetForm,
    cancelModal,
    getExistingClassOptions,
    getClassLevel,
    
    // External
    subjects,
    fetchSubjects,
    addSubjectToDb
  }
}
