const fs = require('fs');
const path = require('path');

// The complete types from Supabase API
const typesContent = `export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      academic_calendar_documents: {
        Row: {
          created_at: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      annual_calendar_events: {
        Row: {
          category: string
          color: string | null
          created_at: string
          description: string | null
          end_date: string | null
          id: string
          location: string | null
          start_date: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          color?: string | null
          created_at?: string
          description?: string | null
          end_date?: string | null
          id?: string
          location?: string | null
          start_date: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          color?: string | null
          created_at?: string
          description?: string | null
          end_date?: string | null
          id?: string
          location?: string | null
          start_date?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      coupon_usage: {
        Row: {
          coupon_id: string | null
          id: string
          school_id: string | null
          used_at: string | null
          user_id: string | null
        }
        Insert: {
          coupon_id?: string | null
          id?: string
          school_id?: string | null
          used_at?: string | null
          user_id?: string | null
        }
        Update: {
          coupon_id?: string | null
          id?: string
          school_id?: string | null
          used_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "coupon_usage_coupon_id_fkey"
            columns: ["coupon_id"]
            isOneToOne: false
            referencedRelation: "coupons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coupon_usage_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      coupons: {
        Row: {
          code: string
          created_at: string | null
          created_by: string | null
          description: string | null
          discount_type: string
          discount_value: number
          expires_at: string | null
          id: string
          is_active: boolean | null
          trial_days: number | null
          updated_at: string | null
          usage_limit: number | null
          used_count: number | null
        }
        Insert: {
          code: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type: string
          discount_value: number
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          trial_days?: number | null
          updated_at?: string | null
          usage_limit?: number | null
          used_count?: number | null
        }
        Update: {
          code?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type?: string
          discount_value?: number
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          trial_days?: number | null
          updated_at?: string | null
          usage_limit?: number | null
          used_count?: number | null
        }
        Relationships: []
      }
      dskp_documents: {
        Row: {
          class_id: string
          class_name: string
          created_at: string | null
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          class_id: string
          class_name: string
          created_at?: string | null
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          class_id?: string
          class_name?: string
          created_at?: string | null
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          subject_id?: string
          subject_name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "dskp_documents_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      failed_payments: {
        Row: {
          admin_data: Json
          created_at: string
          currency: string | null
          error_details: Json | null
          id: string
          last_retry_at: string | null
          max_retries: number | null
          next_retry_at: string | null
          payment_amount: number
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          retry_count: number | null
          school_data: Json
          status: string | null
          stripe_customer_id: string | null
          stripe_session_id: string
          updated_at: string
        }
        Insert: {
          admin_data: Json
          created_at?: string
          currency?: string | null
          error_details?: Json | null
          id?: string
          last_retry_at?: string | null
          max_retries?: number | null
          next_retry_at?: string | null
          payment_amount: number
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          retry_count?: number | null
          school_data: Json
          status?: string | null
          stripe_customer_id?: string | null
          stripe_session_id: string
          updated_at?: string
        }
        Update: {
          admin_data?: Json
          created_at?: string
          currency?: string | null
          error_details?: Json | null
          id?: string
          last_retry_at?: string | null
          max_retries?: number | null
          next_retry_at?: string | null
          payment_amount?: number
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          retry_count?: number | null
          school_data?: Json
          status?: string | null
          stripe_customer_id?: string | null
          stripe_session_id?: string
          updated_at?: string
        }
        Relationships: []
      }`;

// Write to file
const outputPath = path.join(__dirname, '..', 'types', 'supabase.ts');
fs.writeFileSync(outputPath, typesContent);
console.log('Types file generated successfully!');
