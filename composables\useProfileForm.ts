import { ref, computed } from 'vue'
import type { ExtendedProfile, OptionItem, AcademicQualification } from '~/schemas/profileSchemas'

export function useProfileForm() {
  // Editing states for list view
  const editingOptionId = ref<string | null>(null)
  const editingQualificationId = ref<string | null>(null)
  const tempOptionData = ref<{ name: string }>({ name: '' })
  const tempQualificationData = ref<{ name: string; institution: string; year: string }>({ 
    name: '', 
    institution: '', 
    year: '' 
  })

  // Add new item states
  const showAddOptionForm = ref(false)
  const showAddQualificationForm = ref(false)
  const newOptionData = ref<{ name: string }>({ name: '' })
  const newQualificationData = ref<{ name: string; institution: string; year: string }>({ 
    name: '', 
    institution: '', 
    year: '' 
  })

  // Button state logic for add forms
  const isOptionSimpanDisabled = computed(() => {
    return !newOptionData.value.name.trim()
  })

  const isQualificationSimpanDisabled = computed(() => {
    return !newQualificationData.value.name.trim()
  })

  // Generate unique ID for new items
  const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

  // Dynamic field management for Options
  const addOption = () => {
    // Cancel any existing edits
    if (editingOptionId.value) {
      cancelEditOption()
    }

    // Show the add form and reset data
    showAddOptionForm.value = true
    newOptionData.value = { name: '' }
  }

  const addQualification = () => {
    // Cancel any existing edits
    if (editingQualificationId.value) {
      cancelEditQualification()
    }

    // Show the add form and reset data
    showAddQualificationForm.value = true
    newQualificationData.value = { name: '', institution: '', year: '' }
  }

  // List view methods for Options
  const startEditingOption = (option: OptionItem) => {
    // Cancel any existing edits
    if (editingOptionId.value) {
      cancelEditOption()
    }
    editingOptionId.value = option.id
    tempOptionData.value = { name: option.name }
  }

  const saveEditOption = (formData: { options: OptionItem[] }) => {
    if (!editingOptionId.value) return

    const option = formData.options.find(opt => opt.id === editingOptionId.value)
    if (option && tempOptionData.value.name.trim()) {
      option.name = tempOptionData.value.name.trim()
      editingOptionId.value = null
      tempOptionData.value = { name: '' }
    }
  }

  const cancelEditOption = () => {
    editingOptionId.value = null
    tempOptionData.value = { name: '' }
  }

  const deleteOption = (optionId: string, formData: { options: OptionItem[] }) => {
    const index = formData.options.findIndex(opt => opt.id === optionId)
    if (index !== -1) {
      formData.options.splice(index, 1)
    }
  }

  const saveNewOption = (formData: { options: OptionItem[] }) => {
    // Do nothing if button is disabled
    if (isOptionSimpanDisabled.value) return

    const newOption: OptionItem = {
      id: generateId(),
      name: newOptionData.value.name.trim(),
      created_at: new Date().toISOString(),
    }
    formData.options.push(newOption)

    // Reset form
    showAddOptionForm.value = false
    newOptionData.value = { name: '' }
  }

  const cancelNewOption = () => {
    showAddOptionForm.value = false
    newOptionData.value = { name: '' }
  }

  // List view methods for Qualifications
  const startEditingQualification = (qualification: AcademicQualification) => {
    // Cancel any existing edits
    if (editingQualificationId.value) {
      cancelEditQualification()
    }
    editingQualificationId.value = qualification.id
    tempQualificationData.value = {
      name: qualification.name || '',
      institution: qualification.institution || '',
      year: qualification.year?.toString() || ''
    }
  }

  const saveEditQualification = (formData: { academic_qualifications: AcademicQualification[] }) => {
    if (!editingQualificationId.value) return

    const qualification = formData.academic_qualifications.find(qual => qual.id === editingQualificationId.value)
    if (qualification && tempQualificationData.value.name.trim() &&
      tempQualificationData.value.institution.trim() && tempQualificationData.value.year.trim()) {
      qualification.name = tempQualificationData.value.name.trim()
      qualification.institution = tempQualificationData.value.institution.trim()
      qualification.year = parseInt(tempQualificationData.value.year.trim()) || 0
      editingQualificationId.value = null
      tempQualificationData.value = { name: '', institution: '', year: '' }
    }
  }

  const cancelEditQualification = () => {
    editingQualificationId.value = null
    tempQualificationData.value = { name: '', institution: '', year: '' }
  }

  const deleteQualification = (qualificationId: string, formData: { academic_qualifications: AcademicQualification[] }) => {
    const index = formData.academic_qualifications.findIndex(qual => qual.id === qualificationId)
    if (index !== -1) {
      formData.academic_qualifications.splice(index, 1)
    }
  }

  const saveNewQualification = (formData: { academic_qualifications: AcademicQualification[] }) => {
    // Do nothing if button is disabled
    if (isQualificationSimpanDisabled.value) return

    const newQualification: AcademicQualification = {
      id: generateId(),
      name: newQualificationData.value.name.trim(),
      institution: newQualificationData.value.institution.trim() || '',
      year: parseInt(newQualificationData.value.year.trim()) || 0,
      created_at: new Date().toISOString(),
    }
    formData.academic_qualifications.push(newQualification)

    // Reset form
    showAddQualificationForm.value = false
    newQualificationData.value = { name: '', institution: '', year: '' }
  }

  const cancelNewQualification = () => {
    showAddQualificationForm.value = false
    newQualificationData.value = { name: '', institution: '', year: '' }
  }

  return {
    // States
    editingOptionId,
    editingQualificationId,
    tempOptionData,
    tempQualificationData,
    showAddOptionForm,
    showAddQualificationForm,
    newOptionData,
    newQualificationData,
    
    // Computed
    isOptionSimpanDisabled,
    isQualificationSimpanDisabled,
    
    // Methods
    addOption,
    addQualification,
    startEditingOption,
    saveEditOption,
    cancelEditOption,
    deleteOption,
    saveNewOption,
    cancelNewOption,
    startEditingQualification,
    saveEditQualification,
    cancelEditQualification,
    deleteQualification,
    saveNewQualification,
    cancelNewQualification
  }
}
