<template>
  <div>
    <!-- Show loading state while validating -->
    <div v-if="isValidating" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">Validating school...</p>
      </div>
    </div>

    <!-- Show 404 if school doesn't exist -->
    <div v-else-if="!schoolExists"
      class="min-h-screen flex items-center justify-center bg-light-blank dark:bg-dark-background text-light-foreground dark:text-dark-foreground py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8 text-center">
        <div>
          <h1 class="text-6xl font-bold text-gray-900 dark:text-white">404</h1>
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            School Not Found
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            School with code "{{ schoolCode }}" does not exist.
          </p>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Please check the school code or contact your school administrator.
          </p>
        </div>
        <div>
          <NuxtLink to="/"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Go back home
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Render page content if school exists -->
    <slot v-else />
  </div>
</template>

<script setup>
// School validation layout
// Validates school existence before rendering any school subdomain pages

const route = useRoute()

// Get school code from subdomain
const schoolCode = ref('')
const isValidating = ref(true)
const schoolExists = ref(false)

// Extract school code from current host
if (process.client) {
  const host = window.location.host
  const parts = host.split('.')

  // For development (schoolcode.localhost:3000)
  if (host.includes('localhost') && parts.length > 1 && parts[0] !== 'localhost') {
    schoolCode.value = parts[0].toLowerCase()
  }
  // For production (schoolcode.domain.com)
  else if (parts.length >= 3) {
    const subdomain = parts[0].toLowerCase()
    const excludedSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp']

    if (!excludedSubdomains.includes(subdomain)) {
      schoolCode.value = subdomain
    }
  }
}

// Validate school existence
const validateSchool = async () => {
  if (!schoolCode.value) {
    schoolExists.value = true
    isValidating.value = false
    return
  }

  try {
    console.log(`🔍 [Layout] Validating school: ${schoolCode.value}`)

    const { data: response } = await useFetch('/api/schools/exists', {
      method: 'POST',
      body: { code: schoolCode.value }
    })

    schoolExists.value = response.value?.exists || false

    if (!response.value?.exists) {
      console.log(`❌ [Layout] School not found: ${schoolCode.value}`)
    } else {
      console.log(`✅ [Layout] School validated: ${schoolCode.value}`)
    }

  } catch (error) {
    console.error(`❌ [Layout] Error validating school ${schoolCode.value}:`, error)
    schoolExists.value = false
  } finally {
    isValidating.value = false
  }
}

// Run validation on mount
onMounted(() => {
  validateSchool()
})

// Set page title for 404
useHead(() => ({
  title: schoolExists.value ? undefined : '404 - School Not Found | Nuxt'
}))
</script>
