<template>
  <Teleport to="body">
    <Transition name="modal-backdrop" enter-active-class="transition-opacity duration-300 ease-out"
      leave-active-class="transition-opacity duration-200 ease-in" enter-from-class="opacity-0"
      enter-to-class="opacity-100" leave-from-class="opacity-100" leave-to-class="opacity-0">
      <div v-if="isOpen"
        class="fixed inset-0 flex items-start sm:items-center justify-center bg-black bg-opacity-50 px-4 sm:px-0 py-4 sm:py-0 overflow-y-auto"
        :class="zIndexClass" @click.self="closeModal">
        <Transition name="modal-content" appear enter-active-class="transition-all duration-300 ease-out"
          leave-active-class="transition-all duration-200 ease-in"
          enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to-class="opacity-100 translate-y-0 sm:scale-100"
          leave-from-class="opacity-100 translate-y-0 sm:scale-100"
          leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
          <div v-if="isOpen" id="modal-content-area"
            class="bg-light-card dark:bg-dark-card rounded-lg shadow-xl p-4 sm:p-6 w-full transform text-left my-auto max-h-[calc(100vh-2rem)] sm:max-h-[95vh] overflow-hidden flex flex-col"
            :class="{
              'max-w-xs': size === 'xs',
              'max-w-sm': size === 'sm',
              'max-w-md': size === 'md',
              'max-w-lg': size === 'lg',
              'max-w-xl': size === 'xl',
              'max-w-2xl': size === '2xl',
              'max-w-3xl': size === '3xl',
              'max-w-4xl': size === '4xl',
              'max-w-5xl': size === '5xl',
              'max-w-full sm:max-w-md': !size, // Default size
            }" :style="modalContentStyle" role="dialog" aria-modal="true"
            :aria-labelledby="title ? 'modal-title-id' : undefined">
            <div class="flex justify-between items-center mb-4 flex-shrink-0">
              <h2 :id="title ? 'modal-title-id' : undefined"
                class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white truncate pr-4 min-w-0">
                {{ title }}</h2>
              <button type="button" @click="closeModal"
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <!-- Header slot for additional content below title -->
            <div v-if="$slots.header" class="mb-4 flex-shrink-0">
              <slot name="header" />
            </div>
            <div class="text-sm text-gray-700 dark:text-gray-300 flex-1 overflow-y-auto" ref="modalScrollArea">
              <slot />
            </div>
            <!-- Footer slot for buttons -->
            <div v-if="$slots.footer"
              class="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
              <slot name="footer" />
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
const props = defineProps<{
  isOpen: boolean;
  title: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';
  zIndex?: number;
}>();

const emit = defineEmits(['update:isOpen', 'close']);

const closeModal = () => {
  emit('update:isOpen', false);
  emit('close');
};

import { ref, computed } from 'vue';
const minHeight = ref<string | null>(null);
const modalScrollArea = ref<HTMLElement | null>(null);

const modalContentStyle = computed(() => {
  return minHeight.value ? { minHeight: minHeight.value } : {};
});

const zIndexClass = computed(() => {
  const zIndex = props.zIndex || 60; // Increased default from 50 to 60

  // Map numeric z-index to Tailwind classes
  const zIndexMap: Record<number, string> = {
    40: 'z-40',
    50: 'z-50',
    60: 'z-60',
    70: 'z-70',
    80: 'z-80',
    90: 'z-90',
    100: 'z-100',
  };

  return zIndexMap[zIndex] || 'z-60'; // Changed fallback from z-50 to z-60
});

function handleListboxHeight(height: number) {
  if (height > 0) {
    minHeight.value = height + 64 + 'px'; // 64px for modal padding/header
  } else {
    minHeight.value = null;
  }
}

defineExpose({ handleListboxHeight, modalScrollArea });
// For more advanced accessibility like focus trapping and Escape key handling,
// you might consider using a library or implementing it manually.
// Headless UI's Dialog component handles this out of the box.
</script>

<style scoped>
/* Styles are handled by Tailwind utility classes */
</style>
