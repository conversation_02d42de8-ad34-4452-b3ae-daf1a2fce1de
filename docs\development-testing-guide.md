# RPHMate SaaS Development Testing Guide

## 🧪 Overview

This guide provides comprehensive testing procedures for the RPHMate multi-tenant SaaS platform during development. Follow these steps to ensure all features work correctly before production deployment.

## 🚀 Quick Start

### 1. **Environment Setup**
```bash
# Run the setup script
npm run setup:dev

# Seed test data
npm run seed:test

# Start development server
npm run dev
```

### 2. **Start Stripe Webhook Listener** (in separate terminal)
```bash
npm run stripe:listen
```

## 🌐 Testing URLs

### Main Domain (localhost:3000)
- **Landing Page**: http://localhost:3000
- **Pricing Page**: http://localhost:3000/pricing
- **Registration**: http://localhost:3000/billing
- **Admin Login**: http://localhost:3000/login

### Test Schools (subdomains)
**Note**: Demo schools have been removed for production readiness. Schools must be created through the billing flow.

- **Non-existent schools** (properly blocked with 404):
  - http://demo.localhost:3000 → 404 School Not Found
  - http://test.localhost:3000 → 404 School Not Found
  - http://nonexistent.localhost:3000 → 404 School Not Found

- **Real schools**: Created through payment flow only

## 📋 Complete Testing Checklist

### Phase 1: Landing Pages Testing ✅
- [x] **Landing Page** (http://localhost:3000)
  - [x] Hero section loads correctly
  - [x] Feature cards display properly
  - [x] CTA buttons link to pricing page
  - [x] Navigation works (pricing, login)
  - [x] Dark mode toggle functions
  - [x] Mobile responsive design

- [x] **Pricing Page** (http://localhost:3000/pricing)
  - [x] All 3 pricing tiers display correctly
  - [x] Monthly/yearly toggle works
  - [x] Prices update correctly with toggle
  - [x] "Start Free Trial" buttons link to billing
  - [x] FAQ section expands/collapses

- [x] **School Admin Login** (http://localhost:3000/login)
  - [x] Login form accepts input
  - [x] Password visibility toggle works
  - [x] "Forgot password" link present
  - [x] School code direct access works
  - [x] Error messages display correctly

### Phase 2: Subdomain Infrastructure Testing ✅
- [x] **Subdomain Resolution**
  - [x] Subdomain detection works correctly
  - [x] Valid school subdomains resolve properly
  - [x] Invalid subdomains show 404 errors
  - [x] Non-existent schools properly blocked

- [x] **Authentication Flows**
  - [x] Unauthenticated users redirect to /auth/login
  - [x] Login form works on school subdomains
  - [x] Successful login redirects to /dashboard
  - [x] Logout functionality works
  - [x] Session persistence across page reloads

- [x] **School-Specific Features**
  - [x] School admin page (/admin) accessible
  - [x] School context detected correctly
  - [x] Data isolation between schools verified

### Phase 3: Routing & Architecture Cleanup ✅
- [x] **Demo References Removal**
  - [x] All hardcoded demo schools removed
  - [x] Mock data files deleted
  - [x] Configuration files cleaned
  - [x] Documentation updated

- [x] **School Service Route Protection**
  - [x] School services blocked on main domain
  - [x] 404 responses for unauthorized routes
  - [x] Proper error messages displayed
  - [x] Main domain public pages still work

- [x] **Subdomain Security**
  - [x] Non-existent schools return 404
  - [x] Auth pages immediately blocked
  - [x] No brief exposure of forms
  - [x] Consistent server/client rendering

### Phase 4: Billing Integration & Dynamic Subdomain Creation ✅
- [x] **School Code Validation**
  - [x] Real database validation implemented
  - [x] Uniqueness checking works
  - [x] Case-insensitive validation
  - [x] Proper error handling

- [x] **Stripe Integration**
  - [x] Checkout session creation with metadata
  - [x] School information stored in session
  - [x] Webhook processing implemented
  - [x] Payment success handling

- [x] **Automatic School Creation**
  - [x] School records created in database
  - [x] Admin user accounts created
  - [x] School memberships established
  - [x] Profile updates with admin flags

- [x] **Success Page & Redirection**
  - [x] Real school data displayed
  - [x] Dynamic subdomain URL generation
  - [x] Redirect to school portal
  - [x] Proper error handling

## 💳 Stripe Test Cards

Use these test cards for comprehensive payment testing. **Always use test API keys** and never use real card details.

### 🎯 Basic Testing Cards

#### Successful Payments
- **Visa**: `4242 4242 4242 4242`
- **Visa (debit)**: `4000 0566 5566 5556`
- **Mastercard**: `5555 5555 5555 4444`
- **Mastercard (2-series)**: `2223 0031 2200 3222`
- **American Express**: `3782 8224 6310 005`
- **Discover**: `6011 1111 1111 1117`

#### Failed Payments
- **Generic decline**: `************** 0002`
- **Insufficient funds**: `************** 9995`
- **Lost card**: `************** 9987`
- **Stolen card**: `************** 9979`
- **Expired card**: `************** 0069`
- **Incorrect CVC**: `************** 0127`
- **Processing error**: `************** 0119`

### 🔒 Security & Fraud Testing

#### Fraud Prevention (Radar)
- **Always blocked**: `4100 0000 0000 0019`
- **Highest risk**: `************** 4954`
- **Elevated risk**: `************** 9235`
- **CVC check fails**: `************** 0101`
- **Postal code fails**: `************** 0036`

#### 3D Secure Authentication
- **Requires authentication**: `4000 0025 0000 3155`
- **Always authenticate**: `************** 3184`
- **Already set up**: `4000 0038 0000 0446`
- **3DS required**: `************** 3220`
- **3DS not supported**: `3782 8224 6310 005` (Amex)

### 💰 Advanced Testing Scenarios

#### Disputes
- **Fraudulent dispute**: `************** 0259`
- **Product not received**: `************** 2685`
- **Inquiry**: `************** 1976`
- **Early fraud warning**: `************** 5423`

#### Refunds
- **Asynchronous success**: `************** 7726`
- **Asynchronous failure**: `************** 5126`

#### Balance Testing
- **Bypass pending balance**: `************** 0077`

### 🌍 International Testing
- **Malaysia**: `************** 0002`
- **Singapore**: `************** 0003`
- **United Kingdom**: `************** 0000`
- **Germany**: `************** 0016`

### 📝 Testing Guidelines

**For all test cards:**
- **Expiry**: Use any future date (e.g., `12/34`)
- **CVC**: Any 3 digits (4 for Amex)
- **Postal Code**: Any valid postal code
- **Name**: Any name
- **Email**: Any valid email format

**Important Notes:**
- Never use real card details in testing
- Test cards only work with test API keys
- Use different cards to test various scenarios
- Monitor webhook events during testing

## 🔑 Test Credentials

### School Admin Accounts
```
No demo accounts available - schools must be created through payment flow.

To create test school accounts:
1. Visit http://localhost:3000/billing
2. Fill out school registration form
3. Use Stripe test cards for payment
4. Complete checkout process
5. Access new school subdomain
```

### Main Domain Admin
```
Main domain admin access:
- Visit http://localhost:3000/login
- Use Google sign-in (for paying school admins only)
- Or create account through billing flow
```

## 🧪 Testing Scenarios

### Scenario 1: New School Registration & Subdomain Creation
1. Visit http://localhost:3000
2. Click "Start Free Trial" → Pricing page
3. Select "Professional" plan → Billing page
4. Fill registration form with unique school code (e.g., "testschool123")
5. Complete Stripe checkout with test card: `4242 4242 4242 4242`
6. Verify success page displays with real school data
7. Click "Access Your School Portal" → Redirects to school subdomain
8. Complete school setup and admin profile

### Scenario 2: Non-Existent School Security
1. Visit http://demo.localhost:3000
2. Should immediately show 404 "School Not Found"
3. Try http://demo.localhost:3000/auth/login
4. Should immediately show 404 "School Not Found" (no brief exposure)
5. Try http://demo.localhost:3000/auth/daftar
6. Should immediately show 404 "School Not Found"
7. Verify no registration forms are accessible

### Scenario 3: Main Domain vs School Services
1. Visit http://localhost:3000 → Landing page ✅
2. Visit http://localhost:3000/pricing → Pricing page ✅
3. Visit http://localhost:3000/rph → 404 (school service blocked) ✅
4. Visit http://localhost:3000/refleksi → 404 (school service blocked) ✅
5. Verify school services only work on valid school subdomains

### Scenario 4: Payment Testing with Different Cards
1. **Successful Payment**:
   - Use card: `4242 4242 4242 4242`
   - Verify checkout succeeds
   - Check success page displays
   - Confirm school subdomain created

2. **Declined Payment**:
   - Use card: `************** 0002` (generic decline)
   - Verify error handling in Stripe checkout
   - Retry with successful card
   - Confirm process completes

3. **Insufficient Funds**:
   - Use card: `************** 9995`
   - Verify appropriate error message
   - Test retry functionality

4. **3D Secure Authentication**:
   - Use card: `4000 0025 0000 3155`
   - Complete authentication flow
   - Verify payment succeeds after auth

### Scenario 5: Webhook Processing & School Creation
1. Start Stripe webhook listener: `npm run stripe:listen`
2. Monitor webhook listener terminal
3. Complete a test payment with: `4242 4242 4242 4242`
4. Verify webhook events received:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `invoice.payment_succeeded`
5. Check school creation in logs:
   - School record created in database
   - Admin user account created
   - School membership established
   - Profile updated with admin flag

### Scenario 6: School Code Validation
1. Visit billing page
2. Try existing school code → Should show error
3. Try invalid characters (spaces, symbols) → Should show error
4. Try valid unique code → Should show success
5. Verify real-time URL preview updates
6. Test case-insensitive validation

## 🐛 Common Issues & Solutions

### School Not Found (404)
**Problem**: Valid school subdomain shows 404
**Solution**:
1. Verify school exists in database
2. Check school code spelling/case
3. Ensure school was created through payment flow
4. Check Supabase connection

### Stripe Webhooks Not Working
**Problem**: Payments succeed but school not created
**Solution**:
1. Ensure Stripe CLI is running: `npm run stripe:listen`
2. Check webhook secret in .env.local matches CLI output
3. Verify webhook endpoint URL is correct
4. Check server logs for webhook processing errors
5. Ensure SUPABASE_SERVICE_ROLE_KEY is set

### Billing Form Validation Errors
**Problem**: School code validation not working
**Solution**:
1. Check database connection
2. Verify Supabase environment variables
3. Test with different school codes
4. Check browser network tab for API errors

### Success Page Not Loading School Data
**Problem**: Success page shows "Loading..." indefinitely
**Solution**:
1. Check Stripe session ID in URL
2. Verify session data API endpoint
3. Check Stripe secret key configuration
4. Monitor browser console for errors

### TypeScript Errors
**Problem**: Development server shows TypeScript errors
**Solution**:
```bash
npm run typecheck
```
Fix any reported issues before testing.

## 📊 Performance Testing

### Load Testing
- [ ] Test with multiple concurrent users
- [ ] Verify subdomain routing performance
- [ ] Check database query efficiency
- [ ] Monitor memory usage

### Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

## 🔒 Security Testing

### Authentication Security
- [ ] Verify session isolation between schools
- [ ] Test unauthorized access attempts
- [ ] Check password security requirements
- [ ] Validate JWT token handling

### Payment Security
- [ ] Verify Stripe webhook signature validation
- [ ] Test with invalid payment data
- [ ] Check for data leakage in error messages
- [ ] Validate HTTPS enforcement

## 📝 Testing Reports

### Daily Testing Checklist
- [ ] Main domain pages accessible (/, /pricing, /billing)
- [ ] Non-existent school subdomains return 404
- [ ] School registration flow works with test cards
- [ ] Webhook processing creates schools correctly
- [ ] Success page displays real school data
- [ ] No console errors in browser
- [ ] TypeScript check passes: `npm run typecheck`

### Pre-Deployment Testing
- [ ] Complete end-to-end billing flow tested
- [ ] Multiple test card scenarios verified
- [ ] Webhook processing thoroughly tested
- [ ] School creation and admin setup working
- [ ] Cross-browser compatibility verified
- [ ] Performance benchmarks met
- [ ] Security tests passed (school isolation, validation)
- [ ] Documentation updated

## 🚀 Next Steps

After completing all testing:

1. **Document any issues** found during testing
2. **Fix critical bugs** before proceeding
3. **Update documentation** with any changes
4. **Prepare for production deployment**
5. **Set up monitoring and logging**

---

*This testing guide should be updated as new features are added or issues are discovered.*
