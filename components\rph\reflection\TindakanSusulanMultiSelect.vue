<template>
    <div class="space-y-2">
        <div class="flex items-center justify-between">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tindakan Susulan
            </label>
            <Button @click="showOptionsModal = true" size="sm" variant="outline" class="text-xs" type="button">
                <Icon name="heroicons:cog-6-tooth" class="h-3 w-3 mr-1" />
                Urus Pilihan
            </Button>
        </div>

        <!-- Multi-select using existing component -->
        <MultiSelect :key="`tindakan-${options.length}`" :model-value="selectedOptions"
            @update:model-value="selectedOptions = $event" :options="formattedOptions" option-label="label"
            option-value="value" placeholder="Pilih tindakan susulan..." :loading="loading" />

        <!-- Options Management Modal -->
        <ReflectionOptionsModal :show="showOptionsModal" title="Tindakan Susulan" :options="options"
            :loading="optionsLoading" @close="showOptionsModal = false" @add-option="handleAddOption"
            @update-option="handleUpdateOption" @delete-option="handleDeleteOption" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import MultiSelect from '~/components/ui/base/MultiSelect.vue'
import ReflectionOptionsModal from './ReflectionOptionsModal.vue'
import { useReflectionOptions } from '~/composables/useReflectionOptions'

interface Props {
    modelValue: string[]
}

interface Emits {
    (e: 'update:modelValue', value: string[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Composables
const {
    tindakanSusulanOptions: options,
    loading,
    fetchTindakanSusulanOptions,
    createTindakanSusulanOption,
    updateTindakanSusulanOption,
    deleteTindakanSusulanOption
} = useReflectionOptions()

// State
const showOptionsModal = ref(false)
const optionsLoading = ref(false)

// Computed
const selectedOptions = computed({
    get: () => props.modelValue || [],
    set: (value) => emit('update:modelValue', value)
})

// Format options for MultiSelect component
const formattedOptions = computed(() => {
    return options.value.map(option => ({
        value: option.id,
        label: option.option_text + (option.is_default ? ' (Lalai)' : '')
    }))
})

const handleAddOption = async (text: string) => {
    try {
        optionsLoading.value = true
        await createTindakanSusulanOption(text)
    } catch (error) {
        console.error('Error adding option:', error)
    } finally {
        optionsLoading.value = false
    }
}

const handleUpdateOption = async (id: string, text: string) => {
    try {
        optionsLoading.value = true
        await updateTindakanSusulanOption(id, text)
    } catch (error) {
        console.error('Error updating option:', error)
    } finally {
        optionsLoading.value = false
    }
}

const handleDeleteOption = async (id: string) => {
    try {
        optionsLoading.value = true
        await deleteTindakanSusulanOption(id)
        // Remove from selected if it was selected
        selectedOptions.value = selectedOptions.value.filter(selectedId => selectedId !== id)
    } catch (error) {
        console.error('Error deleting option:', error)
    } finally {
        optionsLoading.value = false
    }
}

// Lifecycle
onMounted(async () => {
    await fetchTindakanSusulanOptions()
})

// Watch for modelValue changes and ensure options are loaded
watch(() => props.modelValue, async (newValue) => {
    if (newValue && newValue.length > 0 && options.value.length === 0) {
        // If we have selected values but no options loaded, fetch them
        await fetchTindakanSusulanOptions()
    }
}, { immediate: true })
</script>
