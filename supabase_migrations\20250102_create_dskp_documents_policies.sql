-- Migration: Create RLS policies for DSKP documents and storage
-- Created: 2025-01-02
-- Description: Enable RLS on dskp_documents table and create storage policies for dskp-files bucket

BEGIN;

-- =====================================================
-- ENABLE RLS ON DSKP_DOCUMENTS TABLE
-- =====================================================

-- Enable Row Level Security on dskp_documents table
ALTER TABLE dskp_documents ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- CREATE RLS POLICIES FOR DSKP_DOCUMENTS TABLE
-- =====================================================

-- Policy: Users can view their own DSKP documents
CREATE POLICY "Users can view their own DSKP documents" ON dskp_documents
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own DSKP documents
CREATE POLICY "Users can insert their own DSKP documents" ON dskp_documents
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own DSKP documents
CREATE POLICY "Users can update their own DSKP documents" ON dskp_documents
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own DSKP documents
CREATE POLICY "Users can delete their own DSKP documents" ON dskp_documents
  FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- CREATE STORAGE POLICIES FOR DSKP-FILES BUCKET
-- =====================================================

-- Policy: Users can upload their own DSKP files
CREATE POLICY "Users can upload their own DSKP files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'dskp-files' AND 
    (auth.uid())::text = (storage.foldername(name))[1]
  );

-- Policy: Users can view their own DSKP files
CREATE POLICY "Users can view their own DSKP files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'dskp-files' AND 
    (auth.uid())::text = (storage.foldername(name))[1]
  );

-- Policy: Users can update their own DSKP files
CREATE POLICY "Users can update their own DSKP files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'dskp-files' AND 
    (auth.uid())::text = (storage.foldername(name))[1]
  ) WITH CHECK (
    bucket_id = 'dskp-files' AND 
    (auth.uid())::text = (storage.foldername(name))[1]
  );

-- Policy: Users can delete their own DSKP files
CREATE POLICY "Users can delete their own DSKP files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'dskp-files' AND 
    (auth.uid())::text = (storage.foldername(name))[1]
  );

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE dskp_documents IS 'DSKP (Dokumen Standard Kurikulum dan Pentaksiran) documents uploaded by teachers';
COMMENT ON COLUMN dskp_documents.user_id IS 'Reference to the teacher who uploaded the document';
COMMENT ON COLUMN dskp_documents.class_id IS 'Class level identifier (e.g., t1, f1, etc.)';
COMMENT ON COLUMN dskp_documents.subject_id IS 'Reference to the subject';
COMMENT ON COLUMN dskp_documents.storage_file_path IS 'Path to the file in Supabase storage';

COMMIT;
