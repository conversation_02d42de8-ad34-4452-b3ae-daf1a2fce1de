import { ref } from "vue";
import { useSupabaseClient, useSupabaseUser } from "#imports";
import type { Database } from "~/types/supabase"; // Your Supabase generated types
import type { RphWeek } from "~/types/rph"; // Your RphWeek type

// Define the structure of a week submission record, matching your table
export interface UserWeekSubmission {
  id?: string;
  user_id: string;
  week_id: string;
  status: "Draf" | "Dihantar" | "Disemak" | "Ditolak";
  submitted_at?: string | null;
  reviewed_at?: string | null;
  reviewer_id?: string | null;
  supervisor_comments?: string | null;
  created_at?: string;
  updated_at?: string;
}

export function useWeekSubmissions() {
  const supabase = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  const loadingSubmissionStatus = ref(false);
  const submissionError = ref<Error | null>(null);

  const getWeekSubmission = async (
    weekId: string
  ): Promise<UserWeekSubmission | null> => {
    if (!user.value || !weekId) return null;
    loadingSubmissionStatus.value = true;
    submissionError.value = null;
    try {
      const { data, error } = await supabase
        .from("user_week_submissions")
        .select("*")
        .eq("user_id", user.value.id)
        .eq("week_id", weekId)
        .maybeSingle(); // Use maybeSingle as a record might not exist

      if (error) throw error;
      return data as UserWeekSubmission | null;
    } catch (err: any) {
      submissionError.value = err;
      console.error("Error fetching week submission status:", err);
      return null;
    } finally {
      loadingSubmissionStatus.value = false;
    }
  };

  const upsertWeekSubmission = async (
    weekId: string,
    newStatus: "Draf" | "Dihantar", // Only users can set these two directly
    currentSubmission: UserWeekSubmission | null
  ): Promise<UserWeekSubmission | null> => {
    if (!user.value || !weekId) return null;
    loadingSubmissionStatus.value = true;
    submissionError.value = null;

    // Ensure user_id is explicitly a string and not undefined.
    // This addresses the TypeScript error where `Partial<UserWeekSubmission>`
    // might allow user_id to be undefined, but upsert requires it for the onConflict constraint.
    const submissionData: Omit<
      UserWeekSubmission,
      "id" | "created_at" | "updated_at"
    > & { user_id: string; week_id: string } = {
      user_id: user.value.id, // user.value.id is guaranteed to be a string here due to the check above
      week_id: weekId,
      status: newStatus,
      submitted_at: null, // Initialize all potentially nullable fields
      reviewed_at: null,
      reviewer_id: null,
      supervisor_comments: null,
    };

    if (newStatus === "Dihantar") {
      submissionData.submitted_at = new Date().toISOString();
      // Clear previous review fields if resubmitting a 'Rejected' one
      if (currentSubmission?.status === "Ditolak") {
        submissionData.reviewed_at = null;
        submissionData.reviewer_id = null;
        submissionData.supervisor_comments = null;
      }
    } else {
      // Draft
      submissionData.submitted_at = null; // Clear submission date if reverting to draft
    }

    try {
      // Upsert based on user_id and week_id (unique constraint)
      const { data, error } = await supabase
        .from("user_week_submissions")
        .upsert(submissionData, { onConflict: "user_id,week_id" })
        .select()
        .single();

      if (error) throw error;
      return data as UserWeekSubmission;
    } catch (err: any) {
      submissionError.value = err;
      console.error("Error upserting week submission:", err);
      return null;
    } finally {
      loadingSubmissionStatus.value = false;
    }
  };

  return {
    loadingSubmissionStatus,
    submissionError,
    getWeekSubmission,
    upsertWeekSubmission,
  };
}
