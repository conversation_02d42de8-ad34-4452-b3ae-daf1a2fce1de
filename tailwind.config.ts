import type { Config } from "tailwindcss";
import forms from "@tailwindcss/forms";

export default <Config>{
  darkMode: "class",
  content: [
    "./components/**/*.{vue,ts}",
    "./pages/**/*.vue",
    "./layouts/**/*.vue",
    "./app.vue",
    "./types/**/*.ts",
  ],
  safelist: [
    // Subject color classes - ensure all are generated
    'bg-blue-100', 'bg-blue-200', 'bg-blue-800/30', 'bg-blue-900/30',
    'bg-indigo-100', 'bg-indigo-200', 'bg-indigo-800/30', 'bg-indigo-900/30',
    'bg-cyan-100', 'bg-cyan-200', 'bg-cyan-800/30', 'bg-cyan-900/30',
    'bg-teal-100', 'bg-teal-200', 'bg-teal-800/30', 'bg-teal-900/30',
    'bg-emerald-100', 'bg-emerald-200', 'bg-emerald-800/30', 'bg-emerald-900/30',
    'bg-red-100', 'bg-red-200', 'bg-red-800/30', 'bg-red-900/30',
    'bg-purple-100', 'bg-purple-200', 'bg-purple-800/30', 'bg-purple-900/30',
    'bg-orange-100', 'bg-orange-200', 'bg-orange-800/30', 'bg-orange-900/30',
    'bg-yellow-100', 'bg-yellow-200', 'bg-yellow-800/30', 'bg-yellow-900/30',
    'bg-amber-100', 'bg-amber-200', 'bg-amber-800/30', 'bg-amber-900/30',
    'bg-green-100', 'bg-green-200', 'bg-green-800/30', 'bg-green-900/30',
    'bg-lime-100', 'bg-lime-200', 'bg-lime-800/30', 'bg-lime-900/30',
    'bg-stone-100', 'bg-stone-200', 'bg-stone-800/30', 'bg-stone-900/30',
    'bg-rose-100', 'bg-rose-200', 'bg-rose-800/30', 'bg-rose-900/30',
    'bg-fuchsia-100', 'bg-fuchsia-200', 'bg-fuchsia-800/30', 'bg-fuchsia-900/30',
    'bg-pink-100', 'bg-pink-200', 'bg-pink-800/30', 'bg-pink-900/30',
    'bg-violet-100', 'bg-violet-200', 'bg-violet-800/30', 'bg-violet-900/30',
    'bg-slate-100', 'bg-slate-200', 'bg-slate-800/30', 'bg-slate-900/30',
    'bg-gray-100', 'bg-gray-200', 'bg-gray-800/30', 'bg-gray-900/30',
    'bg-zinc-100', 'bg-zinc-200', 'bg-zinc-800/30', 'bg-zinc-900/30',
    'bg-neutral-100', 'bg-neutral-200', 'bg-neutral-800/30', 'bg-neutral-900/30',
    'bg-sky-100', 'bg-sky-200', 'bg-sky-800/30', 'bg-sky-900/30',
    
    // Text colors
    'text-blue-900', 'text-blue-100',
    'text-indigo-900', 'text-indigo-100',
    'text-cyan-900', 'text-cyan-100',
    'text-teal-900', 'text-teal-100',
    'text-emerald-900', 'text-emerald-100',
    'text-red-900', 'text-red-100',
    'text-purple-900', 'text-purple-100',
    'text-orange-900', 'text-orange-100',
    'text-yellow-900', 'text-yellow-100',
    'text-amber-900', 'text-amber-100',
    'text-green-900', 'text-green-100',
    'text-lime-900', 'text-lime-100',
    'text-stone-900', 'text-stone-100',
    'text-rose-900', 'text-rose-100',
    'text-fuchsia-900', 'text-fuchsia-100',
    'text-pink-900', 'text-pink-100',
    'text-violet-900', 'text-violet-100',
    'text-slate-900', 'text-slate-100',
    'text-gray-900', 'text-gray-100',
    'text-zinc-900', 'text-zinc-100',
    'text-neutral-900', 'text-neutral-100',
    'text-sky-900', 'text-sky-100',
    
    // Border colors
    'border-l-blue-500', 'border-l-blue-600',
    'border-l-indigo-500', 'border-l-indigo-600',
    'border-l-cyan-500', 'border-l-cyan-600',
    'border-l-teal-500', 'border-l-teal-600',
    'border-l-emerald-500', 'border-l-emerald-600',
    'border-l-red-500', 'border-l-red-600',
    'border-l-purple-500', 'border-l-purple-600',
    'border-l-orange-500', 'border-l-orange-600',
    'border-l-yellow-500', 'border-l-yellow-600',
    'border-l-amber-500', 'border-l-amber-600',
    'border-l-green-500', 'border-l-green-600',
    'border-l-lime-500', 'border-l-lime-600',
    'border-l-stone-500', 'border-l-stone-600',
    'border-l-rose-500', 'border-l-rose-600',
    'border-l-fuchsia-500', 'border-l-fuchsia-600',
    'border-l-pink-500', 'border-l-pink-600',
    'border-l-violet-500', 'border-l-violet-600',
    'border-l-slate-500', 'border-l-slate-600',
    'border-l-gray-500', 'border-l-gray-600',
    'border-l-zinc-500', 'border-l-zinc-600',
    'border-l-neutral-500', 'border-l-neutral-600',
    'border-l-sky-500', 'border-l-sky-600',
  ],
  theme: {
    extend: {
      colors: {
        primary: "rgb(var(--color-primary) / <alpha-value>)",
        secondary: "rgb(var(--color-secondary) / <alpha-value>)",
        accent: "#F59E0B", // Keep this for accents

        // Alert colors
        "alert-success": "#4BD08B",
        "alert-info": "#46CAEB",
        "alert-warning": "#F8C076",
        "alert-error": "#FB977D",
        // Corresponding text colors for alerts (adjust if needed for contrast)
        "alert-success-text": "#065F46", // Darker Green
        "alert-info-text": "#075985", // Darker Blue
        "alert-warning-text": "#713F12", // Darker Yellow/Orange
        "alert-error-text": "#7F1D1D", // Darker Red

        // Light theme colors
        light: {
          background: "#F0F5F9", // White background
          foreground: "#111827", // Dark gray text (Gray 900)
          blank: "#E4ECF9",
          card: "#FFFFFF", // Off-white for cards (Gray 50)
          "card-foreground": "#1F2937", // Darker gray for card text (Gray 800)
          border: "#E5E7EB", // Light gray border (Gray 200)
        },
        // Dark theme colors
        dark: {
          background: "#16293E", // Dark gray background (Gray 800)
          foreground: "#F3F4F6", // Light gray text (Gray 100)
          card: "#111C2D", // Medium gray for cards (Gray 700)
          "card-foreground": "#F9FAFB", // Off-white for card text (Gray 50)
          border: "#4B5563", // Medium gray border (Gray 600)
        },
      },
      fontFamily: {
        sans: ["Poppins", "sans-serif"],
      },      boxShadow: {
        md: "0 4px 6px -1px rgba(31, 41, 55, 0.1), 0 2px 4px -2px rgba(31, 41, 55, 0.1)",
        lg: "0 10px 15px -3px rgba(31, 41, 55, 0.1), 0 4px 6px -4px rgba(31, 41, 55, 0.1)",
        "2xl": "0 25px 50px -12px rgba(31, 41, 55, 0.25)",
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [forms],
};
