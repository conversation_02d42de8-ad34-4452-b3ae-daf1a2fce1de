-- Migration to add activity type support to timetable_entries
-- This allows for both academic classes and non-academic activities

-- Add activity_type enum
CREATE TYPE activity_type_enum AS ENUM ('CLASS', 'ASSEMBLY', 'COCURRICULAR', 'MEETING', 'BREAK', 'OTHER');

-- Add activity_type column to timetable_entries
ALTER TABLE timetable_entries 
ADD COLUMN activity_type activity_type_enum DEFAULT 'CLASS' NOT NULL;

-- Add activity_title column for non-class activities
ALTER TABLE timetable_entries 
ADD COLUMN activity_title TEXT;

-- Add activity_description column for additional details
ALTER TABLE timetable_entries 
ADD COLUMN activity_description TEXT;

-- Make class_id and subject_id nullable for non-class activities
ALTER TABLE timetable_entries 
ALTER COLUMN class_id DROP NOT NULL,
ALTER COLUMN subject_id DROP NOT NULL,
ALTER COLUMN class_name DROP NOT NULL,
ALTER COLUMN subject_name DROP NOT NULL;

-- Add constraint to ensure either class data or activity data is provided
ALTER TABLE timetable_entries 
ADD CONSTRAINT check_activity_data 
CHECK (
    (activity_type = 'CLASS' AND class_id IS NOT NULL AND subject_id IS NOT NULL) OR
    (activity_type != 'CLASS' AND activity_title IS NOT NULL)
);

-- Add index for better performance
CREATE INDEX idx_timetable_entries_activity_type ON timetable_entries(activity_type);

-- Add comments for documentation
COMMENT ON COLUMN timetable_entries.activity_type IS 'Type of activity: CLASS for regular classes, ASSEMBLY for school assemblies, COCURRICULAR for co-curricular activities, etc.';
COMMENT ON COLUMN timetable_entries.activity_title IS 'Title of non-class activities (e.g., Perhimpunan Pagi, Gotong-royong)';
COMMENT ON COLUMN timetable_entries.activity_description IS 'Additional description for activities';
