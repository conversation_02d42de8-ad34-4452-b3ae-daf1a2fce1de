<template>
    <!-- Existing Detailed Reflections List -->
    <div v-if="reflections.length > 0" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">
            <PERSON>fleks<PERSON> Terperinci untuk {{ dayLabel }}
        </h3>
        <div class="space-y-3">
            <div v-for="reflection in reflections" :key="reflection.id"
                class="bg-white dark:bg-gray-700 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ getClassSubjectLabel(reflection.class_subject_id) }}
                        </h4>
                        <div class="flex items-center space-x-2 mt-1">
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                Penilaian: {{ reflection.overall_rating }}/5 •
                                {{ reflection.objectives_achieved ? 'Objektif tercapai' : 'Objektif tidak tercapai' }}
                            </p>
                            <span v-if="(reflection as any).tidak_terlaksana"
                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                                <Icon name="heroicons:x-circle" class="h-3 w-3 mr-1" />
                                Tidak Terlaksana
                            </span>
                        </div>
                        <p v-if="reflection.challenges_faced"
                            class="text-sm text-gray-600 dark:text-gray-300 mt-2 line-clamp-2">
                            {{ reflection.challenges_faced }}
                        </p>
                    </div>
                    <div class="flex items-center space-x-2 ml-4">
                        <Button type="button" variant="outline" size="sm" @click="$emit('edit', reflection)"
                            :title="'Edit refleksi untuk ' + getClassSubjectLabel(reflection.class_subject_id)">
                            <Icon name="mdi:pencil" class="h-4 w-4" />
                        </Button>
                        <Button type="button" variant="outline" size="sm" @click="$emit('delete', reflection)"
                            class="text-red-600 hover:text-red-700 dark:text-red-400"
                            :title="'Padam refleksi untuk ' + getClassSubjectLabel(reflection.class_subject_id)">
                            <Icon name="mdi:delete" class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- No Class-Subject Selected Message -->
    <div v-else-if="showNoReflectionsMessage"
        class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
        <div class="flex">
            <Icon name="mdi:information" class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 mt-0.5" />
            <div class="text-sm text-blue-800 dark:text-blue-200">
                Pilih kelas-subjek di atas untuk melihat dan mengedit refleksi untuk {{ dayLabel }}.
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import type { LessonPlanDetailedReflection } from '~/types/reflections'

interface Props {
    reflections: LessonPlanDetailedReflection[]
    dayLabel: string
    showNoReflectionsMessage?: boolean
    additionalMessage?: string
    getClassSubjectLabel: (id: string) => string
}

withDefaults(defineProps<Props>(), {
    showNoReflectionsMessage: true,
    additionalMessage: 'Refleksi tersedia untuk semua kelas-subjek yang dipilih.'
})

defineEmits<{
    edit: [reflection: LessonPlanDetailedReflection]
    delete: [reflection: LessonPlanDetailedReflection]
}>()
</script>
