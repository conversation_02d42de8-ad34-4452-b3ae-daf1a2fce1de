<template>
  <div class="template-preferences">
    <!-- Header -->
    <div class="mb-6">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        Template Refleksi
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        Urus keutamaan dan tetapan untuk template refleksi anda
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="flex items-center space-x-2">
        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
        <span class="text-gray-600 dark:text-gray-400">Memuatkan tetapan...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
      class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
      <div class="flex">
        <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5" />
        <div class="text-sm text-red-800 dark:text-red-200">
          {{ error }}
        </div>
      </div>
    </div>

    <!-- Preferences Form -->
    <div v-else class="space-y-8">
      <!-- Default Template Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Template Lalai
        </h4>

        <div class="space-y-4">
          <!-- Default Template Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Template Lalai
            </label>
            <SimpleTemplateSelector :selected-template-id="preferences.default_template_id"
              @update:selected-template-id="handleDefaultTemplateChange" placeholder="Pilih template lalai (opsional)"
              :include-none-option="true" />
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Template yang akan dipilih secara automatik apabila membuka borang refleksi
            </p>
          </div>

          <!-- Auto Apply Default -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Guna Template Lalai Secara Automatik
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Aplikasikan template lalai secara automatik tanpa perlu memilih
              </p>
            </div>
            <UiToggle :model-value="preferences.auto_apply_default" @update:model-value="toggleAutoApplyDefault"
              :disabled="!hasDefaultTemplate" />
          </div>
        </div>
      </div>

      <!-- Display Preferences Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Paparan Template
        </h4>

        <div class="space-y-4">
          <!-- Sort Order -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Susunan Template
            </label>
            <select :value="preferences.template_sort_order" @change="handleSortOrderChange"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option value="recent">Terkini Digunakan</option>
              <option value="alphabetical">Mengikut Abjad</option>
              <option value="usage">Paling Kerap Digunakan</option>
              <option value="category">Mengikut Kategori</option>
            </select>
          </div>

          <!-- Preview Mode -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Mod Pratonton
            </label>
            <select :value="preferences.template_preview_mode" @change="handlePreviewModeChange"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option value="simple">Ringkas</option>
              <option value="detailed">Terperinci</option>
            </select>
          </div>

          <!-- Show System Templates -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Papar Template Sistem
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Tunjukkan template yang disediakan oleh sistem
              </p>
            </div>
            <UiToggle :model-value="preferences.show_system_templates" @update:model-value="toggleSystemTemplates" />
          </div>

          <!-- Show Usage Stats -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Papar Statistik Penggunaan
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Tunjukkan berapa kali template telah digunakan
              </p>
            </div>
            <UiToggle :model-value="preferences.show_usage_stats" @update:model-value="toggleUsageStats" />
          </div>

          <!-- Show Template Suggestions -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Papar Cadangan Template
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Tunjukkan cadangan template berdasarkan konteks
              </p>
            </div>
            <UiToggle :model-value="preferences.show_template_suggestions"
              @update:model-value="toggleTemplateSuggestions" />
          </div>
        </div>
      </div>

      <!-- Category Preferences Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Kategori Keutamaan
        </h4>

        <div class="space-y-4">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Pilih kategori template yang anda kerap gunakan untuk paparan keutamaan
          </p>

          <div class="grid grid-cols-2 gap-3">
            <label v-for="(label, category) in categoryOptions" :key="category"
              class="flex items-center space-x-2 cursor-pointer">
              <input type="checkbox" :checked="isPreferredCategory(category)"
                @change="togglePreferredCategory(category)"
                class="rounded border-gray-300 text-primary focus:ring-primary" />
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ label }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Advanced Settings Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Tetapan Lanjutan
        </h4>

        <div class="space-y-4">
          <!-- Auto Save Custom Templates -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Auto-Simpan Template Peribadi
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Simpan template peribadi secara automatik apabila dibuat
              </p>
            </div>
            <UiToggle :model-value="preferences.auto_save_custom_templates"
              @update:model-value="toggleAutoSaveCustomTemplates" />
          </div>
        </div>
      </div>

      <!-- Reset Section -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Set Semula Tetapan
        </h4>

        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Set semula semua tetapan template kepada nilai lalai
            </p>
          </div>
          <UiBaseButton @click="handleResetToDefaults" variant="outline" size="sm" :loading="resetting">
            Set Semula
          </UiBaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useTemplatePreferences } from '~/composables/useTemplatePreferences';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import UiToggle from '~/components/ui/base/Toggle.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import SimpleTemplateSelector from '~/components/rph/reflection/SimpleTemplateSelector.vue';

// Composables
const {
  preferences,
  loading,
  error,
  hasDefaultTemplate,
  isPreferredCategory,
  fetchPreferences,
  setDefaultTemplate,
  toggleAutoApplyDefault,
  updateSortOrder,
  updatePreferredCategories,
  toggleSystemTemplates,
  toggleUsageStats,
  toggleTemplateSuggestions,
  updatePreviewMode,
  toggleAutoSaveCustomTemplates,
  resetToDefaults
} = useTemplatePreferences();

// Local state
const resetting = ref(false);

// Category options
const categoryOptions = TEMPLATE_CATEGORY_LABELS;

// Methods
const handleDefaultTemplateChange = async (templateId: string | null) => {
  try {
    await setDefaultTemplate(templateId);
    // You could emit a success toast here
    console.log('Default template updated successfully');
  } catch (err) {
    console.error('Error setting default template:', err);
    // You could emit an error toast here
    // emit('error', 'Failed to update default template');
  }
};

const handleSortOrderChange = async (event: Event) => {
  const target = event.target as HTMLSelectElement;
  try {
    await updateSortOrder(target.value as any);
    console.log('Sort order updated successfully');
  } catch (err) {
    console.error('Error updating sort order:', err);
    // Reset the select to previous value on error
    target.value = preferences.value.template_sort_order;
  }
};

const handlePreviewModeChange = async (event: Event) => {
  const target = event.target as HTMLSelectElement;
  try {
    await updatePreviewMode(target.value as any);
    console.log('Preview mode updated successfully');
  } catch (err) {
    console.error('Error updating preview mode:', err);
    // Reset the select to previous value on error
    target.value = preferences.value.template_preview_mode;
  }
};

const togglePreferredCategory = async (category: string) => {
  try {
    const currentCategories = preferences.value.preferred_categories;
    const newCategories = currentCategories.includes(category)
      ? currentCategories.filter(c => c !== category)
      : [...currentCategories, category];

    await updatePreferredCategories(newCategories);
  } catch (err) {
    console.error('Error updating preferred categories:', err);
  }
};

const handleResetToDefaults = async () => {
  try {
    resetting.value = true;
    await resetToDefaults();
  } catch (err) {
    console.error('Error resetting to defaults:', err);
  } finally {
    resetting.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  try {
    await fetchPreferences();
  } catch (err) {
    console.error('Error loading template preferences:', err);
  }
});
</script>
