// Permission-based route protection middleware
// Created: 2025-07-13

import type { Permission, Role } from '~/composables/usePermissions'

export default defineNuxtRouteMiddleware((to) => {
  // Get permission requirements from route meta
  const requiredPermissions = to.meta.permissions as Permission[] | undefined
  const requiredRoles = to.meta.roles as Role[] | undefined
  const requireAnyPermission = to.meta.requireAnyPermission as boolean | undefined
  const requireAnyRole = to.meta.requireAnyRole as boolean | undefined

  // Skip if no permission requirements
  if (!requiredPermissions && !requiredRoles) {
    return
  }

  // For now, use simplified permission checks
  // TODO: Replace with actual usePermissions() when auto-import is working
  const hasPermission = (permission: Permission) => false
  const hasAnyPermission = (permissions: Permission[]) => false
  const hasAllPermissions = (permissions: Permission[]) => false
  const hasRole = (role: Role) => false
  const hasAnyRole = (roles: Role[]) => false
  const currentRole = ref<Role | null>(null)

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRequiredRole = requireAnyRole 
      ? hasAnyRole(requiredRoles)
      : requiredRoles.every(role => hasRole(role))

    if (!hasRequiredRole) {
      throw createError({
        statusCode: 403,
        statusMessage: `Access denied. Required role${requiredRoles.length > 1 ? 's' : ''}: ${requiredRoles.join(', ')}`
      })
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAnyPermission
      ? hasAnyPermission(requiredPermissions)
      : hasAllPermissions(requiredPermissions)

    if (!hasRequiredPermissions) {
      throw createError({
        statusCode: 403,
        statusMessage: `Access denied. Required permission${requiredPermissions.length > 1 ? 's' : ''}: ${requiredPermissions.join(', ')}`
      })
    }
  }
})

// Helper function to define route permissions in pages
export const definePagePermissions = (config: {
  permissions?: Permission[]
  roles?: Role[]
  requireAnyPermission?: boolean
  requireAnyRole?: boolean
}) => {
  definePageMeta({
    middleware: 'permissions' as any,
    permissions: config.permissions,
    roles: config.roles,
    requireAnyPermission: config.requireAnyPermission,
    requireAnyRole: config.requireAnyRole
  })
}
