// Stripe webhook handler for payment processing
import Stripe from 'stripe'

export default defineEventHandler(async (event) => {
  try {
    console.log('🎯 Stripe webhook called!')

    // Get Stripe keys from environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET

    if (!stripeSecretKey || !stripeWebhookSecret) {
      console.error('Stripe environment variables missing:', {
        secretKey: stripeSecretKey ? 'Present' : 'Missing',
        webhookSecret: stripeWebhookSecret ? 'Present' : 'Missing'
      })
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe configuration missing'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    const body = await readRawBody(event)
    const signature = getHeader(event, 'stripe-signature')

    if (!signature || !body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing signature or body'
      })
    }

    // Verify webhook signature
    let stripeEvent: Stripe.Event
    try {
      stripeEvent = stripe.webhooks.constructEvent(
        body,
        signature,
        stripeWebhookSecret
      )
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message)
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid signature'
      })
    }

    console.log('✅ Received Stripe webhook:', stripeEvent.type)

    // Handle different event types
    switch (stripeEvent.type) {
      case 'checkout.session.completed':
        console.log('🎯 Processing checkout.session.completed event')
        await handleCheckoutCompleted(stripeEvent.data.object as Stripe.Checkout.Session)
        break

      case 'customer.subscription.created':
        console.log('📋 Subscription created:', stripeEvent.data.object)
        break

      case 'invoice.created':
        console.log('📄 Invoice created:', stripeEvent.data.object)
        break

      case 'invoice.finalized':
        console.log('📄 Invoice finalized:', stripeEvent.data.object)
        break

      case 'invoice.paid':
        console.log('💰 Invoice paid:', stripeEvent.data.object)
        break

      case 'invoice.payment_succeeded':
        console.log('✅ Invoice payment succeeded - checking for school creation')
        await handleInvoicePaymentSucceeded(stripeEvent.data.object as Stripe.Invoice)
        break

      default:
        console.log(`❓ Unhandled event type: ${stripeEvent.type}`)
    }

    return { received: true }

  } catch (error: any) {
    console.error('❌ Webhook error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Webhook processing failed'
    })
  }
})

/**
 * Handle successful checkout completion
 */
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('🏫 Processing checkout completion:', session.id)

    // Get session metadata
    const metadata = session.metadata
    if (!metadata) {
      console.error('No metadata found in checkout session')
      return
    }

    const { schoolCode, schoolName, adminEmail, adminFirstName, adminLastName, selectedPlan } = metadata

    if (!schoolCode || !schoolName || !adminEmail) {
      console.error('Missing required metadata:', metadata)
      return
    }

    console.log('📋 School data:', { schoolCode, schoolName, adminEmail })

    // Create school record
    await createSchoolAccount({
      schoolCode,
      schoolName,
      schoolAddress: metadata.schoolAddress || '',
      adminEmail,
      adminFirstName: adminFirstName || '',
      adminLastName: adminLastName || '',
      selectedPlan,
      stripeCustomerId: session.customer as string,
      stripeSessionId: session.id,
      subscriptionStatus: 'trialing'
    })

    console.log(`✅ School account created successfully: ${schoolCode}`)

  } catch (error) {
    console.error('❌ Error handling checkout completion:', error)
    throw error
  }
}

/**
 * Handle invoice payment succeeded (alternative to checkout.session.completed)
 */
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('💰 Processing invoice payment succeeded:', invoice.id)

    // Get the subscription from the invoice (using any to bypass TypeScript issues)
    const invoiceAny = invoice as any
    const subscriptionId = invoiceAny.subscription

    if (!subscriptionId) {
      console.log('❌ No subscription found in invoice')
      return
    }

    // Get the subscription details to find the checkout session
    const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil'
    })

    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['metadata']
    })

    console.log('📋 Subscription metadata:', subscription.metadata)

    // Check if we have the required metadata
    const { schoolCode, schoolName, adminEmail } = subscription.metadata || {}

    if (!schoolCode || !schoolName || !adminEmail) {
      console.log('❌ Missing required metadata in subscription:', subscription.metadata)
      return
    }

    // Create school record using subscription metadata
    await createSchoolAccount({
      schoolCode,
      schoolName,
      schoolAddress: subscription.metadata?.schoolAddress || '',
      adminEmail,
      adminFirstName: subscription.metadata?.adminFirstName || '',
      adminLastName: subscription.metadata?.adminLastName || '',
      selectedPlan: subscription.metadata?.selectedPlan || 'monthly',
      stripeCustomerId: typeof invoice.customer === 'string' ? invoice.customer : (invoice.customer as any)?.id || '',
      stripeSessionId: subscription.metadata?.stripeSessionId || invoice.id || '',
      subscriptionStatus: 'active'
    })

    console.log(`✅ School account created from invoice payment: ${schoolCode}`)

  } catch (error) {
    console.error('❌ Error handling invoice payment:', error)
    throw error
  }
}

/**
 * Create school account in database
 */
async function createSchoolAccount(data: {
  schoolCode: string
  schoolName: string
  schoolAddress: string
  adminEmail: string
  adminFirstName: string
  adminLastName: string
  selectedPlan: string
  stripeCustomerId: string
  stripeSessionId: string
  subscriptionStatus: string
}) {
  try {
    console.log('🏗️ Creating school account:', data.schoolCode)

    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // 1. Create or get admin user (optimized with getUserByEmail)
    let userId: string | undefined
    let existingUser: any = null

    // Try to find existing user by email (more efficient than listing all users)
    try {
      const { data: userByEmail } = await supabase.auth.admin.getUserByEmail(data.adminEmail)
      if (userByEmail?.user) {
        existingUser = userByEmail.user
        userId = existingUser.id
        console.log('👤 Found existing admin user:', userId)

        // Update existing user to be school admin
        await supabase.auth.admin.updateUserById(userId, {
          user_metadata: {
            ...existingUser.user_metadata,
            is_school_admin: true,
            full_name: `${data.adminFirstName} ${data.adminLastName}`.trim()
          }
        })
      }
    } catch (error) {
      // User doesn't exist, we'll create them below
      console.log('👤 User not found, will create new user')
    }

    if (!userId) {
      // Create new user
      const { data: adminUser, error: adminError } = await supabase.auth.admin.createUser({
        email: data.adminEmail,
        password: Math.random().toString(36).slice(-8), // Random password
        email_confirm: true,
        user_metadata: {
          full_name: `${data.adminFirstName} ${data.adminLastName}`.trim(),
          is_school_admin: true
        }
      })

      if (adminError) {
        console.error('Error creating admin user:', adminError)
        throw new Error('Failed to create admin user')
      }

      userId = adminUser?.user?.id
      console.log('👤 Created new admin user:', userId)
    }

    if (!userId) {
      throw new Error('Could not get admin user ID')
    }

    console.log('👤 Admin user ID:', userId)

    // 2-4. Run parallel operations for better performance
    const [
      { data: existingSchool },
      { data: preBillingData }
    ] = await Promise.all([
      // Check if school already exists
      supabase
        .from('schools')
        .select('*')
        .eq('code', data.schoolCode.toLowerCase())
        .single(),
      // Get admin_full_name from pre_billing table
      supabase
        .from('pre_billing')
        .select('admin_full_name')
        .eq('admin_email', data.adminEmail)
        .single()
    ])

    let school
    if (existingSchool) {
      console.log('🏫 School already exists:', existingSchool.id)
      school = existingSchool
    } else {
      // Create new school record
      const { data: newSchool, error: schoolError } = await supabase
        .from('schools')
        .insert({
          name: data.schoolName,
          code: data.schoolCode.toLowerCase(),
          admin_user_id: userId,
          location: data.schoolAddress || null,
          subscription_status: data.subscriptionStatus,
          subscription_plan: data.selectedPlan,
          next_billing_date: data.subscriptionStatus === 'trialing'
            ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days trial
            : null,
          settings: {
            stripe_customer_id: data.stripeCustomerId,
            stripe_session_id: data.stripeSessionId,
            selected_plan: data.selectedPlan,
            created_via: 'stripe_checkout',
            contact_email: data.adminEmail
          },
          is_active: true,
          subdomain_status: 'active'
        })
        .select()
        .single()

      if (schoolError || !newSchool) {
        console.error('Error creating school:', schoolError)
        throw new Error('Failed to create school record')
      }

      school = newSchool
      console.log('🏫 Created new school:', school.id)
    }

    // 3. Check if school membership already exists
    const { data: existingMembership } = await supabase
      .from('school_memberships')
      .select('*')
      .eq('user_id', userId)
      .eq('school_id', school.id)
      .single()

    if (existingMembership) {
      console.log('👥 School membership already exists:', existingMembership.id)
    } else {
      // Create school membership for admin
      const { data: membership, error: membershipError } = await supabase
        .from('school_memberships')
        .insert({
          user_id: userId,
          school_id: school.id,
          role: 'admin',
          status: 'active'
        })
        .select()
        .single()

      if (membershipError) {
        console.error('Error creating school membership:', membershipError)
        // Don't throw here - school is created, membership can be fixed later
      } else {
        console.log('👥 Created membership:', membership.id)
      }
    }

    const adminFullName = preBillingData?.admin_full_name || `${data.adminFirstName} ${data.adminLastName}`.trim()

    console.log('📋 Profile update data:', {
      userId,
      adminFullName,
      preBillingData: preBillingData?.admin_full_name,
      fallbackName: `${data.adminFirstName} ${data.adminLastName}`.trim()
    })

    // 5. Update user profile with school admin flag and proper role
    const profileUpdateData = {
      id: userId,
      full_name: adminFullName,
      is_school_admin: true,
      role: { code: "as", label: "Admin Sekolah" },
      teaching_days_mode: "weekdays",
      is_profile_complete: true // Admin profile is complete after payment
    }

    console.log('📝 Updating profile with data:', profileUpdateData)

    // 5-6. Run profile update and cleanup in parallel for better performance
    const [
      { data: profileResult, error: profileError },
      { error: cleanupError }
    ] = await Promise.all([
      // Update user profile
      supabase
        .from('profiles')
        .upsert(profileUpdateData)
        .select(),
      // Clean up pre-billing data (delete the record)
      supabase
        .from('pre_billing')
        .delete()
        .eq('admin_email', data.adminEmail)
    ])

    if (profileError) {
      console.error('❌ Error updating user profile:', profileError)
      // Don't throw here - school is created, profile can be fixed later
    } else {
      console.log('✅ Profile updated successfully:', profileResult)
      console.log('👤 Updated profile for school admin:', userId)
    }

    if (cleanupError) {
      console.error('❌ Error cleaning up pre-billing data:', cleanupError)
      // Don't throw here - school is created, cleanup can be done manually
    } else {
      console.log('🧹 Cleaned up pre-billing data for:', data.adminEmail)
    }

    console.log('✅ School account creation completed successfully')

    return {
      success: true,
      schoolId: school.id,
      adminId: userId,
      schoolCode: school.code,
      schoolName: school.name
    }

  } catch (error: any) {
    console.error('❌ Error in createSchoolAccount:', error)
    throw error
  }
}
