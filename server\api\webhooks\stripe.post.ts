// Stripe webhook handler for payment processing
import Stripe from 'stripe'

export default defineEventHandler(async (event) => {
  try {
    console.log('🎯 Stripe webhook called!')

    // Get Stripe keys from environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET

    if (!stripeSecretKey || !stripeWebhookSecret) {
      console.error('Stripe environment variables missing:', {
        secretKey: stripeSecretKey ? 'Present' : 'Missing',
        webhookSecret: stripeWebhookSecret ? 'Present' : 'Missing'
      })
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe configuration missing'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    const body = await readRawBody(event)
    const signature = getHeader(event, 'stripe-signature')

    if (!signature || !body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing signature or body'
      })
    }

    // Verify webhook signature
    let stripeEvent: Stripe.Event
    try {
      stripeEvent = stripe.webhooks.constructEvent(
        body,
        signature,
        stripeWebhookSecret
      )
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message)
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid signature'
      })
    }

    console.log('✅ Received Stripe webhook:', stripeEvent.type)

    // Handle different event types
    switch (stripeEvent.type) {
      case 'checkout.session.completed':
        console.log('🎯 Processing checkout.session.completed event')
        await handleCheckoutCompleted(stripeEvent.data.object as Stripe.Checkout.Session)
        break

      case 'customer.subscription.created':
        console.log('📋 Subscription created:', stripeEvent.data.object)
        break

      case 'invoice.created':
        console.log('📄 Invoice created:', stripeEvent.data.object)
        break

      case 'invoice.finalized':
        console.log('📄 Invoice finalized:', stripeEvent.data.object)
        break

      case 'invoice.paid':
        console.log('💰 Invoice paid:', stripeEvent.data.object)
        break

      case 'invoice.payment_succeeded':
        console.log('✅ Invoice payment succeeded - checking for school creation')
        await handleInvoicePaymentSucceeded(stripeEvent.data.object as Stripe.Invoice)
        break

      default:
        console.log(`❓ Unhandled event type: ${stripeEvent.type}`)
    }

    return { received: true }

  } catch (error: any) {
    console.error('❌ Webhook error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Webhook processing failed'
    })
  }
})

/**
 * Handle successful checkout completion
 */
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('🏫 Processing checkout completion:', session.id)

    // Get session metadata
    const metadata = session.metadata
    if (!metadata) {
      console.error('No metadata found in checkout session')
      return
    }

    const { schoolCode, schoolName, adminEmail, adminFirstName, adminLastName, selectedPlan } = metadata

    if (!schoolCode || !schoolName || !adminEmail) {
      console.error('Missing required metadata:', metadata)
      return
    }

    console.log('📋 School data:', { schoolCode, schoolName, adminEmail })

    // Create school record
    await createSchoolAccount({
      schoolCode,
      schoolName,
      schoolAddress: metadata.schoolAddress || '',
      adminEmail,
      adminFirstName: adminFirstName || '',
      adminLastName: adminLastName || '',
      selectedPlan,
      stripeCustomerId: session.customer as string,
      stripeSessionId: session.id,
      subscriptionStatus: 'trialing'
    })

    console.log(`✅ School account created successfully: ${schoolCode}`)

  } catch (error) {
    console.error('❌ Error handling checkout completion:', error)
    throw error
  }
}

/**
 * Handle invoice payment succeeded (alternative to checkout.session.completed)
 */
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('💰 Processing invoice payment succeeded:', invoice.id)

    // Get the subscription from the invoice
    if (!invoice.subscription) {
      console.log('❌ No subscription found in invoice')
      return
    }

    // Get the subscription details to find the checkout session
    const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil'
    })

    const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string, {
      expand: ['metadata']
    })

    console.log('📋 Subscription metadata:', subscription.metadata)

    // Check if we have the required metadata
    const { schoolCode, schoolName, adminEmail } = subscription.metadata

    if (!schoolCode || !schoolName || !adminEmail) {
      console.log('❌ Missing required metadata in subscription:', subscription.metadata)
      return
    }

    // Create school record using subscription metadata
    await createSchoolAccount({
      schoolCode,
      schoolName,
      schoolAddress: subscription.metadata.schoolAddress || '',
      adminEmail,
      adminFirstName: subscription.metadata.adminFirstName || '',
      adminLastName: subscription.metadata.adminLastName || '',
      selectedPlan: subscription.metadata.selectedPlan || 'monthly',
      stripeCustomerId: invoice.customer as string,
      stripeSessionId: subscription.metadata.stripeSessionId || invoice.id,
      subscriptionStatus: 'active'
    })

    console.log(`✅ School account created from invoice payment: ${schoolCode}`)

  } catch (error) {
    console.error('❌ Error handling invoice payment:', error)
    throw error
  }
}

/**
 * Create school account in database
 */
async function createSchoolAccount(data: {
  schoolCode: string
  schoolName: string
  schoolAddress: string
  adminEmail: string
  adminFirstName: string
  adminLastName: string
  selectedPlan: string
  stripeCustomerId: string
  stripeSessionId: string
  subscriptionStatus: string
}) {
  try {
    console.log('🏗️ Creating school account:', data.schoolCode)

    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // 1. Create or get admin user
    const { data: adminUser, error: adminError } = await supabase.auth.admin.createUser({
      email: data.adminEmail,
      password: Math.random().toString(36).slice(-8), // Random password
      email_confirm: true,
      user_metadata: {
        full_name: `${data.adminFirstName} ${data.adminLastName}`.trim(),
        is_school_admin: true
      }
    })

    if (adminError && !adminError.message.includes('already registered')) {
      console.error('Error creating admin user:', adminError)
      throw new Error('Failed to create admin user')
    }

    // If user already exists, get their ID
    let userId = adminUser?.user?.id
    if (!userId) {
      const { data: existingUsers } = await supabase.auth.admin.listUsers()
      const existingUser = existingUsers.users.find(u => u.email === data.adminEmail)
      userId = existingUser?.id
    }

    if (!userId) {
      throw new Error('Could not get admin user ID')
    }

    console.log('👤 Admin user ID:', userId)

    // 2. Create school record
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .insert({
        name: data.schoolName,
        code: data.schoolCode.toLowerCase(),
        admin_user_id: userId,
        address: data.schoolAddress || null,
        contact_email: data.adminEmail,
        subscription_status: data.subscriptionStatus,
        subscription_expires_at: data.subscriptionStatus === 'trialing'
          ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days trial
          : null,
        settings: {
          stripe_customer_id: data.stripeCustomerId,
          stripe_session_id: data.stripeSessionId,
          selected_plan: data.selectedPlan,
          created_via: 'stripe_checkout'
        }
      })
      .select()
      .single()

    if (schoolError || !school) {
      console.error('Error creating school:', schoolError)
      throw new Error('Failed to create school record')
    }

    console.log('🏫 Created school:', school.id)

    // 3. Create school membership for admin
    const { data: membership, error: membershipError } = await supabase
      .from('school_memberships')
      .insert({
        user_id: userId,
        school_id: school.id,
        role: 'admin',
        status: 'active'
      })
      .select()
      .single()

    if (membershipError) {
      console.error('Error creating school membership:', membershipError)
      // Don't throw here - school is created, membership can be fixed later
    } else {
      console.log('👥 Created membership:', membership.id)
    }

    console.log('✅ School account creation completed successfully')

    return {
      success: true,
      schoolId: school.id,
      adminId: userId,
      schoolCode: school.code,
      schoolName: school.name
    }

  } catch (error: any) {
    console.error('❌ Error in createSchoolAccount:', error)
    throw error
  }
}
