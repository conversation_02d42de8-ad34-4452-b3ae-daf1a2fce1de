// Stripe webhook handler for payment processing
export default defineEventHandler(async (event) => {
  try {
    console.log('🎯 Stripe webhook called!')

    return {
      success: true,
      message: 'Webhook endpoint working',
      timestamp: new Date().toISOString(),
      method: getMethod(event)
    }

  } catch (error: any) {
    console.error('❌ Webhook error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Webhook processing failed'
    })
  }
})


