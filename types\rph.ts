import type { Database } from "./supabase";
import type { LessonPlan } from "./lessonPlans";

// RphWeek: Represents a week in the RPH system, aligning with the rph_weeks table
export type RphWeek = Database["public"]["Tables"]["rph_weeks"]["Row"];

// RphWeekUserInput: Data provided by the caller to create a new week.
// Excludes user_id, id, created_at which are handled by Supabase/composable.
export type RphWeekUserInput = Omit<
  Database["public"]["Tables"]["rph_weeks"]["Insert"],
  "id" | "created_at" | "user_id"
>;

// RphWeekUpdate: For updating existing RphWeek entries.
// All fields should be optional as we might update only a subset.
export type RphWeekUpdate = Database["public"]["Tables"]["rph_weeks"]["Update"];

// If you need to associate Lesson Plans with RphWeek directly in types, you can extend RphWeek:
export interface RphWeekWithLessons extends RphWeek {
  lesson_plans?: LessonPlan[];
}

// Placeholder for other RPH specific types if needed in the future
// For example, if we extract the structure of a day's schedule from rph.vue:
// export interface DailySchedule {
//   hari: string; // e.g., "Isnin"
//   lessonPlans: LessonPlan[];
// }
