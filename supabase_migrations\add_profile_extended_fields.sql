-- Migration: Add extended profile fields for comprehensive user information
-- Created: 2025-01-01
-- Description: Add fields for personal information, options, academic qualifications, reference numbers, and appointment information

BEGIN;

-- =====================================================
-- ADD NEW COLUMNS TO profiles TABLE
-- =====================================================

-- 1. <PERSON><PERSON><PERSON>at Peribadi (Personal Information)
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS ic_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS teacher_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS religion VARCHAR(50),
ADD COLUMN IF NOT EXISTS date_of_birth DATE;

-- 2. <PERSON><PERSON><PERSON> (Options) - Dynamic array of user-defined options
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS options JSONB DEFAULT '[]'::jsonb;

-- 3. <PERSON><PERSON><PERSON><PERSON> (Academic Qualifications) - Dynamic array of qualifications
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS academic_qualifications JSONB DEFAULT '[]'::jsonb;

-- 4. Nombor Rujukan (Reference Numbers)
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS file_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS spp_reference_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS salary_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS epf_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS income_tax_number VARCHAR(50);

-- 5. Maklumat Pelantikan (Appointment Information)
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS appointment_date DATE,
ADD COLUMN IF NOT EXISTS position_confirmation_date DATE,
ADD COLUMN IF NOT EXISTS pensionable_position_date DATE,
ADD COLUMN IF NOT EXISTS retirement_date DATE;

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN profiles.ic_number IS 'Identity card number (No. Kad Pengenalan)';
COMMENT ON COLUMN profiles.teacher_type IS 'Type of teacher (Jenis Guru) - same options as role dropdown';
COMMENT ON COLUMN profiles.religion IS 'Religion (Agama)';
COMMENT ON COLUMN profiles.date_of_birth IS 'Date of birth (Tarikh Lahir)';
COMMENT ON COLUMN profiles.options IS 'User-defined options array - dynamic field';
COMMENT ON COLUMN profiles.academic_qualifications IS 'Academic qualifications array - dynamic field';
COMMENT ON COLUMN profiles.file_number IS 'File number (No. Fail)';
COMMENT ON COLUMN profiles.spp_reference_number IS 'SPP reference number (No. Rujukan SPP)';
COMMENT ON COLUMN profiles.salary_number IS 'Salary number (No. Gaji)';
COMMENT ON COLUMN profiles.epf_number IS 'EPF number (No. KWSP)';
COMMENT ON COLUMN profiles.income_tax_number IS 'Income tax number (No. Cukai Pendapatan)';
COMMENT ON COLUMN profiles.appointment_date IS 'Appointment date (Tarikh Pelantikan)';
COMMENT ON COLUMN profiles.position_confirmation_date IS 'Position confirmation date (Tarikh Pengesahan Jawatan)';
COMMENT ON COLUMN profiles.pensionable_position_date IS 'Date entered pensionable position (Tarikh Masuk ke Jawatan Berpencen)';
COMMENT ON COLUMN profiles.retirement_date IS 'Retirement date (Tarikh Pencen)';

-- =====================================================
-- ADD INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Index for JSONB columns
CREATE INDEX IF NOT EXISTS idx_profiles_options ON profiles USING GIN (options);
CREATE INDEX IF NOT EXISTS idx_profiles_academic_qualifications ON profiles USING GIN (academic_qualifications);

-- Index for commonly searched fields
CREATE INDEX IF NOT EXISTS idx_profiles_ic_number ON profiles (ic_number);
CREATE INDEX IF NOT EXISTS idx_profiles_teacher_type ON profiles (teacher_type);

-- =====================================================
-- ADD CONSTRAINTS AND VALIDATIONS
-- =====================================================

-- Ensure IC number format is valid (basic validation)
ALTER TABLE profiles 
ADD CONSTRAINT chk_ic_number_format 
CHECK (ic_number IS NULL OR (LENGTH(ic_number) >= 10 AND LENGTH(ic_number) <= 20));

-- Ensure date constraints are logical
ALTER TABLE profiles 
ADD CONSTRAINT chk_appointment_dates 
CHECK (
    (appointment_date IS NULL OR position_confirmation_date IS NULL OR appointment_date <= position_confirmation_date) AND
    (position_confirmation_date IS NULL OR pensionable_position_date IS NULL OR position_confirmation_date <= pensionable_position_date) AND
    (pensionable_position_date IS NULL OR retirement_date IS NULL OR pensionable_position_date <= retirement_date) AND
    (date_of_birth IS NULL OR appointment_date IS NULL OR date_of_birth < appointment_date)
);

COMMIT;
