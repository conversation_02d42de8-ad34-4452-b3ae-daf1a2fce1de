@import "./effects.css"; /* Import ripple effect styles */
/* assets/css/tailwind.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: 0 133 219; /* #0085DB */
  --color-secondary: 112 122 130; /* #707A82 */
  --color-light-background: 255 255 255;
  --color-light-foreground: 31 41 55; /* gray-800 */
  --color-dark-background: 17 24 39; /* gray-900 */
  --color-dark-foreground: 209 213 219; /* gray-300 */
}

.dark {
  --color-primary: 0 133 219; /* Same Blue for dark mode */
  --color-secondary: 124 143 172; /* Lighter Gray for dark mode text/elements */
}

body {
  @apply bg-light-background text-light-foreground dark:bg-dark-background dark:text-dark-foreground;
  font-family: "Poppins", sans-serif;
  margin: 0;
  padding: 0;
}

html {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Add other global styles or component layer customizations here */

@layer components {
  .form-input {
    @apply w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-dark-background pb-2.5 pt-5 pl-3 text-left text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-500 sm:text-sm sm:leading-6 disabled:cursor-not-allowed disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400 transition duration-300 ease-in-out;
    /* Custom focus styles with modern ring glow effect */
    outline: none;
  }

  .form-input:focus {
    border-color: rgb(var(--color-primary));
    box-shadow: 0 0 0 2px rgb(var(--color-primary) / 0.2);
  }
  /* You can add more global component styles here if needed */

  .listbox-option {
    /* Added hover styles for mouse interaction */
    @apply relative cursor-pointer select-none py-2 pl-3 pr-9 text-light-foreground dark:text-dark-foreground transition-colors duration-300 ease-in-out hover:bg-primary/10 hover:text-primary dark:hover:text-primary;
  }

  .listbox-option--active {
    /* Background removed, only text color changes for keyboard/programmatic active state (when not selected) */
    @apply bg-primary/10 text-primary dark:text-primary;
  }

  .listbox-option--selected {
    /* Styles for selected items (checked) */
    @apply bg-primary/10 font-semibold text-primary dark:text-primary;
  }

  /* Combined class for when an option is both active (hovered/keyboard focus) AND selected */
  .listbox-option--active.listbox-option--selected {
    /* Specific style for hovered selected items, slightly darker background */
    @apply bg-primary/15 text-primary dark:text-primary font-semibold;
  }
  .listbox-option__icon {
    @apply absolute inset-y-0 right-0 flex items-center pr-4 text-primary; /* Icon color set to primary */
  }

  /* Custom scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 0.3s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.8);
  }

  .dark .custom-scrollbar {
    scrollbar-color: rgba(107, 114, 128, 0.5) transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(107, 114, 128, 0.5);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 114, 128, 0.8);
  }
}
