import { z } from "zod";

// Common validation messages
const VALIDATION_MESSAGES = {
  CLASS_REQUIRED: "Sila pilih tahap kelas (cth: Tahun 1, Tingkatan 2).",
  CLASS_NAME_REQUIRED: "<PERSON><PERSON> k<PERSON> diperlukan (min 1 aksara).",
  SUBJECT_ABBREV_REQUIRED: "Singkatan nama subjek diperlukan (1-10 aksara).",
  SUBJECT_ABBREV_MAX: "Singkatan nama subjek tidak boleh melebihi 10 aksara.",
  FULL_NAME_MIN: "<PERSON>a penuh diperlukan (min 3 aksara).",
  AVATAR_URL_INVALID: "Format URL avatar tidak sah.",
  EMAIL_INVALID: "emel tidak sah",
  STUDENT_COUNT_INVALID: "Jumlah murid mesti nombor.",
  STUDENT_COUNT_POSITIVE: "Jumlah murid mesti nombor positif.",
  STUDENT_COUNT_INTEGER: "Jumlah murid mesti nombor bulat.",
  SUBJECT_ID_INVALID: "ID subjek mesti rentetan UUID.",
  SUBJECT_ID_UUID: "ID subjek mesti dalam format UUID yang sah.",
  SUBJECT_ABBREV_STRING: "Singkatan nama subjek mesti rentetan.",
  CLASS_INVALID: "Tahap kelas tidak sah.",
  GENDER_REQUIRED: "Sila pilih Jantina.",
  ROLE_REQUIRED: "Sila pilih Peranan.",
  CLASS_SUBJECTS_MIN: "Sila masukkan sekurang-kurangnya satu Subjek Kelas.",
};

// Define the basic shape of an individual class-subject item.
// This shape matches the data structure managed by ClassSubject.vue and stored in the profile.
const ClassSubjectItemShape = z.object({
  class_id: z
    .string({
      required_error: VALIDATION_MESSAGES.CLASS_REQUIRED,
      invalid_type_error: VALIDATION_MESSAGES.CLASS_INVALID,
    })
    .nonempty(VALIDATION_MESSAGES.CLASS_REQUIRED),
  className: z
    .string()
    .min(1, VALIDATION_MESSAGES.CLASS_NAME_REQUIRED),
  studentCount: z
    .number({
      invalid_type_error: VALIDATION_MESSAGES.STUDENT_COUNT_INVALID,
    })
    .positive(VALIDATION_MESSAGES.STUDENT_COUNT_POSITIVE)
    .int(VALIDATION_MESSAGES.STUDENT_COUNT_INTEGER)
    .nullable()
    .optional(),
  subject_id: z
    .string({
      invalid_type_error: VALIDATION_MESSAGES.SUBJECT_ID_INVALID,
    })
    .uuid(VALIDATION_MESSAGES.SUBJECT_ID_UUID)
    .nullable(),
  subject_abbreviation: z
    .string({
      required_error: VALIDATION_MESSAGES.SUBJECT_ABBREV_REQUIRED,
      invalid_type_error: VALIDATION_MESSAGES.SUBJECT_ABBREV_STRING,
    })
    .min(1, VALIDATION_MESSAGES.SUBJECT_ABBREV_REQUIRED)
    .max(10, VALIDATION_MESSAGES.SUBJECT_ABBREV_MAX)
    .optional()
    .default(""),
});

// Reusable field validation schemas to eliminate duplication
const CommonFieldValidations = {
  fullName: z
    .string()
    .min(3, VALIDATION_MESSAGES.FULL_NAME_MIN),
  
  avatarUrl: z
    .string()
    .url(VALIDATION_MESSAGES.AVATAR_URL_INVALID),
  
  email: z
    .string()
    .email(VALIDATION_MESSAGES.EMAIL_INVALID),
  
  requiredString: (fieldName: string) => z
    .string({ required_error: `Sila pilih ${fieldName}.` })
    .nonempty(`Sila pilih ${fieldName}.`),
};

export const ProfileSchema = z.object({
  id: z.string().uuid(),
  email: CommonFieldValidations.email,
  full_name: CommonFieldValidations.fullName.nullable(),
  avatar_url: CommonFieldValidations.avatarUrl.nullable(),
  is_profile_complete: z.boolean().default(false),
  // Add other profile fields here
});

export const ProfileCompletionSchema = z.object({
  full_name: CommonFieldValidations.fullName,
  jantina: CommonFieldValidations.requiredString("Jantina"),
  peranan: CommonFieldValidations.requiredString("Peranan"),
  avatar_url: CommonFieldValidations.avatarUrl.optional().nullable(),
  classSubjectsData: z
    .array(ClassSubjectItemShape)
    .min(1, VALIDATION_MESSAGES.CLASS_SUBJECTS_MIN),
});

export type UserProfile = z.infer<typeof ProfileSchema>;
export type ProfileCompletionForm = z.infer<typeof ProfileCompletionSchema>;
export type UserClassSubjectEntry = z.infer<typeof ClassSubjectItemShape>; // Added export for the item type
