/**
 * Date utility functions for handling timezone-safe date operations
 */

/**
 * Format a Date object to local string in YYYY-MM-DD format
 * This avoids timezone issues that occur with toISOString()
 */
export const formatDateToLocalString = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * Parse a date string (YYYY-MM-DD) to a Date object in local timezone
 */
export const parseDateFromLocalString = (dateString: string): Date => {
  const [year, month, day] = dateString.split('-').map(Number)
  return new Date(year, month - 1, day)
}

/**
 * Get today's date in YYYY-MM-DD format (local timezone)
 */
export const getTodayLocalString = (): string => {
  return formatDateToLocalString(new Date())
}

/**
 * Check if two date strings represent the same date
 */
export const isSameDate = (date1: string, date2: string): boolean => {
  return date1 === date2
}

/**
 * Add days to a date string and return new date string
 */
export const addDaysToDateString = (dateString: string, days: number): string => {
  const date = parseDateFromLocalString(dateString)
  date.setDate(date.getDate() + days)
  return formatDateToLocalString(date)
}

/**
 * Get the first day of the month for a given date string
 */
export const getFirstDayOfMonth = (dateString: string): string => {
  const date = parseDateFromLocalString(dateString)
  return formatDateToLocalString(new Date(date.getFullYear(), date.getMonth(), 1))
}

/**
 * Get the last day of the month for a given date string
 */
export const getLastDayOfMonth = (dateString: string): string => {
  const date = parseDateFromLocalString(dateString)
  return formatDateToLocalString(new Date(date.getFullYear(), date.getMonth() + 1, 0))
}
