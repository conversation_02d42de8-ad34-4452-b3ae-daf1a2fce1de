<template>
  <button
    :class="[baseClasses, variantClasses, sizeClasses, { 'pl-8 pr-4': prependIcon, 'pr-8 pl-4': appendIcon }, 'overflow-hidden']"
    :aria-label="ariaLabel || undefined" @click="createRipple">
    <Icon v-if="prependIcon" :name="prependIcon" :size="iconSize"
      :class="['absolute left-3 top-1/2 -translate-y-1/2 z-10', iconColor ? iconColor : '']" />
    <span class="relative z-10">
      <slot />
    </span>
    <Icon v-if="appendIcon" :name="appendIcon" :size="iconSize"
      :class="['absolute right-3 top-1/2 -translate-y-1/2 z-10', iconColor ? iconColor : '']" />
    <span v-for="ripple in ripples" :key="ripple.id" class="ripple"
      :style="{ top: ripple.top + 'px', left: ripple.left + 'px', width: ripple.size + 'px', height: ripple.size + 'px' }"></span>
  </button>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'; // Added ref
import Icon from './Icon.vue'; // Import Icon

const props = defineProps<{
  variant?: "primary" | "secondary" | "outline" | "alert-error" | "flat" | "delete"; // Added delete variant
  size?: "xs" | "sm" | "md" | "lg";
  ariaLabel?: string;
  prependIcon?: string; // This will be used as the 'name' for the Icon component
  appendIcon?: string;  // This will be used as the 'name' for the Icon component
  iconColor?: string; // Should be a Tailwind text color class, e.g., 'text-white'
  iconSize?: string;  // e.g., '1.5em', '24px'
}>();

const baseClasses = 'relative font-medium rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 inline-flex items-center justify-center transition-colors duration-200 ease-in-out';

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'primary':
      return 'bg-primary text-white hover:bg-primary/80 focus-visible:ring-primary';
    case 'secondary':
      return 'bg-secondary text-white hover:bg-secondary/80 focus-visible:ring-secondary';
    case 'outline':
      return 'border border-primary text-primary hover:bg-primary/10 focus-visible:ring-primary';
    case 'alert-error': // Added case for alert-error
      return 'bg-alert-error text-white hover:bg-alert-error/80 focus-visible:ring-alert-error';
    case 'flat':
      return 'bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus-visible:ring-primary';
    case 'delete':
      return 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-600';
    default:
      return 'bg-primary text-white hover:bg-primary/80 focus-visible:ring-primary';
  }
});

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'xs':
      return 'text-sm';
    case 'sm':
      return 'px-3 py-1.5 text-sm';
    case 'md':
      return 'px-4 py-2 text-base';
    case 'lg':
      // For lg, apply md styles on small screens, and lg styles on md screens and up
      return 'px-4 py-2 text-base md:px-6 md:py-3 md:text-lg';
    default:
      return 'px-4 py-2 text-base';
  }
});

const ripples = ref<Array<{ id: number, top: number, left: number, size: number }>>([]);

const createRipple = (event: MouseEvent) => {
  const button = event.currentTarget as HTMLElement;
  const rect = button.getBoundingClientRect();
  const diameter = Math.max(button.clientWidth, button.clientHeight) * 2;
  const x = event.clientX - rect.left; // Click position X relative to button
  const y = event.clientY - rect.top;  // Click position Y relative to button

  const newRipple = {
    id: Date.now(),
    top: y - diameter / 2,
    left: x - diameter / 2,
    size: diameter,
  };

  ripples.value.push(newRipple);

  // Remove ripple after animation (600ms)
  setTimeout(() => {
    ripples.value = ripples.value.filter(r => r.id !== newRipple.id);
  }, 600);
};
</script>

<style scoped>
.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  /* Adjust color/opacity as needed */
  transform: scale(0);
  animation: ripple-effect 600ms linear;
  pointer-events: none;
  /* So it doesn't interfere with button clicks */
  z-index: 0;
  /* Ensure ripple is behind content */
}

@keyframes ripple-effect {
  to {
    transform: scale(1);
    opacity: 0;
  }
}

/* Add any component-specific styles here if needed */
</style>
