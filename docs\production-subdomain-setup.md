# Production Subdomain Setup Guide

## 🌐 Overview

This guide provides step-by-step instructions for setting up subdomain-based multi-tenancy for the RPHMate SaaS platform in production using Vercel.

## 🏗️ Architecture

### URL Structure
```
Production:
├── rphmate.com                    # Landing page
├── rphmate.com/pricing           # Pricing page
├── rphmate.com/payment           # Payment/billing
├── rphmate.com/login             # School admin login
├── rphmate.com/schoolcode        # School admin dashboard
└── schoolcode.rphmate.com        # School application
    ├── /                         # School login page
    └── /dashboard                # School dashboard & features

Development:
├── localhost:3000                # Landing page
├── localhost:3000/pricing        # Pricing page
├── localhost:3000/billing        # Payment/billing
├── localhost:3000/login          # School admin login
├── localhost:3000/schoolcode     # School admin dashboard
└── schoolcode.localhost:3000     # School application
    ├── /                         # School login page
    └── /dashboard                # School dashboard & features
```

## 🚀 Vercel Configuration

### 1. Domain Setup

#### 1.1 Add Custom Domain
```bash
# In Vercel Dashboard
1. Go to Project Settings
2. Navigate to Domains
3. Add custom domain: rphmate.com
4. Add wildcard domain: *.rphmate.com
```

#### 1.2 DNS Configuration
```dns
# DNS Records (at your domain registrar)
Type    Name    Value                           TTL
A       @       ***********                    300
A       *       ***********                    300
CNAME   www     cname.vercel-dns.com           300

# Alternative CNAME setup
CNAME   @       your-project.vercel.app        300
CNAME   *       your-project.vercel.app        300
```
TTL: 300
```

### 3. DNS Verification

Test DNS configuration:

```bash
# Test main domain
nslookup yourdomain.com

# Test wildcard subdomain
nslookup demo.yourdomain.com
nslookup test.yourdomain.com
```

## SSL Certificate Configuration

### 1. Wildcard SSL Certificate

Obtain a wildcard SSL certificate that covers all subdomains:

```bash
# Using Let's Encrypt with Certbot
certbot certonly --dns-cloudflare \
  --dns-cloudflare-credentials ~/.secrets/certbot/cloudflare.ini \
  -d yourdomain.com \
  -d *.yourdomain.com

# Using manual DNS challenge
certbot certonly --manual \
  --preferred-challenges dns \
  -d yourdomain.com \
  -d *.yourdomain.com
```

### 2. Certificate Installation

Install the certificate on your web server:

#### Nginx Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com *.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Proxy to Nuxt.js application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com *.yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

#### Apache Configuration
```apache
<VirtualHost *:443>
    ServerName yourdomain.com
    ServerAlias *.yourdomain.com
    
    SSLEngine on
    SSLCertificateFile /etc/letsencrypt/live/yourdomain.com/fullchain.pem
    SSLCertificateKeyFile /etc/letsencrypt/live/yourdomain.com/privkey.pem
    
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # Pass original host header
    ProxyPassReverse / http://localhost:3000/
    ProxyPassReverseMatch ^(/.*) http://localhost:3000$1
</VirtualHost>

<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias *.yourdomain.com
    Redirect permanent / https://yourdomain.com/
</VirtualHost>
```

## Application Configuration

### 1. Environment Variables

Configure production environment variables:

```bash
# .env.production
NUXT_PUBLIC_BASE_DOMAIN=yourdomain.com
NUXT_PUBLIC_DEV_MODE=false
NUXT_PUBLIC_ENABLE_SUBDOMAINS=true

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Security
NUXT_SECRET_KEY=your-secret-key
```

### 2. Nuxt Configuration

Update `nuxt.config.ts` for production:

```typescript
export default defineNuxtConfig({
  // Production-specific configuration
  runtimeConfig: {
    public: {
      baseDomain: process.env.NUXT_PUBLIC_BASE_DOMAIN || 'yourdomain.com',
      enableSubdomains: process.env.NUXT_PUBLIC_ENABLE_SUBDOMAINS || 'true',
    }
  },
  
  // Security headers
  nitro: {
    routeRules: {
      '/**': {
        headers: {
          'X-Frame-Options': 'DENY',
          'X-Content-Type-Options': 'nosniff',
          'X-XSS-Protection': '1; mode=block',
          'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        }
      }
    }
  }
})
```

## Load Balancer Configuration

### 1. AWS Application Load Balancer

```yaml
# ALB Target Group
TargetGroup:
  Type: AWS::ElasticLoadBalancingV2::TargetGroup
  Properties:
    Name: rphmate-targets
    Port: 3000
    Protocol: HTTP
    VpcId: !Ref VPC
    HealthCheckPath: /health
    HealthCheckProtocol: HTTP

# ALB Listener
Listener:
  Type: AWS::ElasticLoadBalancingV2::Listener
  Properties:
    DefaultActions:
      - Type: forward
        TargetGroupArn: !Ref TargetGroup
    LoadBalancerArn: !Ref LoadBalancer
    Port: 443
    Protocol: HTTPS
    Certificates:
      - CertificateArn: !Ref SSLCertificate
```

### 2. Cloudflare Configuration

```yaml
# Cloudflare DNS Records
dns_records:
  - type: A
    name: "@"
    content: "YOUR_SERVER_IP"
    proxied: true
    
  - type: A
    name: "*"
    content: "YOUR_SERVER_IP"
    proxied: true

# Page Rules
page_rules:
  - targets:
      - target: url
        constraint:
          operator: matches
          value: "*.yourdomain.com/*"
    actions:
      - id: ssl
        value: "full"
      - id: cache_level
        value: "standard"
```

## Deployment Scripts

### 1. Production Deployment Script

```bash
#!/bin/bash
# deploy-production.sh

set -e

echo "🚀 Deploying RPHMate to Production"
echo "====================================="

# Build application
echo "📦 Building application..."
npm run build

# Run tests
echo "🧪 Running tests..."
npm run test

# Deploy to server
echo "🚀 Deploying to server..."
rsync -avz --delete .output/ user@server:/var/www/rphmate/

# Restart application
echo "🔄 Restarting application..."
ssh user@server "pm2 restart rphmate"

# Test deployment
echo "🧪 Testing deployment..."
curl -f https://yourdomain.com/health || exit 1
curl -f https://demo.yourdomain.com/health || exit 1

echo "✅ Deployment completed successfully!"
```

### 2. Health Check Endpoint

Create a health check endpoint:

```typescript
// server/api/health.get.ts
export default defineEventHandler(async (event) => {
  const host = getHeader(event, 'host') || ''
  
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    host,
    subdomain: host.split('.')[0] !== host.split('.')[1] ? host.split('.')[0] : null,
    version: process.env.npm_package_version || '1.0.0'
  }
})
```

## Monitoring and Logging

### 1. Subdomain Access Logging

```typescript
// middleware/logging.global.ts
export default defineEventHandler(async (event) => {
  const host = getHeader(event, 'host') || ''
  const subdomain = host.split('.')[0]
  const path = event.node.req.url
  
  console.log(`[${new Date().toISOString()}] ${subdomain} - ${path}`)
})
```

### 2. Error Monitoring

```typescript
// plugins/error-monitoring.client.ts
export default defineNuxtPlugin(() => {
  window.addEventListener('error', (event) => {
    const subdomain = window.location.hostname.split('.')[0]
    
    // Send error to monitoring service
    console.error(`[${subdomain}] Client Error:`, event.error)
  })
})
```

## Security Considerations

### 1. CORS Configuration

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  nitro: {
    routeRules: {
      '/api/**': {
        cors: true,
        headers: {
          'Access-Control-Allow-Origin': 'https://*.yourdomain.com',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        }
      }
    }
  }
})
```

### 2. Rate Limiting

```typescript
// server/middleware/rate-limit.ts
const rateLimiter = new Map()

export default defineEventHandler(async (event) => {
  const host = getHeader(event, 'host') || ''
  const ip = getClientIP(event)
  const key = `${host}:${ip}`
  
  const now = Date.now()
  const windowMs = 15 * 60 * 1000 // 15 minutes
  const maxRequests = 100
  
  const requests = rateLimiter.get(key) || []
  const validRequests = requests.filter((time: number) => now - time < windowMs)
  
  if (validRequests.length >= maxRequests) {
    throw createError({
      statusCode: 429,
      statusMessage: 'Too Many Requests'
    })
  }
  
  validRequests.push(now)
  rateLimiter.set(key, validRequests)
})
```

## Testing Production Setup

### 1. Automated Testing

```bash
#!/bin/bash
# test-production.sh

echo "🧪 Testing Production Subdomain Setup"
echo "===================================="

# Test main domain
echo "Testing main domain..."
curl -f https://yourdomain.com/health

# Test subdomains
echo "Testing subdomains..."
curl -f https://demo.yourdomain.com/health
curl -f https://test.yourdomain.com/health

# Test SSL
echo "Testing SSL..."
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com < /dev/null
openssl s_client -connect demo.yourdomain.com:443 -servername demo.yourdomain.com < /dev/null

echo "✅ All tests passed!"
```

### 2. Load Testing

```bash
# Using Apache Bench
ab -n 1000 -c 10 https://yourdomain.com/
ab -n 1000 -c 10 https://demo.yourdomain.com/

# Using wrk
wrk -t12 -c400 -d30s https://yourdomain.com/
wrk -t12 -c400 -d30s https://demo.yourdomain.com/
```

## Troubleshooting

### Common Issues

1. **Subdomain not resolving**
   - Check DNS configuration
   - Verify wildcard DNS record
   - Clear DNS cache

2. **SSL certificate errors**
   - Ensure wildcard certificate covers subdomains
   - Check certificate expiration
   - Verify certificate chain

3. **Application not detecting subdomain**
   - Check Host header forwarding
   - Verify proxy configuration
   - Review application logs

4. **CORS errors**
   - Configure proper CORS headers
   - Check subdomain in allowed origins
   - Verify request headers

### Monitoring Commands

```bash
# Check DNS resolution
dig yourdomain.com
dig demo.yourdomain.com

# Check SSL certificate
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/fullchain.pem -text -noout

# Check application logs
tail -f /var/log/nginx/access.log
pm2 logs rphmate

# Monitor subdomain traffic
grep "demo\." /var/log/nginx/access.log | tail -20
```

## Maintenance

### 1. Certificate Renewal

```bash
# Automatic renewal with cron
0 12 * * * /usr/bin/certbot renew --quiet && systemctl reload nginx
```

### 2. DNS Updates

```bash
# Script to add new school subdomain
#!/bin/bash
SCHOOL_CODE=$1
if [ -z "$SCHOOL_CODE" ]; then
    echo "Usage: $0 <school-code>"
    exit 1
fi

# Add DNS record (example with Cloudflare API)
curl -X POST "https://api.cloudflare.com/client/v4/zones/ZONE_ID/dns_records" \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json" \
     --data "{\"type\":\"CNAME\",\"name\":\"$SCHOOL_CODE\",\"content\":\"yourdomain.com\"}"
```

This production setup ensures reliable, secure, and scalable subdomain routing for the RPHMate SaaS platform.

## 📋 Implementation Status

### ✅ Phase 1: Landing Pages & Main Domain - COMPLETE
- [x] Landing page implementation (`pages/index.vue`)
- [x] Pricing page with multiple tiers (`pages/pricing.vue`)
- [x] Billing/registration page (`pages/billing.vue`)
- [x] School admin login (`pages/login.vue`)
- [x] School admin dashboard (`pages/[schoolcode].vue`)
- [x] School admin management page (`pages/[school]/admin.vue`)
- [x] Landing layout updates (`layouts/landing.vue`)

### ✅ Phase 2: Subdomain Infrastructure - COMPLETE
- [x] Enable subdomain middleware
- [x] Update authentication flows
- [x] Configure routing for subdomains
- [x] Test subdomain detection
- [x] Update domain configuration for rphmate.com
- [x] School admin access validation API

### ✅ Phase 3: Payment Integration - COMPLETE
- [x] Stripe payment processing integration
- [x] Webhook handler implementation
- [x] Automated school creation after payment
- [x] 30-day free trial implementation
- [x] Checkout session creation API
- [x] Success page implementation
- [x] Error handling and validation

### 🔄 Phase 4: Development Environment Setup - READY
- [ ] Configure local subdomain testing
- [ ] Set up development scripts
- [ ] Test data seeding
- [ ] Environment switching

### 📅 Next Steps
Ready to proceed with Phase 4 for complete development environment setup.
