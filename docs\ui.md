# Component Documentation

## Base Components

### Button.vue

**Props:**

- `variant?: "primary" | "secondary" | "outline"` (Default: "primary")
  - Determines the button's style.
- `size?: "sm" | "md" | "lg"` (Default: "md")
  - Determines the button's padding and text size.

**Slots:**

- `default`: The content of the button (e.g., text, icon).

**Usage Example:**

```vue
<template>
  <Button variant="primary" size="lg">Click Me</Button>
  <Button variant="outline" @click="handleClick">Submit</Button>
</template>

<script setup>
function handleClick() {
  console.log("Button clicked");
}
</script>
```

### Input.vue

**Props:**

- `type?: "text" | "email" | "password"` (Default: "text")
  - The type of the input field.
- `placeholder?: string`
  - Placeholder text for the input.

**Usage Example:**

```vue
<template>
  <Input type="email" placeholder="Enter your email" />
  <Input v-model="password" type="password" placeholder="Enter your password" />
</template>

<script setup>
import { ref } from "vue";
const password = ref("");
</script>
```

## Composite Components

### Card.vue

**Slots:**

- `header`: Optional content for the card's header section.
- `default`: Main content for the card's body.
- `footer`: Optional content for the card's footer section.

**Usage Example:**

```vue
<template>
  <Card>
    <template #header>
      <h2>Card Title</h2>
    </template>
    <p>This is the main content of the card.</p>
    <template #footer>
      <Button variant="secondary">Learn More</Button>
    </template>
  </Card>
</template>
```

### Modal.vue

**Props:**

- `isOpen: boolean` (Required)
  - Controls the visibility of the modal.
- `title: string` (Required)
  - The title displayed at the top of the modal.

**Events:**

- `update:isOpen (value: boolean)`: Emitted when the modal is requested to close (e.g., by clicking the close button).

**Slots:**

- `default`: The main content of the modal.

**Usage Example:**

```vue
<template>
  <Button @click="showModal = true">Open Modal</Button>
  <Modal
    :isOpen="showModal"
    title="My Modal"
    @update:isOpen="showModal = $event"
  >
    <p>This is the content of the modal.</p>
    <Button @click="showModal = false">Close</Button>
  </Modal>
</template>

<script setup>
import { ref } from "vue";
const showModal = ref(false);
</script>
```

### ProfileUpload.vue

**Description:**
A creative profile picture upload component with image preview, drag and drop support, animations, and upload progress visualization. This component is ideal for user profile management screens, account settings pages, or anywhere a profile image upload feature is needed.

**Props:**

- `modelValue?: File | null` (Default: null)
  - The currently selected file (v-model).
- `previewUrl?: string` (Default: '')
  - URL for an existing profile image, used for displaying the current profile picture.
- `name?: string` (Default: undefined)
  - The name to display below the profile picture.
- `size?: 'sm' | 'md' | 'lg'` (Default: 'md')
  - Controls the size of the profile picture container.
- `showName?: boolean` (Default: false)
  - Whether to display the user's name below the profile picture.
- `showActions?: boolean` (Default: true)
  - Whether to show upload/remove action buttons.
- `allowRemove?: boolean` (Default: true)
  - Whether to display the remove button when an image is uploaded.
- `uploadLabel?: string` (Default: 'Update Photo')
  - Label shown on hover for large sizes.
- `uploadButtonLabel?: string` (Default: 'Upload')
  - Text for the upload button.
- `avatarIcon?: string` (Default: 'ph:user-duotone')
  - Icon name to use as placeholder when no image is available.
- `isUploading?: boolean` (Default: false)
  - Whether the component is currently uploading a file.
- `uploadProgress?: number` (Default: 0)
  - Progress of the current upload (0-100).
- `animateSuccess?: boolean` (Default: true)
  - Whether to show success animation when upload completes.

**Events:**

- `update:modelValue` - Emitted when a new file is selected or removed (for v-model binding)
- `fileSelected` - Emitted when a file is selected, with the File object as payload
- `fileRemoved` - Emitted when the image is removed

**Usage Example:**

```vue
<template>
  <div>
    <ProfileUpload
      v-model="profileFile"
      :preview-url="userData.avatar"
      :name="userData.name"
      show-name
      :is-uploading="isUploading"
      :upload-progress="uploadProgress"
      @file-selected="handleFileUpload"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ProfileUpload from "~/components/ui/composite/ProfileUpload.vue";

const profileFile = ref<File | null>(null);
const isUploading = ref(false);
const uploadProgress = ref(0);
const userData = {
  name: "John Doe",
  avatar: "https://example.com/avatar.jpg",
};

const handleFileUpload = async (file: File) => {
  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    // Simulate upload with progress updates
    for (let i = 0; i < 10; i++) {
      await new Promise((resolve) => setTimeout(resolve, 300));
      uploadProgress.value += 10;
    }

    // Save the file to your backend
    // const response = await api.uploadProfilePicture(file);
    // userData.avatar = response.url;
  } catch (error) {
    console.error("Upload failed", error);
  } finally {
    isUploading.value = false;
  }
};
</script>
```

**Features:**

- Circular profile image container with hover effects
- Drag and drop file upload support
- File input triggered by clicking on the image
- Progress indicator ring for upload visualization
- Animated success celebration when upload completes
- Responsive sizing options (sm, md, lg)
- Customizable placeholder icon
- Accessibility support with appropriate aria attributes

### DeleteConfirmationModal.vue

A flexible, reusable confirmation modal for delete operations across the system.

**Props:**

Core Props:
- `isOpen: boolean` - Controls modal visibility
- `title?: string` - Custom modal title (auto-generated if not provided)
- `zIndex?: number` - Modal z-index (default: 50)
- `loading?: boolean` - Shows loading state (default: false)

Content Props:
- `itemType?: string` - Type of item being deleted (e.g., 'Rancangan Pengajaran')
- `itemName?: string` - Name/identifier of the item
- `itemSubtitle?: string` - Additional info (date, location, etc.)
- `confirmationMessage?: string` - Custom confirmation message
- `warningMessage?: string` - Warning text (default: 'Tindakan ini tidak boleh dibatalkan.')

Styling Props:
- `dangerLevel?: 'low' | 'medium' | 'high'` - Controls button color and severity (default: 'high')
- `impactSeverity?: 'low' | 'medium' | 'high'` - Impact section styling (default: 'medium')

**Events:**
- `@confirm` - Emitted when user confirms deletion
- `@cancel` - Emitted when user cancels
- `@update:is-open` - Emitted when modal should be closed

**Slots:**
- `item-details` - Custom content for item details section
- `content` - Additional custom content
- `impact` - Custom impact/consequence content

**Usage Examples:**

Simple deletion:
```vue
<DeleteConfirmationModal
  :is-open="showDeleteModal"
  item-type="fail"
  :item-name="file.name"
  @confirm="deleteFile"
  @cancel="closeModal"
/>
```

High-impact deletion with custom content:
```vue
<DeleteConfirmationModal
  :is-open="showModal"
  title="Padam Kelas & Subjek"
  item-type="kombinasi kelas-subjek"
  danger-level="high"
  impact-severity="high"
  @confirm="confirmDelete"
  @cancel="cancelDelete"
>
  <template #item-details>
    <div v-for="item in itemsToDelete" :key="item.id">
      {{ item.name }}
    </div>
  </template>
  
  <template #impact>
    <p><strong>{{ affectedCount }} related items</strong> will also be deleted.</p>
  </template>
</DeleteConfirmationModal>
```
