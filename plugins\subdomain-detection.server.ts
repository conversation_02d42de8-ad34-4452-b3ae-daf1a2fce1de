// Server-side subdomain detection plugin
// Created: 2025-07-13
// Purpose: Handle subdomain detection and routing on the server side

export default defineNuxtPlugin((nuxtApp) => {
  // Only run on server side
  if (process.client) return

  const config = useRuntimeConfig()

  // Get request headers
  const event = nuxtApp.ssrContext?.event
  if (!event) return

  const host = event?.node?.req?.headers?.host || ''
  const baseDomain = config.public.baseDomain || 'localhost:3000'
  
  // Extract subdomain from host header
  const getSubdomain = (host: string): string | null => {
    // Reserved subdomains that should not be treated as schools
    const reservedSubdomains = ['www', 'api', 'admin', 'auth', 'mail', 'ftp', 'cdn', 'static']

    // Handle development environment
    if (host.includes('localhost')) {
      const parts = host.split('.')
      if (parts.length > 1 && parts[0] !== 'localhost' && !reservedSubdomains.includes(parts[0])) {
        return parts[0]
      }
      return null
    }
    
    // Handle production environment
    const baseParts = baseDomain.split('.')
    const hostParts = host.split('.')
    
    // Remove port from the last part if present
    if (hostParts.length > 0) {
      hostParts[hostParts.length - 1] = hostParts[hostParts.length - 1].split(':')[0]
    }
    
    // If host has more parts than base domain, extract subdomain
    if (hostParts.length > baseParts.length) {
      const subdomainParts = hostParts.slice(0, hostParts.length - baseParts.length)
      return subdomainParts.join('.')
    }
    
    return null
  }

  // Detect current subdomain
  const currentSubdomain = getSubdomain(host)

  // Additional validation - ensure it's a valid school subdomain
  const validSubdomain = currentSubdomain && currentSubdomain !== 'auth' ? currentSubdomain : null

  // Store subdomain in SSR context
  if (nuxtApp.ssrContext) {
    (nuxtApp.ssrContext as any).subdomain = validSubdomain
  }

  // Set initial state for client hydration
  useState('currentSubdomain', () => validSubdomain)

  if (validSubdomain) {
    console.log(`🏫 [SSR] Detected school subdomain: ${validSubdomain}`)

    // Set school context for SSR
    useState('schoolContext', () => ({
      schoolCode: validSubdomain,
      isSchoolContext: true,
      detectedAt: new Date().toISOString()
    }))

    // Add subdomain info to response headers for debugging
    if (event?.node?.res && validSubdomain) {
      event!.node.res.setHeader('X-Detected-Subdomain', validSubdomain!)
      event!.node.res.setHeader('X-School-Context', 'true')
    }

  } else {
    console.log('🌐 [SSR] Main domain detected - no subdomain')

    // Clear school context
    useState('schoolContext', () => null)

    // Add headers for main domain
    if (event?.node?.res) {
      event!.node.res.setHeader('X-School-Context', 'false')
    }
  }

  // Provide server-side subdomain utilities
  return {
    provide: {
      subdomainSSR: {
        current: validSubdomain,
        isSchoolContext: !!validSubdomain,
        host,
        baseDomain
      }
    }
  }
})
