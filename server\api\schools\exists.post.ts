// School existence validation API endpoint
// Checks if a school with the given code exists in the database

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event)
    const { code } = body

    // Validate input
    if (!code || typeof code !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'School code is required'
      })
    }

    const trimmedCode = code.trim().toLowerCase()

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    )

    // Check if school exists
    const { data: school, error: dbError } = await supabase
      .from('schools')
      .select('id, code, name, subscription_status')
      .eq('code', trimmedCode)
      .single()

    if (dbError && dbError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw createError({
        statusCode: 500,
        statusMessage: 'Error checking school existence'
      })
    }

    if (!school) {
      return {
        exists: false,
        code: trimmedCode,
        message: 'School not found'
      }
    }

    // School exists
    return {
      exists: true,
      code: school.code,
      name: school.name,
      subscription_status: school.subscription_status,
      message: 'School found'
    }

  } catch (error: any) {
    console.error('School existence check error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during school existence check'
    })
  }
})
