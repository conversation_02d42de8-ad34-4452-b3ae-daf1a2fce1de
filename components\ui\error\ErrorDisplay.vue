<template>
  <div v-if="error" :class="containerClasses">
    <!-- Error Icon and Title -->
    <div class="flex items-start space-x-3">
      <div class="flex-shrink-0">
        <Icon :name="errorIcon" :class="iconClasses" />
      </div>
      
      <div class="flex-1 min-w-0">
        <!-- Error Message -->
        <div class="text-sm font-medium" :class="titleClasses">
          {{ error.userMessage }}
        </div>
        
        <!-- Technical Details (Expandable) -->
        <div v-if="showTechnicalDetails && error.technicalDetails" class="mt-2">
          <button
            @click="showDetails = !showDetails"
            class="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            type="button"
          >
            <Icon :name="showDetails ? 'heroicons:chevron-up' : 'heroicons:chevron-down'" class="h-3 w-3 mr-1" />
            {{ showDetails ? 'Sembunyikan' : 'Tunjukkan' }} butiran teknikal
          </button>
          
          <div v-if="showDetails" class="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono text-gray-700 dark:text-gray-300 overflow-x-auto">
            <pre>{{ error.technicalDetails }}</pre>
          </div>
        </div>
        
        <!-- Suggestions -->
        <div v-if="error.suggestions.length > 0 && showSuggestions" class="mt-3">
          <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
            Cadangan penyelesaian:
          </p>
          <ul class="space-y-1">
            <li
              v-for="(suggestion, index) in error.suggestions"
              :key="index"
              class="flex items-start space-x-2 text-xs text-gray-600 dark:text-gray-400"
            >
              <Icon name="heroicons:light-bulb" class="h-3 w-3 mt-0.5 flex-shrink-0 text-yellow-500" />
              <span>{{ suggestion }}</span>
            </li>
          </ul>
        </div>
        
        <!-- Error Context -->
        <div v-if="showContext" class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <span class="font-medium">Operasi:</span> {{ error.context.operation }}
          <span v-if="error.context.component" class="ml-3">
            <span class="font-medium">Komponen:</span> {{ error.context.component }}
          </span>
          <span class="ml-3">
            <span class="font-medium">Masa:</span> {{ formatTime(error.timestamp) }}
          </span>
        </div>
      </div>
      
      <!-- Actions -->
      <div v-if="showActions" class="flex-shrink-0 flex items-center space-x-2">
        <!-- Retry Button -->
        <button
          v-if="error.retryable && onRetry"
          @click="$emit('retry', error)"
          class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-600 dark:hover:bg-blue-800/30 transition-colors"
          type="button"
        >
          <Icon name="heroicons:arrow-path" class="h-3 w-3 mr-1" />
          Cuba Lagi
        </button>
        
        <!-- Dismiss Button -->
        <button
          @click="$emit('dismiss', error)"
          class="inline-flex items-center p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
          type="button"
        >
          <Icon name="heroicons:x-mark" class="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Icon from '~/components/ui/base/Icon.vue'
import type { EnhancedError } from '~/composables/useErrorHandler'

interface Props {
  error: EnhancedError | null
  variant?: 'inline' | 'banner' | 'card'
  showTechnicalDetails?: boolean
  showSuggestions?: boolean
  showContext?: boolean
  showActions?: boolean
  onRetry?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'inline',
  showTechnicalDetails: false,
  showSuggestions: true,
  showContext: false,
  showActions: true
})

const emit = defineEmits<{
  retry: [error: EnhancedError]
  dismiss: [error: EnhancedError]
}>()

// Local state
const showDetails = ref(false)

// Computed styles
const containerClasses = computed(() => [
  'rounded-lg border',
  {
    // Inline variant
    'p-3 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800': 
      props.variant === 'inline' && props.error?.type !== 'validation',
    'p-3 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800': 
      props.variant === 'inline' && props.error?.type === 'validation',
    
    // Banner variant
    'p-4 bg-red-100 border-red-300 dark:bg-red-900/30 dark:border-red-700': 
      props.variant === 'banner' && props.error?.type !== 'validation',
    'p-4 bg-yellow-100 border-yellow-300 dark:bg-yellow-900/30 dark:border-yellow-700': 
      props.variant === 'banner' && props.error?.type === 'validation',
    
    // Card variant
    'p-6 bg-white border-red-200 shadow-sm dark:bg-gray-800 dark:border-red-800': 
      props.variant === 'card' && props.error?.type !== 'validation',
    'p-6 bg-white border-yellow-200 shadow-sm dark:bg-gray-800 dark:border-yellow-800': 
      props.variant === 'card' && props.error?.type === 'validation'
  }
])

const iconClasses = computed(() => [
  'h-5 w-5',
  {
    'text-red-600 dark:text-red-400': props.error?.type !== 'validation',
    'text-yellow-600 dark:text-yellow-400': props.error?.type === 'validation'
  }
])

const titleClasses = computed(() => [
  {
    'text-red-800 dark:text-red-200': props.error?.type !== 'validation',
    'text-yellow-800 dark:text-yellow-200': props.error?.type === 'validation'
  }
])

const errorIcon = computed(() => {
  if (!props.error) return 'heroicons:exclamation-triangle'
  
  switch (props.error.type) {
    case 'network':
      return 'heroicons:wifi'
    case 'permission':
      return 'heroicons:lock-closed'
    case 'validation':
      return 'heroicons:exclamation-triangle'
    case 'server':
      return 'heroicons:server'
    case 'client':
      return 'heroicons:computer-desktop'
    default:
      return 'heroicons:exclamation-triangle'
  }
})

// Helper methods
const formatTime = (date: Date): string => {
  return new Intl.DateTimeFormat('ms-MY', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}
</script>
