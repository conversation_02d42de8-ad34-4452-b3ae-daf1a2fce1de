-- Performance Monitoring Setup for eRPH System
-- Comprehensive monitoring for database performance and health

-- =====================================================
-- 1. ENABLE PERFORMANCE MONITORING EXTENSIONS
-- =====================================================

-- Enable pg_stat_statements for query performance tracking
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Enable pg_cron for automated maintenance
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- =====================================================
-- 2. PERFORMANCE MONITORING VIEWS
-- =====================================================

-- View for slow queries identification
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    max_time,
    stddev_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent,
    query_id
FROM pg_stat_statements 
WHERE mean_time > 100 -- Queries taking more than 100ms on average
ORDER BY total_time DESC
LIMIT 50;

-- View for table bloat monitoring
CREATE OR REPLACE VIEW table_bloat_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    CASE 
        WHEN n_live_tup > 0 
        THEN ROUND(100.0 * n_dead_tup / (n_live_tup + n_dead_tup), 2)
        ELSE 0 
    END as bloat_percent,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY bloat_percent DESC;

-- View for index usage statistics
CREATE OR REPLACE VIEW index_efficiency AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
    CASE 
        WHEN idx_scan = 0 THEN 'NEVER_USED'
        WHEN idx_scan < 100 THEN 'RARELY_USED'
        WHEN idx_scan < 1000 THEN 'MODERATELY_USED'
        ELSE 'HEAVILY_USED'
    END as usage_category
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- View for connection and lock monitoring
CREATE OR REPLACE VIEW connection_stats AS
SELECT 
    datname,
    state,
    COUNT(*) as connection_count,
    MAX(EXTRACT(EPOCH FROM (now() - state_change))) as max_idle_time,
    AVG(EXTRACT(EPOCH FROM (now() - state_change))) as avg_idle_time
FROM pg_stat_activity 
WHERE datname = current_database()
GROUP BY datname, state
ORDER BY connection_count DESC;

-- =====================================================
-- 3. AUTOMATED PERFORMANCE ALERTS
-- =====================================================

-- Function to check for performance issues
CREATE OR REPLACE FUNCTION check_performance_issues()
RETURNS TABLE(
    issue_type TEXT,
    severity TEXT,
    description TEXT,
    recommendation TEXT
) AS $$
BEGIN
    -- Check for slow queries
    RETURN QUERY
    SELECT 
        'SLOW_QUERY'::TEXT,
        CASE 
            WHEN mean_time > 5000 THEN 'CRITICAL'
            WHEN mean_time > 1000 THEN 'HIGH'
            ELSE 'MEDIUM'
        END::TEXT,
        'Query taking ' || ROUND(mean_time::NUMERIC, 2) || 'ms on average'::TEXT,
        'Review and optimize query: ' || LEFT(query, 100) || '...'::TEXT
    FROM slow_queries
    WHERE mean_time > 500
    LIMIT 10;
    
    -- Check for table bloat
    RETURN QUERY
    SELECT 
        'TABLE_BLOAT'::TEXT,
        CASE 
            WHEN bloat_percent > 50 THEN 'CRITICAL'
            WHEN bloat_percent > 25 THEN 'HIGH'
            ELSE 'MEDIUM'
        END::TEXT,
        'Table ' || tablename || ' has ' || bloat_percent || '% bloat'::TEXT,
        'Consider running VACUUM FULL on ' || tablename::TEXT
    FROM table_bloat_stats
    WHERE bloat_percent > 20
    LIMIT 5;
    
    -- Check for unused indexes
    RETURN QUERY
    SELECT 
        'UNUSED_INDEX'::TEXT,
        'LOW'::TEXT,
        'Index ' || indexname || ' on ' || tablename || ' is never used'::TEXT,
        'Consider dropping index ' || indexname::TEXT
    FROM index_efficiency
    WHERE usage_category = 'NEVER_USED'
    AND pg_relation_size(indexname::regclass) > 1024 * 1024 -- Larger than 1MB
    LIMIT 5;
    
    -- Check for connection issues
    RETURN QUERY
    SELECT 
        'CONNECTION_ISSUE'::TEXT,
        'HIGH'::TEXT,
        'High number of ' || state || ' connections: ' || connection_count::TEXT,
        'Investigate connection pooling and idle connection cleanup'::TEXT
    FROM connection_stats
    WHERE connection_count > 20
    AND state IN ('idle', 'idle in transaction');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. PERFORMANCE METRICS COLLECTION
-- =====================================================

-- Table to store performance metrics history
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    metric_type TEXT NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    metadata JSONB DEFAULT '{}'
);

-- Function to collect and store performance metrics
CREATE OR REPLACE FUNCTION collect_performance_metrics()
RETURNS void AS $$
BEGIN
    -- Database size metrics
    INSERT INTO performance_metrics (metric_type, metric_name, metric_value, metadata)
    SELECT 
        'database_size',
        'total_size_mb',
        pg_database_size(current_database()) / 1024 / 1024,
        jsonb_build_object('database', current_database());
    
    -- Table size metrics for main tables
    INSERT INTO performance_metrics (metric_type, metric_name, metric_value, metadata)
    SELECT 
        'table_size',
        'size_mb',
        pg_total_relation_size(tablename::regclass) / 1024 / 1024,
        jsonb_build_object('table', tablename)
    FROM (VALUES 
        ('lesson_plans'),
        ('lesson_plan_reflections'),
        ('lesson_plan_detailed_reflections'),
        ('teacher_schedules'),
        ('timetable_entries')
    ) AS t(tablename);
    
    -- Query performance metrics
    INSERT INTO performance_metrics (metric_type, metric_name, metric_value, metadata)
    SELECT 
        'query_performance',
        'avg_execution_time_ms',
        mean_time,
        jsonb_build_object('query_id', query_id, 'calls', calls)
    FROM pg_stat_statements
    WHERE calls > 100
    ORDER BY mean_time DESC
    LIMIT 10;
    
    -- Connection metrics
    INSERT INTO performance_metrics (metric_type, metric_name, metric_value, metadata)
    SELECT 
        'connections',
        'active_connections',
        COUNT(*),
        jsonb_build_object('state', state)
    FROM pg_stat_activity
    WHERE datname = current_database()
    GROUP BY state;
    
    -- Cache hit ratio
    INSERT INTO performance_metrics (metric_type, metric_name, metric_value, metadata)
    SELECT 
        'cache_performance',
        'hit_ratio_percent',
        ROUND(
            100.0 * sum(blks_hit) / NULLIF(sum(blks_hit) + sum(blks_read), 0),
            2
        ),
        jsonb_build_object('database', current_database())
    FROM pg_stat_database
    WHERE datname = current_database();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. AUTOMATED MONITORING SCHEDULE
-- =====================================================

-- Collect performance metrics every hour
SELECT cron.schedule(
    'collect-performance-metrics',
    '0 * * * *',
    'SELECT collect_performance_metrics();'
);

-- Check for performance issues daily
SELECT cron.schedule(
    'daily-performance-check',
    '0 8 * * *',
    'SELECT check_performance_issues();'
);

-- Reset pg_stat_statements weekly to prevent it from growing too large
SELECT cron.schedule(
    'reset-query-stats',
    '0 0 * * 0',
    'SELECT pg_stat_statements_reset();'
);

-- =====================================================
-- 6. DASHBOARD QUERIES
-- =====================================================

-- Query for performance dashboard
CREATE OR REPLACE VIEW performance_dashboard AS
SELECT 
    'Database Size' as metric,
    pg_size_pretty(pg_database_size(current_database())) as current_value,
    'N/A' as trend,
    'INFO' as status
UNION ALL
SELECT 
    'Active Connections',
    COUNT(*)::TEXT,
    'N/A',
    CASE 
        WHEN COUNT(*) > 50 THEN 'WARNING'
        WHEN COUNT(*) > 100 THEN 'CRITICAL'
        ELSE 'OK'
    END
FROM pg_stat_activity 
WHERE state = 'active' AND datname = current_database()
UNION ALL
SELECT 
    'Cache Hit Ratio',
    ROUND(100.0 * sum(blks_hit) / NULLIF(sum(blks_hit) + sum(blks_read), 0), 2)::TEXT || '%',
    'N/A',
    CASE 
        WHEN ROUND(100.0 * sum(blks_hit) / NULLIF(sum(blks_hit) + sum(blks_read), 0), 2) < 95 THEN 'WARNING'
        WHEN ROUND(100.0 * sum(blks_hit) / NULLIF(sum(blks_hit) + sum(blks_read), 0), 2) < 90 THEN 'CRITICAL'
        ELSE 'OK'
    END
FROM pg_stat_database 
WHERE datname = current_database()
UNION ALL
SELECT 
    'Slow Queries Count',
    COUNT(*)::TEXT,
    'N/A',
    CASE 
        WHEN COUNT(*) > 10 THEN 'WARNING'
        WHEN COUNT(*) > 20 THEN 'CRITICAL'
        ELSE 'OK'
    END
FROM slow_queries;

-- =====================================================
-- 7. CLEANUP AND MAINTENANCE
-- =====================================================

-- Clean up old performance metrics (keep 3 months)
SELECT cron.schedule(
    'cleanup-performance-metrics',
    '0 2 1 * *',
    'DELETE FROM performance_metrics WHERE recorded_at < CURRENT_DATE - INTERVAL ''3 months'';'
);

-- Function to generate performance report
CREATE OR REPLACE FUNCTION generate_performance_report(days_back INTEGER DEFAULT 7)
RETURNS TABLE(
    report_section TEXT,
    metric_name TEXT,
    current_value TEXT,
    avg_value TEXT,
    trend TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'Database Growth'::TEXT,
        'Size (MB)'::TEXT,
        (SELECT metric_value::TEXT FROM performance_metrics 
         WHERE metric_type = 'database_size' 
         ORDER BY recorded_at DESC LIMIT 1),
        ROUND(AVG(metric_value), 2)::TEXT,
        CASE 
            WHEN (SELECT metric_value FROM performance_metrics 
                  WHERE metric_type = 'database_size' 
                  ORDER BY recorded_at DESC LIMIT 1) > AVG(metric_value) * 1.1 
            THEN 'INCREASING'
            ELSE 'STABLE'
        END
    FROM performance_metrics 
    WHERE metric_type = 'database_size' 
    AND recorded_at >= CURRENT_DATE - (days_back || ' days')::INTERVAL;
END;
$$ LANGUAGE plpgsql;
