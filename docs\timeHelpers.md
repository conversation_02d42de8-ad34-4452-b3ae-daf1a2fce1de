# Time Utilities Documentation

## Overview
The `timeHelpers.ts` utility file provides consistent time formatting, parsing, and manipulation functions across the application, specifically designed for the timetable and schedule management system.

## Functions

### `formatTime(time: string): string`
Formats a time string to 12-hour format with AM/PM.

**Parameters:**
- `time` - Time string in HH:MM or HH:MM:SS format

**Returns:**
- Formatted time string (e.g., "2:30 PM")

**Example:**
```typescript
formatTime("14:30") // returns "2:30 PM"
formatTime("08:00") // returns "8:00 AM"
```

### `normalizeTimeFormat(time: string): string`
Normalizes time format for consistent comparison between database and UI values.

**Parameters:**
- `time` - Time string in HH:MM or HH:MM:SS format

**Returns:**
- Normalized time string in HH:MM:SS format for storage consistency

**Example:**
```typescript
normalizeTimeFormat("14:30")    // returns "14:30:00"
normalizeTimeFormat("14:30:00") // returns "14:30:00"
```

### `formatTimeRange(startTime: string, endTime: string): string`
Creates a formatted time range label.

**Parameters:**
- `startTime` - Start time in HH:MM format
- `endTime` - End time in HH:MM format

**Returns:**
- Formatted time range (e.g., "8:00 AM - 9:00 AM")

**Example:**
```typescript
formatTimeRange("08:00", "09:00") // returns "8:00 AM - 9:00 AM"
```

### `calculateDurationMinutes(startTime: string, endTime: string): number`
Calculates duration between two times in minutes.

**Parameters:**
- `startTime` - Start time in HH:MM format
- `endTime` - End time in HH:MM format

**Returns:**
- Duration in minutes

**Example:**
```typescript
calculateDurationMinutes("08:00", "09:30") // returns 90
```

### `addDurationToTime(time: string, durationMinutes: number): string`
Adds duration in minutes to a time string.

**Parameters:**
- `time` - Start time in HH:MM format
- `durationMinutes` - Duration to add in minutes

**Returns:**
- New time in HH:MM format

**Example:**
```typescript
addDurationToTime("08:00", 90) // returns "09:30"
```

### `isTimeInRange(time: string, startTime: string, endTime: string): boolean`
Checks if a time is within a time range (inclusive).

**Parameters:**
- `time` - Time to check in HH:MM format
- `startTime` - Range start time in HH:MM format  
- `endTime` - Range end time in HH:MM format

**Returns:**
- True if time is within the range

**Example:**
```typescript
isTimeInRange("08:30", "08:00", "09:00") // returns true
```

### `isValidTimeFormat(time: string): boolean`
Validates a time string format.

**Parameters:**
- `time` - Time string to validate

**Returns:**
- True if time format is valid (HH:MM or HH:MM:SS)

**Example:**
```typescript
isValidTimeFormat("08:30")    // returns true
isValidTimeFormat("25:30")    // returns false
isValidTimeFormat("08:30:00") // returns true
```

### `timeToMinutes(time: string): number`
Converts 24-hour time to minutes since midnight.

**Parameters:**
- `time` - Time string in HH:MM format

**Returns:**
- Minutes since midnight

**Example:**
```typescript
timeToMinutes("08:30") // returns 510 (8*60 + 30)
```

### `minutesToTime(minutes: number): string`
Converts minutes since midnight to HH:MM format.

**Parameters:**
- `minutes` - Minutes since midnight

**Returns:**
- Time string in HH:MM format

**Example:**
```typescript
minutesToTime(510) // returns "08:30"
```

## Constants

### `DURATION_PRESETS`
Array of common duration presets for quick time slot setup.

```typescript
const DURATION_PRESETS = [
  { label: '15 min', minutes: 15 },
  { label: '30 min', minutes: 30 },
  { label: '45 min', minutes: 45 },
  { label: '60 min', minutes: 60 },
  { label: '90 min', minutes: 90 }
]
```

### `COMMON_SCHOOL_TIMES`
Common school time periods for Malaysian schools.

```typescript
const COMMON_SCHOOL_TIMES = {
  MORNING_START: '07:30',
  MORNING_ASSEMBLY: '07:45',
  FIRST_PERIOD: '08:00',
  FIRST_BREAK: '10:00',
  FIRST_BREAK_END: '10:20',
  LUNCH_BREAK: '12:20',
  LUNCH_BREAK_END: '13:00',
  AFTERNOON_END: '15:00'
}
```

## Usage in Components

### In Vue Components
```vue
<script setup lang="ts">
import { formatTime, formatTimeRange, calculateDurationMinutes } from '~/utils/timeHelpers'

// Format individual time
const displayTime = formatTime("14:30") // "2:30 PM"

// Format time range
const timeSlotLabel = formatTimeRange("08:00", "09:00") // "8:00 AM - 9:00 AM"

// Calculate duration
const duration = calculateDurationMinutes("08:00", "09:30") // 90 minutes
</script>
```

### In Composables
```typescript
import { formatTimeRange, normalizeTimeFormat } from '~/utils/timeHelpers'

export function useTimeSlots() {
  const getTimeLabel = (start: string, end: string) => {
    return formatTimeRange(start, end)
  }
  
  const normalizeForComparison = (time: string) => {
    return normalizeTimeFormat(time)
  }
}
```

## Migration from Legacy Code

When migrating from individual component time functions:

1. **Replace individual `formatTime` functions** with the utility import
2. **Update time range creation** to use `formatTimeRange`
3. **Replace duration calculations** with `calculateDurationMinutes`
4. **Use duration presets** from `DURATION_PRESETS` constant

**Before:**
```typescript
const formatTime = (time: string): string => {
  const [hours, minutes] = time.split(':').map(Number)
  const period = hours >= 12 ? 'PM' : 'AM'
  const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`
}
```

**After:**
```typescript
import { formatTime } from '~/utils/timeHelpers'
```

This ensures consistency across the application and reduces code duplication while maintaining the same functionality.
