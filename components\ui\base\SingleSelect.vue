<template>
  <Listbox v-model="selectedOptionSync" :disabled="props.disabled" @update:modelValue="emitSelection">
    <div class="relative" :class="props.containerClass"> <!-- Standard Label (top of input) -->
      <label v-if="props.variant === 'standard' && props.label" :for="listboxButtonId"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {{ props.label }}
        <span v-if="props.required" class="text-red-500 ml-1">*</span>
      </label>

      <!-- Floating Label -->
      <FloatingLabel v-if="props.variant === 'floating'" :for-input="listboxButtonId" :label="props.placeholder"
        :is-floated="isLabelFloated" :is-focused="isFocused" />

      <ListboxButton :id="listboxButtonId" class="form-input relative cursor-pointer pr-10" :class="[
        { 'cursor-not-allowed': props.disabled },
        { '!pt-3.5 !pb-3.5': props.variant === 'standard' },
        { '!py-2 !h-auto': props.variant === 'compact' },
        props.buttonClass
      ]" @click="toggleListbox" @focus="handleButtonFocus" @blur="handleButtonBlur" ref="listboxButtonRef"
        role="combobox" :aria-expanded="isOpen" :aria-controls="listboxOptionsId">
        <span class="flex items-center justify-between w-full">
          <span class="flex items-center min-w-0"> <!-- Added min-w-0 for better truncation -->
            <span class="block truncate">
              {{ getDisplayValue() }}
            </span>
            <span v-if="props.allowClear && selectedOptionSync && !props.disabled"
              class="ml-1 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/50 flex-shrink-0"
              @click.stop="clearSelection" role="button" tabindex="0" aria-label="Clear selection"
              @keydown.enter.prevent="clearSelection" @keydown.space.prevent="clearSelection">
              <XMarkIcon class="h-4 w-4 text-gray-500 dark:text-gray-400" />
            </span>
          </span>
          <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronDownIcon class="h-5 w-5 text-gray-400 transition-transform duration-200 ease-in-out"
              :class="{ 'rotate-180': isOpen }" aria-hidden="true" />
          </span>
        </span>
      </ListboxButton>
      <Transition
        :enter-active-class="listboxAbove ? 'transition ease-out duration-100 transform' : 'transition ease-out duration-100 transform'"
        :enter-from-class="listboxAbove ? 'opacity-0 translate-y-2' : 'opacity-0 -translate-y-2'"
        :enter-to-class="listboxAbove ? 'opacity-100 translate-y-0' : 'opacity-100 translate-y-0'"
        :leave-active-class="listboxAbove ? 'transition ease-in duration-75 transform' : 'transition ease-in duration-75 transform'"
        :leave-from-class="listboxAbove ? 'opacity-100 translate-y-0' : 'opacity-100 translate-y-0'"
        :leave-to-class="listboxAbove ? 'opacity-0 translate-y-2' : 'opacity-0 -translate-y-2'">
        <ListboxOptions :id="listboxOptionsId" ref="listboxOptionsRef"
          class="absolute z-20 mt-1 w-full rounded-md bg-white dark:bg-dark-background shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
          :class="[listboxAbove ? 'bottom-full mb-1' : 'top-full mt-1', props.optionsContainerClass]">
          <div v-if="props.showSearch" class="p-2">
            <input :id="`search-input-${baseId}`" ref="searchInputRef" type="text" v-model="searchQuery"
              :placeholder="props.searchPlaceholder" @keydown.stop @blur="handleSearchInputBlur" class="form-input w-full text-sm px-2 py-1.5 border-gray-300 dark:border-gray-600
                        dark:bg-dark-input focus:ring-primary focus:border-primary dark:focus:ring-primary
                        dark:focus:border-primary" aria-label="Search options" />
          </div>
          <div :style="{ 'max-height': listboxMaxHeightPx + 'px' }" class="overflow-auto py-1"
            :class="props.optionsListClass">
            <ListboxOption v-if="filteredOptions.length === 0 && searchQuery" as="template" :value="null" disabled>
              <li
                class="listbox-option listbox-option--disabled px-3 py-2 text-center text-gray-500 dark:text-gray-400">
                {{ props.noResultsText }}
              </li>
            </ListboxOption>
            <ListboxOption v-for="option in filteredOptions" :key="option[props.optionValue]" :value="option"
              as="template" v-slot="{ active, selected }" :disabled="option.disabled">
              <li :class="[
                {
                  'listbox-option': true,
                  'listbox-option--active': active && !selected && !option.disabled,
                  'listbox-option--selected': selected && !active,
                  'listbox-option--active listbox-option--selected': active && selected,
                  'listbox-option--disabled': option.disabled
                },
                option.disabled
                  ? 'px-3 py-2 cursor-default select-none relative text-gray-400 dark:text-gray-500 font-medium'
                  : 'px-3 py-2 cursor-pointer select-none relative hover:bg-primary/10 dark:hover:bg-primary/20'
              ]">
                <!-- Conditionally render custom slot or default label -->
                <template v-if="option.labelSlot">
                  <slot name="customOptionLabel" :option="option" :active="active" :selected="selected"></slot>
                </template>
                <template v-else>
                  <span :class="['block truncate', selected ? 'font-semibold' : 'font-normal']">
                    {{ option[props.optionLabel] }}
                  </span>
                </template>

                <span v-if="selected"
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-primary dark:text-primary-dark">
                  <CheckIcon class="h-5 w-5" aria-hidden="true" />
                </span>
              </li>
            </ListboxOption>
          </div>
        </ListboxOptions>
      </Transition>
    </div>
  </Listbox>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted, nextTick, useId } from 'vue';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue';
import { CheckIcon, ChevronDownIcon, XMarkIcon } from '@heroicons/vue/20/solid';
import FloatingLabel from './FloatingLabel.vue';

const emit = defineEmits(['update:modelValue', 'dropdown-state-changed', 'dropdown-will-open', 'dropdown-did-open']);

const props = withDefaults(defineProps<{
  modelValue: Record<string, any> | null | string | number;
  options: Record<string, any>[];
  optionLabel?: string;
  optionValue?: string;
  placeholder?: string;
  label?: string;
  variant?: 'floating' | 'standard' | 'compact';
  required?: boolean;
  disabled?: boolean;
  containerClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  buttonClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  optionsContainerClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  optionsListClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  boundarySelector?: string;
  showSearch?: boolean;
  searchPlaceholder?: string;
  noResultsText?: string;
  allowClear?: boolean;
}>(), {
  optionLabel: 'label',
  optionValue: 'value',
  placeholder: 'Select option',
  label: '',
  variant: 'floating',
  required: false,
  disabled: false,
  containerClass: '',
  buttonClass: '',
  optionsContainerClass: '',
  optionsListClass: '',
  boundarySelector: undefined,
  showSearch: false, // Default for showSearch is now false
  searchPlaceholder: 'Search...',
  noResultsText: 'No results found.',
  allowClear: true,
});

const listboxButtonRef = ref<any>(null);
const listboxOptionsRef = ref<any>(null);
const searchInputRef = ref<HTMLInputElement | null>(null);

const listboxAbove = ref(false);
const isOpen = ref(false);
const isFocused = ref(false);
const isSearchFocused = ref(false);

const baseId = useId();
const listboxButtonId = `listbox-button-${baseId}`;
const listboxOptionsId = `listbox-options-${baseId}`;

const selectedOptionSync = ref<Record<string, any> | null>(null);
const searchQuery = ref('');
const listboxMaxHeightPx = ref(240);
const searchAreaHeightEstimate = ref(50);

const isLabelFloated = computed(() => {
  return isFocused.value || isOpen.value || (selectedOptionSync.value !== null && selectedOptionSync.value !== undefined);
});

const getDisplayValue = () => {
  if (selectedOptionSync.value) {
    return selectedOptionSync.value[props.optionLabel];
  }

  // For floating variant, return non-breaking space character to maintain layout
  if (props.variant === 'floating') {
    return '\u00A0'; // Non-breaking space character
  }

  // For standard variant, return the placeholder
  return props.placeholder || '';
};

const findOptionByValue = (value: any) => {
  if (value === null || value === undefined || value === 'null') return null;
  return props.options.find(opt => opt[props.optionValue] === value) || null;
};

watch(() => props.modelValue, (newValue) => {
  // Clear selection if the value is null, undefined, or 'null' string
  if (newValue === null || newValue === undefined || newValue === 'null') {
    selectedOptionSync.value = null;
    return;
  }
  selectedOptionSync.value = findOptionByValue(newValue);
}, { immediate: true });

const filteredOptions = computed(() => {
  if (!props.showSearch || !searchQuery.value) {
    return props.options;
  }
  return props.options.filter(option => {
    const label = String(option[props.optionLabel] || '').toLowerCase();
    return label.includes(searchQuery.value.toLowerCase());
  });
});

const emitSelection = (selectedItem: Record<string, any> | null) => {
  if (selectedItem) {
    emit('update:modelValue', selectedItem[props.optionValue]);
  } else {
    // Handled by clearSelection or if modelValue is programmatically set to null/undefined
  }
  isOpen.value = false;
  if (props.showSearch) {
    searchQuery.value = '';
  }
};

const clearSelection = (event?: MouseEvent | KeyboardEvent) => {
  if (event) event.stopPropagation();
  selectedOptionSync.value = null;
  emit('update:modelValue', null);
  if (props.showSearch) {
    searchQuery.value = '';
  }
  isOpen.value = false;
  nextTick(() => {
    const buttonElement = listboxButtonRef.value?.$el;
    buttonElement?.focus();
  });
};

const calculatePosition = () => {
  const buttonEl = listboxButtonRef.value?.$el || listboxButtonRef.value;
  if (!buttonEl || typeof buttonEl.getBoundingClientRect !== 'function' || !isOpen.value) return;

  const buttonRect = buttonEl.getBoundingClientRect();
  const currentSearchAreaHeight = props.showSearch ? searchAreaHeightEstimate.value : 0;
  const optionsHeightEstimate = listboxMaxHeightPx.value + currentSearchAreaHeight;

  let effectiveSpaceBelow: number;
  let effectiveSpaceAbove: number;
  const boundaryEl = props.boundarySelector ? document.querySelector(props.boundarySelector) : null;

  if (boundaryEl) {
    const boundaryRect = boundaryEl.getBoundingClientRect();
    effectiveSpaceBelow = boundaryRect.bottom - buttonRect.bottom;
    effectiveSpaceAbove = buttonRect.top - boundaryRect.top;
  } else {
    effectiveSpaceBelow = window.innerHeight - buttonRect.bottom;
    effectiveSpaceAbove = buttonRect.top;
  }

  listboxAbove.value = effectiveSpaceBelow < optionsHeightEstimate && effectiveSpaceAbove > optionsHeightEstimate;
};

const getExpectedDropdownHeight = () => {
  const optionHeight = 40; // px, adjust to your actual option height
  const visibleOptions = Math.min(filteredOptions.value.length, 8); // or your max visible
  const searchBarHeight = props.showSearch ? 50 : 0;
  return optionHeight * visibleOptions + searchBarHeight;
};

const toggleListbox = () => {
  if (props.disabled) return;
  if (!isOpen.value) {
    // Anticipate dropdown height before opening
    const expectedHeight = getExpectedDropdownHeight();
    emit('dropdown-will-open', { expectedHeight });
    // Wait a tick for modal to expand, then open dropdown
    nextTick(() => {
      isOpen.value = true;
      emit('dropdown-state-changed', { isOpen: true });
    });
  } else {
    isOpen.value = false;
    emit('dropdown-state-changed', { isOpen: false });
  }
};

const handleButtonFocus = () => {
  if (!isOpen.value) {
    isFocused.value = true;
  }
  isSearchFocused.value = false;
};

const handleButtonBlur = () => {
  setTimeout(() => {
    if (!isOpen.value && !isSearchFocused.value) {
      isFocused.value = false;
    }
  }, 100);
};

const handleSearchInputBlur = () => {
  isSearchFocused.value = false;
  setTimeout(() => {
    const activeEl = document.activeElement;
    const optionsEl = listboxOptionsRef.value?.$el || listboxOptionsRef.value;
    const buttonEl = listboxButtonRef.value?.$el || listboxButtonRef.value;

    if (activeEl !== buttonEl && (optionsEl && typeof optionsEl.contains === 'function' && !optionsEl.contains(activeEl))) {
      if (isOpen.value) {
        isOpen.value = false;
        emit('dropdown-state-changed', { isOpen: false }); // Changed payload
      }
      isFocused.value = false;
    }
  }, 150);
};

const handleClickOutside = (event: MouseEvent) => {
  if (!isOpen.value) return;
  const target = event.target as Node;
  const buttonEl = listboxButtonRef.value?.$el || listboxButtonRef.value;
  const optionsEl = listboxOptionsRef.value?.$el || listboxOptionsRef.value;

  if (buttonEl && typeof buttonEl.contains === 'function' && buttonEl.contains(target)) return;
  if (optionsEl && typeof optionsEl.contains === 'function' && optionsEl.contains(target)) return;

  isOpen.value = false;
  isFocused.value = false;
  emit('dropdown-state-changed', { isOpen: false }); // Changed payload
};

onMounted(() => {
  window.addEventListener('click', handleClickOutside, true);
  window.addEventListener('resize', calculatePosition);
  window.addEventListener('scroll', calculatePosition, true);
});

onUnmounted(() => {
  window.removeEventListener('click', handleClickOutside, true);
  window.removeEventListener('resize', calculatePosition);
  window.removeEventListener('scroll', calculatePosition, true);
});

watch(isOpen, (newValue) => {
  if (newValue) {
    nextTick(() => {
      calculatePosition();
      // Added setTimeout to ensure the element is ready for focus after DOM updates and transitions
      setTimeout(() => {
        if (props.showSearch && searchInputRef.value) {
          searchInputRef.value.focus();
          isSearchFocused.value = true;
        } else if (!props.showSearch && listboxOptionsRef.value) {
          const optionsEl = listboxOptionsRef.value?.$el || listboxOptionsRef.value;
          optionsEl?.focus();
        }
        // Emit the actual dropdown height after it is rendered
        const optionsEl = listboxOptionsRef.value?.$el || listboxOptionsRef.value;
        if (optionsEl && typeof optionsEl.getBoundingClientRect === 'function') {
          emit('dropdown-did-open', { actualHeight: optionsEl.getBoundingClientRect().height });
        }
      }, 50); // 50ms delay, adjust if necessary
    });
  } else {
    // If closing, ensure search focus is false
    isSearchFocused.value = false;
    // Check if button still has focus, otherwise float label down
    const buttonEl = listboxButtonRef.value?.$el || listboxButtonRef.value;
    if (document.activeElement !== buttonEl) {
      isFocused.value = false;
    }
    emit('dropdown-state-changed', { isOpen: false }); // Changed payload
  }
});

</script>
