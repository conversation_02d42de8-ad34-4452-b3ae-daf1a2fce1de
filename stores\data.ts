import { defineStore } from "pinia";
import { ItemSchema, type Item } from "~/schemas"; // Import your schema

export const useDataStore = defineStore("data", {
  state: (): { items: Item[] } => ({
    items: [
      // Ensure mock data also fits schema or adjust schema
      { id: 1, title: "Item 1", description: "Description 1 for this item." },
      { id: 2, title: "Item 2", description: "Description 2 for this item." },
    ],
  }),
  actions: {
    addItem(item: Item) {
      const validationResult = ItemSchema.safeParse(item);
      if (!validationResult.success) {
        console.error(
          "Invalid item data:",
          validationResult.error.flatten().fieldErrors
        );
        // Optionally throw an error or return a status with errors
        // throw new Error('Invalid item data');
        return {
          success: false,
          errors: validationResult.error.flatten().fieldErrors,
        };
      }
      // Only push valid data, which is in validationResult.data
      this.items.push(validationResult.data);
      return { success: true, data: validationResult.data };
    },

    updateItem(itemId: number, updatedFields: Partial<Item>) {
      const itemIndex = this.items.findIndex((i) => i.id === itemId);
      if (itemIndex === -1) {
        console.error(`Item with id ${itemId} not found.`);
        return { success: false, errors: { general: ["Item not found"] } };
      }

      const currentItem = this.items[itemIndex];
      const potentialUpdatedItem = { ...currentItem, ...updatedFields };

      const validationResult = ItemSchema.safeParse(potentialUpdatedItem);
      if (!validationResult.success) {
        console.error(
          "Invalid updated item data:",
          validationResult.error.flatten().fieldErrors
        );
        return {
          success: false,
          errors: validationResult.error.flatten().fieldErrors,
        };
      }

      this.items[itemIndex] = validationResult.data;
      return { success: true, data: validationResult.data };
    },

    // Example action fetching data (conceptual)
    async fetchAndAddItem(itemId: number) {
      // In a real scenario, you would fetch from an API:
      // const response = await fetch(`/api/items/${itemId}`);
      // const rawItemData = await response.json();

      // For demonstration, let's assume rawItemData is fetched
      const rawItemData = {
        id: itemId,
        title: "Fetched Item Title",
        description:
          "This is a description for the fetched item that should be long enough.",
      }; // Example data

      const validationResult = ItemSchema.safeParse(rawItemData);
      if (!validationResult.success) {
        console.error(
          "Fetched item data is invalid:",
          validationResult.error.flatten().fieldErrors
        );
        return {
          success: false,
          errors: validationResult.error.flatten().fieldErrors,
        };
      }
      this.items.push(validationResult.data);
      return { success: true, data: validationResult.data };
    },
  },
  getters: {
    getItemById:
      (state) =>
      (id: number): Item | undefined => {
        return state.items.find((item) => item.id === id);
      },
    // You can add more getters as needed
  },
});
