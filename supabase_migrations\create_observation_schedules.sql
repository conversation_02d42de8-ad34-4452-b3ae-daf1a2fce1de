-- Migration: Create observation_schedules table for teacher observation scheduling
-- Created: 2025-01-05
-- Description: Table to store observation schedules for teacher PDPC monitoring

BEGIN;

-- =====================================================
-- CREATE OBSERVATION_SCHEDULES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS observation_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- User reference
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Observer information
    observer_name TEXT NOT NULL,
    observer_position TEXT NOT NULL,
    
    -- Observation details
    observation_date DATE NOT NULL,
    class_subject_id TEXT NOT NULL, -- Format: "class_id_subject_id"
    
    -- Status tracking
    status TEXT NOT NULL DEFAULT 'dijadualkan' CHECK (status IN ('dijadualkan', 'selesai', 'dibatalkan', 'dijadualkan semula')),
    
    -- Additional notes
    notes TEXT,
    
    -- Constraints
    CONSTRAINT observation_schedules_observer_name_check CHECK (char_length(observer_name) > 0),
    CONSTRAINT observation_schedules_observer_position_check CHECK (char_length(observer_position) > 0),
    CONSTRAINT observation_schedules_class_subject_id_check CHECK (char_length(class_subject_id) > 0)
);

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for user queries
CREATE INDEX idx_observation_schedules_user_id ON observation_schedules (user_id, observation_date DESC);

-- Index for date-based queries
CREATE INDEX idx_observation_schedules_date ON observation_schedules (observation_date);

-- Index for status queries
CREATE INDEX idx_observation_schedules_status ON observation_schedules (status);

-- Composite index for user and status
CREATE INDEX idx_observation_schedules_user_status ON observation_schedules (user_id, status);

-- =====================================================
-- CREATE UPDATED_AT TRIGGER
-- =====================================================

-- Create trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_observation_schedules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_observation_schedules_updated_at
    BEFORE UPDATE ON observation_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_observation_schedules_updated_at();

-- =====================================================
-- SET UP ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS
ALTER TABLE observation_schedules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own observation schedules"
ON observation_schedules FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own observation schedules"
ON observation_schedules FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own observation schedules"
ON observation_schedules FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own observation schedules"
ON observation_schedules FOR DELETE
USING (auth.uid() = user_id);

-- =====================================================
-- ADD TABLE AND COLUMN COMMENTS
-- =====================================================

COMMENT ON TABLE observation_schedules IS 'Teacher observation schedules for PDPC monitoring and quality assurance';
COMMENT ON COLUMN observation_schedules.id IS 'Primary key UUID';
COMMENT ON COLUMN observation_schedules.user_id IS 'Foreign key to auth.users table (teacher being observed)';
COMMENT ON COLUMN observation_schedules.observer_name IS 'Name of the person conducting the observation';
COMMENT ON COLUMN observation_schedules.observer_position IS 'Position/title of the observer';
COMMENT ON COLUMN observation_schedules.observation_date IS 'Date when the observation is scheduled';
COMMENT ON COLUMN observation_schedules.class_subject_id IS 'Composite ID in format "class_id_subject_id"';
COMMENT ON COLUMN observation_schedules.status IS 'Current status of the observation (dijadualkan, selesai, dibatalkan, dijadualkan semula)';
COMMENT ON COLUMN observation_schedules.notes IS 'Additional notes or comments about the observation';
COMMENT ON COLUMN observation_schedules.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN observation_schedules.updated_at IS 'Timestamp when record was last updated (auto-updated via trigger)';

COMMIT;

-- =====================================================
-- EXAMPLE USAGE
-- =====================================================

/*
-- Insert a new observation schedule
INSERT INTO observation_schedules (
    user_id, 
    observer_name, 
    observer_position, 
    observation_date, 
    class_subject_id, 
    notes
) VALUES (
    'user-uuid-here',
    'Puan Siti Aminah',
    'Guru Besar',
    '2025-01-15',
    'f5_8fe00a97-5456-4db7-8d2b-fa6646c32ae3',
    'Observation for annual performance review'
);

-- Query observation schedules for a user
SELECT * FROM observation_schedules 
WHERE user_id = 'user-uuid-here' 
ORDER BY observation_date DESC;

-- Update observation status
UPDATE observation_schedules 
SET status = 'completed', notes = 'Observation completed successfully'
WHERE id = 'schedule-uuid-here';
*/
