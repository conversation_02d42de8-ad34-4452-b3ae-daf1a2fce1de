<template>
  <div class="system-template-viewer">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Template Sistem
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Template yang disediakan oleh sistem untuk pelbagai jenis refleksi
        </p>
      </div>
    </div>

    <!-- Category Filter -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-2">
        <button @click="selectedCategory = null" :class="[
          'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
          selectedCategory === null
            ? 'bg-primary text-white'
            : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
        ]">
          Semua <PERSON>
        </button>
        <button v-for="(label, category) in categoryLabels" :key="category" @click="selectedCategory = category" :class="[
          'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
          selectedCategory === category
            ? 'bg-primary text-white'
            : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
        ]">
          {{ label }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-2">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        <span class="text-gray-600 dark:text-gray-400">Memuatkan template...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
      class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
      <div class="flex">
        <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5" />
        <div class="text-sm text-red-800 dark:text-red-200">
          {{ error }}
        </div>
      </div>
    </div>

    <!-- Templates Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="template in filteredTemplates" :key="template.id"
        class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
        <!-- Template Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
              {{ template.name }}
            </h3>
            <p v-if="template.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
              {{ template.description }}
            </p>
          </div>

          <!-- Actions -->
          <div class="flex items-center space-x-1 ml-2">
            <!-- Favorite Button -->
            <button @click="toggleFavorite(template.id)" :class="[
              'p-1 rounded-full transition-colors',
              template.is_favorite
                ? 'text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
                : 'text-gray-400 hover:text-red-500 hover:bg-gray-50 dark:hover:bg-gray-800'
            ]" :title="template.is_favorite ? 'Buang dari kegemaran' : 'Tambah ke kegemaran'">
              <Icon :name="template.is_favorite ? 'heroicons:heart-solid' : 'heroicons:heart'" class="w-4 h-4" />
            </button>

            <!-- Preview Button -->
            <button @click="previewTemplate(template)"
              class="p-1 rounded-full text-gray-400 hover:text-blue-500 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              title="Pratonton template">
              <Icon name="heroicons:eye" class="w-4 h-4" />
            </button>

            <!-- Copy to Personal Button -->
            <button @click="copyToPersonal(template)"
              class="p-1 rounded-full text-gray-400 hover:text-green-500 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              title="Salin ke template peribadi">
              <Icon name="heroicons:document-duplicate" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <!-- Template Metadata -->
        <div class="space-y-3">
          <!-- Category -->
          <div class="flex items-center space-x-2">
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {{ getCategoryLabel(template.category) }}
            </span>
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Sistem
            </span>
          </div>

          <!-- Prompts Count -->
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <Icon name="heroicons:chat-bubble-left-right" class="w-4 h-4 inline mr-1" />
            {{ Object.keys(template.prompts).length }} soalan panduan
          </div>

          <!-- Usage Stats -->
          <div v-if="template.user_usage_count && template.user_usage_count > 0"
            class="text-sm text-gray-600 dark:text-gray-400">
            <Icon name="heroicons:chart-bar" class="w-4 h-4 inline mr-1" />
            Anda telah guna {{ template.user_usage_count }} kali
          </div>

          <!-- Last Used -->
          <div v-if="template.last_used" class="text-xs text-gray-500 dark:text-gray-500">
            Terakhir digunakan: {{ formatLastUsed(template.last_used) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Template Preview Modal -->
    <TemplatePreviewModal :is-open="showPreviewModal" :template="previewingTemplate"
      @update:is-open="showPreviewModal = $event" @edit-template="handleEditTemplate" />

    <!-- Copy Confirmation Modal -->
    <Modal :is-open="showCopyModal" title="Salin Template ke Peribadi" @update:is-open="showCopyModal = $event"
      size="md">
      <div v-if="copyingTemplate" class="space-y-4">
        <p class="text-gray-600 dark:text-gray-400">
          Adakah anda ingin menyalin template "{{ copyingTemplate.name }}" ke dalam koleksi template peribadi anda?
        </p>
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <p class="text-sm text-blue-800 dark:text-blue-200">
            Template yang disalin akan menjadi template peribadi yang boleh anda edit mengikut keperluan.
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <UiBaseButton @click="showCopyModal = false" variant="outline">
            Batal
          </UiBaseButton>
          <UiBaseButton @click="confirmCopyTemplate" variant="primary" :loading="copying">
            Salin Template
          </UiBaseButton>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useReflectionTemplates } from '~/composables/useReflectionTemplates';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import type { ReflectionTemplate, ReflectionTemplateCategory } from '~/types/reflections';
import TemplatePreviewModal from './TemplatePreviewModal.vue';
import Modal from '~/components/ui/composite/Modal.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';

// Composables
const {
  templates,
  loading,
  error,
  fetchAllTemplates,
  toggleTemplateFavorite,
  createUserTemplate
} = useReflectionTemplates();

// State
const selectedCategory = ref<ReflectionTemplateCategory | null>(null);
const showPreviewModal = ref(false);
const showCopyModal = ref(false);
const previewingTemplate = ref<ReflectionTemplate | null>(null);
const copyingTemplate = ref<ReflectionTemplate | null>(null);
const copying = ref(false);

// Category labels
const categoryLabels = TEMPLATE_CATEGORY_LABELS;

// Computed
const filteredTemplates = computed(() => {
  const systemTemplates = templates.value.filter(t => t.is_system_template);
  if (!selectedCategory.value) {
    return systemTemplates;
  }
  return systemTemplates.filter(template => template.category === selectedCategory.value);
});

// Methods
const getCategoryLabel = (category: string): string => {
  return TEMPLATE_CATEGORY_LABELS[category as keyof typeof TEMPLATE_CATEGORY_LABELS] || category;
};

const formatLastUsed = (lastUsed: string): string => {
  const date = new Date(lastUsed);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) {
    return 'Baru sahaja';
  } else if (diffInHours < 24) {
    return `${diffInHours}j lalu`;
  } else if (diffInHours < 24 * 7) {
    const days = Math.floor(diffInHours / 24);
    return `${days}h lalu`;
  } else {
    return date.toLocaleDateString('ms-MY', {
      day: 'numeric',
      month: 'short'
    });
  }
};

const toggleFavorite = async (templateId: string) => {
  try {
    await toggleTemplateFavorite(templateId);
    // Refresh templates to update UI
    await fetchAllTemplates();
  } catch (err) {
    console.error('Error toggling favorite:', err);
  }
};

const previewTemplate = (template: ReflectionTemplate) => {
  previewingTemplate.value = template;
  showPreviewModal.value = true;
};

const copyToPersonal = (template: ReflectionTemplate) => {
  copyingTemplate.value = template;
  showCopyModal.value = true;
};

const confirmCopyTemplate = async () => {
  if (!copyingTemplate.value) return;

  try {
    copying.value = true;

    // Create a new template based on the system template
    await createUserTemplate({
      name: `${copyingTemplate.value.name} (Salinan)`,
      description: copyingTemplate.value.description || '',
      category: copyingTemplate.value.category,
      prompts: { ...copyingTemplate.value.prompts },
      default_values: { ...copyingTemplate.value.default_values }
    });

    showCopyModal.value = false;
    copyingTemplate.value = null;

    // You could emit a success event here for toast notifications
    console.log('Template copied successfully');
  } catch (err) {
    console.error('Error copying template:', err);
  } finally {
    copying.value = false;
  }
};

const handleEditTemplate = (template: ReflectionTemplate) => {
  // This would typically navigate to the template editor
  // For system templates, we might want to copy them first
  if (template.is_system_template) {
    copyToPersonal(template);
  }
};

// Lifecycle
onMounted(async () => {
  try {
    await fetchAllTemplates();
  } catch (err) {
    console.error('Error loading system templates:', err);
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
