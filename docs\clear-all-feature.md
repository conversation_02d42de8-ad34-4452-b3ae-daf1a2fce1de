# Clear All Timetable Feature

## Overview

The Clear All feature allows users to quickly remove all timetable entries with a single action. This feature provides a safe and user-friendly way to reset the entire timetable when needed.

## Implementation Details

### Button Placement
- **Location**: Timetable header, positioned to the left of other action buttons
- **Visibility**: Only shown when timetable entries exist (`v-if="timetableEntries.length > 0"`)
- **Responsive Design**: 
  - Desktop/Tablet: "Kosongkan Semua" 
  - Mobile: "Kosong"

### UX Design Considerations
1. **Destructive Action Styling**: Red border and text to indicate danger
2. **Conditional Display**: Button only appears when there's something to clear
3. **Responsive Text**: Shorter text on mobile to preserve space
4. **Proper Icon**: Uses `mdi:delete-sweep` for clear visual indication

### Safety Features

#### Confirmation Modal
- Uses the reusable `UiCompositeDeleteConfirmationModal`
- **High Danger Level**: Configured with `danger-level="high"` and `impact-severity="high"`
- **Detailed Impact Preview**: Shows exactly what will be deleted:
  - Groups entries by day for easy scanning
  - Shows time slots and class names for each entry
  - Displays total count of entries to be deleted
- **Strong Warning**: Includes permanent data loss warning

#### User Experience Features
1. **Loading State**: Shows loading indicator during deletion process
2. **Error Handling**: Catches and logs errors gracefully
3. **Immediate UI Update**: Local state updates immediately after successful deletion
4. **Database Refresh**: Fetches latest data to ensure UI consistency

## Technical Implementation

### Modal State Management
```typescript
const isClearAllModalOpen = ref(false)
const isClearingAll = ref(false)
```

### Key Functions

#### `openClearAllModal()`
- Opens the confirmation modal
- Simple state change to show modal

#### `getEntriesForDay(day: DayOfWeek): TimetableEntry[]`
- Helper function to group entries by day
- Used in modal preview to show organized view of what will be deleted

#### `handleClearAll()`
- Core deletion logic
- Uses `deleteTimetableEntry()` for each entry (leverages existing composable)
- Handles loading states and error management
- Refreshes data after completion

### Integration with Existing System
- **Composables**: Uses existing `useTimetable()` composable
- **Modal System**: Integrates with reusable `DeleteConfirmationModal`
- **Styling**: Follows established design patterns
- **TypeScript**: Fully typed with proper error handling

## User Flow

1. **User clicks "Kosongkan Semua" button**
2. **Confirmation modal opens** showing:
   - Clear explanation of action
   - Detailed list of entries to be deleted (grouped by day)
   - Strong warning about permanent deletion
3. **User confirms or cancels**
4. **If confirmed**: 
   - Loading state shows during deletion
   - All entries deleted from database
   - UI updates immediately
   - Modal closes automatically
5. **If cancelled**: Modal closes, no changes made

## Mobile Optimization

- **Button Text**: Shortened to "Kosong" on mobile screens
- **Modal Layout**: Uses responsive modal design
- **Touch Targets**: Proper button sizing for touch interaction
- **Scroll Areas**: Entry preview scrolls if too many items

## Error Handling

- Network errors are caught and logged
- UI reverts gracefully on failure
- User feedback through console (could be enhanced with toast notifications)
- Loading state properly cleared on error

## Future Enhancements

1. **Toast Notifications**: Add success/error toast messages
2. **Undo Functionality**: Temporary "undo" option after deletion
3. **Bulk Selection**: Allow selective clearing by day or time range
4. **Export Before Clear**: Option to export data before clearing
