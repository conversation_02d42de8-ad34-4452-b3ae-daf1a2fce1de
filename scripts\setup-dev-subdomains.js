#!/usr/bin/env node

// Development Subdomain Setup Script
// Created: 2025-07-13
// Purpose: Configure local development environment for subdomain routing

import { execSync } from 'child_process'
import { readFileSync, writeFileSync, existsSync } from 'fs'
import { platform } from 'os'
import { join } from 'path'

const HOSTS_FILE_PATHS = {
  win32: 'C:\\Windows\\System32\\drivers\\etc\\hosts',
  darwin: '/etc/hosts',
  linux: '/etc/hosts'
}

const DEV_DOMAIN = 'localhost'
const DEV_PORT = '3000'

// Sample school codes for development - REMOVED FOR PRODUCTION READINESS
const SAMPLE_SCHOOLS = [
  // No hardcoded demo schools - only real schools from database
]

class DevSubdomainSetup {
  constructor() {
    this.platform = platform()
    this.hostsFile = HOSTS_FILE_PATHS[this.platform]
    
    if (!this.hostsFile) {
      throw new Error(`Unsupported platform: ${this.platform}`)
    }
  }

  /**
   * Check if running with administrator/sudo privileges
   */
  checkPrivileges() {
    try {
      if (this.platform === 'win32') {
        // On Windows, try to access a system file
        execSync('net session >nul 2>&1', { stdio: 'ignore' })
      } else {
        // On Unix-like systems, check if we can write to /etc/hosts
        execSync('touch /tmp/hosts-test && rm /tmp/hosts-test', { stdio: 'ignore' })
      }
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Read current hosts file content
   */
  readHostsFile() {
    try {
      if (!existsSync(this.hostsFile)) {
        console.warn(`⚠️  Hosts file not found: ${this.hostsFile}`)
        return ''
      }
      return readFileSync(this.hostsFile, 'utf8')
    } catch (error) {
      console.error(`❌ Error reading hosts file: ${error.message}`)
      return ''
    }
  }

  /**
   * Add subdomain entries to hosts file
   */
  addSubdomainEntries() {
    console.log('📝 Adding subdomain entries to hosts file...')
    
    const hostsContent = this.readHostsFile()
    const lines = hostsContent.split('\n')
    
    // Remove existing RPHMate entries
    const filteredLines = lines.filter(line => 
      !line.includes('# RPHMate Development')
    )
    
    // Add new entries
    const newEntries = [
      '',
      '# RPHMate Development Subdomains',
      '# Added by setup-dev-subdomains.js',
      `127.0.0.1 ${DEV_DOMAIN}`,
      ...SAMPLE_SCHOOLS.map(school => `127.0.0.1 ${school}.${DEV_DOMAIN}`),
      '# End RPHMate Development Subdomains',
      ''
    ]
    
    const updatedContent = [...filteredLines, ...newEntries].join('\n')
    
    try {
      writeFileSync(this.hostsFile, updatedContent, 'utf8')
      console.log('✅ Subdomain entries added to hosts file')
      console.log('📋 Added entries:')
      SAMPLE_SCHOOLS.forEach(school => {
        console.log(`   - ${school}.${DEV_DOMAIN}:${DEV_PORT}`)
      })
    } catch (error) {
      console.error(`❌ Error writing hosts file: ${error.message}`)
      console.log('💡 Try running with administrator/sudo privileges')
      return false
    }
    
    return true
  }

  /**
   * Remove subdomain entries from hosts file
   */
  removeSubdomainEntries() {
    console.log('🗑️  Removing subdomain entries from hosts file...')
    
    const hostsContent = this.readHostsFile()
    const lines = hostsContent.split('\n')
    
    // Remove RPHMate entries
    let inRPHMateSection = false
    const filteredLines = lines.filter(line => {
      if (line.includes('# RPHMate Development Subdomains')) {
        inRPHMateSection = true
        return false
      }
      if (line.includes('# End RPHMate Development Subdomains')) {
        inRPHMateSection = false
        return false
      }
      return !inRPHMateSection
    })
    
    const updatedContent = filteredLines.join('\n')
    
    try {
      writeFileSync(this.hostsFile, updatedContent, 'utf8')
      console.log('✅ Subdomain entries removed from hosts file')
    } catch (error) {
      console.error(`❌ Error writing hosts file: ${error.message}`)
      return false
    }
    
    return true
  }

  /**
   * Create development configuration files
   */
  createDevConfig() {
    console.log('📄 Creating development configuration...')
    
    // Create .env.development file
    const envContent = `# Development Environment Configuration
# Generated by setup-dev-subdomains.js

# Base domain for subdomains
NUXT_PUBLIC_BASE_DOMAIN=${DEV_DOMAIN}:${DEV_PORT}
NUXT_PUBLIC_DEV_MODE=true

# Supabase Configuration
SUPABASE_URL=https://nhgyywlfopodxomxbegx.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZ3l5d2xmb3BvZHhvbXhiZWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTU1OTgsImV4cCI6MjA2Mjc5MTU5OH0.OaAqdBGba5YxutbQ8oIE_KULFGXdkBcfFzmdYUav4Sc

# Development Sample Schools
DEV_SAMPLE_SCHOOLS=${SAMPLE_SCHOOLS.join(',')}

# Enable subdomain detection in development
NUXT_PUBLIC_ENABLE_SUBDOMAINS=true
`

    try {
      writeFileSync('.env.development', envContent)
      console.log('✅ Created .env.development file')
    } catch (error) {
      console.error(`❌ Error creating .env.development: ${error.message}`)
    }

    // Create development documentation
    const docContent = `# Development Subdomain Setup

## Available Development URLs

### Main Application
- http://${DEV_DOMAIN}:${DEV_PORT} - Landing page and admin dashboard

### School Subdomains
${SAMPLE_SCHOOLS.map(school => `- http://${school}.${DEV_DOMAIN}:${DEV_PORT} - ${school.charAt(0).toUpperCase() + school.slice(1)} School Dashboard`).join('\n')}

## Testing Subdomain Routing

1. Start the development server:
   \`\`\`bash
   npm run dev
   \`\`\`

2. Test main domain:
   \`\`\`bash
   curl http://${DEV_DOMAIN}:${DEV_PORT}
   \`\`\`

3. Test school subdomains:
   \`\`\`bash
   curl http://demo.${DEV_DOMAIN}:${DEV_PORT}
   curl http://test.${DEV_DOMAIN}:${DEV_PORT}
   \`\`\`

## Browser Testing

Open these URLs in your browser:
- http://${DEV_DOMAIN}:${DEV_PORT} (should show landing page)
- http://demo.${DEV_DOMAIN}:${DEV_PORT} (should show demo school dashboard)
- http://test.${DEV_DOMAIN}:${DEV_PORT} (should show test school dashboard)

## Troubleshooting

### Subdomain not working?
1. Check hosts file entries: \`cat ${this.hostsFile}\`
2. Clear browser cache and DNS cache
3. Restart development server
4. Check Nuxt configuration for subdomain handling

### DNS Cache Issues
- Windows: \`ipconfig /flushdns\`
- macOS: \`sudo dscacheutil -flushcache\`
- Linux: \`sudo systemctl restart systemd-resolved\`

## Cleanup

To remove development subdomain entries:
\`\`\`bash
node scripts/setup-dev-subdomains.js --cleanup
\`\`\`
`

    try {
      writeFileSync('docs/dev-subdomain-setup.md', docContent)
      console.log('✅ Created development documentation')
    } catch (error) {
      console.error(`❌ Error creating documentation: ${error.message}`)
    }
  }

  /**
   * Main setup process
   */
  async setup() {
    console.log('🚀 RPHMate Development Subdomain Setup')
    console.log('==========================================\n')

    // Check privileges
    if (!this.checkPrivileges()) {
      console.log('⚠️  Administrator/sudo privileges required to modify hosts file')
      console.log('💡 Please run this script with elevated privileges:')
      if (this.platform === 'win32') {
        console.log('   - Run Command Prompt as Administrator')
      } else {
        console.log('   - Run with sudo: sudo node scripts/setup-dev-subdomains.js')
      }
      console.log('\n📄 Creating configuration files without hosts modification...')
    } else {
      // Add subdomain entries
      if (!this.addSubdomainEntries()) {
        console.log('❌ Failed to add subdomain entries')
        return false
      }
    }

    // Create development configuration
    this.createDevConfig()

    console.log('\n🎉 Development subdomain setup completed!')
    console.log('\n📋 Next steps:')
    console.log('1. Start the development server: npm run dev')
    console.log(`2. Open http://${DEV_DOMAIN}:${DEV_PORT} in your browser`)
    console.log(`3. Test subdomains like http://demo.${DEV_DOMAIN}:${DEV_PORT}`)
    console.log('4. Check docs/dev-subdomain-setup.md for detailed instructions')

    return true
  }

  /**
   * Cleanup process
   */
  async cleanup() {
    console.log('🧹 Cleaning up development subdomain setup...')
    
    if (!this.checkPrivileges()) {
      console.log('⚠️  Administrator/sudo privileges required to modify hosts file')
      return false
    }

    return this.removeSubdomainEntries()
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)
  const setup = new DevSubdomainSetup()

  try {
    if (args.includes('--cleanup') || args.includes('-c')) {
      await setup.cleanup()
    } else {
      await setup.setup()
    }
  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { DevSubdomainSetup }
