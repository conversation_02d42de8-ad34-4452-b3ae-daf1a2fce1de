<template>
  <div class="bg-light-card dark:bg-dark-card text-light-card-foreground dark:text-dark-card-foreground rounded-lg"
    :class="{
      'p-4': !noDefaultPadding,
      'shadow-md': props.variant === 'default',
      'shadow-lg': props.variant === 'shadow-lg',
      'shadow-2xl': props.variant === 'shadow-2xl',
      'border border-light-border dark:border-dark-border': props.variant === 'flat'
    }">
    <div v-if="$slots.header" class="mb-4 border-b border-light-border dark:border-dark-border pb-2">
      <slot name="header" />
    </div>
    <div v-if="$slots.default" :class="{ 'mb-4': !noDefaultSpacing }">
      <slot />
    </div>
    <div v-if="$slots.footer" class="mt-4 border-t border-light-border dark:border-dark-border pt-2">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  variant?: 'default' | 'flat' | 'shadow-lg' | 'shadow-2xl';
  noDefaultPadding?: boolean;
  noDefaultSpacing?: boolean;
}>(), {
  variant: 'default',
  noDefaultPadding: false,
  noDefaultSpacing: true,
});
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
