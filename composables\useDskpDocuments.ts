import { ref, computed } from 'vue';
import type { Database } from '~/types/supabase';
import type {
  DskpDocument,
  DskpDocumentInput,
  DskpStatus,
  ClassSubjectOption
} from '~/types/dskpDocuments';
import {
  MAX_DSKP_FILE_SIZE_BYTES,
  SUPPORTED_DSKP_FILE_TYPES
} from '~/types/dskpDocuments';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import { getClassLevelName } from '~/utils/classLevelMapping';

type GenericSupabaseClient = ReturnType<typeof useSupabaseClient<Database>>;

const DSKP_FILES_BUCKET = 'dskp-files';

// Storage and utility helpers
function sanitizeFilenameForStorage(originalName: string): string {
  return originalName.replace(/[^a-zA-Z0-9_.-]/g, '_');
}

function generateStorageFilePath(
  userId: string,
  classId: string,
  subjectId: string,
  originalFileName: string
): string {
  const sanitizedFileName = sanitizeFilenameForStorage(originalFileName);
  const timestamp = Date.now();
  return `${userId}/${classId}/${subjectId}/${timestamp}_${sanitizedFileName}`;
}

// Validation helpers
interface FileValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

function validateDskpFile(file: File): FileValidationResult {
  // Check file size
  if (file.size > MAX_DSKP_FILE_SIZE_BYTES) {
    return {
      isValid: false,
      errorMessage: `Saiz fail terlalu besar. Maksimum 10MB dibenarkan.`
    };
  }

  // Check file type
  const supportedTypes = SUPPORTED_DSKP_FILE_TYPES as readonly string[];
  if (!supportedTypes.includes(file.type)) {
    return {
      isValid: false,
      errorMessage: `Jenis fail tidak disokong. Sila pilih fail PDF, Word, Excel, atau PowerPoint.`
    };
  }

  return { isValid: true };
}

// Main composable
export const useDskpDocuments = () => {
  const client = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  // State
  const dskpDocuments = ref<DskpDocument[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Computed
  const dskpDocumentsByClassSubject = computed(() => {
    const map = new Map<string, DskpDocument>();
    dskpDocuments.value.forEach(doc => {
      const key = `${doc.class_id}_${doc.subject_id}`;
      map.set(key, doc);
    });
    return map;
  });

  // Fetch all DSKP documents for the current user
  const fetchDskpDocuments = async (): Promise<void> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const { data, error: fetchError } = await client
        .from('dskp_documents')
        .select('*')
        .eq('user_id', user.value.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('[fetchDskpDocuments] Database error:', fetchError);
        throw fetchError;
      }

      dskpDocuments.value = data || [];
    } catch (e) {
      console.error('[fetchDskpDocuments] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to fetch DSKP documents';
    } finally {
      loading.value = false;
    }
  };

  // Get DSKP status for class-subject combinations
  const getDskpStatusForClassSubjects = (
    userClassSubjects: UserClassSubjectEntry[]
  ): DskpStatus[] => {
    // Group by class_id and subject_id to avoid duplicates
    const classSubjectGroups = new Map<string, UserClassSubjectEntry>();
    
    userClassSubjects.forEach(entry => {
      const key = `${entry.class_id}_${entry.subject_id}`;
      if (!classSubjectGroups.has(key)) {
        classSubjectGroups.set(key, entry);
      }
    });

    // Convert groups to DskpStatus array
    return Array.from(classSubjectGroups.entries()).map(([key, group]) => {
      const dskpDoc = dskpDocumentsByClassSubject.value.get(key);
      const classLevelName = getClassLevelName(group.class_id);

      return {
        class_id: group.class_id,
        subject_id: group.subject_id,
        class_name: classLevelName, // Use class level name instead of specific class name
        subject_name: '', // Will be resolved by useSubjects
        subject_abbreviation: group.subject_abbreviation,
        has_dskp: !!dskpDoc,
        dskp_document: dskpDoc,
      };
    });
  };

  // Create class-subject options for dropdowns
  const createClassSubjectOptions = (
    userClassSubjects: UserClassSubjectEntry[],
    subjects: any[]
  ): ClassSubjectOption[] => {
    // Group by class_id and subject_id to avoid duplicates
    const classSubjectGroups = new Map<string, UserClassSubjectEntry>();
    
    userClassSubjects.forEach(entry => {
      const key = `${entry.class_id}_${entry.subject_id}`;
      if (!classSubjectGroups.has(key)) {
        classSubjectGroups.set(key, entry);
      }
    });

    return Array.from(classSubjectGroups.values()).map(entry => {
      const subject = subjects.find(s => s.id === entry.subject_id);
      const subjectName = subject?.name || entry.subject_abbreviation || 'Subjek';
      const classLevelName = getClassLevelName(entry.class_id);

      return {
        value: `${entry.class_id}_${entry.subject_id}`,
        label: `${classLevelName} - ${subjectName}`,
        class_id: entry.class_id,
        subject_id: entry.subject_id,
        class_name: classLevelName,
        subject_name: subjectName,
        subject_abbreviation: entry.subject_abbreviation,
      };
    });
  };

  // Upload a new DSKP document
  const uploadDskpDocument = async (
    dskpInput: DskpDocumentInput,
    file: File
  ): Promise<DskpDocument | null> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return null;
    }

    // Validate file
    const validation = validateDskpFile(file);
    if (!validation.isValid) {
      error.value = validation.errorMessage || 'Invalid file';
      return null;
    }

    loading.value = true;
    error.value = null;

    try {
      // Generate storage file path
      const storageFilePath = generateStorageFilePath(
        user.value.id,
        dskpInput.class_id,
        dskpInput.subject_id,
        file.name
      );

      // Upload file to storage
      const { error: uploadError } = await client.storage
        .from(DSKP_FILES_BUCKET)
        .upload(storageFilePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (uploadError) {
        console.error('[uploadDskpDocument] Storage upload error:', uploadError);
        throw uploadError;
      }

      // Create database record
      const dskpDocumentData = {
        user_id: user.value.id,
        class_id: dskpInput.class_id,
        subject_id: dskpInput.subject_id,
        class_name: dskpInput.class_name,
        subject_name: dskpInput.subject_name,
        file_name: file.name,
        storage_file_path: storageFilePath,
        file_mime_type: file.type,
        file_size_bytes: file.size,
      };

      const { data, error: insertError } = await client
        .from('dskp_documents')
        .insert(dskpDocumentData)
        .select()
        .single();

      if (insertError) {
        // If database insert fails, clean up the uploaded file
        await client.storage
          .from(DSKP_FILES_BUCKET)
          .remove([storageFilePath]);

        console.error('[uploadDskpDocument] Database insert error:', insertError);
        throw insertError;
      }

      // Update local state
      dskpDocuments.value.unshift(data);

      return data;
    } catch (e) {
      console.error('[uploadDskpDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to upload DSKP document';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Update an existing DSKP document (replace file)
  const updateDskpDocument = async (
    dskpDocument: DskpDocument,
    newFile: File
  ): Promise<DskpDocument | null> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return null;
    }

    // Validate file
    const validation = validateDskpFile(newFile);
    if (!validation.isValid) {
      error.value = validation.errorMessage || 'Invalid file';
      return null;
    }

    loading.value = true;
    error.value = null;

    try {
      // Generate new storage file path
      const newStorageFilePath = generateStorageFilePath(
        user.value.id,
        dskpDocument.class_id,
        dskpDocument.subject_id,
        newFile.name
      );

      // Upload new file to storage
      const { error: uploadError } = await client.storage
        .from(DSKP_FILES_BUCKET)
        .upload(newStorageFilePath, newFile, {
          cacheControl: '3600',
          upsert: false,
        });

      if (uploadError) {
        console.error('[updateDskpDocument] Storage upload error:', uploadError);
        throw uploadError;
      }

      // Update database record
      const updateData = {
        file_name: newFile.name,
        storage_file_path: newStorageFilePath,
        file_mime_type: newFile.type,
        file_size_bytes: newFile.size,
        updated_at: new Date().toISOString(),
      };

      const { data, error: updateError } = await client
        .from('dskp_documents')
        .update(updateData)
        .eq('id', dskpDocument.id)
        .select()
        .single();

      if (updateError) {
        // If database update fails, clean up the new uploaded file
        await client.storage
          .from(DSKP_FILES_BUCKET)
          .remove([newStorageFilePath]);

        console.error('[updateDskpDocument] Database update error:', updateError);
        throw updateError;
      }

      // Remove old file from storage
      const { error: deleteError } = await client.storage
        .from(DSKP_FILES_BUCKET)
        .remove([dskpDocument.storage_file_path]);

      if (deleteError) {
        console.warn('[updateDskpDocument] Warning: Failed to delete old file:', deleteError);
      }

      // Update local state
      const index = dskpDocuments.value.findIndex(doc => doc.id === dskpDocument.id);
      if (index !== -1) {
        dskpDocuments.value[index] = data;
      }

      return data;
    } catch (e) {
      console.error('[updateDskpDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to update DSKP document';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Delete a DSKP document
  const deleteDskpDocument = async (dskpDocument: DskpDocument): Promise<boolean> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return false;
    }

    loading.value = true;
    error.value = null;

    try {
      // Delete from database first
      const { error: deleteError } = await client
        .from('dskp_documents')
        .delete()
        .eq('id', dskpDocument.id);

      if (deleteError) {
        console.error('[deleteDskpDocument] Database delete error:', deleteError);
        throw deleteError;
      }

      // Delete file from storage
      const { error: storageDeleteError } = await client.storage
        .from(DSKP_FILES_BUCKET)
        .remove([dskpDocument.storage_file_path]);

      if (storageDeleteError) {
        console.warn('[deleteDskpDocument] Warning: Failed to delete file from storage:', storageDeleteError);
      }

      // Update local state
      const index = dskpDocuments.value.findIndex(doc => doc.id === dskpDocument.id);
      if (index !== -1) {
        dskpDocuments.value.splice(index, 1);
      }

      return true;
    } catch (e) {
      console.error('[deleteDskpDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to delete DSKP document';
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Get temporary public URL for file preview
  const getTemporaryPublicUrl = async (
    filePath: string,
    expiresInSeconds: number = 3600
  ): Promise<string | null> => {
    if (!filePath) {
      error.value = 'File path is required';
      return null;
    }

    try {
      const { data, error: signedUrlError } = await client.storage
        .from(DSKP_FILES_BUCKET)
        .createSignedUrl(filePath, expiresInSeconds);

      if (signedUrlError) {
        console.error('[getTemporaryPublicUrl] Supabase error creating signed URL:', signedUrlError);
        throw signedUrlError;
      }

      return data?.signedUrl || null;
    } catch (e) {
      console.error('[getTemporaryPublicUrl] Error getting temporary public URL:', e);
      error.value = e instanceof Error ? e.message : 'Failed to get temporary public URL';
      return null;
    }
  };

  // Validate file before upload
  const validateFile = (file: File): FileValidationResult => {
    // Check file size
    if (file.size > MAX_DSKP_FILE_SIZE_BYTES) {
      return {
        isValid: false,
        errorMessage: `Saiz fail terlalu besar. Maksimum 10MB dibenarkan.`
      };
    }

    // Check file type
    const supportedTypes = SUPPORTED_DSKP_FILE_TYPES as readonly string[];

    if (!supportedTypes.includes(file.type)) {
      return {
        isValid: false,
        errorMessage: `Jenis fail tidak disokong. Sila pilih fail PDF, Word, Excel, atau PowerPoint.`
      };
    }

    return { isValid: true };
  };

  return {
    // State
    dskpDocuments: readonly(dskpDocuments),
    loading: readonly(loading),
    error: readonly(error),

    // Computed
    dskpDocumentsByClassSubject,

    // Methods
    fetchDskpDocuments,
    getDskpStatusForClassSubjects,
    createClassSubjectOptions,
    uploadDskpDocument,
    updateDskpDocument,
    deleteDskpDocument,
    getTemporaryPublicUrl,
    validateFile,
  };
};
