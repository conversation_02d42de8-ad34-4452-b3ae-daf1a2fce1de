<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Coupon Management
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Create and manage promotional coupons for school registrations
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="showCreateModal = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-2" />
            Create Coupon
          </button>
        </div>
      </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <!-- Total Coupons -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:ticket" class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Coupons</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.totalCoupons }}</p>
          </div>
        </div>
      </div>

      <!-- Active Coupons -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:check-circle" class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Coupons</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.activeCoupons }}</p>
          </div>
        </div>
      </div>

      <!-- Total Usage -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:chart-bar" class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Usage</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.totalUsage }}</p>
          </div>
        </div>
      </div>

      <!-- Revenue Generated -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:currency-dollar" class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue Generated</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">RM {{ stats.revenueGenerated }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Coupons
          </label>
          <div class="relative">
            <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by code or description..."
              class="pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
          </div>
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status
          </label>
          <select
            v-model="statusFilter"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="expired">Expired</option>
            <option value="disabled">Disabled</option>
          </select>
        </div>

        <!-- Type Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Type
          </label>
          <select
            v-model="typeFilter"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Types</option>
            <option value="percentage">Percentage</option>
            <option value="fixed">Fixed Amount</option>
            <option value="free_trial">Free Trial</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Coupons Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">
            Coupons ({{ filteredCoupons.length }})
          </h2>
          <div class="flex items-center space-x-2">
            <button
              @click="refreshCoupons"
              class="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Icon name="heroicons:arrow-path" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="p-6">
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse flex items-center space-x-4">
            <div class="w-20 h-4 bg-gray-200 rounded"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-200 rounded w-1/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Coupons List -->
      <div v-else-if="filteredCoupons.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Code
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Description
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Discount
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Usage
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Expires
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="coupon in filteredCoupons" :key="coupon.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-white font-mono">
                  {{ coupon.code }}
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ coupon.description }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  <span v-if="coupon.discount_type === 'percentage'">
                    {{ coupon.discount_value }}%
                  </span>
                  <span v-else-if="coupon.discount_type === 'fixed'">
                    RM {{ coupon.discount_value }}
                  </span>
                  <span v-else-if="coupon.discount_type === 'free_trial'">
                    {{ coupon.trial_days }} days free
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                  {{ coupon.used_count }} / {{ coupon.usage_limit || '∞' }}
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                  <div 
                    class="bg-blue-600 h-2 rounded-full" 
                    :style="{ width: getUsagePercentage(coupon) + '%' }"
                  ></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(coupon)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getCouponStatus(coupon) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ coupon.expires_at ? formatDate(coupon.expires_at) : 'Never' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button
                    @click="viewCouponUsage(coupon)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Usage
                  </button>
                  <button
                    @click="editCoupon(coupon)"
                    class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    Edit
                  </button>
                  <button
                    @click="toggleCouponStatus(coupon)"
                    :class="coupon.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                  >
                    {{ coupon.is_active ? 'Disable' : 'Enable' }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-else class="p-12 text-center">
        <Icon name="heroicons:ticket" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No coupons found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Get started by creating your first promotional coupon.
        </p>
        <div class="mt-6">
          <button
            @click="showCreateModal = true"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 mr-2" />
            Create Coupon
          </button>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <CreateCouponModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @created="handleCouponCreated"
    />

    <EditCouponModal
      v-if="showEditModal"
      :coupon="selectedCoupon"
      @close="showEditModal = false"
      @updated="handleCouponUpdated"
    />

    <CouponUsageModal
      v-if="showUsageModal"
      :coupon="selectedCoupon"
      @close="showUsageModal = false"
    />
  </div>
</template>

<script setup lang="ts">
// Layout and meta
definePageMeta({
  layout: 'admin',
  middleware: 'admin-auth'
})

// Composables
const supabase = useSupabaseClient()

// State
const isLoading = ref(true)
const coupons = ref<any[]>([])
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showUsageModal = ref(false)
const selectedCoupon = ref(null)

// Filters
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')

// Stats
const stats = ref({
  totalCoupons: 0,
  activeCoupons: 0,
  totalUsage: 0,
  revenueGenerated: 0
})

// Computed
const filteredCoupons = computed(() => {
  let filtered = coupons.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(coupon => 
      coupon.code.toLowerCase().includes(query) ||
      coupon.description?.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(coupon => getCouponStatus(coupon) === statusFilter.value)
  }

  if (typeFilter.value) {
    filtered = filtered.filter(coupon => coupon.discount_type === typeFilter.value)
  }

  return filtered
})

// Methods
const fetchCoupons = async () => {
  try {
    isLoading.value = true

    const { data, error } = await supabase
      .from('coupons')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    coupons.value = data || []

    // Calculate stats
    calculateStats()
  } catch (error) {
    console.error('Error fetching coupons:', error)
  } finally {
    isLoading.value = false
  }
}

const calculateStats = () => {
  const total = coupons.value.length
  const active = coupons.value.filter(c => getCouponStatus(c) === 'active').length
  const usage = coupons.value.reduce((sum, c) => sum + c.used_count, 0)
  
  stats.value = {
    totalCoupons: total,
    activeCoupons: active,
    totalUsage: usage,
    revenueGenerated: 0 // TODO: Calculate from actual usage data
  }
}

const refreshCoupons = () => {
  fetchCoupons()
}

const getCouponStatus = (coupon: any) => {
  if (!coupon.is_active) return 'disabled'
  if (coupon.expires_at && new Date(coupon.expires_at) < new Date()) return 'expired'
  if (coupon.usage_limit && coupon.used_count >= coupon.usage_limit) return 'expired'
  return 'active'
}

const getStatusBadgeClass = (coupon: any) => {
  const status = getCouponStatus(coupon)
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800'
    case 'expired': return 'bg-red-100 text-red-800'
    case 'disabled': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getUsagePercentage = (coupon: any) => {
  if (!coupon.usage_limit) return 0
  return Math.min((coupon.used_count / coupon.usage_limit) * 100, 100)
}

const editCoupon = (coupon: any) => {
  selectedCoupon.value = coupon
  showEditModal.value = true
}

const viewCouponUsage = (coupon: any) => {
  selectedCoupon.value = coupon
  showUsageModal.value = true
}

const toggleCouponStatus = async (coupon: any) => {
  try {
    const { error } = await (supabase as any)
      .from('coupons')
      .update({ is_active: !coupon.is_active })
      .eq('id', coupon.id)

    if (error) throw error
    await fetchCoupons()
  } catch (error) {
    console.error('Error toggling coupon status:', error)
  }
}

const handleCouponCreated = () => {
  showCreateModal.value = false
  fetchCoupons()
}

const handleCouponUpdated = () => {
  showEditModal.value = false
  fetchCoupons()
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  fetchCoupons()
})
</script>
