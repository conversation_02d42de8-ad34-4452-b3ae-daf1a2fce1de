// Global middleware to block school service routes on main domain
// School services should only be accessible via school subdomains

export default defineNuxtRouteMiddleware(async (to) => {
  // DISABLED: This middleware is now handled by a-subdomain.global.ts
  // Disabling to improve performance and avoid conflicts
  return

  // Detect if we're on main domain
  const isMainDomain = process.client ? 
    (window.location.hostname === 'localhost' || !window.location.hostname.includes('.')) :
    !useState('currentSubdomain').value

  if (isMainDomain) {
    // School service accessed on main domain - return 404
    throw createError({
      statusCode: 404,
      statusMessage: 'Page Not Found',
      data: {
        message: 'This service is only available through your school portal.',
        suggestion: 'Please access this feature through your school subdomain (schoolcode.rphmate.com)'
      }
    })
  }

  // On school subdomain - allow access
  return
})
