// Global middleware to block school service routes on main domain
// School services should only be accessible via school subdomains

export default defineNuxtRouteMiddleware(async (to) => {
  // List of school service routes that should only be accessible on school subdomains
  const schoolServiceRoutes = [
    '/rph',
    '/rpt', 
    '/refleksi',
    '/jadual-mengajar',
    '/jadual-pencerapan',
    '/maklumat-guru',
    '/dskp',
    '/takwim-tahunan',
    '/kelas-subjek',
    '/kalendar-akademik',
    '/template-refleksi',
    '/tugas-guru',
    '/assignments',
    '/classes',
    '/items',
    '/notifications',
    '/preferences',
    '/profile',
    '/settings'
  ]

  // Check if current route is a school service route
  const isSchoolServiceRoute = schoolServiceRoutes.some(route => 
    to.path === route || to.path.startsWith(`${route}/`)
  )

  if (!isSchoolServiceRoute) {
    return // Not a school service route, continue normally
  }

  // Detect if we're on main domain
  const isMainDomain = process.client ? 
    (window.location.hostname === 'localhost' || !window.location.hostname.includes('.')) :
    !useState('currentSubdomain').value

  if (isMainDomain) {
    // School service accessed on main domain - return 404
    throw createError({
      statusCode: 404,
      statusMessage: 'Page Not Found',
      data: {
        message: 'This service is only available through your school portal.',
        suggestion: 'Please access this feature through your school subdomain (schoolcode.rphmate.com)'
      }
    })
  }

  // On school subdomain - allow access
  return
})
