<template>
    <Transition name="fade" :appear="!noAppear">
        <div v-if="visible" :class="alertClasses" role="alert">
            <div class="flex items-center">
                <span v-if="iconName" class="flex items-center mr-2">
                    <Icon :name="iconName" :class="iconClasses" />
                </span>
                <span class="flex-grow" :class="textClasses">
                    <slot>{{ message }}</slot>
                </span>
                <button v-if="dismissible" @click="dismiss"
                    :class="['ml-auto -mx-1.5 -my-1.5 p-1.5 rounded-md focus:ring-2', closeButtonClasses]"
                    :aria-label="closeButtonAriaLabel">
                    <Icon name="heroicons:x-mark-20-solid" class="h-5 w-5" />
                </button>
            </div>
        </div>
    </Transition>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import Icon from './Icon.vue'; // Assuming you have an Icon component

const props = withDefaults(defineProps<{
    type?: 'success' | 'info' | 'warning' | 'error';
    message?: string;
    dismissible?: boolean;
    duration?: number; // Duration in ms, 0 for indefinite
    showIcon?: boolean;
    customIcon?: string; // Allow passing a custom icon name
    modelValue?: boolean; // For v-model support
    closeButtonAriaLabel?: string;
    noAppear?: boolean; // Disable appear animation for static alerts
}>(), {
    type: 'info',
    message: '',
    dismissible: false,
    duration: 0,
    showIcon: true,
    customIcon: undefined,
    modelValue: true,
    closeButtonAriaLabel: 'Dismiss',
    noAppear: false
});

const emit = defineEmits(['dismiss', 'update:modelValue']);

const visible = ref(props.modelValue);
let timeoutId: ReturnType<typeof setTimeout> | null = null;

const startTimeout = () => {
    if (timeoutId) {
        clearTimeout(timeoutId);
    }
    if (props.duration && props.duration > 0) {
        timeoutId = setTimeout(() => {
            if (visible.value) {
                dismiss();
            }
        }, props.duration);
    }
};

watch(() => props.modelValue, (newValue) => {
    visible.value = newValue;
    if (newValue) {
        startTimeout(); // Start timeout whenever alert becomes visible
    } else {
        if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
        }
    }
});

// Start timeout on initial mount if visible
if (props.modelValue) {
    startTimeout();
}

const dismiss = () => {
    if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
    }
    visible.value = false;
    emit('dismiss');
    emit('update:modelValue', false);
};

const baseClasses = 'p-4 rounded-md';

const typeClasses = computed(() => {
    switch (props.type) {
        case 'success':
            return 'bg-alert-success text-alert-success-text';
        case 'info':
            return 'bg-alert-info text-alert-info-text';
        case 'warning':
            return 'bg-alert-warning text-alert-warning-text';
        case 'error':
            return 'bg-alert-error text-alert-error-text';
        default:
            return 'bg-alert-info text-alert-info-text'; // Default to info
    }
});

const iconClasses = computed(() => {
    const sizeClasses = 'h-6 w-6'; // Keep increased size
    switch (props.type) {
        case 'success':
            return `text-alert-success-text ${sizeClasses}`;
        case 'info':
            return `text-alert-info-text ${sizeClasses}`;
        case 'warning':
            return `text-alert-warning-text ${sizeClasses}`;
        case 'error':
            return `text-alert-error-text ${sizeClasses}`;
        default:
            return `text-alert-info-text ${sizeClasses}`;
    }
});

const closeButtonClasses = computed(() => {
    // Based on the text color for better contrast with the background
    switch (props.type) {
        case 'success':
            return 'text-alert-success-text hover:bg-alert-success/80 focus:ring-alert-success-text';
        case 'info':
            return 'text-alert-info-text hover:bg-alert-info/80 focus:ring-alert-info-text';
        case 'warning':
            return 'text-alert-warning-text hover:bg-alert-warning/80 focus:ring-alert-warning-text';
        case 'error':
            return 'text-alert-error-text hover:bg-alert-error/80 focus:ring-alert-error-text';
        default:
            return 'text-alert-info-text hover:bg-alert-info/80 focus:ring-alert-info-text';
    }
});

const textClasses = 'text-sm font-medium';

const alertClasses = computed(() => [
    baseClasses,
    typeClasses.value,
]);

const iconName = computed(() => {
    if (!props.showIcon) return undefined;
    if (props.customIcon) return props.customIcon;
    switch (props.type) {
        case 'success':
            return 'heroicons:check-circle-20-solid';
        case 'info':
            return 'heroicons:information-circle-20-solid';
        case 'warning':
            return 'heroicons:exclamation-triangle-20-solid';
        case 'error':
            return 'heroicons:x-circle-20-solid';
        default:
            return 'heroicons:information-circle-20-solid';
    }
});

</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
}
</style>
