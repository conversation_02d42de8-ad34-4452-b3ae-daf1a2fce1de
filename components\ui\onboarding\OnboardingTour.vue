<template>
  <div>
    <!-- Help button for returning users -->
    <button v-if="!isActive && shouldShowTooltips" @click="showHelpMenu"
      class="fixed bottom-4 right-4 z-40 p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-colors"
      type="button" title="Help & Onboarding">
      <Icon name="heroicons:question-mark-circle" class="h-6 w-6" />
    </button>

    <!-- Help menu -->
    <Teleport to="body">
      <transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0 scale-95"
        enter-to-class="opacity-100 scale-100" leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-95">
        <div v-if="showMenu"
          class="fixed bottom-20 right-4 z-50 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700"
          @click.stop>
          <div class="p-4">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Help & Tours
              </h3>
              <button @click="hideHelpMenu"
                class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" type="button">
                <Icon name="heroicons:x-mark" class="h-5 w-5" />
              </button>
            </div>

            <!-- Available tours -->
            <div class="space-y-2">
              <div v-for="tour in tours" :key="tour.id"
                class="p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ tour.name }}
                    </h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {{ tour.steps.length }} steps
                    </p>

                    <!-- Progress indicator -->
                    <div class="flex items-center mt-2">
                      <div v-if="getTourProgress(tour.id)?.completed"
                        class="flex items-center text-xs text-green-600 dark:text-green-400">
                        <Icon name="heroicons:check-circle" class="h-4 w-4 mr-1" />
                        Completed
                      </div>
                      <div v-else-if="getTourProgress(tour.id)?.skipped"
                        class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <Icon name="heroicons:x-circle" class="h-4 w-4 mr-1" />
                        Skipped
                      </div>
                      <div v-else class="flex items-center text-xs text-blue-600 dark:text-blue-400">
                        <Icon name="heroicons:play-circle" class="h-4 w-4 mr-1" />
                        Available
                      </div>
                    </div>
                  </div>

                  <button @click="startTourFromMenu(tour.id)"
                    class="ml-2 px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                    type="button">
                    {{ getTourProgress(tour.id)?.completed ? 'Replay' : 'Start' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Settings -->
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
              <label class="flex items-center">
                <input type="checkbox" :checked="shouldShowTooltips"
                  @change="toggleTooltips(($event.target as HTMLInputElement).checked)"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700" />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Show tooltips and tours
                </span>
              </label>
            </div>
          </div>
        </div>
      </transition>
    </Teleport>

    <!-- Active tour tooltip -->
    <OnboardingTooltip v-if="isActive && currentStep" :title="currentStep.title" :content="currentStep.content"
      :icon="currentStep.icon" :position="currentStep.position" :current-step="currentStepIndex + 1"
      :total-steps="currentTour?.steps.length || 1" :show-steps="true" :show-skip="true" :show-previous="!isFirstStep"
      :persistent="currentStep.persistent" :auto-show="true" @next="nextStep" @previous="previousStep" @skip="skipTour"
      @done="completeTour" @hide="endTour">
      <template #trigger="{ show, isVisible }">
        <!-- Highlight target element -->
        <div v-if="currentStep.target" :class="[
          'fixed pointer-events-none z-40 border-2 border-blue-500 rounded-lg',
          'animate-pulse shadow-lg shadow-blue-500/50'
        ]" :style="highlightStyle"></div>
      </template>
    </OnboardingTooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useOnboarding } from '~/composables/useOnboarding'
import OnboardingTooltip from './OnboardingTooltip.vue'
import Icon from '../base/Icon.vue'

const {
  loading,
  error,
  currentTour,
  currentStep,
  currentStepIndex,
  isActive,
  isFirstStep,
  isLastStep,
  shouldShowTooltips,
  tours,
  loadUserState,
  startTour,
  nextStep,
  previousStep,
  skipTour,
  completeTour,
  endTour,
  toggleTooltips,
  checkAutoStartTours,
  getTourProgress
} = useOnboarding()

// Local state
const showMenu = ref(false)
const highlightStyle = ref({})

// Methods
const showHelpMenu = () => {
  showMenu.value = true
}

const hideHelpMenu = () => {
  showMenu.value = false
}

const startTourFromMenu = (tourId: string) => {
  hideHelpMenu()
  startTour(tourId)
}

const updateHighlight = () => {
  if (!currentStep.value?.target) {
    highlightStyle.value = {}
    return
  }

  const targetElement = document.querySelector(currentStep.value.target) as HTMLElement
  if (!targetElement) {
    highlightStyle.value = {}
    return
  }

  const rect = targetElement.getBoundingClientRect()
  const padding = 4

  highlightStyle.value = {
    top: `${rect.top - padding}px`,
    left: `${rect.left - padding}px`,
    width: `${rect.width + padding * 2}px`,
    height: `${rect.height + padding * 2}px`
  }
}

// Handle click outside help menu
const handleClickOutside = (event: Event) => {
  if (showMenu.value) {
    const target = event.target as Node
    const helpButton = document.querySelector('[title="Help & Onboarding"]')
    const helpMenu = document.querySelector('.fixed.bottom-20.right-4')

    if (!helpMenu?.contains(target) && !helpButton?.contains(target)) {
      hideHelpMenu()
    }
  }
}

// Watch for current step changes to update highlight
watch(currentStep, () => {
  if (currentStep.value) {
    nextTick(updateHighlight)
  }
}, { immediate: true })

// Watch for active state changes
watch(isActive, (active) => {
  if (active) {
    nextTick(updateHighlight)
  } else {
    highlightStyle.value = {}
  }
})

// Lifecycle
onMounted(async () => {
  await loadUserState()

  // Check for auto-start tours after a short delay to ensure DOM is ready
  setTimeout(() => {
    checkAutoStartTours()
  }, 1000)

  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', updateHighlight)
  window.addEventListener('scroll', updateHighlight)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updateHighlight)
  window.removeEventListener('scroll', updateHighlight)
})
</script>
