import { ref, computed } from 'vue';
import type { Database } from '~/types/supabase';
import type {
  RptDocument,
  RptDocumentInput,
  RptStatus,
  ClassSubjectOption,
  MAX_RPT_FILE_SIZE_BYTES,
  SUPPORTED_RPT_FILE_TYPES
} from '~/types/rptDocuments';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import { getClassLevelName } from '~/utils/classLevelMapping';

type GenericSupabaseClient = ReturnType<typeof useSupabaseClient<Database>>;

const RPT_FILES_BUCKET = 'rpt-files';

// Storage and utility helpers
function sanitizeFilenameForStorage(originalName: string): string {
  return originalName.replace(/[^a-zA-Z0-9_.-]/g, '_');
}

function constructStoragePath(
  userId: string,
  classId: string,
  subjectId: string,
  sanitizedFileName: string
): string {
  return `${userId}/${classId}_${subjectId}/${Date.now()}_${sanitizedFileName}`;
}

async function uploadFileToStorage(
  supabaseClient: GenericSupabaseClient,
  bucket: string,
  storagePath: string,
  file: File
): Promise<{ path: string }> {
  const { data, error } = await supabaseClient.storage
    .from(bucket)
    .upload(storagePath, file, { cacheControl: '3600', upsert: false });
  
  if (error) {
    console.error(`[RPT StorageHelper] Supabase Storage upload error:`, error);
    throw error;
  }
  
  if (!data?.path) {
    console.error('[RPT StorageHelper] File upload failed, no path returned.');
    throw new Error('File upload failed, no path returned.');
  }
  
  return { path: data.path };
}

async function deleteFileFromStorage(
  supabaseClient: GenericSupabaseClient,
  bucket: string,
  storagePath: string
): Promise<void> {
  const { error } = await supabaseClient.storage
    .from(bucket)
    .remove([storagePath]);
  
  if (error) {
    console.error(`[RPT StorageHelper] Failed to delete file '${storagePath}':`, error.message);
    // Not throwing here, as DB operation might have succeeded
  }
}

export const useRptDocuments = () => {
  const client = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  // State
  const rptDocuments = ref<RptDocument[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Computed
  const rptDocumentsByClassSubject = computed(() => {
    const map = new Map<string, RptDocument>();
    rptDocuments.value.forEach(doc => {
      const key = `${doc.class_id}_${doc.subject_id}`;
      map.set(key, doc);
    });
    return map;
  });

  // Fetch all RPT documents for the current user
  const fetchRptDocuments = async (): Promise<void> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const { data, error: fetchError } = await client
        .from('rpt_documents')
        .select('*')
        .eq('user_id', user.value.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('[fetchRptDocuments] Database error:', fetchError);
        throw fetchError;
      }

      rptDocuments.value = data || [];
    } catch (e) {
      console.error('[fetchRptDocuments] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to fetch RPT documents';
    } finally {
      loading.value = false;
    }
  };

  // Get RPT status for all user's class-subjects, grouped by class level
  const getRptStatusForClassSubjects = (userClassSubjects: UserClassSubjectEntry[]): RptStatus[] => {
    // Group by class_id + subject_id combination
    const classSubjectGroups = new Map<string, {
      class_id: string;
      subject_id: string;
      subject_abbreviation?: string;
      classNames: string[];
    }>();

    // Group user class subjects by class_id + subject_id
    userClassSubjects
      .filter(cs => cs.subject_id) // Filter out entries without subject_id
      .forEach(cs => {
        const key = `${cs.class_id}_${cs.subject_id}`;

        if (!classSubjectGroups.has(key)) {
          classSubjectGroups.set(key, {
            class_id: cs.class_id,
            subject_id: cs.subject_id!,
            subject_abbreviation: cs.subject_abbreviation,
            classNames: []
          });
        }

        const group = classSubjectGroups.get(key)!;
        group.classNames.push(cs.className);
      });

    // Convert groups to RptStatus array
    return Array.from(classSubjectGroups.entries()).map(([key, group]) => {
      const rptDoc = rptDocumentsByClassSubject.value.get(key);
      const classLevelName = getClassLevelName(group.class_id);

      return {
        class_id: group.class_id,
        subject_id: group.subject_id,
        class_name: classLevelName, // Use class level name instead of specific class name
        subject_name: '', // Will be resolved by useSubjects
        subject_abbreviation: group.subject_abbreviation,
        has_rpt: !!rptDoc,
        rpt_document: rptDoc,
      };
    });
  };

  // Create class-subject options for dropdowns, grouped by class level
  const createClassSubjectOptions = (userClassSubjects: UserClassSubjectEntry[]): ClassSubjectOption[] => {
    // Group by class_id + subject_id combination
    const classSubjectGroups = new Map<string, {
      class_id: string;
      subject_id: string;
      subject_abbreviation?: string;
    }>();

    // Group user class subjects by class_id + subject_id
    userClassSubjects
      .filter(cs => cs.subject_id) // Filter out entries without subject_id
      .forEach(cs => {
        const key = `${cs.class_id}_${cs.subject_id}`;

        if (!classSubjectGroups.has(key)) {
          classSubjectGroups.set(key, {
            class_id: cs.class_id,
            subject_id: cs.subject_id!,
            subject_abbreviation: cs.subject_abbreviation,
          });
        }
      });

    // Convert groups to ClassSubjectOption array
    return Array.from(classSubjectGroups.entries()).map(([key, group]) => {
      const classLevelName = getClassLevelName(group.class_id);

      return {
        value: key,
        label: `${classLevelName} - ${group.subject_abbreviation || 'Subject'}`,
        class_id: group.class_id,
        subject_id: group.subject_id,
        class_name: classLevelName, // Use class level name
        subject_name: '', // Will be resolved by useSubjects
        subject_abbreviation: group.subject_abbreviation,
      };
    });
  };

  // Upload new RPT document
  const uploadRptDocument = async (
    rptInput: RptDocumentInput,
    file: File
  ): Promise<RptDocument | null> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return null;
    }

    loading.value = true;
    error.value = null;
    let uploadedStoragePath: string | null = null;

    try {
      const userId = user.value.id;
      const originalFileName = file.name;
      const sanitizedFileName = sanitizeFilenameForStorage(originalFileName);
      const storagePath = constructStoragePath(
        userId,
        rptInput.class_id,
        rptInput.subject_id,
        sanitizedFileName
      );

      // Upload file to storage
      const uploadResult = await uploadFileToStorage(
        client,
        RPT_FILES_BUCKET,
        storagePath,
        file
      );
      uploadedStoragePath = uploadResult.path;

      // Insert record into database
      const newRptData: Database['public']['Tables']['rpt_documents']['Insert'] = {
        user_id: userId,
        class_id: rptInput.class_id,
        subject_id: rptInput.subject_id,
        class_name: rptInput.class_name,
        subject_name: rptInput.subject_name,
        file_name: originalFileName,
        storage_file_path: uploadedStoragePath,
        file_mime_type: file.type,
        file_size_bytes: file.size,
      };

      const { data: dbData, error: dbError } = await client
        .from('rpt_documents')
        .insert(newRptData)
        .select()
        .single();

      if (dbError) {
        console.error('[uploadRptDocument] Database insert error:', dbError);
        // Clean up uploaded file
        if (uploadedStoragePath) {
          await deleteFileFromStorage(client, RPT_FILES_BUCKET, uploadedStoragePath);
        }
        throw dbError;
      }

      // Add to local state
      const newRptDocument = dbData as RptDocument;
      rptDocuments.value.unshift(newRptDocument);

      return newRptDocument;
    } catch (e) {
      console.error('[uploadRptDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to upload RPT document';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Update existing RPT document (replace file)
  const updateRptDocument = async (
    rptId: string,
    newFile: File,
    updatedInput?: Partial<RptDocumentInput>
  ): Promise<RptDocument | null> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return null;
    }

    loading.value = true;
    error.value = null;
    let newUploadedStoragePath: string | null = null;

    try {
      const userId = user.value.id;
      const existingDoc = rptDocuments.value.find(doc => doc.id === rptId);
      
      if (!existingDoc) {
        throw new Error('RPT document not found');
      }

      // Upload new file
      const originalFileName = newFile.name;
      const sanitizedFileName = sanitizeFilenameForStorage(originalFileName);
      const storagePath = constructStoragePath(
        userId,
        existingDoc.class_id,
        existingDoc.subject_id,
        sanitizedFileName
      );

      const uploadResult = await uploadFileToStorage(
        client,
        RPT_FILES_BUCKET,
        storagePath,
        newFile
      );
      newUploadedStoragePath = uploadResult.path;

      // Update database record
      const updateData: Database['public']['Tables']['rpt_documents']['Update'] = {
        file_name: originalFileName,
        storage_file_path: newUploadedStoragePath,
        file_mime_type: newFile.type,
        file_size_bytes: newFile.size,
        ...updatedInput,
      };

      const { data: dbData, error: dbError } = await client
        .from('rpt_documents')
        .update(updateData)
        .eq('id', rptId)
        .eq('user_id', userId)
        .select()
        .single();

      if (dbError) {
        console.error('[updateRptDocument] Database update error:', dbError);
        // Clean up new uploaded file
        if (newUploadedStoragePath) {
          await deleteFileFromStorage(client, RPT_FILES_BUCKET, newUploadedStoragePath);
        }
        throw dbError;
      }

      // Delete old file from storage
      if (existingDoc.storage_file_path) {
        await deleteFileFromStorage(client, RPT_FILES_BUCKET, existingDoc.storage_file_path);
      }

      // Update local state
      const updatedDoc = dbData as RptDocument;
      const index = rptDocuments.value.findIndex(doc => doc.id === rptId);
      if (index !== -1) {
        rptDocuments.value[index] = updatedDoc;
      }

      return updatedDoc;
    } catch (e) {
      console.error('[updateRptDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to update RPT document';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Delete RPT document
  const deleteRptDocument = async (rptId: string): Promise<boolean> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return false;
    }

    loading.value = true;
    error.value = null;

    try {
      const userId = user.value.id;
      const existingDoc = rptDocuments.value.find(doc => doc.id === rptId);

      if (!existingDoc) {
        throw new Error('RPT document not found');
      }

      // Delete from database
      const { error: dbError } = await client
        .from('rpt_documents')
        .delete()
        .eq('id', rptId)
        .eq('user_id', userId);

      if (dbError) {
        console.error('[deleteRptDocument] Database delete error:', dbError);
        throw dbError;
      }

      // Delete file from storage
      if (existingDoc.storage_file_path) {
        await deleteFileFromStorage(client, RPT_FILES_BUCKET, existingDoc.storage_file_path);
      }

      // Remove from local state
      const index = rptDocuments.value.findIndex(doc => doc.id === rptId);
      if (index !== -1) {
        rptDocuments.value.splice(index, 1);
      }

      return true;
    } catch (e) {
      console.error('[deleteRptDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to delete RPT document';
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Get temporary public URL for file preview
  const getTemporaryPublicUrl = async (
    filePath: string,
    expiresInSeconds: number = 3600
  ): Promise<string | null> => {
    if (!filePath) {
      error.value = 'File path is required';
      return null;
    }

    try {
      const { data, error: signedUrlError } = await client.storage
        .from(RPT_FILES_BUCKET)
        .createSignedUrl(filePath, expiresInSeconds);

      if (signedUrlError) {
        console.error('[getTemporaryPublicUrl] Supabase error creating signed URL:', signedUrlError);
        throw signedUrlError;
      }

      return data?.signedUrl || null;
    } catch (e) {
      console.error('[getTemporaryPublicUrl] Error getting temporary public URL:', e);
      error.value = e instanceof Error ? e.message : 'Failed to get temporary public URL';
      return null;
    }
  };

  // Validate file before upload
  const validateFile = (file: File): { isValid: boolean; errorMessage?: string } => {
    // Check file size
    if (file.size > 10 * 1024 * 1024) { // 10MB
      return {
        isValid: false,
        errorMessage: `Fail terlalu besar. Saiz maksimum ialah 10MB.`
      };
    }

    // Check file type
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/msword',
      'application/vnd.ms-excel',
      'application/vnd.ms-powerpoint',
    ];

    if (!supportedTypes.includes(file.type)) {
      return {
        isValid: false,
        errorMessage: `Jenis fail tidak disokong. Sila pilih fail PDF, Word, Excel, atau PowerPoint.`
      };
    }

    return { isValid: true };
  };

  return {
    // State
    rptDocuments: readonly(rptDocuments),
    loading: readonly(loading),
    error: readonly(error),

    // Computed
    rptDocumentsByClassSubject,

    // Methods
    fetchRptDocuments,
    getRptStatusForClassSubjects,
    createClassSubjectOptions,
    uploadRptDocument,
    updateRptDocument,
    deleteRptDocument,
    getTemporaryPublicUrl,
    validateFile,
  };
};
