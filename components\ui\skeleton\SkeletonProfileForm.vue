<template>
  <div class="space-y-8">
    <!-- <PERSON> Header -->
    <SkeletonPageHeader :title-width="'12rem'" :subtitle-width="'25rem'" :show-actions="false" />

    <!-- Form Cards -->
    <div class="space-y-8">
      <!-- Card 1: Maklumat Peribadi -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-2">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <SkeletonBox height="1.5rem" width="12rem" />
          </div>
        </div>

        <!-- Card Content -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="md:col-span-2 space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
          </div>
        </div>
      </div>

      <!-- Card 2: Opsyen -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
              <SkeletonBox height="1.5rem" width="6rem" />
            </div>
            <SkeletonBox height="2rem" width="8rem" class="rounded-md" />
          </div>
        </div>

        <!-- Card Content -->
        <div class="p-6">
          <div class="text-center py-8">
            <SkeletonBox height="3rem" width="3rem" class="mx-auto mb-3 rounded" />
            <SkeletonBox height="1rem" width="20rem" class="mx-auto" variant="light" />
          </div>
        </div>
      </div>

      <!-- Card 3: Kelulusan Akademik -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
              <SkeletonBox height="1.5rem" width="15rem" />
            </div>
            <SkeletonBox height="2rem" width="10rem" class="rounded-md" />
          </div>
        </div>

        <!-- Card Content -->
        <div class="p-6">
          <div class="text-center py-8">
            <SkeletonBox height="3rem" width="3rem" class="mx-auto mb-3 rounded" />
            <SkeletonBox height="1rem" width="25rem" class="mx-auto" variant="light" />
          </div>
        </div>
      </div>

      <!-- Card 4: Nombor Rujukan -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-2">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <SkeletonBox height="1.5rem" width="10rem" />
          </div>
        </div>

        <!-- Card Content -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="md:col-span-2 space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
          </div>
        </div>
      </div>

      <!-- Card 5: Maklumat Pelantikan -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-2">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <SkeletonBox height="1.5rem" width="12rem" />
          </div>
        </div>

        <!-- Card Content -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
            <div class="space-y-2">
              <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
            </div>
          </div>
        </div>
      </div>

      <!-- Submit Buttons -->
      <div class="flex justify-end space-x-4">
        <SkeletonBox height="2.5rem" width="5rem" class="rounded-md" />
        <SkeletonBox height="2.5rem" width="8rem" class="rounded-md" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
</script>
