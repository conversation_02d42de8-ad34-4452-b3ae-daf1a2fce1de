-- Migration: Update lesson_plan_detailed_reflections table with new fields
-- Created: 2024-12-28

BEGIN;

-- =====================================================
-- ADD NEW COLUMNS TO lesson_plan_detailed_reflections
-- =====================================================

-- Add new fields for enhanced detailed reflections
ALTER TABLE lesson_plan_detailed_reflections
ADD COLUMN IF NOT EXISTS jumlah_murid_mencapai_objektif INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS tindakan_susulan JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS tidak_terlaksana UUID DEFAULT NULL;

-- =====================================================
-- ADD CONSTRAINTS AND VALIDATIONS
-- =====================================================

-- Add check constraint for student count (must be non-negative)
ALTER TABLE lesson_plan_detailed_reflections 
ADD CONSTRAINT check_jumlah_murid_mencapai_objektif_non_negative 
CHECK (jumlah_murid_mencapai_objektif >= 0);

-- Add check constraint for tindakan_susulan (must be array)
ALTER TABLE lesson_plan_detailed_reflections 
ADD CONSTRAINT check_tindakan_susulan_is_array 
CHECK (jsonb_typeof(tindakan_susulan) = 'array');

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for querying by student achievement count
CREATE INDEX IF NOT EXISTS idx_lesson_plan_detailed_reflections_student_count 
ON lesson_plan_detailed_reflections (jumlah_murid_mencapai_objektif);

-- GIN index for tindakan_susulan JSONB queries
CREATE INDEX IF NOT EXISTS idx_lesson_plan_detailed_reflections_tindakan_susulan 
ON lesson_plan_detailed_reflections USING GIN (tindakan_susulan);

-- Index for tidak_terlaksana filtering
CREATE INDEX IF NOT EXISTS idx_lesson_plan_detailed_reflections_tidak_terlaksana 
ON lesson_plan_detailed_reflections (tidak_terlaksana) 
WHERE tidak_terlaksana IS NOT NULL;

-- =====================================================
-- ADD FOREIGN KEY CONSTRAINTS FOR REFERENCE INTEGRITY
-- =====================================================

-- Add foreign key constraint for tidak_terlaksana (references tidak_terlaksana table)
ALTER TABLE lesson_plan_detailed_reflections 
ADD CONSTRAINT fk_lesson_plan_detailed_reflections_tidak_terlaksana 
FOREIGN KEY (tidak_terlaksana) REFERENCES tidak_terlaksana(id) 
ON DELETE SET NULL;

-- =====================================================
-- CREATE VALIDATION FUNCTION FOR TINDAKAN_SUSULAN
-- =====================================================

-- Function to validate tindakan_susulan array contains valid UUIDs
CREATE OR REPLACE FUNCTION validate_tindakan_susulan_array(tindakan_array JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if it's an array
    IF jsonb_typeof(tindakan_array) != 'array' THEN
        RETURN FALSE;
    END IF;
    
    -- Check if all elements are valid UUID strings that exist in tindakan_susulan table
    RETURN (
        SELECT bool_and(
            jsonb_typeof(elem) = 'string' AND
            EXISTS (
                SELECT 1 FROM tindakan_susulan 
                WHERE id = (elem #>> '{}')::uuid
            )
        )
        FROM jsonb_array_elements(tindakan_array) AS elem
    );
END;
$$ LANGUAGE plpgsql;

-- Add check constraint using the validation function
ALTER TABLE lesson_plan_detailed_reflections 
ADD CONSTRAINT check_tindakan_susulan_valid_uuids 
CHECK (validate_tindakan_susulan_array(tindakan_susulan));

-- =====================================================
-- UPDATE EXISTING RECORDS WITH DEFAULT VALUES
-- =====================================================

-- Update existing records to have default values for new fields
UPDATE lesson_plan_detailed_reflections 
SET 
    jumlah_murid_mencapai_objektif = 0,
    tindakan_susulan = '[]'::jsonb,
    tidak_terlaksana = NULL
WHERE 
    jumlah_murid_mencapai_objektif IS NULL OR
    tindakan_susulan IS NULL;

-- =====================================================
-- ADD COLUMN COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN lesson_plan_detailed_reflections.jumlah_murid_mencapai_objektif IS 'Number of students who achieved the learning objectives';
COMMENT ON COLUMN lesson_plan_detailed_reflections.tindakan_susulan IS 'Array of follow-up action IDs (references tindakan_susulan table)';
COMMENT ON COLUMN lesson_plan_detailed_reflections.tidak_terlaksana IS 'UUID reference to tidak_terlaksana table for reason why lesson was not implemented';

-- =====================================================
-- CREATE HELPER FUNCTIONS FOR QUERYING
-- =====================================================

-- Function to get tindakan_susulan details for a reflection
CREATE OR REPLACE FUNCTION get_tindakan_susulan_details(reflection_id UUID)
RETURNS TABLE(
    id UUID,
    option_text TEXT,
    is_default BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT ts.id, ts.option_text, ts.is_default
    FROM lesson_plan_detailed_reflections lpdr
    CROSS JOIN jsonb_array_elements_text(lpdr.tindakan_susulan) AS tindakan_id
    JOIN tindakan_susulan ts ON ts.id = tindakan_id::uuid
    WHERE lpdr.id = reflection_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get tidak_terlaksana details for a reflection
CREATE OR REPLACE FUNCTION get_tidak_terlaksana_details(reflection_id UUID)
RETURNS TABLE(
    id UUID,
    option_text TEXT,
    is_default BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT tt.id, tt.option_text, tt.is_default
    FROM lesson_plan_detailed_reflections lpdr
    JOIN tidak_terlaksana tt ON tt.id = lpdr.tidak_terlaksana::uuid
    WHERE lpdr.id = reflection_id;
END;
$$ LANGUAGE plpgsql;

COMMIT;

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

/*
-- Update a reflection with new fields
UPDATE lesson_plan_detailed_reflections 
SET 
    jumlah_murid_mencapai_objektif = 25,
    tindakan_susulan = '["uuid1", "uuid2"]'::jsonb,
    tidak_terlaksana = 'uuid3'
WHERE id = 'reflection-uuid-here';

-- Query reflections with tindakan_susulan details
SELECT 
    lpdr.*,
    (SELECT array_agg(ts.option_text) 
     FROM jsonb_array_elements_text(lpdr.tindakan_susulan) AS tindakan_id
     JOIN tindakan_susulan ts ON ts.id = tindakan_id::uuid) AS tindakan_susulan_texts
FROM lesson_plan_detailed_reflections lpdr
WHERE lesson_plan_id = 'lesson-plan-uuid-here';

-- Query reflections with tidak_terlaksana details
SELECT 
    lpdr.*,
    tt.option_text AS tidak_terlaksana_text
FROM lesson_plan_detailed_reflections lpdr
LEFT JOIN tidak_terlaksana tt ON tt.id = lpdr.tidak_terlaksana::uuid
WHERE lesson_plan_id = 'lesson-plan-uuid-here';

-- Get average student achievement for a lesson plan
SELECT 
    lesson_plan_id,
    AVG(jumlah_murid_mencapai_objektif) AS avg_students_achieved,
    COUNT(*) AS total_reflections
FROM lesson_plan_detailed_reflections 
WHERE lesson_plan_id = 'lesson-plan-uuid-here'
GROUP BY lesson_plan_id;
*/
