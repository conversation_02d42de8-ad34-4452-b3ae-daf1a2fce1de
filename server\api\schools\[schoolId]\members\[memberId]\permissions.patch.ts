// Update member permissions API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'
import type { Permission } from '~/composables/usePermissions'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')
    const schoolId = getRouterParam(event, 'schoolId')
    const memberId = getRouterParam(event, 'memberId')

    // Get request body
    const body = await readBody(event)
    const { permissions } = body

    // Validate input
    if (!schoolId || !memberId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School ID and member ID are required'
      })
    }

    if (!permissions || typeof permissions !== 'object') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Permissions object is required'
      })
    }

    // Initialize Supabase client with service role for admin operations
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Check if the requesting user has admin access to this school
    const { data: requesterMembership, error: requesterError } = await supabase
      .from('school_memberships')
      .select('role')
      .eq('user_id', user.id)
      .eq('school_id', schoolId)
      .eq('status', 'active')
      .single()

    // Also check if user is the school admin
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .select('admin_user_id')
      .eq('id', schoolId)
      .single()

    if (schoolError || !school) {
      throw createError({
        statusCode: 404,
        statusMessage: 'School not found'
      })
    }

    const isSchoolAdmin = school.admin_user_id === user.id
    const hasPermission = isSchoolAdmin || 
      (requesterMembership && requesterMembership.role === 'admin')

    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Only school admins can update permissions'
      })
    }

    // Get the current membership to validate the update
    const { data: currentMembership, error: currentError } = await supabase
      .from('school_memberships')
      .select('*')
      .eq('id', memberId)
      .eq('school_id', schoolId)
      .single()

    if (currentError || !currentMembership) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Member not found'
      })
    }

    // Prevent users from modifying their own permissions (unless they're the school owner)
    if (currentMembership.user_id === user.id && !isSchoolAdmin) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Cannot modify your own permissions'
      })
    }

    // Validate permission keys (optional - for security)
    const validPermissions = [
      'manage_school_settings', 'view_school_analytics', 'manage_billing',
      'invite_teachers', 'manage_teachers', 'remove_teachers', 'assign_roles',
      'manage_subjects', 'manage_templates', 'manage_documents',
      'create_classes', 'manage_classes', 'assign_teachers_to_classes',
      'create_lesson_plans', 'edit_own_lesson_plans', 'edit_all_lesson_plans', 
      'delete_lesson_plans', 'approve_lesson_plans',
      'create_schedules', 'edit_own_schedules', 'edit_all_schedules', 'manage_timetables',
      'view_own_reports', 'view_all_reports', 'export_data',
      'manage_coupons', 'view_system_logs'
    ]

    // Filter out invalid permissions
    const filteredPermissions: Record<string, boolean> = {}
    Object.entries(permissions).forEach(([permission, granted]) => {
      if (validPermissions.includes(permission) && typeof granted === 'boolean') {
        filteredPermissions[permission] = granted
      }
    })

    // Update the membership permissions
    const { data: updatedMembership, error: updateError } = await supabase
      .from('school_memberships')
      .update({
        permissions: filteredPermissions,
        updated_at: new Date().toISOString()
      })
      .eq('id', memberId)
      .eq('school_id', schoolId)
      .select('*')
      .single()

    if (updateError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to update permissions: ${updateError.message}`
      })
    }

    return {
      success: true,
      membership: {
        id: updatedMembership.id,
        role: updatedMembership.role,
        status: updatedMembership.status,
        permissions: updatedMembership.permissions,
        updated_at: updatedMembership.updated_at
      },
      message: 'Permissions updated successfully'
    }

  } catch (error: any) {
    console.error('Update permissions error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during permission update'
    })
  }
})
