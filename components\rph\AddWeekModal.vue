<template>
  <UiCompositeModal :is-open="isOpen" :title="title" @close="emits('close')">
    <div class="p-4 space-y-4">
      <!-- Success feedback -->
      <div v-if="lastAddedWeek"
        class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
        <div class="flex items-center space-x-2">
          <UiBaseIcon name="heroicons:check-circle-solid" class="w-5 h-5 text-green-600" />
          <p class="text-sm text-green-800 dark:text-green-200">
            <strong>{{ lastAddedWeek }}</strong> berjaya ditambah!
          </p>
        </div>
      </div>

      <div>
        <label for="weekNumberInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Nombor Minggu:
        </label>
        <input id="weekNumberInput" type="number" :min="1" v-model.number="form.week_number"
          class="mt-1 block w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-gray-900 dark:text-white"
          placeholder="Masukkan nombor minggu" :disabled="isAdding" />
        <p v-if="generatedWeekName" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Nama Minggu Akan Dijana: <strong>{{ generatedWeekName }}</strong>
        </p>
      </div>
    </div> <template #footer>
      <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
        <UiBaseButton @click="emits('close')" variant="outline" :disabled="isAdding">Tutup</UiBaseButton>
        <UiBaseButton @click="handleAddWeek" variant="primary" :disabled="!isValidForm || isAdding">
          <span v-if="isAdding">Menambah...</span>
          <span v-else>Tambah Minggu</span>
        </UiBaseButton>
      </div>
    </template>
  </UiCompositeModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue';
import type { RphWeekUserInput } from '~/types/rph';
import UiBaseIcon from '~/components/ui/base/Icon.vue';

interface Props {
  isOpen: boolean;
  title?: string;
  initialWeekNumber: number;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Tambah Minggu Baharu',
});

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'week-added', weekData: Pick<RphWeekUserInput, 'week_number' | 'name'>): void
}>();

const form = reactive({
  week_number: props.initialWeekNumber,
});

const isAdding = ref(false);
const lastAddedWeek = ref<string | null>(null);

const generatedWeekName = computed(() => {
  if (form.week_number > 0) {
    return `Minggu ${form.week_number}`;
  }
  return '';
});

const isValidForm = computed(() => {
  return form.week_number > 0;
});

watch(() => props.initialWeekNumber, (newVal) => {
  form.week_number = newVal;
});

watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    // Reset form when modal opens, based on current props
    form.week_number = props.initialWeekNumber;
    // Reset states when modal opens
    isAdding.value = false;
    lastAddedWeek.value = null;
  }
});

const handleAddWeek = async () => {
  if (isValidForm.value && generatedWeekName.value) {
    isAdding.value = true;

    try {
      const weekDataToEmit: Pick<RphWeekUserInput, 'week_number' | 'name'> = {
        week_number: form.week_number,
        name: generatedWeekName.value,
        // Other fields (theme, topic, dates) are not collected and thus not emitted
      };

      // Store the name of the week being added for feedback
      lastAddedWeek.value = generatedWeekName.value;

      emits('week-added', weekDataToEmit);

      // Increment week number for next potential addition
      form.week_number = form.week_number + 1;

      // Clear the success message after a few seconds
      setTimeout(() => {
        lastAddedWeek.value = null;
      }, 3000);

    } catch (error) {
      console.error('Error adding week:', error);
    } finally {
      isAdding.value = false;
    }
  }
};
</script>
