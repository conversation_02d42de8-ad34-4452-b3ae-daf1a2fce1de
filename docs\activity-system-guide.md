# Activity System for Timetable

## Overview

The timetable now supports both **academic classes** and **non-academic activities** like assemblies, co-curricular activities, meetings, and breaks. This guide explains how to use the new activity system.

## Activity Types

### Available Activity Types

| Type | Label | Icon | Description | Example Use Cases |
|------|--------|------|-------------|-------------------|
| `CLASS` | Kelas | mdi:school | Regular academic classes | Matematik, Sains, Bahasa Melayu |
| `ASSEMBLY` | Perhimpunan | mdi:account-group | School assemblies and official events | Perhimpunan Pagi, <PERSON><PERSON> |
| `COCURRICULAR` | Kokurikulum | mdi:run | Co-curricular activities and sports | Badminton, Kelab Sains, Persatuan |
| `MEETING` | Mesyuarat | mdi:account-supervisor | Staff meetings and administration | Mesyuarat Guru, Taklimat |
| `BREAK` | Rehat | mdi:coffee | Break times and meals | <PERSON><PERSON>, <PERSON><PERSON><PERSON> |
| `OTHER` | Lain-lain | mdi:calendar-star | Other uncategorized activities | Gotong-royong, Lawatan |

## How to Add Activities

### 1. Adding a Regular Class (Kelas)

1. Click on an empty time slot in the timetable
2. Select **"Kelas"** as the activity type
3. Choose from your configured class-subject combinations
4. Optionally add room and notes
5. Click **"Simpan"**

### 2. Adding Non-Academic Activities

1. Click on an empty time slot in the timetable
2. Select the appropriate activity type (Perhimpunan, Kokurikulum, etc.)
3. Fill in the required information:
   - **Tajuk Aktiviti**: The name of the activity (e.g., "Perhimpunan Pagi")
   - **Penerangan Aktiviti**: Optional detailed description
   - **Bilik/Lokasi**: Where the activity takes place (e.g., "Dewan", "Padang")
   - **Nota Tambahan**: Any additional notes
4. Click **"Simpan"**

## Visual Indicators

Each activity type has its own color scheme and icon for easy identification:

- **Classes**: Blue background with school icon
- **Assemblies**: Purple background with group icon
- **Co-curricular**: Green background with running icon
- **Meetings**: Orange background with supervisor icon
- **Breaks**: Yellow background with coffee icon
- **Other**: Gray background with star icon

## Editing Activities

To edit any activity:
1. Click on the activity card in the timetable
2. The modal will open with current information pre-filled
3. Make your changes
4. Click **"Kemaskini"**

## Deleting Activities

To delete any activity:
1. Click the red "×" button on the activity card
2. Confirm the deletion in the popup modal

## Legend

The timetable footer shows legends for:
- **Subjek**: All subjects used in class activities
- **Aktiviti**: All non-class activity types currently in the timetable

## Database Changes

The system extends the existing `timetable_entries` table with:
- `activity_type`: The type of activity (CLASS, ASSEMBLY, etc.)
- `activity_title`: Title for non-class activities
- `activity_description`: Optional description for activities
- `class_id`, `subject_id`: Made nullable for non-class activities

## API Usage

### Creating a Class Entry
```typescript
const classEntry = {
  activity_type: 'CLASS',
  class_id: 'class-uuid',
  subject_id: 'subject-uuid',
  class_name: 'Tingkatan 2A',
  subject_name: 'Matematik',
  // ... other fields
}
```

### Creating an Activity Entry
```typescript
const activityEntry = {
  activity_type: 'ASSEMBLY',
  activity_title: 'Perhimpunan Pagi',
  activity_description: 'Perhimpunan mingguan sekolah',
  class_id: null,
  subject_id: null,
  // ... other fields
}
```

## Benefits

1. **Unified Interface**: One system for both classes and activities
2. **Visual Clarity**: Different colors and icons for easy identification
3. **Flexible**: Supports any type of school activity
4. **Backward Compatible**: Existing class entries continue to work
5. **Comprehensive**: Covers all common school schedule needs

## Tips

1. Use **"BREAK"** for recess and meal times
2. Use **"ASSEMBLY"** for formal school gatherings
3. Use **"COCURRICULAR"** for sports and club activities
4. Use **"MEETING"** for staff administrative time
5. Use **"OTHER"** for special events like field trips or cleaning days
6. Always fill in the location/room for better organization
7. Use notes for additional context or instructions
