<template>
  <div class="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-4 sm:p-6 border border-primary/20">
    <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
      <div class="flex items-center space-x-3 sm:space-x-4">
        <SkeletonBox height="2.5rem" width="2.5rem" class="rounded-xl" />
        <div class="space-y-2">
          <SkeletonBox height="2rem" :width="titleWidth" />
          <SkeletonBox height="1rem" :width="subtitleWidth" variant="light" />
        </div>
      </div>
      <div v-if="showActions" class="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
        <SkeletonBox v-for="action in actionCount" :key="`action-${action}`" height="2.5rem"
          :width="getActionWidth(action)" class="rounded-md" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  titleWidth?: string
  subtitleWidth?: string
  showActions?: boolean
  actionCount?: number
}

withDefaults(defineProps<Props>(), {
  titleWidth: '20rem',
  subtitleWidth: '30rem',
  showActions: true,
  actionCount: 2
})

const getActionWidth = (index: number): string => {
  const widths = ['8rem', '10rem', '6rem', '12rem']
  return widths[(index - 1) % widths.length]
}
</script>
