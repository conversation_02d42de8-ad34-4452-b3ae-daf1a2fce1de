# Lesson Plan Rating Calculation System

## Overview

The lesson plan rating calculation system automatically generates overall ratings for lesson plans based on detailed reflections for each teaching period (waktu). This document explains the calculation logic, formulas, and examples.

## Core Concepts

### 1. Teaching Periods (Waktu)
- Each lesson plan covers multiple teaching periods
- A teaching period is defined by a unique combination of:
  - Class-Subject (e.g., "1A - Bahasa Inggeris")
  - Day (e.g., "Isnin", "Selasa")
- Multiple physical periods of the same class-subject on the same day count as **ONE** teaching period

### 2. Rating Sources
Each teaching period can have a rating from two sources:
- **Detailed Reflection**: User-created reflection with specific ratings (1-5 stars)
- **Default Rating**: System default of 5 stars when no detailed reflection exists

### 3. Overall Rating Calculation
The overall rating for a lesson plan is calculated as:
```
Overall Rating = (Sum of all period ratings) / (Total number of periods)
```

## Calculation Formula

### Basic Formula
```
Overall Rating = (Σ(Period Ratings)) / (Total Periods)
```

Where:
- **Period Ratings** = Individual rating for each teaching period
- **Total Periods** = Number of unique class-subject-day combinations

### Detailed Formula
```
Overall Rating = ((Ada Refleksi × Their Ratings) + (<PERSON><PERSON> × 5)) / (Total Periods)
```

Where:
- **Ada Refleksi** = Number of periods with detailed reflections
- **Their Ratings** = Sum of ratings from detailed reflections
- **Guna Default** = Number of periods using default rating (5 stars)
- **Total Periods** = Ada Refleksi + Guna Default

## Examples

### Example 1: No Detailed Reflections
**Scenario:**
- Lesson plan covers 4 teaching periods
- No detailed reflections created yet
- All periods use default rating

**Calculation:**
```
Jumlah Waktu: 4
Ada Refleksi: 0
Guna Default: 4

Overall Rating = (0 × 0) + (4 × 5) / 4 = 20 / 4 = 5.0 stars
```

### Example 2: Mixed Reflections
**Scenario:**
- Lesson plan covers 4 teaching periods
- 1 period has detailed reflection with 4 stars
- 3 periods use default rating (5 stars)

**Calculation:**
```
Jumlah Waktu: 4
Ada Refleksi: 1 (with rating 4)
Guna Default: 3

Overall Rating = (1 × 4) + (3 × 5) / 4 = (4 + 15) / 4 = 19 / 4 = 4.75
Rounded to: 4.8 stars (precision: 0.1)
```

### Example 3: All Periods with Reflections
**Scenario:**
- Lesson plan covers 3 teaching periods
- Period 1: 5 stars
- Period 2: 3 stars  
- Period 3: 4 stars

**Calculation:**
```
Jumlah Waktu: 3
Ada Refleksi: 3
Guna Default: 0

Overall Rating = (5 + 3 + 4) + (0 × 5) / 3 = 12 / 3 = 4.0 stars
```

## Detailed Reflection Rating Calculation

### Individual Period Rating
For detailed reflections, the overall rating for each period is automatically calculated as:
```
Period Overall Rating = (Activity Effectiveness + Student Engagement) / 2
```

**Example:**
- Activity Effectiveness: 4 stars
- Student Engagement: 5 stars
- Period Overall Rating = (4 + 5) / 2 = 4.5 → rounded to 5 stars

### Rating Components
1. **Activity Effectiveness** (1-5 scale): How effective the teaching activities were
2. **Student Engagement** (1-5 scale): Level of student participation and interest

## System Behavior

### Automatic Generation
1. When a lesson plan is uploaded, a reflection is automatically created
2. Initial overall rating is calculated based on existing detailed reflections
3. If no detailed reflections exist, default rating of 5 stars is used

### Real-time Updates
1. When detailed reflections are added/updated, the overall rating recalculates automatically
2. The calculation considers all periods for the lesson plan
3. Changes are reflected immediately in the UI

### Rounding Rules
- Final ratings are rounded to the nearest 0.1 (one decimal place)
- Individual period ratings are rounded to the nearest integer (1-5 scale)

## Implementation Notes

### Database Structure
- `lesson_plan_reflections`: Stores overall ratings for lesson plans
- `lesson_plan_detailed_reflections`: Stores detailed ratings for individual periods

### Key Functions
- `calculateLessonPlanRating()`: Main calculation function
- `calculateOverallRating()`: Calculates individual period ratings
- `roundToPrecision()`: Handles rounding to specified precision

### Configuration
- Default rating: 5 stars
- Rounding precision: 0.1
- Rating scale: 1-5 stars

## UI Display

### Quick Mode
- Shows calculated overall rating (read-only)
- Displays breakdown of periods with/without reflections
- Shows individual period ratings in expandable section

### Detailed Mode
- Allows editing individual period reflections
- Automatically calculates period ratings from components
- Updates overall lesson plan rating in real-time

## Future Considerations

### Weighting System
Currently all periods are weighted equally. Future enhancements could include:
- Subject-based weighting
- Time-based weighting (longer periods = higher weight)
- Difficulty-based weighting

### Additional Metrics
Potential additional rating components:
- Resource adequacy
- Time management
- Learning objectives achievement
- Student feedback integration
