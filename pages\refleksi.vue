<template>
    <!-- Initial Page Loading State (shows only when first loading with no cached data) -->
    <SkeletonReflectionPage v-if="isInitialLoading" />

    <!-- Onboarding Tour -->
    <OnboardingTour />

    <!-- Main Content - shows after initial load or when not initially loading -->
    <div v-if="!isInitialLoading" class="space-y-8">
        <!-- Page Header -->
        <UiCompositePageHeader title="Analitik Refleksi Pembelajaran"
            subtitle="Pantau prestasi pengajaran dan analisis refleksi pembelajaran anda"
            icon="heroicons:chart-bar-solid">
            <template #actions>
                <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Minggu:</label>
                    <UiBaseSingleSelect :model-value="selectedWeekId" @update:model-value="selectedWeekId = $event"
                        :options="weekOptions" option-label="label" option-value="value" placeholder="Semua <PERSON>gu"
                        variant="compact" :allow-clear="false" class="w-full sm:w-48" />
                </div>
            </template>
        </UiCompositePageHeader>

        <!-- Quick Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
            <!-- Total Lesson Plans -->
            <UiCompositeCard class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Jumlah RPH</p>
                        <p class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ totalLessonPlans }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Refleksi lengkap</p>
                    </div>
                    <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <UiBaseIcon name="heroicons:document-text" class="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Average Rating -->
            <UiCompositeCard class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Purata Penilaian</p>
                        <div class="flex items-center gap-2">
                            <p class="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{{ averageRating }}</p>
                            <div class="flex items-center">
                                <UiBaseIcon v-for="i in 5" :key="i" name="heroicons:star-solid"
                                    :class="i <= Math.round(averageRating) ? 'text-yellow-400' : 'text-gray-300'"
                                    class="h-4 w-4" />
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Daripada 5 bintang</p>
                    </div>
                    <div class="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                        <UiBaseIcon name="heroicons:star" class="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Teaching Periods -->
            <UiCompositeCard class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Jumlah Waktu</p>
                        <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ totalPeriods }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ periodsWithReflections }} ada
                            refleksi</p>
                    </div>
                    <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                        <Icon name="heroicons:clock" class="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Performance Trend -->
            <UiCompositeCard class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Trend Prestasi</p>
                        <div class="flex items-center gap-2">
                            <p class="text-3xl font-bold" :class="trendColor">{{ trendIndicator }}</p>
                            <Icon :name="trendIcon" :class="trendColor" class="h-6 w-6" />
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ trendDescription }}</p>
                    </div>
                    <div class="p-3 rounded-lg" :class="trendBgColor">
                        <Icon name="heroicons:chart-bar-square" class="h-8 w-8" :class="trendColor" />
                    </div>
                </div>
            </UiCompositeCard>
        </div>

        <!-- Detailed Analysis Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Lesson Plans Overview -->
            <UiCompositeCard class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Senarai RPH & Refleksi</h2>
                    <UiBaseButton @click="showAllReflections = !showAllReflections" variant="outline" size="sm">
                        {{ showAllReflections ? 'Lihat Ringkas' : 'Lihat Semua' }}
                    </UiBaseButton>
                </div>

                <!-- Reflections List with Loading State -->
                <div class="space-y-4 max-h-96 overflow-y-auto">
                    <!-- Content Loading State (for week selection changes) -->
                    <div v-if="loadingReflections" class="text-center py-8">
                        <div class="inline-flex items-center gap-2 text-gray-500">
                            <Icon name="heroicons:arrow-path" class="h-5 w-5 animate-spin" />
                            Memuatkan refleksi...
                        </div>
                    </div>

                    <!-- Reflections List Content -->
                    <div v-if="!loadingReflections" class="space-y-4">
                        <div v-for="reflection in displayedReflections" :key="reflection.lesson_plan_id"
                            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors relative">
                            <!-- Main Content -->
                            <div class="flex justify-between items-start gap-4 mb-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center gap-2 mb-2">
                                        <h3 class="font-medium text-gray-900 dark:text-white truncate">
                                            RPH {{ reflection.lesson_plan_id.slice(0, 8) }}
                                        </h3>
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                                            Lengkap
                                        </span>
                                    </div>

                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                        Minggu: {{ getWeekNameForLessonPlan(reflection.lesson_plan_id) }}
                                    </p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        {{ formatDate(reflection.created_at) }}
                                    </p>

                                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                        <span>{{ calculateReflectionStats(reflection).totalPeriods }} waktu</span>
                                        <span>{{ calculateReflectionStats(reflection).periodsWithCustomData }} refleksi
                                            terperinci</span>
                                    </div>
                                </div>

                                <!-- Overall Rating (Top Right) -->
                                <div class="flex items-center gap-1">
                                    <Icon v-for="i in 5" :key="i" name="heroicons:star-solid"
                                        :class="i <= Math.round(calculateReflectionStats(reflection).averageRating) ? 'text-yellow-400' : 'text-gray-300'"
                                        class="h-4 w-4" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-white ml-1">
                                        {{ calculateReflectionStats(reflection).averageRating }}
                                    </span>
                                </div>
                            </div>

                            <!-- Edit Button (Bottom Right) -->
                            <div class="flex justify-end">
                                <UiBaseButton @click="editReflection(reflection)" variant="outline" size="sm"
                                    prepend-icon="mdi:pencil">
                                    Edit
                                </UiBaseButton>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State (when no reflections exist) -->
                    <div v-if="!loadingReflections && allReflections.length === 0" class="text-center py-12">
                        <div class="flex flex-col items-center gap-4">
                            <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-full">
                                <Icon name="heroicons:chat-bubble-left-ellipsis" class="h-8 w-8 text-gray-400" />
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Belum Ada Refleksi</h3>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">
                                    Mulakan dengan menambah refleksi untuk rancangan pengajaran anda
                                </p>
                            </div>
                            <UiBaseButton @click="navigateToRph" variant="primary">
                                Pergi ke RPH
                            </UiBaseButton>
                        </div>
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Performance Insights -->
            <UiCompositeCard class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Analisis Prestasi</h2>

                <!-- Rating Distribution -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Taburan Penilaian</h3>
                    <div class="space-y-2">
                        <div v-for="rating in ratingDistribution" :key="rating.stars" class="flex items-center gap-3">
                            <div class="flex items-center gap-1 w-16">
                                <span class="text-sm text-gray-600 dark:text-gray-400">{{ rating.stars }}</span>
                                <Icon name="heroicons:star-solid" class="h-3 w-3 text-yellow-400" />
                            </div>
                            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                                    :style="{ width: `${rating.percentage}%` }"></div>
                            </div>
                            <span class="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">
                                {{ rating.count }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quick Insights -->
                <div>
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Ringkasan</h3>
                    <div class="space-y-2">
                        <div v-for="insight in quickInsights" :key="insight.type"
                            class="flex items-start gap-3 p-3 rounded-lg" :class="insight.type === 'success' ? 'bg-green-50 dark:bg-green-900/20' :
                                insight.type === 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/20' :
                                    'bg-blue-50 dark:bg-blue-900/20'">
                            <Icon :name="insight.icon" :class="insight.type === 'success' ? 'text-green-600 dark:text-green-400' :
                                insight.type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                                    'text-blue-600 dark:text-blue-400'" class="h-5 w-5 mt-0.5" />
                            <div>
                                <p class="text-sm font-medium" :class="insight.type === 'success' ? 'text-green-800 dark:text-green-200' :
                                    insight.type === 'warning' ? 'text-yellow-800 dark:text-yellow-200' :
                                        'text-blue-800 dark:text-blue-200'">
                                    {{ insight.title }}
                                </p>
                                <p class="text-xs" :class="insight.type === 'success' ? 'text-green-600 dark:text-green-400' :
                                    insight.type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                                        'text-blue-600 dark:text-blue-400'">
                                    {{ insight.description }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </UiCompositeCard>
        </div>
    </div>

    <!-- Reflection Modal -->
    <ReflectionModal :is-open="showReflectionModal" :lesson-plan="selectedLessonPlanForEdit"
        :user-class-subjects="userClassSubjects" @update:is-open="showReflectionModal = $event"
        @saved="handleReflectionSaved" @success="handleBulkSuccess" @error="handleBulkError" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import type { Database } from '~/types/supabase';
import { useDetailedReflections } from '@/composables/useDetailedReflections';
import { useLessonPlans } from '@/composables/useLessonPlans';
import { useRphWeeks } from '@/composables/useRphWeeks';
import type { LessonPlanDetailedReflectionSingle } from '@/composables/useReflectionPeriods';
// PageLoader replaced with skeleton screens
import ReflectionModal from '@/components/rph/ReflectionModal.vue';
import UiCompositeCard from '@/components/ui/composite/Card.vue';
import UiBaseButton from '@/components/ui/base/Button.vue';
import { useToast } from '~/composables/useToast';
import SkeletonReflectionPage from '@/components/ui/skeleton/SkeletonReflectionPage.vue';
import OnboardingTour from '@/components/ui/onboarding/OnboardingTour.vue';

const router = useRouter();
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();

// Toast notifications
const { error: showErrorToast, success: showSuccessToast } = useToast();

// Week management
const { weeks: availableWeeks, fetchWeeks } = useRphWeeks();
const selectedWeekId = ref<string>('');

// Week options for dropdown
const weekOptions = computed(() => [
    { value: '', label: 'Semua Minggu' },
    ...availableWeeks.value.map(week => ({
        value: week.id,
        label: week.name
    }))
]);

// Reflections using single-row JSONB composable
const { fetchDetailedReflections } = useDetailedReflections();
const { lessonPlans, fetchLessonPlans } = useLessonPlans();

// Caching configuration
const CACHE_DURATION = 30000; // 30 seconds

// Cache storage
const cache = {
    averageRating: { value: 0 as number | null, timestamp: 0 },
    totalPeriods: { value: 0 as number | null, timestamp: 0 },
    periodsWithReflections: { value: 0 as number | null, timestamp: 0 },
    trendIndicator: { value: '—' as string, timestamp: 0 },
    ratingDistribution: { value: [] as Array<{ stars: number; count: number; percentage: number }>, timestamp: 0 }
};

// Reactive state
const allReflections = ref<LessonPlanDetailedReflectionSingle[]>([]);
const loadingReflections = ref(false);
const showAllReflections = ref(false);
const isInitialLoading = ref(true);

// Modal state
const showReflectionModal = ref(false);
const selectedLessonPlanForEdit = ref<any>(null);
const userClassSubjects = ref<any[]>([]);

// Display computed - filtered reflections
const displayedReflections = computed(() => {
    const limit = showAllReflections.value ? allReflections.value.length : 10;
    return allReflections.value.slice(0, limit);
});

// Helper function to get cached value
const getCachedValue = <T>(key: keyof typeof cache): T => {
    const now = Date.now();
    if (now - cache[key].timestamp < CACHE_DURATION) {
        return cache[key].value as T;
    }
    return null as any; // Will be recomputed
};

// Optimized batch data fetching
const fetchAllReflections = async () => {
    try {
        loadingReflections.value = true;
        isInitialLoading.value = true;

        // Fetch lesson plans first
        await fetchLessonPlans(selectedWeekId.value || undefined);

        if (lessonPlans.value.length === 0) {
            allReflections.value = [];
            return;
        }

        // Batch fetch all reflections in a single query
        const lessonPlanIds = lessonPlans.value.map(lp => lp.id);
        const { data, error } = await supabase
            .from('lesson_plan_detailed_reflections')
            .select('*')
            .in('lesson_plan_id', lessonPlanIds);

        if (error) throw error;
        allReflections.value = data as any as LessonPlanDetailedReflectionSingle[];
    } catch (error) {
        console.error('Error fetching reflections:', error);
        showErrorToast('Gagal memuatkan data refleksi. Sila cuba lagi.');
        allReflections.value = [];
    } finally {
        loadingReflections.value = false;
        isInitialLoading.value = false;
    }
};

// Cached computed properties
const totalLessonPlans = computed(() => allReflections.value.length);

const averageRating = computed(() => {
    const cached = getCachedValue<number>('averageRating');
    if (cached !== null) return cached;

    let totalRating = 0;
    let totalPeriods = 0;

    allReflections.value.forEach(reflection => {
        const reflections = reflection.reflections || {};
        const periods = Object.values(reflections);
        periods.forEach(period => {
            totalRating += period.overall_rating;
            totalPeriods++;
        });
    });

    const result = totalPeriods > 0 ? Number((totalRating / totalPeriods).toFixed(1)) : 0;
    cache.averageRating = { value: result, timestamp: Date.now() };
    return result;
});

const totalPeriods = computed(() => {
    const cached = getCachedValue<number>('totalPeriods');
    if (cached !== null) return cached;

    const result = allReflections.value.reduce((acc, reflection) => {
        const reflections = reflection.reflections || {};
        return acc + Object.keys(reflections).length;
    }, 0);

    cache.totalPeriods = { value: result, timestamp: Date.now() };
    return result;
});

const periodsWithReflections = computed(() => {
    const cached = getCachedValue<number>('periodsWithReflections');
    if (cached !== null) return cached;

    const result = allReflections.value.reduce((acc, reflection) => {
        const reflections = reflection.reflections || {};
        const customPeriods = Object.values(reflections).filter(p => p.overall_rating !== 5).length;
        return acc + customPeriods;
    }, 0);

    cache.periodsWithReflections = { value: result, timestamp: Date.now() };
    return result;
});

// Optimized trend analysis with caching
const trendIndicator = computed(() => {
    const cached = getCachedValue<string>('trendIndicator');
    if (cached !== null) return cached;

    if (allReflections.value.length < 2) {
        cache.trendIndicator = { value: '—', timestamp: Date.now() };
        return '—';
    }

    const recent = allReflections.value.slice(0, Math.ceil(allReflections.value.length / 2));
    const older = allReflections.value.slice(Math.ceil(allReflections.value.length / 2));

    // Calculate average rating for recent reflections
    let recentTotal = 0, recentCount = 0;
    recent.forEach(reflection => {
        const reflections = reflection.reflections || {};
        Object.values(reflections).forEach(period => {
            recentTotal += period.overall_rating;
            recentCount++;
        });
    });

    // Calculate average rating for older reflections
    let olderTotal = 0, olderCount = 0;
    older.forEach(reflection => {
        const reflections = reflection.reflections || {};
        Object.values(reflections).forEach(period => {
            olderTotal += period.overall_rating;
            olderCount++;
        });
    });

    if (recentCount === 0 || olderCount === 0) {
        cache.trendIndicator = { value: '—', timestamp: Date.now() };
        return '—';
    }

    const recentAvg = recentTotal / recentCount;
    const olderAvg = olderTotal / olderCount;

    const diff = recentAvg - olderAvg;
    let result = '0.0';
    if (diff > 0.2) result = '+' + diff.toFixed(1);
    if (diff < -0.2) result = diff.toFixed(1);

    cache.trendIndicator = { value: result, timestamp: Date.now() };
    return result;
});

const trendIcon = computed(() => {
    const trend = parseFloat(trendIndicator.value);
    if (trend > 0.2) return 'heroicons:arrow-trending-up';
    if (trend < -0.2) return 'heroicons:arrow-trending-down';
    return 'heroicons:minus';
});

const trendColor = computed(() => {
    const trend = parseFloat(trendIndicator.value);
    if (trend > 0.2) return 'text-green-600 dark:text-green-400';
    if (trend < -0.2) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
});

const trendBgColor = computed(() => {
    const trend = parseFloat(trendIndicator.value);
    if (trend > 0.2) return 'bg-green-100 dark:bg-green-900/20';
    if (trend < -0.2) return 'bg-red-100 dark:bg-red-900/20';
    return 'bg-gray-100 dark:bg-gray-700';
});

const trendDescription = computed(() => {
    const trend = parseFloat(trendIndicator.value);
    if (trend > 0.2) return 'Prestasi meningkat';
    if (trend < -0.2) return 'Prestasi menurun';
    return 'Prestasi stabil';
});

// Optimized rating distribution with caching
const ratingDistribution = computed(() => {
    const cached = getCachedValue<Array<{ stars: number; count: number; percentage: number }>>('ratingDistribution');
    if (cached !== null) return cached;

    const distribution = [1, 2, 3, 4, 5].map(stars => ({
        stars,
        count: 0,
        percentage: 0
    }));

    let totalPeriods = 0;

    // Count ratings from all periods across all reflections
    allReflections.value.forEach(reflection => {
        const reflections = reflection.reflections || {};
        Object.values(reflections).forEach(period => {
            const roundedRating = Math.round(period.overall_rating);
            const distItem = distribution.find(d => d.stars === roundedRating);
            if (distItem) {
                distItem.count++;
            }
            totalPeriods++;
        });
    });

    // Calculate percentages
    distribution.forEach(item => {
        item.percentage = totalPeriods > 0 ? (item.count / totalPeriods) * 100 : 0;
    });

    cache.ratingDistribution = { value: distribution, timestamp: Date.now() };
    return distribution;
});

// Quick insights
const quickInsights = computed(() => {
    const insights = [];

    if (averageRating.value >= 4.5) {
        insights.push({
            type: 'success',
            icon: 'heroicons:trophy',
            title: 'Prestasi Cemerlang',
            description: `Purata penilaian ${averageRating.value} menunjukkan kualiti pengajaran yang tinggi`
        });
    } else if (averageRating.value < 3.5) {
        insights.push({
            type: 'warning',
            icon: 'heroicons:exclamation-triangle',
            title: 'Ruang Penambahbaikan',
            description: 'Pertimbangkan untuk menambah refleksi terperinci bagi meningkatkan kualiti'
        });
    }

    const completionRate = (periodsWithReflections.value / totalPeriods.value) * 100;
    if (completionRate >= 80) {
        insights.push({
            type: 'success',
            icon: 'heroicons:check-circle',
            title: 'Refleksi Lengkap',
            description: `${completionRate.toFixed(0)}% waktu mempunyai refleksi terperinci`
        });
    } else {
        insights.push({
            type: 'info',
            icon: 'heroicons:information-circle',
            title: 'Tambah Refleksi',
            description: `${(100 - completionRate).toFixed(0)}% waktu boleh ditambah refleksi terperinci`
        });
    }

    return insights;
});

// Helper function to calculate statistics from JSONB reflections
const calculateReflectionStats = (reflection: LessonPlanDetailedReflectionSingle) => {
    const reflections = reflection.reflections || {};
    const periods = Object.values(reflections);

    const totalPeriods = periods.length;
    const periodsWithCustomData = periods.filter(p => p.overall_rating !== 5).length; // Non-default ratings
    const averageRating = totalPeriods > 0
        ? periods.reduce((sum, p) => sum + p.overall_rating, 0) / totalPeriods
        : 5;

    return {
        totalPeriods,
        periodsWithCustomData,
        averageRating: Number(averageRating.toFixed(1))
    };
};

// Format date helper
const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Navigation
const navigateToRph = () => {
    router.push('/rph');
};

// Helper function to get week name by lesson plan
const getWeekNameForLessonPlan = (lessonPlanId: string): string => {
    const lessonPlan = lessonPlans.value.find(lp => lp.id === lessonPlanId);
    if (!lessonPlan) return 'Minggu Tidak Diketahui';

    const week = availableWeeks.value.find(w => w.id === lessonPlan.week_id);
    return week ? week.name : 'Minggu Tidak Diketahui';
};

// Event handlers
const editReflection = (reflection: LessonPlanDetailedReflectionSingle) => {
    const lessonPlan = lessonPlans.value.find(lp => lp.id === reflection.lesson_plan_id);
    if (lessonPlan) {
        selectedLessonPlanForEdit.value = lessonPlan;
        showReflectionModal.value = true;
    }
};

const handleReflectionSaved = () => {
    // Don't close the modal - let user continue editing or close manually
    // selectedLessonPlanForEdit.value remains the same
    // Invalidate cache and refresh data
    Object.keys(cache).forEach(key => {
        cache[key as keyof typeof cache].timestamp = 0;
    });
    fetchAllReflections();
};

const handleBulkSuccess = (message: string) => {
    showSuccessToast(message);
    // Refresh data after bulk operation
    Object.keys(cache).forEach(key => {
        cache[key as keyof typeof cache].timestamp = 0;
    });
    fetchAllReflections();
};

const handleBulkError = (message: string) => {
    showErrorToast(message);
};

// Fetch user's class subjects from profiles table
const fetchUserClassSubjects = async () => {
    if (!user.value) return;

    try {
        const { data, error } = await supabase
            .from('profiles')
            .select('class_subjects')
            .eq('id', user.value.id)
            .single() as { data: { class_subjects: any[] } | null, error: any };

        if (error) {
            console.error('Error fetching user class subjects:', error);
            return;
        }

        if (data?.class_subjects) {
            userClassSubjects.value = data.class_subjects;
        }
    } catch (err) {
        console.error('Error fetching user class subjects:', err);
    }
};

// Watchers
watch(selectedWeekId, () => {
    // Invalidate cache when week changes
    Object.keys(cache).forEach(key => {
        cache[key as keyof typeof cache].timestamp = 0;
    });
    fetchAllReflections();
});

// Lifecycle hooks
onMounted(async () => {
    await fetchWeeks();
    await fetchUserClassSubjects();
    fetchAllReflections();
});
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
