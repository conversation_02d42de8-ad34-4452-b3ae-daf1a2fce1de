browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  enable debug logging with { debug: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🏫 [SSR] Detected school subdomain: xba12234 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:54 🏫 Detected school subdomain: xba12234
a-subdomain.global.ts:40 🏫 Running school validation for: /rph
a-subdomain.global.ts:26 🔓 Skipping middleware for auth route: /auth/login
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
runtime-core.esm-bundler.js?v=40128521:50 [Vue warn]: Hydration node mismatch:
- rendered on server: <!-- Sidebar -->  
- expected on client: Symbol(v-fgt) 
  at <Blank ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="blank" layoutProps= {ref: RefImpl} name="blank" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="blank" name="blank"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=40128521:50
handleMismatch @ runtime-core.esm-bundler.js?v=40128521:2068
onMismatch @ runtime-core.esm-bundler.js?v=40128521:1680
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1763
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:1637 Hydration completed but contains mismatches.
logMismatchError @ runtime-core.esm-bundler.js?v=40128521:1637
handleMismatch @ runtime-core.esm-bundler.js?v=40128521:2077
onMismatch @ runtime-core.esm-bundler.js?v=40128521:1680
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1763
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:50 [Vue warn]: Hydration children mismatch on <div class=​"flex h-screen bg-light-background dark:​bg-dark-background text-light-foreground dark:​text-dark-foreground" data-v-433a9abd>​…​</div>​flex 
Server rendered element contains more child nodes than client vdom. 
  at <Blank ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="blank" layoutProps= {ref: RefImpl} name="blank" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="blank" name="blank"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=40128521:50
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1896
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:50 [Vue warn]: Hydration class mismatch on <div class=​"flex h-screen bg-light-background dark:​bg-dark-background text-light-foreground dark:​text-dark-foreground" data-v-433a9abd>​…​</div>​flex 
  - rendered on server: class="flex h-screen bg-light-background dark:bg-dark-background text-light-foreground dark:text-dark-foreground"
  - expected on client: class="min-h-screen flex items-center justify-center bg-light-blank dark:bg-dark-background text-light-foreground dark:text-dark-foreground py-12 px-4 sm:px-6 lg:px-8"
  Note: this mismatch is check-only. The DOM will not be rectified in production due to performance overhead.
  You should fix the source of the mismatch. 
  at <Blank ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="blank" layoutProps= {ref: RefImpl} name="blank" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="blank" name="blank"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=40128521:50
propHasMismatch @ runtime-core.esm-bundler.js?v=40128521:2207
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1939
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
index.mjs?v=40128521:21 Fetch finished loading: GET "http://xba12234.localhost:3000/api/_nuxt_icon/logos.json?icons=google-icon".
(anonymous) @ index.mjs?v=40128521:21
$fetch.native @ ofetch.03887fc3.mjs?v=40128521:320
send @ iconify.mjs?v=40128521:742
execNext @ iconify.mjs?v=40128521:1076
setTimeout
sendQuery @ iconify.mjs?v=40128521:1078
query @ iconify.mjs?v=40128521:1093
sendAPIQuery @ iconify.mjs?v=40128521:1171
(anonymous) @ iconify.mjs?v=40128521:1288
(anonymous) @ iconify.mjs?v=40128521:1287
setTimeout
loadNewIcons @ iconify.mjs?v=40128521:1241
(anonymous) @ iconify.mjs?v=40128521:1344
loadIcons @ iconify.mjs?v=40128521:1341
(anonymous) @ iconify.mjs?v=40128521:1356
loadIcon @ iconify.mjs?v=40128521:1350
loadIcon @ shared.js?v=40128521:15
watch.immediate @ css.js?v=40128521:125
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=40128521:6244
job @ reactivity.esm-bundler.js?v=40128521:1747
watch @ reactivity.esm-bundler.js?v=40128521:1783
doWatch @ runtime-core.esm-bundler.js?v=40128521:6272
watch @ runtime-core.esm-bundler.js?v=40128521:6205
setup @ css.js?v=40128521:115
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=40128521:7952
setupComponent @ runtime-core.esm-bundler.js?v=40128521:7913
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5243
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
processFragment @ runtime-core.esm-bundler.js?v=40128521:5139
patch @ runtime-core.esm-bundler.js?v=40128521:4699
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
processFragment @ runtime-core.esm-bundler.js?v=40128521:5139
patch @ runtime-core.esm-bundler.js?v=40128521:4699
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountSuspense @ runtime-core.esm-bundler.js?v=40128521:6867
process @ runtime-core.esm-bundler.js?v=40128521:6808
patch @ runtime-core.esm-bundler.js?v=40128521:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
processFragment @ runtime-core.esm-bundler.js?v=40128521:5139
patch @ runtime-core.esm-bundler.js?v=40128521:4699
handleMismatch @ runtime-core.esm-bundler.js?v=40128521:2094
onMismatch @ runtime-core.esm-bundler.js?v=40128521:1680
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1763
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
