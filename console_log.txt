stripe:1  GET http://localhost:3000/api/webhooks/stripe 404 (Page not found: /api/webhooks/stripe)
vue-router.mjs?v=40128521:31 [Vue Router warn]: No match found for location with path "/api/webhooks/stripe"
warn @ vue-router.mjs?v=40128521:31
resolve @ vue-router.mjs?v=40128521:2224
pushWithRedirect @ vue-router.mjs?v=40128521:2340
push @ vue-router.mjs?v=40128521:2308
install @ vue-router.mjs?v=40128521:2663
use @ runtime-core.esm-bundler.js?v=40128521:3885
setup @ router.js?v=40128521:67
(anonymous) @ nuxt.js?v=40128521:139
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
applyPlugin @ nuxt.js?v=40128521:139
executePlugin @ nuxt.js?v=40128521:158
applyPlugins @ nuxt.js?v=40128521:189
await in applyPlugins
initApp @ entry.js:60
(anonymous) @ entry.js:75
browser.mjs?v=40128521:48 ssr:warn [Vue Router warn]: No match found for location with path "/api/webhooks/stripe" 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at warn (/node_modules/vue-router/dist/vue-router.mjs)
  at resolve (/node_modules/vue-router/dist/vue-router.mjs)
  at pushWithRedirect (/node_modules/vue-router/dist/vue-router.mjs)

log @ browser.mjs?v=40128521:48
_log @ core.mjs?v=40128521:483
resolveLog @ core.mjs?v=40128521:451
_logFn @ core.mjs?v=40128521:479
(anonymous) @ core.mjs?v=40128521:408
(anonymous) @ dev-server-logs.js?v=40128521:28
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
dev:ssr-logs
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
(anonymous) @ dev-server-logs.js?v=40128521:36
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ dev-server-logs.js?v=40128521:36
(anonymous) @ nuxt.js?v=40128521:139
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
applyPlugin @ nuxt.js?v=40128521:139
executePlugin @ nuxt.js?v=40128521:158
applyPlugins @ nuxt.js?v=40128521:189
await in applyPlugins
initApp @ entry.js:60
(anonymous) @ entry.js:75
browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  override existing env vars with { override: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  write to custom object with { processEnv: myObject }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🌐 [SSR] Main domain detected - no subdomain 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

browser.mjs?v=40128521:48 ssr:warn [Vue Router warn]: No match found for location with path "/api/webhooks/stripe" 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at warn (/node_modules/vue-router/dist/vue-router.mjs)
  at resolve (/node_modules/vue-router/dist/vue-router.mjs)
  at pushWithRedirect (/node_modules/vue-router/dist/vue-router.mjs)

log @ browser.mjs?v=40128521:48
_log @ core.mjs?v=40128521:483
resolveLog @ core.mjs?v=40128521:451
_logFn @ core.mjs?v=40128521:479
(anonymous) @ core.mjs?v=40128521:408
(anonymous) @ dev-server-logs.js?v=40128521:28
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
dev:ssr-logs
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
(anonymous) @ dev-server-logs.js?v=40128521:36
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ dev-server-logs.js?v=40128521:36
(anonymous) @ nuxt.js?v=40128521:139
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
applyPlugin @ nuxt.js?v=40128521:139
executePlugin @ nuxt.js?v=40128521:158
applyPlugins @ nuxt.js?v=40128521:189
await in applyPlugins
initApp @ entry.js:60
(anonymous) @ entry.js:75
subdomain-detection.client.ts:87 🌐 Main domain detected - no subdomain
vue-router.mjs?v=40128521:31 [Vue Router warn]: No match found for location with path "/api/webhooks/stripe"
warn @ vue-router.mjs?v=40128521:31
resolve @ vue-router.mjs?v=40128521:2275
pushWithRedirect @ vue-router.mjs?v=40128521:2340
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
nuxt.js?v=40128521:124 [nuxt] error caught during app initialization H3Error: Page not found: /api/webhooks/stripe
    at createError (index.mjs?v=40128521:71:15)
    at createError (error.js?v=40128521:33:21)
    at router.js?v=40128521:208:54
    at fn (nuxt.js?v=40128521:216:44)
    at Object.runWithContext (runtime-core.esm-bundler.js?v=40128521:4016:18)
    at callWithNuxt (nuxt.js?v=40128521:222:24)
    at nuxt.js?v=40128521:38:41
    at EffectScope.run (reactivity.esm-bundler.js?v=40128521:67:16)
    at Object.runWithContext (nuxt.js?v=40128521:38:31)
    at router.js?v=40128521:208:23
(anonymous) @ nuxt.js?v=40128521:124
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:error
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
showError @ error.js?v=40128521:14
(anonymous) @ router.js?v=40128521:208
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
(anonymous) @ router.js?v=40128521:208
(anonymous) @ vue-router.mjs?v=40128521:2488
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
runWithContext @ vue-router.mjs?v=40128521:2426
(anonymous) @ vue-router.mjs?v=40128521:2488
triggerAfterEach @ vue-router.mjs?v=40128521:2488
(anonymous) @ vue-router.mjs?v=40128521:2416
Promise.then
pushWithRedirect @ vue-router.mjs?v=40128521:2382
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
