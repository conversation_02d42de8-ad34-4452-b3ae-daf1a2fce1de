:3000/:1  GET http://xba12234.localhost:3000/ 500 (Server Error)
browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  enable debug logging with { debug: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr 🏫 [SSR] Detected school subdomain: xba12234 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:54 🏫 Detected school subdomain: xba12234
a-subdomain.global.ts:239 Fetch finished loading: POST "http://xba12234.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=********:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=********:258
$fetch2 @ ofetch.03887fc3.mjs?v=********:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:239
handleSecureSchoolRouting @ a-subdomain.global.ts:194
(anonymous) @ a-subdomain.global.ts:29
executeAsync @ index.mjs?v=********:110
(anonymous) @ a-subdomain.global.ts:29
(anonymous) @ router.js?v=********:169
fn @ nuxt.js?v=********:216
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
callWithNuxt @ nuxt.js?v=********:222
(anonymous) @ nuxt.js?v=********:38
run @ reactivity.esm-bundler.js?v=********:67
runWithContext @ nuxt.js?v=********:38
(anonymous) @ router.js?v=********:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=********:1385
runWithContext @ vue-router.mjs?v=********:1360
(anonymous) @ vue-router.mjs?v=********:1385
(anonymous) @ vue-router.mjs?v=********:1363
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
runWithContext @ vue-router.mjs?v=********:2426
(anonymous) @ vue-router.mjs?v=********:2698
Promise.then
(anonymous) @ vue-router.mjs?v=********:2698
runGuardQueue @ vue-router.mjs?v=********:2698
(anonymous) @ vue-router.mjs?v=********:2445
Promise.then
navigate @ vue-router.mjs?v=********:2439
pushWithRedirect @ vue-router.mjs?v=********:2372
push @ vue-router.mjs?v=********:2308
replace @ vue-router.mjs?v=********:2311
(anonymous) @ router.js?v=********:223
_function @ index.mjs?v=********:133
(anonymous) @ index.mjs?v=********:48
(anonymous) @ index.mjs?v=********:48
app:created
serialTaskCaller @ index.mjs?v=********:46
callHookWith @ index.mjs?v=********:198
callHook @ index.mjs?v=********:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
devtools.client.js?v=********:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
runtime-core.esm-bundler.js?v=********:7060 <Suspense> is an experimental feature and its API will likely change.
