browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr 🏫 [SSR] Detected school subdomain: xba12234 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:54 🏫 Detected school subdomain: xba12234
a-subdomain.global.ts:233 Fetch finished loading: POST "http://xba12234.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=********:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=********:258
$fetch2 @ ofetch.03887fc3.mjs?v=********:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:233
handleSecureSchoolRouting @ a-subdomain.global.ts:194
(anonymous) @ a-subdomain.global.ts:29
executeAsync @ index.mjs?v=********:110
(anonymous) @ a-subdomain.global.ts:29
(anonymous) @ router.js?v=********:169
fn @ nuxt.js?v=********:216
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
callWithNuxt @ nuxt.js?v=********:222
(anonymous) @ nuxt.js?v=********:38
run @ reactivity.esm-bundler.js?v=********:67
runWithContext @ nuxt.js?v=********:38
(anonymous) @ router.js?v=********:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=********:1385
runWithContext @ vue-router.mjs?v=********:1360
(anonymous) @ vue-router.mjs?v=********:1385
(anonymous) @ vue-router.mjs?v=********:1363
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
runWithContext @ vue-router.mjs?v=********:2426
(anonymous) @ vue-router.mjs?v=********:2698
Promise.then
(anonymous) @ vue-router.mjs?v=********:2698
runGuardQueue @ vue-router.mjs?v=********:2698
(anonymous) @ vue-router.mjs?v=********:2445
Promise.then
navigate @ vue-router.mjs?v=********:2439
pushWithRedirect @ vue-router.mjs?v=********:2372
push @ vue-router.mjs?v=********:2308
replace @ vue-router.mjs?v=********:2311
(anonymous) @ router.js?v=********:223
_function @ index.mjs?v=********:133
(anonymous) @ index.mjs?v=********:48
(anonymous) @ index.mjs?v=********:48
app:created
serialTaskCaller @ index.mjs?v=********:46
callHookWith @ index.mjs?v=********:198
callHook @ index.mjs?v=********:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:7060 <Suspense> is an experimental feature and its API will likely change.
devtools.client.js?v=********:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Extraneous non-props attributes (school-context) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes. 
  at <Sidebar is-mobile-open=false school-context= {school: null, membership: null, isLoading: true, error: null} onClose=fn<closeMobileSidebar>  ... > 
  at <School ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="school" layoutProps= {ref: RefImpl} name="school" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="school" name="school"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
renderComponentRoot @ runtime-core.esm-bundler.js?v=********:6621
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5312
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
fetch-retry.js?v=********:5 Fetch finished loading: GET "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/schools?select=*&code=eq.xba12234".
fetchWithRetry @ fetch-retry.js?v=********:5
(anonymous) @ fetch.js?v=********:23
(anonymous) @ fetch.js?v=********:44
fulfilled @ fetch.js?v=********:4
Promise.then
step @ fetch.js?v=********:6
(anonymous) @ fetch.js?v=********:7
__awaiter @ fetch.js?v=********:3
(anonymous) @ fetch.js?v=********:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=********:119
useSchoolContext.ts:116 ✅ School context loaded: SK Tiong Widu (xba12234)
school.vue:223 🏫 School context refreshed: SK Tiong Widu
school.vue:188 [Vue warn]: Extraneous non-props attributes (school-context) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes. 
  at <Sidebar is-mobile-open=false school-context= {school: {…}, membership: {…}, isLoading: false, error: null} onClose=fn<closeMobileSidebar>  ... > 
  at <School ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <AsyncComponentWrapper ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <LayoutLoader key="school" layoutProps= {ref: RefImpl} name="school" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="school" name="school"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
renderComponentRoot @ runtime-core.esm-bundler.js?v=********:6621
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5424
run @ reactivity.esm-bundler.js?v=********:207
updateComponent @ runtime-core.esm-bundler.js?v=********:5285
processComponent @ runtime-core.esm-bundler.js?v=********:5220
patch @ runtime-core.esm-bundler.js?v=********:4725
patchBlockChildren @ runtime-core.esm-bundler.js?v=********:5079
patchElement @ runtime-core.esm-bundler.js?v=********:4997
processElement @ runtime-core.esm-bundler.js?v=********:4856
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5433
run @ reactivity.esm-bundler.js?v=********:207
runIfDirty @ reactivity.esm-bundler.js?v=********:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
flushJobs @ runtime-core.esm-bundler.js?v=********:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queueJob @ runtime-core.esm-bundler.js?v=********:314
effect2.scheduler @ runtime-core.esm-bundler.js?v=********:5475
trigger @ reactivity.esm-bundler.js?v=********:235
endBatch @ reactivity.esm-bundler.js?v=********:293
notify @ reactivity.esm-bundler.js?v=********:566
trigger @ reactivity.esm-bundler.js?v=********:540
set value @ reactivity.esm-bundler.js?v=********:1412
refreshSchoolContext @ school.vue:188
await in refreshSchoolContext
(anonymous) @ school.vue:266
(anonymous) @ runtime-core.esm-bundler.js?v=********:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=********:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=********:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=********:382
flushJobs @ runtime-core.esm-bundler.js?v=********:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=********:333
resolve @ runtime-core.esm-bundler.js?v=********:7164
resolve @ runtime-core.esm-bundler.js?v=********:7171
(anonymous) @ runtime-core.esm-bundler.js?v=********:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
maklumat-guru.vue:399 Fetch finished loading: GET "http://xba12234.localhost:3000/api/_nuxt_icon/logos.json?icons=google-icon".
(anonymous) @ index.mjs?v=********:21
$fetch.native @ ofetch.03887fc3.mjs?v=********:320
send @ iconify.mjs?v=********:742
execNext @ iconify.mjs?v=********:1076
setTimeout
sendQuery @ iconify.mjs?v=********:1078
query @ iconify.mjs?v=********:1093
sendAPIQuery @ iconify.mjs?v=********:1171
(anonymous) @ iconify.mjs?v=********:1288
(anonymous) @ iconify.mjs?v=********:1287
setTimeout
loadNewIcons @ iconify.mjs?v=********:1241
(anonymous) @ iconify.mjs?v=********:1344
loadIcons @ iconify.mjs?v=********:1341
(anonymous) @ iconify.mjs?v=********:1356
loadIcon @ iconify.mjs?v=********:1350
loadIcon @ shared.js?v=********:15
watch.immediate @ css.js?v=********:125
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=********:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=********:6244
job @ reactivity.esm-bundler.js?v=********:1747
watch @ reactivity.esm-bundler.js?v=********:1783
doWatch @ runtime-core.esm-bundler.js?v=********:6272
watch @ runtime-core.esm-bundler.js?v=********:6205
setup @ css.js?v=********:115
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=********:7952
setupComponent @ runtime-core.esm-bundler.js?v=********:7913
mountComponent @ runtime-core.esm-bundler.js?v=********:5243
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
processFragment @ runtime-core.esm-bundler.js?v=********:5139
patch @ runtime-core.esm-bundler.js?v=********:4699
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
processFragment @ runtime-core.esm-bundler.js?v=********:5139
patch @ runtime-core.esm-bundler.js?v=********:4699
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountSuspense @ runtime-core.esm-bundler.js?v=********:6867
process @ runtime-core.esm-bundler.js?v=********:6808
patch @ runtime-core.esm-bundler.js?v=********:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
processFragment @ runtime-core.esm-bundler.js?v=********:5139
patch @ runtime-core.esm-bundler.js?v=********:4699
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
patchSuspense @ runtime-core.esm-bundler.js?v=********:7028
process @ runtime-core.esm-bundler.js?v=********:6826
patch @ runtime-core.esm-bundler.js?v=********:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5433
run @ reactivity.esm-bundler.js?v=********:207
runIfDirty @ reactivity.esm-bundler.js?v=********:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
flushJobs @ runtime-core.esm-bundler.js?v=********:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queueJob @ runtime-core.esm-bundler.js?v=********:314
effect2.scheduler @ runtime-core.esm-bundler.js?v=********:5475
trigger @ reactivity.esm-bundler.js?v=********:235
endBatch @ reactivity.esm-bundler.js?v=********:293
notify @ reactivity.esm-bundler.js?v=********:566
trigger @ reactivity.esm-bundler.js?v=********:540
set value @ reactivity.esm-bundler.js?v=********:1412
finalizeNavigation @ vue-router.mjs?v=********:2504
(anonymous) @ vue-router.mjs?v=********:2414
Promise.then
pushWithRedirect @ vue-router.mjs?v=********:2382
push @ vue-router.mjs?v=********:2308
navigateTo @ router.js?v=********:140
(anonymous) @ maklumat-guru.vue:399
withAsyncContext @ runtime-core.esm-bundler.js?v=********:3401
setup @ maklumat-guru.vue:399
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=********:7952
setupComponent @ runtime-core.esm-bundler.js?v=********:7913
mountComponent @ runtime-core.esm-bundler.js?v=********:5243
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountSuspense @ runtime-core.esm-bundler.js?v=********:6867
process @ runtime-core.esm-bundler.js?v=********:6808
patch @ runtime-core.esm-bundler.js?v=********:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
processFragment @ runtime-core.esm-bundler.js?v=********:5139
patch @ runtime-core.esm-bundler.js?v=********:4699
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
processFragment @ runtime-core.esm-bundler.js?v=********:5139
patch @ runtime-core.esm-bundler.js?v=********:4699
patchBlockChildren @ runtime-core.esm-bundler.js?v=********:5079
patchElement @ runtime-core.esm-bundler.js?v=********:4997
processElement @ runtime-core.esm-bundler.js?v=********:4856
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5433
run @ reactivity.esm-bundler.js?v=********:207
runIfDirty @ reactivity.esm-bundler.js?v=********:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
flushJobs @ runtime-core.esm-bundler.js?v=********:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queueJob @ runtime-core.esm-bundler.js?v=********:314
effect2.scheduler @ runtime-core.esm-bundler.js?v=********:5475
trigger @ reactivity.esm-bundler.js?v=********:235
endBatch @ reactivity.esm-bundler.js?v=********:293
notify @ reactivity.esm-bundler.js?v=********:566
trigger @ reactivity.esm-bundler.js?v=********:540
set value @ reactivity.esm-bundler.js?v=********:1412
refreshSchoolContext @ school.vue:188
await in refreshSchoolContext
(anonymous) @ school.vue:266
(anonymous) @ runtime-core.esm-bundler.js?v=********:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=********:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=********:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=********:382
flushJobs @ runtime-core.esm-bundler.js?v=********:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=********:333
resolve @ runtime-core.esm-bundler.js?v=********:7164
resolve @ runtime-core.esm-bundler.js?v=********:7171
(anonymous) @ runtime-core.esm-bundler.js?v=********:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
