browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  enable debug logging with { debug: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr 🌐 [SSR] Main domain detected - no subdomain 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:87 🌐 Main domain detected - no subdomain
devtools.client.js?v=********:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
a-subdomain.global.ts:307 Fetch finished loading: POST "http://localhost:3000/api/schools/validate-admin-access".
(anonymous) @ index.mjs?v=********:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=********:258
$fetch2 @ ofetch.03887fc3.mjs?v=********:316
handleMainDomainRouting @ a-subdomain.global.ts:307
await in handleMainDomainRouting
handleRouting @ a-subdomain.global.ts:201
(anonymous) @ a-subdomain.global.ts:24
executeAsync @ index.mjs?v=********:110
(anonymous) @ a-subdomain.global.ts:24
(anonymous) @ router.js?t=*************&v=********:169
fn @ nuxt.js?v=********:216
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
callWithNuxt @ nuxt.js?v=********:222
(anonymous) @ nuxt.js?v=********:38
run @ reactivity.esm-bundler.js?v=********:67
runWithContext @ nuxt.js?v=********:38
(anonymous) @ router.js?t=*************&v=********:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=********:1385
runWithContext @ vue-router.mjs?v=********:1360
(anonymous) @ vue-router.mjs?v=********:1385
(anonymous) @ vue-router.mjs?v=********:1363
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
runWithContext @ vue-router.mjs?v=********:2426
(anonymous) @ vue-router.mjs?v=********:2698
Promise.then
(anonymous) @ vue-router.mjs?v=********:2698
runGuardQueue @ vue-router.mjs?v=********:2698
(anonymous) @ vue-router.mjs?v=********:2445
Promise.then
navigate @ vue-router.mjs?v=********:2439
pushWithRedirect @ vue-router.mjs?v=********:2372
push @ vue-router.mjs?v=********:2308
replace @ vue-router.mjs?v=********:2311
(anonymous) @ router.js?t=*************&v=********:223
_function @ index.mjs?v=********:133
(anonymous) @ index.mjs?v=********:48
(anonymous) @ index.mjs?v=********:48
app:created
serialTaskCaller @ index.mjs?v=********:46
callHookWith @ index.mjs?v=********:198
callHook @ index.mjs?v=********:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:7060 <Suspense> is an experimental feature and its API will likely change.
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration class mismatch on <a href=​"/​login" class=​"text-gray-700 dark:​text-gray-300 hover:​text-blue-600 dark:​hover:​text-blue-400 transition-colors" data-v-fe8b218c>​ Sign In ​</a>​ 
  - rendered on server: class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
  - expected on client: class="router-link-active router-link-exact-active text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
  Note: this mismatch is check-only. The DOM will not be rectified in production due to performance overhead.
  You should fix the source of the mismatch. 
  at <RouterLink ref=fn to="/login" activeClass=undefined  ... > 
  at <NuxtLink to="/login" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
propHasMismatch @ runtime-core.esm-bundler.js?v=********:2207
hydrateElement @ runtime-core.esm-bundler.js?v=********:1939
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:1637 Hydration completed but contains mismatches.
logMismatchError @ runtime-core.esm-bundler.js?v=********:1637
hydrateElement @ runtime-core.esm-bundler.js?v=********:1940
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration node mismatch:
- rendered on server: <svg class=​"h-12 w-12 text-green-600 dark:​text-green-400" fill=​"none" stroke=​"currentColor" viewBox=​"0 0 24 24" data-v-3d9dc270>​…​</svg>​  
- expected on client: a 
  at <RouterLink ref=fn to="/" activeClass=undefined  ... > 
  at <NuxtLink to="/" class="flex items-center justify-center space-x-2 mb-6" > 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
handleMismatch @ runtime-core.esm-bundler.js?v=********:2068
onMismatch @ runtime-core.esm-bundler.js?v=********:1680
hydrateNode @ runtime-core.esm-bundler.js?v=********:1778
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration children mismatch on <div class=​"mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:​bg-green-900/​30" data-v-3d9dc270>​…​</div>​flex 
Server rendered element contains fewer child nodes than client vdom. 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
hydrateChildren @ runtime-core.esm-bundler.js?v=********:2015
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration class mismatch on <div class=​"mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:​bg-green-900/​30" data-v-3d9dc270>​…​</div>​flex 
  - rendered on server: class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/30"
  - expected on client: class="text-center"
  Note: this mismatch is check-only. The DOM will not be rectified in production due to performance overhead.
  You should fix the source of the mismatch. 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
propHasMismatch @ runtime-core.esm-bundler.js?v=********:2207
hydrateElement @ runtime-core.esm-bundler.js?v=********:1939
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration node mismatch:
- rendered on server: <div data-v-3d9dc270>​…​</div>​  
- expected on client: form 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
handleMismatch @ runtime-core.esm-bundler.js?v=********:2068
onMismatch @ runtime-core.esm-bundler.js?v=********:1680
hydrateNode @ runtime-core.esm-bundler.js?v=********:1778
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration children mismatch on <div class=​"max-w-md w-full space-y-8 text-center" data-v-3d9dc270>​…​</div>​ 
Server rendered element contains more child nodes than client vdom. 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
hydrateElement @ runtime-core.esm-bundler.js?v=********:1896
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration class mismatch on <div class=​"max-w-md w-full space-y-8 text-center" data-v-3d9dc270>​…​</div>​ 
  - rendered on server: class="max-w-md w-full space-y-8 text-center"
  - expected on client: class="max-w-md w-full space-y-8"
  Note: this mismatch is check-only. The DOM will not be rectified in production due to performance overhead.
  You should fix the source of the mismatch. 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
propHasMismatch @ runtime-core.esm-bundler.js?v=********:2207
hydrateElement @ runtime-core.esm-bundler.js?v=********:1939
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Hydration children mismatch on <section class=​"min-h-screen flex items-center justify-center bg-gray-50 dark:​bg-gray-900 py-12 px-4 sm:​px-6 lg:​px-8" data-v-3d9dc270>​…​</section>​flex 
Server rendered element contains fewer child nodes than client vdom. 
  at <Login onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouteProvider key="/login" vnode= {__v_isVNode: true, __v_skip: true, type: {…}, props: {…}, key: null, …} route= {fullPath: '/login', hash: '', query: {…}, name: 'login', path: '/login', …}  ... > 
  at <RouterView name=undefined route=undefined > 
  at <NuxtPage > 
  at <Landing ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="landing" layoutProps= {ref: RefImpl} name="landing" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="landing" name="landing"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
hydrateChildren @ runtime-core.esm-bundler.js?v=********:2015
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
index.mjs?v=********:21 Fetch finished loading: GET "http://localhost:3000/api/_nuxt_icon/heroicons.json?icons=eye".
(anonymous) @ index.mjs?v=********:21
$fetch.native @ ofetch.03887fc3.mjs?v=********:320
send @ iconify.mjs?v=********:742
execNext @ iconify.mjs?v=********:1076
setTimeout
sendQuery @ iconify.mjs?v=********:1078
query @ iconify.mjs?v=********:1093
sendAPIQuery @ iconify.mjs?v=********:1171
(anonymous) @ iconify.mjs?v=********:1288
(anonymous) @ iconify.mjs?v=********:1287
setTimeout
loadNewIcons @ iconify.mjs?v=********:1241
(anonymous) @ iconify.mjs?v=********:1344
loadIcons @ iconify.mjs?v=********:1341
(anonymous) @ iconify.mjs?v=********:1356
loadIcon @ iconify.mjs?v=********:1350
loadIcon @ shared.js?v=********:15
watch.immediate @ css.js?v=********:125
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=********:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=********:6244
job @ reactivity.esm-bundler.js?v=********:1747
watch @ reactivity.esm-bundler.js?v=********:1783
doWatch @ runtime-core.esm-bundler.js?v=********:6272
watch @ runtime-core.esm-bundler.js?v=********:6205
setup @ css.js?v=********:115
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=********:7952
setupComponent @ runtime-core.esm-bundler.js?v=********:7913
mountComponent @ runtime-core.esm-bundler.js?v=********:5243
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
handleMismatch @ runtime-core.esm-bundler.js?v=********:2094
onMismatch @ runtime-core.esm-bundler.js?v=********:1680
hydrateNode @ runtime-core.esm-bundler.js?v=********:1778
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
index.mjs?v=********:21 Fetch finished loading: GET "http://localhost:3000/api/_nuxt_icon/logos.json?icons=google-icon".
(anonymous) @ index.mjs?v=********:21
$fetch.native @ ofetch.03887fc3.mjs?v=********:320
send @ iconify.mjs?v=********:742
execNext @ iconify.mjs?v=********:1076
setTimeout
sendQuery @ iconify.mjs?v=********:1078
query @ iconify.mjs?v=********:1093
sendAPIQuery @ iconify.mjs?v=********:1171
(anonymous) @ iconify.mjs?v=********:1288
(anonymous) @ iconify.mjs?v=********:1287
setTimeout
loadNewIcons @ iconify.mjs?v=********:1241
(anonymous) @ iconify.mjs?v=********:1344
loadIcons @ iconify.mjs?v=********:1341
(anonymous) @ iconify.mjs?v=********:1356
loadIcon @ iconify.mjs?v=********:1350
loadIcon @ shared.js?v=********:15
watch.immediate @ css.js?v=********:125
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=********:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=********:6244
job @ reactivity.esm-bundler.js?v=********:1747
watch @ reactivity.esm-bundler.js?v=********:1783
doWatch @ runtime-core.esm-bundler.js?v=********:6272
watch @ runtime-core.esm-bundler.js?v=********:6205
setup @ css.js?v=********:115
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=********:7952
setupComponent @ runtime-core.esm-bundler.js?v=********:7913
mountComponent @ runtime-core.esm-bundler.js?v=********:5243
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5353
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
processComponent @ runtime-core.esm-bundler.js?v=********:5209
patch @ runtime-core.esm-bundler.js?v=********:4725
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
mountChildren @ runtime-core.esm-bundler.js?v=********:4957
mountElement @ runtime-core.esm-bundler.js?v=********:4880
processElement @ runtime-core.esm-bundler.js?v=********:4845
patch @ runtime-core.esm-bundler.js?v=********:4713
handleMismatch @ runtime-core.esm-bundler.js?v=********:2094
onMismatch @ runtime-core.esm-bundler.js?v=********:1680
hydrateNode @ runtime-core.esm-bundler.js?v=********:1778
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=********:2045
hydrateNode @ runtime-core.esm-bundler.js?v=********:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
