🎯 Stripe webhook called!
✅ Received Stripe webhook: payment_method.attached
❓ Unhandled event type: payment_method.attached
🎯 Stripe webhook called!
✅ Received Stripe webhook: customer.subscription.created
📋 Subscription created: { id: 'sub_1RlpaUGKbo6Mo0fooicpxJ4i',
  object: 'subscription',
  application: null,
  application_fee_percent: null,
  automatic_tax: { disabled_reason: null, enabled: false, liability: null },
  billing_cycle_anchor: **********,
  billing_cycle_anchor_config: null,
  billing_mode: { type: 'classic' },
  billing_thresholds: null,
  cancel_at: null,
  cancel_at_period_end: false,
  canceled_at: null,
  cancellation_details: { comment: null, feedback: null, reason: null },
  collection_method: 'charge_automatically',
  created: **********,
  currency: 'myr',
  customer: 'cus_ShAmVA6eWoM39X',
  days_until_due: null,
  default_payment_method: 'pm_1RlpaTGKbo6Mo0foaj65dSGR',
  default_source: null,
  default_tax_rates: [],
  description: null,
  discounts: [],
  ended_at: null,
  invoice_settings: { account_tax_ids: null, issuer: { type: 'self' } },
  items:
   { object: 'list',
     data: [ [Object] ],
     has_more: false,
     total_count: 1,
     url: '/v1/subscription_items?subscription=sub_1RlpaUGKbo6Mo0fooicpxJ4i' },
  latest_invoice: 'in_1RlpaTGKbo6Mo0foakh71mhD',
  livemode: false,
  metadata: { schoolCode: 'xba12234', plan: 'professional' },
  next_pending_invoice_item_invoice: null,
  on_behalf_of: null,
  pause_collection: null,
  payment_settings:
   { payment_method_options:
      { acss_debit: null,
        bancontact: null,
        card: [Object],
        customer_balance: null,
        konbini: null,
        sepa_debit: null,
        us_bank_account: null },
     payment_method_types: [ 'card' ],
     save_default_payment_method: 'off' },
  pending_invoice_item_interval: null,
  pending_setup_intent: null,
  pending_update: null,
  plan:
   { id: 'price_1Rlpa8GKbo6Mo0fo0guncY75',
     object: 'plan',
     active: false,
     amount: 19900,
     amount_decimal: '19900',
     billing_scheme: 'per_unit',
     created: **********,
     currency: 'myr',
     interval: 'month',
     interval_count: 1,
     livemode: false,
     metadata: {},
     meter: null,
     nickname: null,
     product: 'prod_ShE2YgqMekUlp3',
     tiers_mode: null,
     transform_usage: null,
     trial_period_days: null,
     usage_type: 'licensed' },
  quantity: 1,
  schedule: null,
  start_date: **********,
  status: 'trialing',
  test_clock: null,
  transfer_data: null,
  trial_end: **********,
  trial_settings: { end_behavior: { missing_payment_method: 'create_invoice' } },
  trial_start: ********** }
🎯 Stripe webhook called!
✅ Received Stripe webhook: setup_intent.created
❓ Unhandled event type: setup_intent.created
🎯 Stripe webhook called!
✅ Received Stripe webhook: setup_intent.succeeded
❓ Unhandled event type: setup_intent.succeeded
🎯 Stripe webhook called!
✅ Received Stripe webhook: checkout.session.completed
🎯 Processing checkout.session.completed event
🏫 Processing checkout completion: cs_test_b1J4zn3YRJmuy40PeNxWu8CeRutJOX1UM5B0f795w8UI1I2xbP3BNXaDTZ
📋 School data: { schoolCode: 'xba12234',
  schoolName: 'SK Tiong Widu',
  adminEmail: '<EMAIL>' }
🏗️ Creating school account: xba12234
🎯 Stripe webhook called!
✅ Received Stripe webhook: invoice.created
📄 Invoice created: { id: 'in_1RlpaTGKbo6Mo0foakh71mhD',
  object: 'invoice',
  account_country: 'MY',
  account_name: 'Rphmate',
  account_tax_ids: null,
  amount_due: 0,
  amount_overpaid: 0,
  amount_paid: 0,
  amount_remaining: 0,
  amount_shipping: 0,
  application: null,
  attempt_count: 0,
  attempted: true,
  auto_advance: false,
  automatic_tax:
   { disabled_reason: null,
     enabled: false,
     liability: null,
     provider: null,
     status: null },
  automatically_finalizes_at: null,
  billing_reason: 'subscription_create',
  collection_method: 'charge_automatically',
  created: **********,
  currency: 'myr',
  custom_fields: null,
  customer: 'cus_ShAmVA6eWoM39X',
  customer_address:
   { city: 'Tambunan',
     country: 'MY',
     line1: 'Kg. Tondulu',
     line2: null,
     postal_code: '89657',
     state: 'Sabah' },
  customer_email: '<EMAIL>',
  customer_name: 'Courtney V Sunggip',
  customer_phone: null,
  customer_shipping: null,
  customer_tax_exempt: 'none',
  customer_tax_ids: [],
  default_payment_method: null,
  default_source: null,
  default_tax_rates: [],
  description: null,
  discounts: [],
  due_date: null,
  effective_at: **********,
  ending_balance: 0,
  footer: null,
  from_invoice: null,
  hosted_invoice_url:
   'https://invoice.stripe.com/i/acct_1RkFYfGKbo6Mo0fo/test_YWNjdF8xUmtGWWZHS2JvNk1vMGZvLF9TaEUyemdubXh5VFJjaHZHM1cyc1duTEFSeldPTGU3LDE0MzI5MDk3Ng0200xzoSy1Cm?s=ap',
  invoice_pdf:
   'https://pay.stripe.com/invoice/acct_1RkFYfGKbo6Mo0fo/test_YWNjdF8xUmtGWWZHS2JvNk1vMGZvLF9TaEUyemdubXh5VFJjaHZHM1cyc1duTEFSeldPTGU3LDE0MzI5MDk3Ng0200xzoSy1Cm/pdf?s=ap',
  issuer: { type: 'self' },
  last_finalization_error: null,
  latest_revision: null,
  lines:
   { object: 'list',
     data: [ [Object] ],
     has_more: false,
     total_count: 1,
     url: '/v1/invoices/in_1RlpaTGKbo6Mo0foakh71mhD/lines' },
  livemode: false,
  metadata: {},
  next_payment_attempt: null,
  number: 'QDCHVQSF-0006',
  on_behalf_of: null,
  parent:
   { quote_details: null,
     subscription_details: { metadata: [Object], subscription: 'sub_1RlpaUGKbo6Mo0fooicpxJ4i' },
     type: 'subscription_details' },
  payment_settings:
   { default_mandate: null,
     payment_method_options:
      { acss_debit: null,
        bancontact: null,
        card: [Object],
        customer_balance: null,
        konbini: null,
        sepa_debit: null,
        us_bank_account: null },
     payment_method_types: [ 'card' ] },
  period_end: **********,
  period_start: **********,
  post_payment_credit_notes_amount: 0,
  pre_payment_credit_notes_amount: 0,
  receipt_number: null,
  rendering: null,
  shipping_cost: null,
  shipping_details: null,
  starting_balance: 0,
  statement_descriptor: null,
  status: 'paid',
  status_transitions:
   { finalized_at: **********,
     marked_uncollectible_at: null,
     paid_at: **********,
     voided_at: null },
  subtotal: 0,
  subtotal_excluding_tax: 0,
  test_clock: null,
  total: 0,
  total_discount_amounts: [],
  total_excluding_tax: 0,
  total_pretax_credit_amounts: [],
  total_taxes: [],
  webhooks_delivered_at: null }
🎯 Stripe webhook called!
✅ Received Stripe webhook: invoice.finalized
📄 Invoice finalized: { id: 'in_1RlpaTGKbo6Mo0foakh71mhD',
  object: 'invoice',
  account_country: 'MY',
  account_name: 'Rphmate',
  account_tax_ids: null,
  amount_due: 0,
  amount_overpaid: 0,
  amount_paid: 0,
  amount_remaining: 0,
  amount_shipping: 0,
  application: null,
  attempt_count: 0,
  attempted: true,
  auto_advance: false,
  automatic_tax:
   { disabled_reason: null,
     enabled: false,
     liability: null,
     provider: null,
     status: null },
  automatically_finalizes_at: null,
  billing_reason: 'subscription_create',
  collection_method: 'charge_automatically',
  created: **********,
  currency: 'myr',
  custom_fields: null,
  customer: 'cus_ShAmVA6eWoM39X',
  customer_address:
   { city: 'Tambunan',
     country: 'MY',
     line1: 'Kg. Tondulu',
     line2: null,
     postal_code: '89657',
     state: 'Sabah' },
  customer_email: '<EMAIL>',
  customer_name: 'Courtney V Sunggip',
  customer_phone: null,
  customer_shipping: null,
  customer_tax_exempt: 'none',
  customer_tax_ids: [],
  default_payment_method: null,
  default_source: null,
  default_tax_rates: [],
  description: null,
  discounts: [],
  due_date: null,
  effective_at: **********,
  ending_balance: 0,
  footer: null,
  from_invoice: null,
  hosted_invoice_url:
   'https://invoice.stripe.com/i/acct_1RkFYfGKbo6Mo0fo/test_YWNjdF8xUmtGWWZHS2JvNk1vMGZvLF9TaEUyemdubXh5VFJjaHZHM1cyc1duTEFSeldPTGU3LDE0MzI5MDk3Ng0200xzoSy1Cm?s=ap',
  invoice_pdf:
   'https://pay.stripe.com/invoice/acct_1RkFYfGKbo6Mo0fo/test_YWNjdF8xUmtGWWZHS2JvNk1vMGZvLF9TaEUyemdubXh5VFJjaHZHM1cyc1duTEFSeldPTGU3LDE0MzI5MDk3Ng0200xzoSy1Cm/pdf?s=ap',
  issuer: { type: 'self' },
  last_finalization_error: null,
  latest_revision: null,
  lines:
   { object: 'list',
     data: [ [Object] ],
     has_more: false,
     total_count: 1,
     url: '/v1/invoices/in_1RlpaTGKbo6Mo0foakh71mhD/lines' },
  livemode: false,
  metadata: {},
  next_payment_attempt: null,
  number: 'QDCHVQSF-0006',
  on_behalf_of: null,
  parent:
   { quote_details: null,
     subscription_details: { metadata: [Object], subscription: 'sub_1RlpaUGKbo6Mo0fooicpxJ4i' },
     type: 'subscription_details' },
  payment_settings:
   { default_mandate: null,
     payment_method_options:
      { acss_debit: null,
        bancontact: null,
        card: [Object],
        customer_balance: null,
        konbini: null,
        sepa_debit: null,
        us_bank_account: null },
     payment_method_types: [ 'card' ] },
  period_end: **********,
  period_start: **********,
  post_payment_credit_notes_amount: 0,
  pre_payment_credit_notes_amount: 0,
  receipt_number: null,
  rendering: null,
  shipping_cost: null,
  shipping_details: null,
  starting_balance: 0,
  statement_descriptor: null,
  status: 'paid',
  status_transitions:
   { finalized_at: **********,
     marked_uncollectible_at: null,
     paid_at: **********,
     voided_at: null },
  subtotal: 0,
  subtotal_excluding_tax: 0,
  test_clock: null,
  total: 0,
  total_discount_amounts: [],
  total_excluding_tax: 0,
  total_pretax_credit_amounts: [],
  total_taxes: [],
  webhooks_delivered_at: null }
🎯 Stripe webhook called!
✅ Received Stripe webhook: invoice.paid
💰 Invoice paid: { id: 'in_1RlpaTGKbo6Mo0foakh71mhD',
  object: 'invoice',
  account_country: 'MY',
  account_name: 'Rphmate',
  account_tax_ids: null,
  amount_due: 0,
  amount_overpaid: 0,
  amount_paid: 0,
  amount_remaining: 0,
  amount_shipping: 0,
  application: null,
  attempt_count: 0,
  attempted: true,
  auto_advance: false,
  automatic_tax:
   { disabled_reason: null,
     enabled: false,
     liability: null,
     provider: null,
     status: null },
  automatically_finalizes_at: null,
  billing_reason: 'subscription_create',
  collection_method: 'charge_automatically',
  created: **********,
  currency: 'myr',
  custom_fields: null,
  customer: 'cus_ShAmVA6eWoM39X',
  customer_address:
   { city: 'Tambunan',
     country: 'MY',
     line1: 'Kg. Tondulu',
     line2: null,
     postal_code: '89657',
     state: 'Sabah' },
  customer_email: '<EMAIL>',
  customer_name: 'Courtney V Sunggip',
  customer_phone: null,
  customer_shipping: null,
  customer_tax_exempt: 'none',
  customer_tax_ids: [],
  default_payment_method: null,
  default_source: null,
  default_tax_rates: [],
  description: null,
  discounts: [],
  due_date: null,
  effective_at: **********,
  ending_balance: 0,
  footer: null,
  from_invoice: null,
  hosted_invoice_url:
   'https://invoice.stripe.com/i/acct_1RkFYfGKbo6Mo0fo/test_YWNjdF8xUmtGWWZHS2JvNk1vMGZvLF9TaEUyemdubXh5VFJjaHZHM1cyc1duTEFSeldPTGU3LDE0MzI5MDk3Ng0200xzoSy1Cm?s=ap',
  invoice_pdf:
   'https://pay.stripe.com/invoice/acct_1RkFYfGKbo6Mo0fo/test_YWNjdF8xUmtGWWZHS2JvNk1vMGZvLF9TaEUyemdubXh5VFJjaHZHM1cyc1duTEFSeldPTGU3LDE0MzI5MDk3Ng0200xzoSy1Cm/pdf?s=ap',
  issuer: { type: 'self' },
  last_finalization_error: null,
  latest_revision: null,
  lines:
   { object: 'list',
     data: [ [Object] ],
     has_more: false,
     total_count: 1,
     url: '/v1/invoices/in_1RlpaTGKbo6Mo0foakh71mhD/lines' },
  livemode: false,
  metadata: {},
  next_payment_attempt: null,
  number: 'QDCHVQSF-0006',
  on_behalf_of: null,
  parent:
   { quote_details: null,
     subscription_details: { metadata: [Object], subscription: 'sub_1RlpaUGKbo6Mo0fooicpxJ4i' },
     type: 'subscription_details' },
  payment_settings:
   { default_mandate: null,
     payment_method_options:
      { acss_debit: null,
        bancontact: null,
        card: [Object],
        customer_balance: null,
        konbini: null,
        sepa_debit: null,
        us_bank_account: null },
     payment_method_types: [ 'card' ] },
  period_end: **********,
  period_start: **********,
  post_payment_credit_notes_amount: 0,
  pre_payment_credit_notes_amount: 0,
  receipt_number: null,
  rendering: null,
  shipping_cost: null,
  shipping_details: null,
  starting_balance: 0,
  statement_descriptor: null,
  status: 'paid',
  status_transitions:
   { finalized_at: **********,
     marked_uncollectible_at: null,
     paid_at: **********,
     voided_at: null },
  subtotal: 0,
  subtotal_excluding_tax: 0,
  test_clock: null,
  total: 0,
  total_discount_amounts: [],
  total_excluding_tax: 0,
  total_pretax_credit_amounts: [],
  total_taxes: [],
  webhooks_delivered_at: null }
🎯 Stripe webhook called!
✅ Received Stripe webhook: invoice.payment_succeeded
✅ Invoice payment succeeded - checking for school creation
💰 Processing invoice payment succeeded: in_1RlpaTGKbo6Mo0foakh71mhD
❌ No subscription found in invoice

 ERROR  Error creating admin user: A user with this email address has already been registered

    at handleError (node_modules\@supabase\auth-js\src\lib\fetch.ts:102:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async _handleRequest (node_modules\@supabase\auth-js\src\lib\fetch.ts:195:5)
    at async _request (node_modules\@supabase\auth-js\src\lib\fetch.ts:157:16)
    at async GoTrueAdminApi.createUser (node_modules\@supabase\auth-js\src\GoTrueAdminApi.ts:154:14)
    at async createSchoolAccount (server\api\webhooks\stripe.post.ts:231:1)
    at async handleCheckoutCompleted (server\api\webhooks\stripe.post.ts:122:1)
    at async Object.handler (server\api\webhooks\stripe.post.ts:59:1)
    at async /D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2003:19
    at async Object.callAsync (/D:/XAMPP/htdocs/erphv9/node_modules/unctx/dist/index.mjs:72:16)


 ERROR  ❌ Error in createSchoolAccount: Failed to create admin user

    at createSchoolAccount (server\api\webhooks\stripe.post.ts:243:1)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async handleCheckoutCompleted (server\api\webhooks\stripe.post.ts:122:1)
    at async Object.handler (server\api\webhooks\stripe.post.ts:59:1)
    at async /D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2003:19
    at async Object.callAsync (/D:/XAMPP/htdocs/erphv9/node_modules/unctx/dist/index.mjs:72:16)
    at async Server.toNodeHandle (/D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2295:7)


 ERROR  ❌ Error handling checkout completion: Failed to create admin user

    at createSchoolAccount (server\api\webhooks\stripe.post.ts:243:1)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async handleCheckoutCompleted (server\api\webhooks\stripe.post.ts:122:1)
    at async Object.handler (server\api\webhooks\stripe.post.ts:59:1)
    at async /D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2003:19
    at async Object.callAsync (/D:/XAMPP/htdocs/erphv9/node_modules/unctx/dist/index.mjs:72:16)
    at async Server.toNodeHandle (/D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2295:7)


 ERROR  ❌ Webhook error: Failed to create admin user

    at createSchoolAccount (server\api\webhooks\stripe.post.ts:243:1)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async handleCheckoutCompleted (server\api\webhooks\stripe.post.ts:122:1)
    at async Object.handler (server\api\webhooks\stripe.post.ts:59:1)
    at async /D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2003:19
    at async Object.callAsync (/D:/XAMPP/htdocs/erphv9/node_modules/unctx/dist/index.mjs:72:16)
    at async Server.toNodeHandle (/D:/XAMPP/htdocs/erphv9/node_modules/h3/dist/index.mjs:2295:7)