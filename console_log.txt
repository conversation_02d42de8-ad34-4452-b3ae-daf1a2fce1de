browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  override existing env vars with { override: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=********:48 ssr 🏫 [SSR] Detected school subdomain: xba12234 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:54 🏫 Detected school subdomain: xba12234
a-subdomain.global.ts:233 Fetch finished loading: POST "http://xba12234.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=********:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=********:258
$fetch2 @ ofetch.03887fc3.mjs?v=********:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:233
handleSecureSchoolRouting @ a-subdomain.global.ts:194
(anonymous) @ a-subdomain.global.ts:29
executeAsync @ index.mjs?v=********:110
(anonymous) @ a-subdomain.global.ts:29
(anonymous) @ router.js?v=********:169
fn @ nuxt.js?v=********:216
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
callWithNuxt @ nuxt.js?v=********:222
(anonymous) @ nuxt.js?v=********:38
run @ reactivity.esm-bundler.js?v=********:67
runWithContext @ nuxt.js?v=********:38
(anonymous) @ router.js?v=********:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=********:1385
runWithContext @ vue-router.mjs?v=********:1360
(anonymous) @ vue-router.mjs?v=********:1385
(anonymous) @ vue-router.mjs?v=********:1363
runWithContext @ runtime-core.esm-bundler.js?v=********:4016
runWithContext @ vue-router.mjs?v=********:2426
(anonymous) @ vue-router.mjs?v=********:2698
Promise.then
(anonymous) @ vue-router.mjs?v=********:2698
runGuardQueue @ vue-router.mjs?v=********:2698
(anonymous) @ vue-router.mjs?v=********:2445
Promise.then
navigate @ vue-router.mjs?v=********:2439
pushWithRedirect @ vue-router.mjs?v=********:2372
push @ vue-router.mjs?v=********:2308
replace @ vue-router.mjs?v=********:2311
(anonymous) @ router.js?v=********:223
_function @ index.mjs?v=********:133
(anonymous) @ index.mjs?v=********:48
(anonymous) @ index.mjs?v=********:48
app:created
serialTaskCaller @ index.mjs?v=********:46
callHookWith @ index.mjs?v=********:198
callHook @ index.mjs?v=********:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
devtools.client.js?v=********:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
runtime-core.esm-bundler.js?v=********:7060 <Suspense> is an experimental feature and its API will likely change.
runtime-core.esm-bundler.js?v=********:50 [Vue warn]: Extraneous non-props attributes (school-context) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes. 
  at <Sidebar is-mobile-open=false school-context= {school: null, membership: null, isLoading: true, error: null} onClose=fn<closeMobileSidebar>  ... > 
  at <School ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="school" layoutProps= {ref: RefImpl} name="school" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="school" name="school"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
renderComponentRoot @ runtime-core.esm-bundler.js?v=********:6621
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5312
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=********:1998
hydrateElement @ runtime-core.esm-bundler.js?v=********:1879
hydrateNode @ runtime-core.esm-bundler.js?v=********:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=********:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5331
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
(anonymous) @ runtime-core.esm-bundler.js?v=********:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
useSchoolContext.ts:116 ✅ School context loaded: SK Tiong Widu (xba12234)
school.vue:223 🏫 School context refreshed: SK Tiong Widu
school.vue:188 [Vue warn]: Extraneous non-props attributes (school-context) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes. 
  at <Sidebar is-mobile-open=false school-context= {school: {…}, membership: {…}, isLoading: false, error: null} onClose=fn<closeMobileSidebar>  ... > 
  at <School ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <AsyncComponentWrapper ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <LayoutLoader key="school" layoutProps= {ref: RefImpl} name="school" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="school" name="school"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=********:50
renderComponentRoot @ runtime-core.esm-bundler.js?v=********:6621
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5424
run @ reactivity.esm-bundler.js?v=********:207
updateComponent @ runtime-core.esm-bundler.js?v=********:5285
processComponent @ runtime-core.esm-bundler.js?v=********:5220
patch @ runtime-core.esm-bundler.js?v=********:4725
patchBlockChildren @ runtime-core.esm-bundler.js?v=********:5079
patchElement @ runtime-core.esm-bundler.js?v=********:4997
processElement @ runtime-core.esm-bundler.js?v=********:4856
patch @ runtime-core.esm-bundler.js?v=********:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5433
run @ reactivity.esm-bundler.js?v=********:207
runIfDirty @ reactivity.esm-bundler.js?v=********:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
flushJobs @ runtime-core.esm-bundler.js?v=********:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queueJob @ runtime-core.esm-bundler.js?v=********:314
effect2.scheduler @ runtime-core.esm-bundler.js?v=********:5475
trigger @ reactivity.esm-bundler.js?v=********:235
endBatch @ reactivity.esm-bundler.js?v=********:293
notify @ reactivity.esm-bundler.js?v=********:566
trigger @ reactivity.esm-bundler.js?v=********:540
set value @ reactivity.esm-bundler.js?v=********:1412
refreshSchoolContext @ school.vue:188
await in refreshSchoolContext
(anonymous) @ school.vue:266
(anonymous) @ runtime-core.esm-bundler.js?v=********:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=********:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=********:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=********:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=********:382
flushJobs @ runtime-core.esm-bundler.js?v=********:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=********:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=********:333
resolve @ runtime-core.esm-bundler.js?v=********:7164
resolve @ runtime-core.esm-bundler.js?v=********:7171
(anonymous) @ runtime-core.esm-bundler.js?v=********:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=********:7234
mountComponent @ runtime-core.esm-bundler.js?v=********:5250
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=********:7311
hydrateNode @ runtime-core.esm-bundler.js?v=********:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=********:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=********:5337
run @ reactivity.esm-bundler.js?v=********:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=********:5481
mountComponent @ runtime-core.esm-bundler.js?v=********:5256
hydrateNode @ runtime-core.esm-bundler.js?v=********:1799
hydrate @ runtime-core.esm-bundler.js?v=********:1673
mount @ runtime-core.esm-bundler.js?v=********:3959
app.mount @ runtime-dom.esm-bundler.js?v=********:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
