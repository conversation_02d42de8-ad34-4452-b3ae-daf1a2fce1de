-- eRPH+ Database Backup
-- Generated on: 2025-07-13
-- Project ID: nhgyywlfopodxomxbegx
-- Database: eRPH+

-- This backup includes schema and data for all tables

-- ============================================================================
-- SCHEMA BACKUP
-- ============================================================================

-- Get complete schema structure
SELECT 'Creating schema backup...' as status;

-- Export table structures
\echo 'Exporting table structures...'

-- ============================================================================
-- DATA BACKUP QUERIES
-- ============================================================================

-- Export all table data
\echo 'Exporting table data...'

-- academic_calendar_documents
\copy (SELECT * FROM academic_calendar_documents) TO 'academic_calendar_documents.csv' WITH CSV HEADER;

-- annual_calendar_events  
\copy (SELECT * FROM annual_calendar_events) TO 'annual_calendar_events.csv' WITH CSV HEADER;

-- dskp_documents
\copy (SELECT * FROM dskp_documents) TO 'dskp_documents.csv' WITH CSV HEADER;

-- global_settings
\copy (SELECT * FROM global_settings) TO 'global_settings.csv' WITH CSV HEADER;

-- items
\copy (SELECT * FROM items) TO 'items.csv' WITH CSV HEADER;

-- jadual_pencerapan
\copy (SELECT * FROM jadual_pencerapan) TO 'jadual_pencerapan.csv' WITH CSV HEADER;

-- lesson_plan_detailed_reflections
\copy (SELECT * FROM lesson_plan_detailed_reflections) TO 'lesson_plan_detailed_reflections.csv' WITH CSV HEADER;

-- lesson_plans
\copy (SELECT * FROM lesson_plans) TO 'lesson_plans.csv' WITH CSV HEADER;

-- observation_schedules
\copy (SELECT * FROM observation_schedules) TO 'observation_schedules.csv' WITH CSV HEADER;

-- profiles
\copy (SELECT * FROM profiles) TO 'profiles.csv' WITH CSV HEADER;

-- reflection_templates
\copy (SELECT * FROM reflection_templates) TO 'reflection_templates.csv' WITH CSV HEADER;

-- rph_weeks
\copy (SELECT * FROM rph_weeks) TO 'rph_weeks.csv' WITH CSV HEADER;

-- rpt_documents
\copy (SELECT * FROM rpt_documents) TO 'rpt_documents.csv' WITH CSV HEADER;

-- subjects
\copy (SELECT * FROM subjects) TO 'subjects.csv' WITH CSV HEADER;

-- teacher_activities
\copy (SELECT * FROM teacher_activities) TO 'teacher_activities.csv' WITH CSV HEADER;

-- teacher_observer_assignments
\copy (SELECT * FROM teacher_observer_assignments) TO 'teacher_observer_assignments.csv' WITH CSV HEADER;

-- teacher_schedules
\copy (SELECT * FROM teacher_schedules) TO 'teacher_schedules.csv' WITH CSV HEADER;

-- teacher_tasks
\copy (SELECT * FROM teacher_tasks) TO 'teacher_tasks.csv' WITH CSV HEADER;

-- tidak_terlaksana
\copy (SELECT * FROM tidak_terlaksana) TO 'tidak_terlaksana.csv' WITH CSV HEADER;

-- timetable_entries
\copy (SELECT * FROM timetable_entries) TO 'timetable_entries.csv' WITH CSV HEADER;

-- tindakan_susulan
\copy (SELECT * FROM tindakan_susulan) TO 'tindakan_susulan.csv' WITH CSV HEADER;

-- user_preferences
\copy (SELECT * FROM user_preferences) TO 'user_preferences.csv' WITH CSV HEADER;

-- user_reflection_template_preferences
\copy (SELECT * FROM user_reflection_template_preferences) TO 'user_reflection_template_preferences.csv' WITH CSV HEADER;

-- user_week_submissions
\copy (SELECT * FROM user_week_submissions) TO 'user_week_submissions.csv' WITH CSV HEADER;

\echo 'Backup completed successfully!'
\echo 'All table data has been exported to CSV files.'
