<template>
  <Modal :is-open="isOpen" :title="isEditing ? `Edit Template: ${template?.name || ''}` : 'Cipta Template Baharu'"
    @update:is-open="$emit('update:is-open', $event)" size="xl">
    <form @submit.prevent="saveTemplate" class="space-y-6">
      <!-- Basic Information -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Maklumat Asas
        </h3>

        <!-- Template Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Nama Template <span class="text-red-500">*</span>
          </label>
          <input v-model="formData.name" type="text" required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Contoh: Template Pembelajaran Kumpulan" />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Penerangan
          </label>
          <textarea v-model="formData.description" rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Terangkan tujuan dan kegunaan template ini..."></textarea>
        </div>

        <!-- Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Kategori <span class="text-red-500">*</span>
          </label>
          <select v-model="formData.category" required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
            <option value="">Pilih Kategori</option>
            <option v-for="(label, value) in categoryOptions" :key="value" :value="value">
              {{ label }}
            </option>
          </select>
        </div>
      </div>

      <!-- Prompts Section -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            Soalan Panduan
          </h3>
          <UiBaseButton type="button" @click="addPrompt" variant="outline" size="sm" prepend-icon="heroicons:plus">
            Tambah Soalan
          </UiBaseButton>
        </div>

        <div v-if="promptEntries.length === 0"
          class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <Icon name="heroicons:chat-bubble-left-right" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p class="text-gray-500 dark:text-gray-400">
            Belum ada soalan panduan. Klik "Tambah Soalan" untuk mula.
          </p>
        </div>

        <div v-else class="space-y-4">
          <div v-for="(prompt, index) in promptEntries" :key="index"
            class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-start justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                Soalan {{ index + 1 }}
              </h4>
              <button type="button" @click="removePrompt(index)"
                class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300">
                <Icon name="heroicons:trash" class="w-4 h-4" />
              </button>
            </div>

            <!-- Field Key -->
            <div class="mb-3">
              <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Kunci Medan
              </label>
              <select v-model="prompt.key"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
                <option value="">Pilih medan</option>
                <option v-for="field in availableFields" :key="field.key" :value="field.key"
                  :disabled="isFieldUsed(field.key, index)">
                  {{ field.label }}
                </option>
              </select>
            </div>

            <!-- Prompt Text -->
            <div>
              <label class="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Soalan Panduan
              </label>
              <textarea v-model="prompt.text" rows="2"
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                placeholder="Tulis soalan panduan untuk medan ini..."></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- Default Values Section -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Nilai Lalai
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Tetapkan nilai lalai yang akan digunakan apabila template ini dipilih.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Overall Rating -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Penilaian Keseluruhan
            </label>
            <select v-model="formData.default_values.overall_rating"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option :value="1">1 - Sangat Lemah</option>
              <option :value="2">2 - Lemah</option>
              <option :value="3">3 - Sederhana</option>
              <option :value="4">4 - Baik</option>
              <option :value="5">5 - Sangat Baik</option>
            </select>
          </div>

          <!-- Activity Effectiveness -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Keberkesanan Aktiviti
            </label>
            <select v-model="formData.default_values.activity_effectiveness"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option :value="1">1 - Sangat Lemah</option>
              <option :value="2">2 - Lemah</option>
              <option :value="3">3 - Sederhana</option>
              <option :value="4">4 - Baik</option>
              <option :value="5">5 - Sangat Baik</option>
            </select>
          </div>

          <!-- Student Engagement -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Penglibatan Pelajar
            </label>
            <select v-model="formData.default_values.student_engagement"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option :value="1">1 - Sangat Lemah</option>
              <option :value="2">2 - Lemah</option>
              <option :value="3">3 - Sederhana</option>
              <option :value="4">4 - Baik</option>
              <option :value="5">5 - Sangat Baik</option>
            </select>
          </div>

          <!-- Time Management -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Pengurusan Masa
            </label>
            <select v-model="formData.default_values.time_management"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option value="early">Awal</option>
              <option value="on_time">Tepat Masa</option>
              <option value="late">Lewat</option>
              <option value="not_applicable">Tidak Berkenaan</option>
            </select>
          </div>

          <!-- Resource Adequacy -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Kecukupan Sumber
            </label>
            <select v-model="formData.default_values.resource_adequacy"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option value="inadequate">Tidak Mencukupi</option>
              <option value="adequate">Mencukupi</option>
              <option value="excellent">Sangat Baik</option>
              <option value="not_applicable">Tidak Berkenaan</option>
            </select>
          </div>

          <!-- Objectives Achieved -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Objektif Tercapai
            </label>
            <select v-model="formData.default_values.objectives_achieved"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100">
              <option :value="true">Ya</option>
              <option :value="false">Tidak</option>
            </select>
          </div>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <UiBaseButton @click="$emit('update:is-open', false)" variant="outline">
          Batal
        </UiBaseButton>
        <UiBaseButton @click="saveTemplate" variant="primary" :disabled="!isFormValid || saving" :loading="saving">
          {{ isEditing ? 'Kemaskini' : 'Simpan' }} Template
        </UiBaseButton>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useReflectionTemplates } from '~/composables/useReflectionTemplates';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import type { ReflectionTemplate, ReflectionTemplateFormData, ReflectionTemplateCategory } from '~/types/reflections';
import Modal from '~/components/ui/composite/Modal.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';

interface Props {
  isOpen: boolean;
  template?: ReflectionTemplate | null;
}

interface Emits {
  (e: 'update:is-open', value: boolean): void;
  (e: 'template-saved'): void;
}

const props = withDefaults(defineProps<Props>(), {
  template: null
});

const emit = defineEmits<Emits>();

// Composables
const { createUserTemplate, updateUserTemplate } = useReflectionTemplates();

// State
const saving = ref(false);
const formData = ref<ReflectionTemplateFormData>({
  name: '',
  description: '',
  category: '' as ReflectionTemplateCategory,
  prompts: {},
  default_values: {
    overall_rating: 4,
    activity_effectiveness: 4,
    student_engagement: 4,
    time_management: 'on_time',
    resource_adequacy: 'adequate',
    objectives_achieved: true
  }
});

interface PromptEntry {
  key: string;
  text: string;
}

const promptEntries = ref<PromptEntry[]>([]);

// Available fields for prompts
const availableFields = [
  { key: 'challenges_faced', label: 'Cabaran Dihadapi' },
  { key: 'successful_strategies', label: 'Strategi Berjaya' },
  { key: 'improvements_needed', label: 'Penambahbaikan Diperlukan' },
  { key: 'additional_notes', label: 'Catatan Tambahan' }
];

// Category options
const categoryOptions = TEMPLATE_CATEGORY_LABELS;

// Computed
const isEditing = computed(() => !!props.template?.id);

const isFormValid = computed(() => {
  return formData.value.name.trim() !== '' &&
    formData.value.category.length > 0 &&
    promptEntries.value.length > 0 &&
    promptEntries.value.every(p => p.key && p.text.trim());
});

// Methods
const addPrompt = () => {
  promptEntries.value.push({ key: '', text: '' });
};

const removePrompt = (index: number) => {
  promptEntries.value.splice(index, 1);
  updatePromptsFromEntries();
};

const isFieldUsed = (fieldKey: string, currentIndex: number): boolean => {
  return promptEntries.value.some((entry, index) =>
    entry.key === fieldKey && index !== currentIndex
  );
};

const updatePromptsFromEntries = () => {
  const prompts: Record<string, string> = {};
  promptEntries.value.forEach(entry => {
    if (entry.key && entry.text.trim()) {
      prompts[entry.key] = entry.text.trim();
    }
  });
  formData.value.prompts = prompts;
};

const loadTemplateData = () => {
  if (props.template) {
    formData.value = {
      name: props.template.name,
      description: props.template.description || '',
      category: props.template.category,
      prompts: { ...props.template.prompts },
      default_values: { ...props.template.default_values }
    };

    // Convert prompts to entries
    promptEntries.value = Object.entries(props.template.prompts).map(([key, text]) => ({
      key,
      text
    }));
  } else {
    // Reset form for new template
    formData.value = {
      name: '',
      description: '',
      category: '' as ReflectionTemplateCategory,
      prompts: {},
      default_values: {
        overall_rating: 4,
        activity_effectiveness: 4,
        student_engagement: 4,
        time_management: 'on_time',
        resource_adequacy: 'adequate',
        objectives_achieved: true
      }
    };
    promptEntries.value = [];
  }
};

const saveTemplate = async () => {
  if (!isFormValid.value) return;

  try {
    saving.value = true;
    updatePromptsFromEntries();

    if (isEditing.value && props.template?.id) {
      await updateUserTemplate(props.template.id, formData.value);
    } else {
      await createUserTemplate(formData.value);
    }

    emit('template-saved');
  } catch (err) {
    console.error('Error saving template:', err);
  } finally {
    saving.value = false;
  }
};

// Watch for template changes
watch(() => props.template, loadTemplateData, { immediate: true });

// Watch prompt entries to update form data
watch(promptEntries, updatePromptsFromEntries, { deep: true });
</script>
