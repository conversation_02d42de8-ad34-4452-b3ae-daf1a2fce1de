import { ref, computed } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'
import type { Database } from '~/types/supabase'

export interface TeacherTask {
  id: string
  user_id: string
  category: 'pentadbiran' | 'kokurikulum' | 'hal_ehwal_murid'
  task_description: string
  is_completed: boolean
  priority: 'rendah' | 'sederhana' | 'tinggi'
  due_date: string | null
  notes: string | null
  created_at: string
  updated_at: string
}

export function useTeacherTasks() {
  const client = useSupabaseClient<Database>()
  const user = useSupabaseUser()
  
  const tasks = ref<TeacherTask[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed properties for different task categories
  const administrativeTasks = computed(() => 
    tasks.value.filter(task => task.category === 'pentadbiran')
  )

  const cocurricularTasks = computed(() => 
    tasks.value.filter(task => task.category === 'kokurikulum')
  )

  const studentAffairsTasks = computed(() => 
    tasks.value.filter(task => task.category === 'hal_ehwal_murid')
  )

  // Fetch all tasks for the current user
  const fetchTasks = async (): Promise<TeacherTask[]> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return []
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await client
        .from('teacher_tasks')
        .select('*')
        .eq('user_id', user.value.id)
        .order('created_at', { ascending: false })

      if (fetchError) {
        error.value = fetchError.message
        return []
      }

      const fetchedTasks = (data || []) as TeacherTask[]
      tasks.value = fetchedTasks
      return fetchedTasks

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch tasks'
      console.error('Error fetching tasks:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // Create a new task
  const createTask = async (
    category: TeacherTask['category'], 
    description: string
  ): Promise<TeacherTask | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return null
    }

    if (!description.trim()) {
      error.value = 'Task description is required'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: insertError } = await client
        .from('teacher_tasks')
        .insert({
          user_id: user.value.id,
          category,
          task_description: description.trim(),
          is_completed: false,
          priority: 'sederhana'
        })
        .select()
        .single()

      if (insertError) {
        error.value = insertError.message
        return null
      }

      const newTask = data as TeacherTask
      tasks.value.unshift(newTask) // Add to beginning of array
      return newTask

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create task'
      console.error('Error creating task:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // Update an existing task
  const updateTask = async (
    taskId: string, 
    updates: Partial<Pick<TeacherTask, 'task_description' | 'is_completed' | 'priority' | 'due_date' | 'notes'>>
  ): Promise<TeacherTask | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: updateError } = await client
        .from('teacher_tasks')
        .update(updates)
        .eq('id', taskId)
        .eq('user_id', user.value.id) // Ensure user can only update their own tasks
        .select()
        .single()

      if (updateError) {
        error.value = updateError.message
        return null
      }

      const updatedTask = data as TeacherTask
      
      // Update local state
      const index = tasks.value.findIndex(task => task.id === taskId)
      if (index >= 0) {
        tasks.value[index] = updatedTask
      }

      return updatedTask

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update task'
      console.error('Error updating task:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // Delete a task
  const deleteTask = async (taskId: string): Promise<boolean> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { error: deleteError } = await client
        .from('teacher_tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.value.id) // Ensure user can only delete their own tasks

      if (deleteError) {
        error.value = deleteError.message
        return false
      }

      // Update local state
      tasks.value = tasks.value.filter(task => task.id !== taskId)
      return true

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete task'
      console.error('Error deleting task:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // Toggle task completion status
  const toggleTaskCompletion = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.find(t => t.id === taskId)
    if (!task) {
      error.value = 'Task not found'
      return false
    }

    const updatedTask = await updateTask(taskId, { 
      is_completed: !task.is_completed 
    })
    
    return updatedTask !== null
  }

  // Get tasks by category
  const getTasksByCategory = (category: TeacherTask['category']) => {
    return computed(() => tasks.value.filter(task => task.category === category))
  }

  // Get task statistics
  const taskStats = computed(() => {
    const total = tasks.value.length
    const completed = tasks.value.filter(task => task.is_completed).length
    const pending = total - completed
    
    const byCategory = {
      pentadbiran: tasks.value.filter(task => task.category === 'pentadbiran').length,
      kokurikulum: tasks.value.filter(task => task.category === 'kokurikulum').length,
      hal_ehwal_murid: tasks.value.filter(task => task.category === 'hal_ehwal_murid').length
    }

    return {
      total,
      completed,
      pending,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
      byCategory
    }
  })

  return {
    // State
    tasks,
    loading,
    error,
    
    // Computed
    administrativeTasks,
    cocurricularTasks,
    studentAffairsTasks,
    taskStats,
    
    // Methods
    fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    getTasksByCategory
  }
}
