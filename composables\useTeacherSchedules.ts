import { ref, computed } from 'vue'
import type {
  TeacherSchedule,
  TeacherScheduleFormData,
  TeacherScheduleWithDetails,
  DayOfWeek,
  ClassSubjectCombination,
  TeacherScheduleValidationErrors,
  TeacherScheduleDetails
} from '~/types/teacherSchedule'
import type { Database } from '~/types/supabase'

type TeacherScheduleRow = Database['public']['Tables']['teacher_schedules']['Row']
type TeacherScheduleInsert = Database['public']['Tables']['teacher_schedules']['Insert']
type TeacherScheduleUpdate = Database['public']['Tables']['teacher_schedules']['Update']

// Global state
const schedules = ref<TeacherSchedule[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// Helper functions
const transformSchedule = (row: TeacherScheduleRow): TeacherSchedule => ({
  ...row,
  schedule_details: row.schedule_details as TeacherScheduleDetails | null
})

const handleError = (err: unknown): string => {
  return err instanceof Error ? err.message : 'Unknown error'
}

const getCurrentUserId = async (client: any): Promise<string> => {
  const { data: { user } } = await client.auth.getUser()
  return user?.id || ''
}

export const useTeacherSchedules = () => {
  const { client } = useSupabase()

  // Day options for Malaysian education system
  const dayOptions = computed(() => [
    { value: 'ISNIN', label: 'Isnin' },
    { value: 'SELASA', label: 'Selasa' },
    { value: 'RABU', label: 'Rabu' },
    { value: 'KHAMIS', label: 'Khamis' },
    { value: 'JUMAAT', label: 'Jumaat' },
    { value: 'AHAD', label: 'Ahad' }
  ])

  // Get available class-subject combinations from user profile
  const getAvailableClassSubjects = (): ClassSubjectCombination[] => {
    const { data: profile } = useNuxtData('user-profile')

    if (!profile.value?.class_subjects) {
      return []
    }

    let classSubjects: any[] = []

    // Parse class_subjects if it's a string
    if (typeof profile.value.class_subjects === 'string') {
      try {
        classSubjects = JSON.parse(profile.value.class_subjects)
      } catch {
        return []
      }
    } else {
      classSubjects = profile.value.class_subjects as any[]
    }

    return classSubjects.map(cs => ({
      class_id: cs.class_id,
      subject_id: cs.subject_id,
      class_name: cs.className || cs.class_id,
      subject_name: cs.subject_name || cs.subject_id,
      combined_label: `${cs.className || cs.class_id} - ${cs.subject_name || cs.subject_id}`
    }))
  }

  // Database operations with consistent error handling
  const executeWithLoading = async <T>(
    operation: () => Promise<T>,
    defaultReturn?: T
  ): Promise<T> => {
    try {
      loading.value = true
      error.value = null
      return await operation()
    } catch (err) {
      error.value = handleError(err)
      if (defaultReturn !== undefined) return defaultReturn
      throw err
    } finally {
      loading.value = false
    }
  }

  // Fetch all teacher schedules for current user
  const fetchSchedules = async (): Promise<TeacherSchedule[]> => {
    return executeWithLoading(async () => {
      const { data, error: fetchError } = await client
        .from('teacher_schedules')
        .select('*')
        .order('created_at', { ascending: false })

      if (fetchError) throw fetchError

      const transformedData = (data || []).map(transformSchedule)
      schedules.value = transformedData
      return transformedData
    }, [])
  }

  // Fetch a single teacher schedule by lesson_plan_id
  const fetchScheduleByLessonPlanId = async (lessonPlanId: string): Promise<TeacherSchedule | null> => {
    return executeWithLoading(async () => {
      const { data, error: fetchError } = await client
        .from('teacher_schedules')
        .select('*')
        .eq('lesson_plan_id', lessonPlanId)
        .single()

      if (fetchError) {
        if (fetchError.code === 'PGRST116') return null // No rows found
        throw fetchError
      }

      return transformSchedule(data)
    }, null)
  }

  // Get schedules with class and subject details
  const getSchedulesWithDetails = (): TeacherScheduleWithDetails[] => {
    const uniqueCombinations = new Map<string, TeacherScheduleWithDetails>()

    schedules.value.forEach(schedule => {
      // Handle new structure (class_subjects)
      if (schedule.schedule_details?.class_subjects) {
        schedule.schedule_details.class_subjects.forEach(classSubject => {
          const key = `${classSubject.class_id}-${classSubject.subject_id}`

          if (!uniqueCombinations.has(key)) {
            uniqueCombinations.set(key, {
              ...schedule,
              class_name: classSubject.class_name,
              subject_name: classSubject.subject_name,
              days_scheduled: classSubject.days_scheduled,
              total_periods: classSubject.total_periods
            } as TeacherScheduleWithDetails)
          }
        })
      }
      // Handle legacy structure (periods)
      else if (schedule.schedule_details?.periods) {
        schedule.schedule_details.periods.forEach((period: any) => {
          const key = `${period.class_id}-${period.subject_id}`

          if (!uniqueCombinations.has(key)) {
            uniqueCombinations.set(key, {
              ...schedule,
              class_name: period.class_name || period.class_id,
              subject_name: period.subject_name || period.subject_id,
              days_scheduled: [],
              total_periods: 0
            } as TeacherScheduleWithDetails)
          }

          const existing = uniqueCombinations.get(key)!
          if (!existing.days_scheduled.includes(period.day)) {
            existing.days_scheduled.push(period.day)
          }
          existing.total_periods++
        })
      }
    })

    return Array.from(uniqueCombinations.values())
  }

  // Create a new teacher schedule
  const createSchedule = async (
    formData: TeacherScheduleFormData | any, // Accept both new and legacy formats
    lessonPlanId: string | null = null
  ): Promise<TeacherSchedule> => {
    return executeWithLoading(async () => {
      // Handle both new structure (class_subjects) and legacy structure (periods)
      let scheduleDetails: any
      if ('class_subjects' in formData) {
        // New structure
        scheduleDetails = { class_subjects: formData.class_subjects }
      } else if ('periods' in formData) {
        // Legacy structure
        scheduleDetails = { periods: formData.periods }
      } else {
        throw new Error('Invalid form data structure')
      }

      const insertData: TeacherScheduleInsert = {
        schedule_details: scheduleDetails,
        user_id: await getCurrentUserId(client),
        lesson_plan_id: lessonPlanId
      }

      const { data, error: createError } = await client
        .from('teacher_schedules')
        .insert(insertData)
        .select()
        .single()

      if (createError) throw createError

      const transformedData = transformSchedule(data)
      schedules.value = [transformedData, ...schedules.value]
      return transformedData
    })
  }

  // Update an existing teacher schedule
  const updateSchedule = async (
    id: string,
    formData: any // Accept both new and legacy formats
  ): Promise<TeacherSchedule> => {
    return executeWithLoading(async () => {
      // Handle both new structure (class_subjects) and legacy structure (periods)
      let scheduleDetails: any
      if ('class_subjects' in formData) {
        scheduleDetails = { class_subjects: formData.class_subjects }
      } else if ('periods' in formData) {
        scheduleDetails = { periods: formData.periods }
      } else {
        throw new Error('Invalid form data structure')
      }

      const updateData: TeacherScheduleUpdate = {
        schedule_details: scheduleDetails
      }

      const { data, error: updateError } = await client
        .from('teacher_schedules')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (updateError) throw updateError

      const transformedData = transformSchedule(data)

      // Update cache
      const index = schedules.value.findIndex(s => s.id === id)
      if (index !== -1) {
        schedules.value[index] = transformedData
      }

      return transformedData
    })
  }

  // Delete a teacher schedule
  const deleteSchedule = async (id: string): Promise<void> => {
    return executeWithLoading(async () => {
      const { error: deleteError } = await client
        .from('teacher_schedules')
        .delete()
        .eq('id', id)

      if (deleteError) throw deleteError

      // Update cache
      schedules.value = schedules.value.filter(s => s.id !== id)
    })
  }

  // Validate schedule form data (supports both new and legacy formats)
  const validateScheduleForm = (formData: any): TeacherScheduleValidationErrors | null => {
    const errors: TeacherScheduleValidationErrors = {}

    // Handle legacy format (periods)
    if ('periods' in formData) {
      if (!formData.periods?.length) {
        errors.general = ['Sila tambah sekurang-kurangnya satu tempoh jadual.']
        return errors
      }
    }
    // Handle new format (class_subjects)
    else if ('class_subjects' in formData) {
      if (!formData.class_subjects?.length) {
        errors.general = ['Sila tambah sekurang-kurangnya satu kombinasi kelas-subjek.']
        return errors
      }
    }
    else {
      errors.general = ['Format data tidak sah.']
      return errors
    }

    const addError = (message: string) => {
      if (!errors.general) errors.general = []
      errors.general.push(message)
    }

    // Handle legacy format validation
    if ('periods' in formData && formData.periods) {
      const seenPeriods = new Set<string>()

      formData.periods.forEach((period: any, index: number) => {
        const periodNum = index + 1

        if (!period.class_id) addError(`Tempoh ${periodNum}: Sila pilih kelas.`)
        if (!period.subject_id) addError(`Tempoh ${periodNum}: Sila pilih subjek.`)
        if (!period.day) addError(`Tempoh ${periodNum}: Sila pilih hari.`)

        const periodKey = `${period.class_id}-${period.subject_id}-${period.day}`
        if (seenPeriods.has(periodKey)) {
          addError(`Tempoh ${periodNum}: Kombinasi kelas, subjek, dan hari ini sudah wujud.`)
        } else {
          seenPeriods.add(periodKey)
        }
      })

      // Check for duplicates across existing schedules
      formData.periods.forEach((newPeriod: any) => {
        const isDuplicate = schedules.value.some(schedule =>
          schedule.schedule_details?.periods?.some((existingPeriod: any) =>
            existingPeriod.class_id === newPeriod.class_id &&
            existingPeriod.subject_id === newPeriod.subject_id &&
            existingPeriod.day === newPeriod.day
          )
        )

        if (isDuplicate) {
          const className = newPeriod.class_name || newPeriod.class_id
          const subjectName = newPeriod.subject_name || newPeriod.subject_id
          addError(`Kombinasi ${className} - ${subjectName} pada ${newPeriod.day} sudah wujud dalam jadual lain.`)
        }
      })
    }

    // Handle new format validation (class_subjects)
    if ('class_subjects' in formData && formData.class_subjects) {
      // For now, just basic validation - can be expanded later
      formData.class_subjects.forEach((classSubject: any, index: number) => {
        const csNum = index + 1
        if (!classSubject.class_id) addError(`Kelas-Subjek ${csNum}: Sila pilih kelas.`)
        if (!classSubject.subject_id) addError(`Kelas-Subjek ${csNum}: Sila pilih subjek.`)
        if (!classSubject.days_scheduled?.length) addError(`Kelas-Subjek ${csNum}: Sila pilih sekurang-kurangnya satu hari.`)
      })
    }

    return Object.keys(errors).length > 0 ? errors : null
  }

  // Check if a specific class-subject-day combination exists
  const hasScheduleForPeriod = (classId: string, subjectId: string, day: DayOfWeek): boolean => {
    return schedules.value.some(schedule => {
      // Check new structure first
      if (schedule.schedule_details?.class_subjects) {
        return schedule.schedule_details.class_subjects.some(cs =>
          cs.class_id === classId &&
          cs.subject_id === subjectId &&
          cs.days_scheduled.includes(day)
        )
      }
      // Fallback to legacy structure
      return schedule.schedule_details?.periods?.some((period: any) =>
        period.class_id === classId &&
        period.subject_id === subjectId &&
        period.day === day
      )
    })
  }

  // Get all scheduled periods for a class-subject combination
  const getScheduledDaysForClassSubject = (classId: string, subjectId: string): DayOfWeek[] => {
    const days: DayOfWeek[] = []
    schedules.value.forEach(schedule => {
      // Check new structure first
      if (schedule.schedule_details?.class_subjects) {
        schedule.schedule_details.class_subjects.forEach(cs => {
          if (cs.class_id === classId && cs.subject_id === subjectId) {
            cs.days_scheduled.forEach(day => {
              if (!days.includes(day)) {
                days.push(day)
              }
            })
          }
        })
      }
      // Fallback to legacy structure
      else if (schedule.schedule_details?.periods) {
        schedule.schedule_details.periods.forEach((period: any) => {
          if (period.class_id === classId &&
              period.subject_id === subjectId &&
              !days.includes(period.day)) {
            days.push(period.day)
          }
        })
      }
    })
    return days
  }

  // Get total periods count for a user
  const getTotalPeriodsCount = (): number => {
    return schedules.value.reduce((total, schedule) =>
      total + (schedule.schedule_details?.periods?.length || 0), 0
    )
  }

  return {
    // State
    schedules: computed(() => schedules.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Computed
    dayOptions,
    schedulesWithDetails: computed(() => getSchedulesWithDetails()),
    totalPeriodsCount: computed(() => getTotalPeriodsCount()),

    // Methods
    fetchSchedules,
    fetchScheduleByLessonPlanId,
    createSchedule,
    updateSchedule,
    deleteSchedule,
    validateScheduleForm,
    getAvailableClassSubjects,
    hasScheduleForPeriod,
    getScheduledDaysForClassSubject
  }
}
