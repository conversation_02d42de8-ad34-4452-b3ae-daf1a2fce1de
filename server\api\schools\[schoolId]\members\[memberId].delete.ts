// Remove school member API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')
    const schoolId = getRouterParam(event, 'schoolId')
    const memberId = getRouterParam(event, 'memberId')

    // Validate input
    if (!schoolId || !memberId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School ID and member ID are required'
      })
    }

    // Initialize Supabase client with service role for admin operations
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Check if the requesting user has admin/supervisor access to this school
    const { data: requesterMembership, error: membershipError } = await supabase
      .from('school_memberships')
      .select('role')
      .eq('user_id', user.id)
      .eq('school_id', schoolId)
      .eq('status', 'active')
      .single()

    // Also check if user is the school admin
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .select('admin_user_id')
      .eq('id', schoolId)
      .single()

    if (schoolError || !school) {
      throw createError({
        statusCode: 404,
        statusMessage: 'School not found'
      })
    }

    const isSchoolAdmin = school.admin_user_id === user.id
    const hasPermission = isSchoolAdmin || 
      (requesterMembership && ['admin', 'supervisor'].includes(requesterMembership.role))

    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to remove members'
      })
    }

    // Get the current membership to validate the removal
    const { data: currentMembership, error: currentError } = await supabase
      .from('school_memberships')
      .select('*')
      .eq('id', memberId)
      .eq('school_id', schoolId)
      .single()

    if (currentError || !currentMembership) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Member not found'
      })
    }

    // Prevent users from removing themselves (unless they're the school owner)
    if (currentMembership.user_id === user.id && !isSchoolAdmin) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Cannot remove yourself from the school'
      })
    }

    // Prevent non-admins from removing admin members
    if (currentMembership.role === 'admin' && !isSchoolAdmin) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Only school owners can remove admin members'
      })
    }

    // Instead of hard deleting, we'll set status to 'removed' for audit trail
    const { data: removedMembership, error: removeError } = await supabase
      .from('school_memberships')
      .update({
        status: 'removed',
        removed_at: new Date().toISOString(),
        removed_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', memberId)
      .eq('school_id', schoolId)
      .select('*')
      .single()

    if (removeError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to remove member: ${removeError.message}`
      })
    }

    // Transform the response
    const transformedMembership = {
      id: removedMembership.id,
      role: removedMembership.role,
      status: removedMembership.status,
      joined_at: removedMembership.joined_at,
      removed_at: removedMembership.removed_at,
      user_id: removedMembership.user_id
    }

    return {
      success: true,
      membership: transformedMembership,
      message: 'Member removed successfully'
    }

  } catch (error: any) {
    console.error('Remove member error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during member removal'
    })
  }
})
