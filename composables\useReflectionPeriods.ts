import { ref, computed } from 'vue';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import type { DetailedReflectionFormData } from '~/types/reflections';

// Types for period reflection data
export interface PeriodReflectionData {
  overall_rating: number;
  objectives_achieved: boolean;
  challenges_faced: string;
  activity_effectiveness: number;
  time_management: 'early' | 'on_time' | 'late' | 'not_applicable';
  student_engagement: number;
  resource_adequacy: 'inadequate' | 'adequate' | 'excellent' | 'not_applicable';
  improvements_needed: string;
  successful_strategies: string;
  action_items: string[];
  additional_notes: string;
  jumlah_murid_mencapai_objektif: number;
  tindakan_susulan: string[];
  tidak_terlaksana: string | null;
}

export interface LessonPlanDetailedReflectionSingle {
  id: string;
  lesson_plan_id: string;
  user_id: string;
  reflections: Record<string, PeriodReflectionData>; // key: "class_subject_id_day"
  overall_rating: number;
  total_periods: number;
  periods_with_custom_data: number;
  created_at: string;
  updated_at: string;
}

// Global state for single-row reflections
const detailedReflections = ref<Map<string, LessonPlanDetailedReflectionSingle>>(new Map());
const loading = ref(false);
const error = ref<string | null>(null);

export const useReflectionPeriods = () => {
  const supabase = useSupabaseClient();
  const user = useSupabaseUser();

  // Helper function to create period key
  const createPeriodKey = (classSubjectId: string, day: string): string => {
    return `${classSubjectId}_${day.toLowerCase()}`;
  };

  // Helper function to create default reflection data
  const createDefaultReflectionData = (studentCount: number = 0): PeriodReflectionData => ({
    overall_rating: 5,
    objectives_achieved: true,
    challenges_faced: '',
    activity_effectiveness: 5,
    time_management: 'on_time',
    student_engagement: 5,
    resource_adequacy: 'adequate',
    improvements_needed: '',
    successful_strategies: '',
    action_items: [],
    additional_notes: '',
    jumlah_murid_mencapai_objektif: studentCount,
    tindakan_susulan: [],
    tidak_terlaksana: null
  });

  // Get reflections for a lesson plan
  const fetchDetailedReflections = async (lessonPlanId: string): Promise<LessonPlanDetailedReflectionSingle | null> => {
    if (!user.value) return null;

    // Check cache first
    if (detailedReflections.value.has(lessonPlanId)) {
      return detailedReflections.value.get(lessonPlanId)!;
    }

    try {
      loading.value = true;
      error.value = null;

      const { data, error: fetchError } = await supabase
        .from('lesson_plan_detailed_reflections')
        .select('*')
        .eq('lesson_plan_id', lessonPlanId)
        .eq('user_id', user.value.id)
        .maybeSingle();

      if (fetchError) throw fetchError;

      if (data) {
        const reflection = data as any as LessonPlanDetailedReflectionSingle;
        // Cache the result
        detailedReflections.value.set(lessonPlanId, reflection);
        return reflection;
      }

      return null;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch detailed reflections';
      console.error('Error fetching detailed reflections:', err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Get reflection for specific period
  const getPeriodReflection = async (
    lessonPlanId: string,
    classSubjectId: string,
    day: string
  ): Promise<PeriodReflectionData | null> => {
    const reflections = await fetchDetailedReflections(lessonPlanId);
    if (!reflections) return null;

    const periodKey = createPeriodKey(classSubjectId, day);
    return reflections.reflections[periodKey] || null;
  };

  // Update reflection for specific period
  const updatePeriodReflection = async (
    lessonPlanId: string,
    classSubjectId: string,
    day: string,
    reflectionData: Partial<PeriodReflectionData>
  ): Promise<LessonPlanDetailedReflectionSingle | null> => {
    if (!user.value) return null;

    try {
      loading.value = true;
      error.value = null;

      const periodKey = createPeriodKey(classSubjectId, day);
      
      // Get current reflections
      const currentReflections = await fetchDetailedReflections(lessonPlanId);
      if (!currentReflections) return null;

      // Update the specific period
      const updatedReflections = {
        ...currentReflections.reflections,
        [periodKey]: {
          ...currentReflections.reflections[periodKey],
          ...reflectionData
        }
      };

      const { data, error: updateError } = await (supabase as any)
        .from('lesson_plan_detailed_reflections')
        .update({ reflections: updatedReflections })
        .eq('lesson_plan_id', lessonPlanId)
        .eq('user_id', user.value.id)
        .select()
        .single();

      if (updateError) throw updateError;

      if (data) {
        const updatedReflection = data as any as LessonPlanDetailedReflectionSingle;
        // Update cache
        detailedReflections.value.set(lessonPlanId, updatedReflection);
        return updatedReflection;
      }

      return null;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update period reflection';
      console.error('Error updating period reflection:', err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Update detailed reflection by ID (for compatibility)
  const updateDetailedReflection = async (
    reflectionId: string,
    updates: Partial<LessonPlanDetailedReflectionSingle>
  ): Promise<LessonPlanDetailedReflectionSingle | null> => {
    // This is a compatibility function for existing code
    console.warn('updateDetailedReflection is deprecated, use updatePeriodReflection instead');
    return null;
  };

  // Clear cache for a specific lesson plan
  const clearReflectionCache = (lessonPlanId: string) => {
    detailedReflections.value.delete(lessonPlanId);
  };

  // Clear all cache
  const clearAllReflectionCache = () => {
    detailedReflections.value.clear();
  };

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Methods
    fetchDetailedReflections,
    getPeriodReflection,
    updatePeriodReflection,
    updateDetailedReflection, // Compatibility function

    // Helpers
    createPeriodKey,
    createDefaultReflectionData,
    clearReflectionCache,
    clearAllReflectionCache
  };
};
