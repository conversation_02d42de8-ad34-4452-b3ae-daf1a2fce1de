<template>
    <div class="space-y-2">
        <label v-if="label" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ label }}
        </label>
        <div class="flex items-center">
            <button
                @click="decrement"
                :disabled="disabled || (min !== undefined && currentValue <= min)"
                class="flex items-center justify-center w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-l-md bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                type="button"
            >
                <Icon name="heroicons:minus" class="h-4 w-4 text-gray-600 dark:text-gray-300" />
            </button>
            
            <input
                :value="currentValue"
                @input="handleInput"
                @blur="handleBlur"
                :disabled="disabled"
                :min="min"
                :max="max"
                type="number"
                class="w-20 h-10 text-center border-t border-b border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            />
            
            <button
                @click="increment"
                :disabled="disabled || (max !== undefined && currentValue >= max)"
                class="flex items-center justify-center w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                type="button"
            >
                <Icon name="heroicons:plus" class="h-4 w-4 text-gray-600 dark:text-gray-300" />
            </button>
        </div>
        
        <div v-if="showRange && (min !== undefined || max !== undefined)" class="text-xs text-gray-500 dark:text-gray-400">
            <span v-if="min !== undefined && max !== undefined">
                Julat: {{ min }} - {{ max }}
            </span>
            <span v-else-if="min !== undefined">
                Minimum: {{ min }}
            </span>
            <span v-else-if="max !== undefined">
                Maksimum: {{ max }}
            </span>
        </div>
        
        <div v-if="description" class="text-xs text-gray-500 dark:text-gray-400">
            {{ description }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Icon from './Icon.vue'

interface Props {
    modelValue: number
    label?: string
    description?: string
    min?: number
    max?: number
    step?: number
    disabled?: boolean
    showRange?: boolean
}

interface Emits {
    (e: 'update:modelValue', value: number): void
}

const props = withDefaults(defineProps<Props>(), {
    step: 1,
    disabled: false,
    showRange: true
})

const emit = defineEmits<Emits>()

// Internal value for handling input
const internalValue = ref(props.modelValue)

// Computed value that ensures it's within bounds
const currentValue = computed({
    get: () => internalValue.value,
    set: (value: number) => {
        const clampedValue = clampValue(value)
        internalValue.value = clampedValue
        emit('update:modelValue', clampedValue)
    }
})

// Clamp value within min/max bounds
const clampValue = (value: number): number => {
    let clampedValue = value
    
    if (props.min !== undefined && clampedValue < props.min) {
        clampedValue = props.min
    }
    
    if (props.max !== undefined && clampedValue > props.max) {
        clampedValue = props.max
    }
    
    return clampedValue
}

// Methods
const increment = () => {
    if (props.disabled) return
    currentValue.value = currentValue.value + props.step
}

const decrement = () => {
    if (props.disabled) return
    currentValue.value = currentValue.value - props.step
}

const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = parseInt(target.value) || 0
    internalValue.value = value
}

const handleBlur = () => {
    // Clamp and emit the final value on blur
    currentValue.value = internalValue.value
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
    internalValue.value = newValue
}, { immediate: true })
</script>
