export interface LessonPlanReflection {
  id: string;
  lesson_plan_id: string;
  user_id: string;
  reflection_date: string;
  
  // Quick Mode Fields (Essential)
  overall_rating: number; // 1-5 scale
  objectives_achieved: boolean;
  challenges_faced?: string | null; // Made optional
    // Detailed Mode Fields (Optional)
  activity_effectiveness?: number | null; // 1-5 scale
  time_management?: 'on_time' | 'early' | 'late' | null;
  student_engagement?: number | null; // 1-5 scale
  resource_adequacy?: 'inadequate' | 'adequate' | 'excellent' | null;
  improvements_needed?: string | null;
  successful_strategies?: string | null;
  action_items?: string[] | null;
  additional_notes?: string | null;
  
  // System fields
  is_detailed_mode: boolean;
  created_at: string;
  updated_at: string;
}

export interface ReflectionFormData {
  // Quick Mode
  overall_rating: number;
  objectives_achieved: boolean;
  challenges_faced: string; // Keep as string for form handling, but can be empty
  
  // Detailed Mode
  activity_effectiveness: number;
  time_management: 'on_time' | 'early' | 'late' | 'not_applicable';
  student_engagement: number;
  resource_adequacy: 'inadequate' | 'adequate' | 'excellent' | 'not_applicable';
  improvements_needed: string;
  successful_strategies: string;
  action_items: string[];
  additional_notes: string;
}

export interface ReflectionStats {
  total_reflections: number;
  completion_rate: number;
  average_rating: number;
  objectives_achievement_rate: number;
  most_common_challenges: string[];
  improvement_trends: {
    subject: string;
    trend: 'improving' | 'stable' | 'declining';
    average_rating: number;
  }[];
}

export interface WeeklyReflectionTrend {
  week_id: string;
  week_name: string;
  total_lesson_plans: number;
  completed_reflections: number;
  completion_percentage: number;
}

export interface ReflectionWithLessonPlan extends LessonPlanReflection {
  lesson_plans?: {
    file_name: string;
    week_id: string | null;
  };
  // Include new detailed reflection fields
  jumlah_murid_mencapai_objektif?: number;
  tindakan_susulan?: string[] | string; // Can be array or string depending on source
  tidak_terlaksana?: string;
}

export type ReflectionMode = 'quick' | 'detailed';

// New detailed reflection for specific kelas-subjek and day
export interface LessonPlanDetailedReflection {
  id: string;
  lesson_plan_id: string;
  class_subject_id: string;
  day: string;
  
  // All reflection fields (both quick and detailed)
  overall_rating: number; // 1-5 scale
  objectives_achieved: boolean;
  challenges_faced?: string | null; // Made optional
  activity_effectiveness?: number | null; // 1-5 scale
  time_management?: 'on_time' | 'early' | 'late' | null;
  student_engagement?: number | null; // 1-5 scale
  resource_adequacy?: 'inadequate' | 'adequate' | 'excellent' | null;
  improvements_needed?: string | null;
  successful_strategies?: string | null;
  action_items?: string[] | null;
  additional_notes?: string | null;
  
  // System fields
  created_at: string;
  updated_at: string;
}

// Form data for detailed reflection
export interface DetailedReflectionFormData {
  // Quick Mode fields
  overall_rating: number;
  objectives_achieved: boolean;
  challenges_faced: string; // Keep as string for form handling, but can be empty

  // Detailed Mode fields
  activity_effectiveness: number;
  time_management: 'on_time' | 'early' | 'late' | 'not_applicable';
  student_engagement: number;
  resource_adequacy: 'inadequate' | 'adequate' | 'excellent' | 'not_applicable';
  improvements_needed: string;
  successful_strategies: string;
  action_items: string[];
  additional_notes: string;

  // New fields
  jumlah_murid_mencapai_objektif: number;
  tindakan_susulan: string[]; // Array of UUIDs
  tidak_terlaksana: string | null; // UUID or null
}

// Helper types for UI components
export interface ClassSubjectOption {
  id: string; // e.g., "f1_d21dcaa8-d036-4e0e-8717-bc587bed7825"
  label: string; // e.g., "1A - Mathematics"
  class_id: string; // e.g., "f1"
  className: string; // e.g., "1A"
  subject_id: string; // e.g., "d21dcaa8-d036-4e0e-8717-bc587bed7825"
  subjectName?: string; // e.g., "Mathematics"
}

export interface DayOption {
  value: string; // e.g., "ahad"
  label: string; // e.g., "Ahad"
}

export interface SubmissionStatus {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
}

// Rating calculation interfaces for the new auto-calculated system
export interface CalculatedRating {
  lesson_plan_id: string;
  total_periods: number;
  total_stars: number;
  calculated_rating: number; // Rounded to nearest 0.1
  periods_with_reflections: number;
  periods_using_default: number;
  periods_tidak_terlaksana: number;
  is_calculated: boolean; // Flag to indicate this is auto-calculated vs manually set
}

// Modified ReflectionFormData for quick mode with calculated rating
export interface QuickReflectionFormData {
  // Rating is now calculated and read-only in quick mode
  calculated_rating?: CalculatedRating;
  
  // Still editable fields in quick mode
  objectives_achieved: boolean;
  challenges_faced: string;
}

// For displaying rating breakdown to users
export interface RatingBreakdown {
  class_subject_label: string;
  day_label: string;
  rating: number;
  has_reflection: boolean;
  is_default: boolean;
}

// Enhanced lesson plan reflection with calculated rating
export interface LessonPlanReflectionWithCalculation extends LessonPlanReflection {
  calculated_rating?: CalculatedRating;
  rating_breakdown?: RatingBreakdown[];
}

// =====================================================
// REFLECTION TEMPLATE TYPES
// =====================================================

export type ReflectionTemplateCategory = 'lesson_type' | 'assessment' | 'behavior' | 'technology' | 'general';

export interface ReflectionTemplate {
  id: string;
  name: string;
  description: string | null;
  category: ReflectionTemplateCategory;
  prompts: Record<string, string>; // Field name -> prompt text
  default_values: Partial<DetailedReflectionFormData>; // Default values for form fields
  is_system_template: boolean;
  created_by: string | null;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface UserReflectionTemplatePreference {
  id: string;
  user_id: string;
  template_id: string;
  customizations: Record<string, any>; // User-specific modifications
  usage_count: number;
  last_used: string | null;
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
}

export interface ReflectionTemplateWithPreference extends ReflectionTemplate {
  user_preference?: UserReflectionTemplatePreference;
  is_favorite?: boolean;
  user_usage_count?: number;
  last_used?: string | null;
}

// For template selection UI
export interface TemplateOption {
  id: string;
  name: string;
  description: string | null;
  category: ReflectionTemplateCategory;
  is_system_template: boolean;
  is_favorite?: boolean;
  usage_count?: number;
  last_used?: string | null;
}

// For template application
export interface TemplateApplicationResult {
  applied_prompts: Record<string, string>;
  applied_defaults: Partial<DetailedReflectionFormData>;
  template_id: string;
  template_name: string;
}

// For template creation/editing
export interface ReflectionTemplateFormData {
  name: string;
  description: string;
  category: ReflectionTemplateCategory;
  prompts: Record<string, string>;
  default_values: Partial<DetailedReflectionFormData>;
}

// Template usage analytics
export interface TemplateUsageStats {
  template_id: string;
  template_name: string;
  total_usage: number;
  user_usage: number;
  last_used: string | null;
  category: ReflectionTemplateCategory;
  is_system_template: boolean;
}
