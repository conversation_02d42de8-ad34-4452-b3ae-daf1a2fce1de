<template>
    <div :class="[
        'relative p-3 rounded-lg border-l-4 transition-all duration-200 h-full',
        subjectColor.bg_color,
        subjectColor.color,
        isPreview ? '' : (editable ? 'cursor-pointer hover:shadow-md' : 'cursor-default')
    ]" @click="editable ? handleClick() : null">
        <!-- Activity Icon for non-class activities -->
        <div v-if="!isClassActivity && activityConfig" class="absolute bottom-2 right-2">
            <Icon :name="activityConfig.icon" :class="['h-4 w-4', subjectColor.text_color]" />
        </div>

        <!-- Content -->
        <div class="space-y-1" :class="{ 'pr-6': !isClassActivity }">
            <!-- Class Activity Display -->
            <div v-if="isClassActivity">
                <div :class="['font-semibold text-sm', subjectColor.text_color]">
                    {{ entry.subject_name }}
                </div>
                <div :class="['text-xs font-medium absolute bottom-2 right-2', subjectColor.text_color]">
                    {{ entry.class_name }}
                </div>
            </div>

            <!-- Non-Class Activity Display -->
            <div v-else>
                <div :class="['font-semibold text-sm', subjectColor.text_color]">
                    {{ entry.activity_title || (activityConfig?.label || 'Aktiviti') }}
                </div>
                <div v-if="entry.activity_description" :class="['text-xs', subjectColor.text_color, 'opacity-90']">
                    {{ entry.activity_description }}
                </div>
            </div>
        </div>

        <!-- Delete Button (not shown in preview mode or dummy entries) -->
        <button v-if="!isPreview && !entry.is_dummy && editable"
            class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors duration-200 shadow-sm"
            @click.stop="$emit('delete', entry)" :title="`Padam ${isClassActivity ? 'kelas' : 'aktiviti'}`">
            <Icon name="mdi:close" class="h-3 w-3" />
        </button>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from '~/components/ui/base/Icon.vue'
import type { TimetableEntry, SubjectColor } from '~/types/timetable'
import { isClassActivity as checkIsClassActivity, getActivityConfig } from '~/types/timetable'

interface Props {
    entry: TimetableEntry
    subjectColor: SubjectColor
    isPreview?: boolean
    editable?: boolean // New prop
}

interface Emits {
    (e: 'edit', entry: TimetableEntry): void
    (e: 'delete', entry: TimetableEntry): void
}

const props = withDefaults(defineProps<Props>(), {
    isPreview: false,
    editable: false // Default to false
})

const emit = defineEmits<Emits>()

// Computed properties
const isClassActivity = computed(() => checkIsClassActivity(props.entry))

const activityConfig = computed(() => {
    if (isClassActivity.value) return null
    return getActivityConfig(props.entry.activity_type)
})

// Methods
const handleClick = () => {
    if (!props.isPreview && !props.entry.is_dummy && props.editable) {
        emit('edit', props.entry)
    }
}
</script>
