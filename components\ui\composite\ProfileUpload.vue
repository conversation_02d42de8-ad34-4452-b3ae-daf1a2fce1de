<template>
    <div class="flex flex-col items-center">
        <!-- Profile Preview Container -->
        <div class="relative mb-4 group" :class="[sizeClasses.container]">
            <!-- Profile Image Preview -->
            <div class="overflow-hidden bg-light-border dark:bg-dark-border flex items-center justify-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-dark-card"
                tabindex="0" :class="[
                    sizeClasses.preview,
                    {
                        'border-4 border-primary': isDragging,
                        'border-4 border-red-500': !!errorMessage, // Error state border
                        'animate-pulse': internalIsUploading && !errorMessage
                    }
                ]" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave"
                @drop.prevent="handleFileDrop" @click="triggerFileInput" @keypress.enter="triggerFileInput"
                @keypress.space="triggerFileInput">
                <!-- Display Image if Available -->
                <img v-if="previewUrl" :src="previewUrl" :alt="`${name || 'Profile'} preview`"
                    class="object-cover w-full h-full" />

                <!-- Default Placeholder -->
                <div v-else class="flex flex-col items-center justify-center w-full h-full">
                    <Icon :name="avatarIcon || 'ph:user-duotone'" :size="sizeClasses.icon"
                        class="text-light-foreground/50 dark:text-dark-foreground/50" />
                </div>

                <!-- Overlay for Hover Effects -->
                <div
                    class="absolute inset-0 bg-primary/0 hover:bg-primary/20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div class="text-white flex flex-col items-center">
                        <Icon name="ph:camera-duotone" :size="sizeClasses.actionIcon" />
                        <span class="text-xs mt-1 font-medium" v-if="size === 'lg'">{{ uploadLabel }}</span>
                    </div>
                </div>

                <!-- Edit Badge -->
                <div class="absolute bg-primary text-white p-1 rounded-full shadow-lg border-2 border-white dark:border-dark-card transform transition-all duration-300 opacity-0 group-hover:opacity-100 flex items-center justify-center"
                    :class="[sizeClasses.badge]">
                    <Icon name="ph:pencil" :size="sizeClasses.badgeIcon" />
                </div>
            </div>

            <!-- Loading Progress Ring (Shows when uploading) -->
            <svg v-if="internalIsUploading && !errorMessage"
                class="absolute top-0 left-0 transform -translate-x-[3px] -translate-y-[3px]" :class="[sizeClasses.svg]"
                viewBox="0 0 100 100" role="progressbar" :aria-valuenow="internalUploadProgress" aria-valuemin="0"
                aria-valuemax="100">
                <circle cx="50" cy="50" r="45" fill="none" stroke="currentColor" stroke-width="8"
                    class="text-primary/30" />
                <circle cx="50" cy="50" r="45" fill="none" stroke="currentColor" stroke-width="8" stroke-linecap="round"
                    class="text-primary progress-ring__circle--animated" :stroke-dasharray="283"
                    :stroke-dashoffset="283 - (283 * internalUploadProgress) / 100" transform="rotate(-90 50 50)">
                </circle>
            </svg>
        </div>

        <!-- Name Display -->
        <h3 v-if="name && showName" class="font-medium text-light-foreground dark:text-dark-foreground text-lg">
            {{ name }}
        </h3>

        <!-- Error Message Display -->
        <div v-if="errorMessage" class="mt-2 mb-2 text-sm text-red-600 dark:text-red-400" role="alert"
            aria-live="polite">
            {{ errorMessage }}
        </div>

        <!-- File Input (Hidden) -->
        <input ref="fileInputRef" type="file" class="hidden" accept="image/*" @change="handleFileChange" />

        <!-- Action Buttons -->
        <div v-if="showActions" class="mt-3 flex space-x-2">
            <Button v-if="previewUrl && allowRemove" variant="alert-error" size="sm" prependIcon="ph:trash"
                @click="removeImage">
                Padam
            </Button>
            <Button variant="primary" size="sm" prependIcon="ph:camera" @click="triggerFileInput" type="button">
                {{ uploadButtonLabel }}
            </Button>
        </div>

        <!-- Fancy Effects -->
        <div class="relative">
            <!-- Stars Animation Effect -->
            <div v-if="animateSuccess && showSuccessAnimation"
                class="absolute -top-16 left-1/2 transform -translate-x-1/2">
                <div v-for="i in 8" :key="`star-${i}`" class="absolute animate-star opacity-0" :style="{
                    top: `${Math.random() * 40 - 20}px`,
                    left: `${Math.random() * 40 - 20}px`,
                    '--star-delay': `${Math.random() * 0.3}s`,
                    '--star-duration': `${0.5 + Math.random() * 0.5}s`
                }">
                    <Icon :name="`${['ph:star', 'ph:sparkle', 'ph:confetti'][Math.floor(Math.random() * 3)]}`"
                        :size="['16px', '20px', '24px'][Math.floor(Math.random() * 3)]" class="text-accent" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
// import { useSupabase } from '~/composables/useSupabase'; // Import useSupabase - REMOVED
import Button from '../base/Button.vue';
import Icon from '../base/Icon.vue';

const props = withDefaults(defineProps<{
    modelValue?: File | null;
    previewUrl?: string;
    name?: string;
    size?: 'sm' | 'md' | 'lg';
    showName?: boolean;
    showActions?: boolean;
    allowRemove?: boolean;
    uploadLabel?: string;
    uploadButtonLabel?: string;
    avatarIcon?: string;
    // isUploading and uploadProgress will now be managed internally
    // isUploading?: boolean; 
    // uploadProgress?: number;
    animateSuccess?: boolean;
    acceptedFileTypes?: string[];
    maxFileSize?: number; // in bytes
    bucketName?: string; // Supabase bucket name - Kept for potential parent use
    folderName?: string; // Optional folder within the bucket - Kept for potential parent use
}>(), {
    modelValue: null,
    previewUrl: '',
    size: 'md',
    showName: false,
    showActions: true,
    allowRemove: true,
    uploadLabel: 'Update Photo',
    uploadButtonLabel: 'Muatnaik',
    avatarIcon: 'ph:user-duotone',
    animateSuccess: true,
    acceptedFileTypes: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/avif'],
    maxFileSize: 5 * 1024 * 1024, // 5MB default
    bucketName: 'avatars',
    folderName: '',
});

const emit = defineEmits(['update:modelValue', 'fileSelected', 'fileRemoved', 'error']); // REMOVED 'upload-success', 'upload-error'

// const { client: supabaseClient } = useSupabase(); // Get Supabase client - REMOVED

// Internal state for upload (will not be driven by internal uploads anymore)
const internalIsUploading = ref(false);
const internalUploadProgress = ref(0);

// State variables
const fileInputRef = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const localPreviewUrl = ref<string>(props.previewUrl || '');
const showSuccessAnimation = ref(false);
const errorMessage = ref<string | null>(null);

// Compute the preview URL (either from props or local state)
const previewUrl = computed(() => props.previewUrl || localPreviewUrl.value);

// Size classes based on the size prop
const sizeClasses = computed(() => {
    switch (props.size) {
        case 'sm':
            return {
                container: 'w-24 h-24',
                preview: 'w-24 h-24',
                icon: '24px',
                actionIcon: '16px',
                badge: 'bottom-0 right-0 w-6 h-6',
                badgeIcon: '12px',
                svg: 'w-[96px] h-[96px]'
            };
        case 'lg':
            return {
                container: 'w-40 h-40',
                preview: 'w-40 h-40',
                icon: '48px',
                actionIcon: '28px',
                badge: 'bottom-1 right-1 w-10 h-10',
                badgeIcon: '20px',
                svg: 'w-[166px] h-[166px]'
            };
        default: // md
            return {
                container: 'w-32 h-32',
                preview: 'w-32 h-32',
                icon: '36px',
                actionIcon: '20px',
                badge: 'bottom-0 right-0 w-8 h-8',
                badgeIcon: '16px',
                svg: 'w-[134px] h-[134px]'
            };
    }
});

// Watch for changes in the modelValue prop
watch(() => props.modelValue, (newFile) => {
    if (newFile) {
        errorMessage.value = null;
        // If a File object is passed, it means we might not need to immediately upload,
        // but rather just show its preview. The actual upload can be triggered by a separate action
        // or when the parent form submits. For this component's direct upload behavior,
        // handleFile will manage the upload.
        createLocalPreview(newFile);
    } else if (!props.previewUrl) {
        localPreviewUrl.value = '';
    }
});

// Watch for changes in upload progress (now internalUploadProgress)
watch(internalUploadProgress, (newProgress) => {
    // This watcher will be dormant as internalUploadProgress is not updated by this component anymore.
    // Parent would need to trigger success animation via a prop if desired.
    if (newProgress === 100 && props.animateSuccess) {
        setTimeout(() => {
            showSuccessAnimation.value = true;
            setTimeout(() => {
                showSuccessAnimation.value = false;
            }, 1500);
        }, 300);
    }
});

// Methods
const triggerFileInput = () => {
    if (internalIsUploading.value) return;
    errorMessage.value = null;
    fileInputRef.value?.click();
};

const handleDragOver = (event: DragEvent) => {
    if (internalIsUploading.value) return; // Check internal state
    isDragging.value = true;
    event.dataTransfer!.dropEffect = 'copy';
};

const handleDragLeave = () => {
    if (internalIsUploading.value) return; // Check internal state
    isDragging.value = false;
};

const handleFileDrop = (event: DragEvent) => {
    if (internalIsUploading.value) return; // Check internal state
    isDragging.value = false;
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
        const file = event.dataTransfer.files[0];
        handleFile(file);
    }
};

const handleFileChange = (event: Event) => {
    if (internalIsUploading.value) return; // Check internal state
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
        const file = target.files[0];
        handleFile(file);
    }
};

const isImageFile = (file: File): boolean => {
    // This basic check is kept, but more specific validation happens in handleFile
    return file.type.startsWith('image/');
};

const handleFile = async (file: File) => {
    errorMessage.value = null;

    if (!props.acceptedFileTypes.includes(file.type)) {
        const errorMsg = `Invalid file type. Accepted: ${props.acceptedFileTypes.map(type => type.split('/')[1]).join(', ')}.`;
        errorMessage.value = errorMsg;
        emit('error', { type: 'fileType', message: errorMsg, file });
        if (fileInputRef.value) fileInputRef.value.value = '';
        // emit('upload-error', errorMsg); // REMOVED
        return;
    }

    if (file.size > props.maxFileSize) {
        const errorMsg = `File is too large. Max size: ${Math.round(props.maxFileSize / (1024 * 1024))}MB.`;
        errorMessage.value = errorMsg;
        emit('error', { type: 'fileSize', message: errorMsg, file });
        if (fileInputRef.value) fileInputRef.value.value = '';
        // emit('upload-error', errorMsg); // REMOVED
        return;
    }

    emit('update:modelValue', file); // Update modelValue for parent
    emit('fileSelected', file);
    createLocalPreview(file);

    // --- Supabase Upload Logic REMOVED ---
    // internalIsUploading.value = true; // REMOVED
    // internalUploadProgress.value = 0; // REMOVED

    // try {
    //     const user = (await supabaseClient.auth.getUser()).data.user;
    //     if (!user) {
    //         throw new Error('User not authenticated for avatar upload.');
    //     }

    //     const fileExt = file.name.split('.').pop();
    //     const fileName = `${user.id}-${Date.now()}.${fileExt}`;
    //     const filePath = props.folderName ? `${props.folderName}/${fileName}` : `${user.id}/${fileName}`;

    //     const { error: uploadError } = await supabaseClient.storage
    //         .from(props.bucketName)
    //         .upload(filePath, file, {
    //             cacheControl: '3600',
    //             upsert: true,
    //             contentType: file.type,
    //         });

    //     if (uploadError) {
    //         throw uploadError;
    //     }

    //     const { data: publicUrlData } = supabaseClient.storage
    //         .from(props.bucketName)
    //         .getPublicUrl(filePath);

    //     if (!publicUrlData || !publicUrlData.publicUrl) {
    //         throw new Error('Could not get public URL for avatar.');
    //     }

    //     internalUploadProgress.value = 100;
    //     emit('upload-success', publicUrlData.publicUrl);

    // } catch (error: any) {
    //     const uploadErrorMsg = error.message || 'Failed to upload image.';
    //     errorMessage.value = uploadErrorMsg;
    //     emit('upload-error', uploadErrorMsg);
    //     emit('error', { type: 'uploadError', message: uploadErrorMsg, file });
    // } finally {
    //     internalIsUploading.value = false;
    //     setTimeout(() => {
    //         if (internalUploadProgress.value === 100 && !errorMessage.value) {
    //             // internalUploadProgress.value = 0;
    //         } else if (errorMessage.value) {
    //             internalUploadProgress.value = 0;
    //         }
    //     }, 1500);
    // }
    // --- End Supabase Upload Logic ---
};

const createLocalPreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
        if (e.target?.result) {
            localPreviewUrl.value = e.target.result as string;
        }
    };
    reader.readAsDataURL(file);
};

const removeImage = () => {
    emit('update:modelValue', null);
    emit('fileRemoved');
    localPreviewUrl.value = '';
    if (fileInputRef.value) {
        fileInputRef.value.value = '';
    }
    errorMessage.value = null; // Clear error on remove
};
</script>

<style scoped>
@keyframes star-animation {
    0% {
        transform: translateY(0) scale(0.5);
        opacity: 0;
    }

    20% {
        opacity: 1;
    }

    100% {
        transform: translateY(-40px) scale(1.2);
        opacity: 0;
    }
}

.animate-star {
    animation: star-animation var(--star-duration, 0.8s) ease-out var(--star-delay, 0s);
}

.progress-ring__circle--animated {
    transition: stroke-dashoffset 0.25s ease-out;
}
</style>
