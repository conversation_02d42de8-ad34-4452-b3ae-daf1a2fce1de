<template>
    <div> <label :for="inputId" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop" :class="[
                'relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 hover:bg-primary/5 hover:border-primary/20 dark:hover:bg-primary/10 transition-colors duration-150 ease-in-out',
                isDraggingOver ? 'border-primary dark:border-primary-400' : 'border-gray-300 dark:border-gray-500'
            ]">
            <div class="flex flex-col items-center justify-center pt-5 pb-6 text-center pointer-events-none">
                <svg class="w-10 h-10 mb-3 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                    </path>
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span class="font-semibold">Klik untuk muat naik</span> atau seret dan lepas
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ acceptedFileTypesDisplay }} (MAX. {{
                    maxFileSizeMb }}MB)</p>
            </div>
            <input :id="inputId" type="file" class="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
                @change="handleFileSelect" :accept="accept" />
        </label>
        <div v-if="internalFile" :class="filePreviewClasses">
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0 mr-3">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate" :title="internalFile.name">
                        {{ internalFile.name }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ (internalFile.size / 1024).toFixed(2) }} KB
                    </p>
                </div>
                <button @click="clearUploadedFile" type="button" :class="deleteButtonClasses">
                    <span class="sr-only">Padam fail</span>
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
        <div v-else-if="isEditing && existingFileNameToDisplay" :class="filePreviewClasses">
            <div class="flex items-center">
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate"
                        :title="existingFileNameToDisplay">
                        {{ existingFileNameToDisplay }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        Fail semasa (klik di atas untuk mengganti)
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: File as PropType<File | null>,
        default: null,
    },
    maxFileSizeMb: {
        type: Number,
        default: 10,
    },
    isEditing: {
        type: Boolean,
        default: false,
    },
    existingFileName: {
        type: String as PropType<string | null>,
        default: null,
    },
    accept: {
        type: String,
        default: ".docx,.pdf,.xlsx,.png,.jpeg,.jpg",
    },
    inputId: {
        type: String,
        default: 'fileUpload',
    }
});

const emit = defineEmits(['update:modelValue', 'file-error']);

const internalFile = ref<File | null>(props.modelValue);
const isDraggingOver = ref(false);

const MAX_FILE_SIZE_BYTES = computed(() => props.maxFileSizeMb * 1024 * 1024);

const acceptedFileTypesDisplay = computed(() => {
    return props.accept.split(',').map(ext => ext.replace('.', '').toUpperCase()).join(', ');
});

const existingFileNameToDisplay = computed(() => props.existingFileName || 'Tiada nama fail');

const filePreviewClasses = computed(() =>
    'mt-3 p-3 border border-primary/20 dark:border-primary/20 rounded-md bg-primary/5 dark:bg-primary/10 shadow'
);

const deleteButtonClasses = computed(() =>
    'text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors duration-150 ease-in-out p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none'
);

watch(() => props.modelValue, (newValue) => {
    if (newValue !== internalFile.value) {
        internalFile.value = newValue;
    }
});

const resetFileInput = () => {
    const fileInput = document.getElementById(props.inputId) as HTMLInputElement;
    if (fileInput) fileInput.value = '';
};

const processFile = (file: File | null) => {
    if (!file) {
        internalFile.value = null;
        emit('update:modelValue', null);
        return;
    } if (file.size > MAX_FILE_SIZE_BYTES.value) {
        emit('file-error', `Fail terlalu besar. Saiz maksimum ialah ${props.maxFileSizeMb}MB.`);
        internalFile.value = null; // Clear internal state
        emit('update:modelValue', null); // Ensure parent is also cleared
        resetFileInput();
        return;
    }

    internalFile.value = file;
    emit('update:modelValue', file);
    emit('file-error', null); // Clear any previous error
};

const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    processFile(target.files && target.files[0] ? target.files[0] : null);
};

const handleDragOver = (event: DragEvent) => {
    isDraggingOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
    isDraggingOver.value = false;
};

const handleDrop = (event: DragEvent) => {
    isDraggingOver.value = false;
    const files = event.dataTransfer?.files;
    processFile(files && files[0] ? files[0] : null);
    event.preventDefault();
};

const clearUploadedFile = () => {
    internalFile.value = null;
    emit('update:modelValue', null);
    emit('file-error', null); // Clear any error associated with the cleared file    resetFileInput();
};

</script>
