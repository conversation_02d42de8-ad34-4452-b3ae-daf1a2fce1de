<template>
    <Modal :is-open="isOpen" :title="modalTitle" @update:is-open="$emit('close')" size="2xl">
        <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Error Display -->
            <div v-if="errors?.general && errors.general.length"
                class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex">
                    <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5 flex-shrink-0" />
                    <ul class="text-sm text-red-800 dark:text-red-200 space-y-1">
                        <li v-for="(error, index) in errors.general" :key="index">{{ error }}</li>
                    </ul>
                </div>
            </div>

            <!-- Schedule Periods Management -->
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Waktu <PERSON>jar</h3>
                <div v-for="(period, index) in formData.periods" :key="index"
                    class="p-4 border rounded-lg space-y-3 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 relative">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Class Subject Combination -->
                        <div>
                            <label :for="`class-subject-${index}`"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Kelas & Subjek
                            </label>
                            <SingleSelect :id="`class-subject-${index}`"
                                :model-value="`${period.class_id}_${period.subject_id}`"
                                @update:model-value="(val: string) => updatePeriodClassSubject(index, val)"
                                :options="classSubjectOptions" placeholder="Pilih kelas dan subjek"
                                :disabled="isEditing" variant="standard" />
                        </div>

                        <!-- Days Selection -->
                        <div>
                            <label :for="`day-${index}`"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Hari
                            </label>
                            <SingleSelect :id="`day-${index}`" :model-value="period.day"
                                @update:model-value="(val: DayOfWeek) => updatePeriodDay(index, val)"
                                :options="dayOptions" placeholder="Pilih hari" variant="standard" />
                        </div>
                    </div>
                    <button type="button" @click="removePeriod(index)" v-if="formData.periods.length > 1"
                        class="absolute -top-2 -right-2 p-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800">
                        <Icon name="mdi:close" class="h-4 w-4" />
                    </button>
                </div>

                <Button type="button" @click="addPeriod" variant="outline" size="sm" :disabled="isEditing">
                    <Icon name="mdi:plus" class="mr-2 h-4 w-4" />
                    Tambah Waktu
                </Button>
                <p v-if="isEditing" class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Penambahan atau pembuangan waktu tidak dibenarkan semasa mengedit. Sila padam dan cipta semula
                    jadual
                    untuk perubahan struktur.
                </p>
            </div>
        </form>

        <template #footer>
            <div class="flex justify-end space-x-3">
                <Button variant="outline" @click="$emit('close')" :disabled="loading">
                    Batal
                </Button>
                <Button variant="primary" @click="handleSubmit" :disabled="loading || !isFormValid">
                    <Icon v-if="loading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                    {{ isEditing ? 'Kemaskini' : 'Simpan Jadual' }}
                </Button>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Modal from '~/components/ui/composite/Modal.vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import SingleSelect from '~/components/ui/base/SingleSelect.vue'
import type {
    TeacherSchedule,
    LegacyTeacherScheduleFormData,
    ClassSubjectCombination,
    DayOfWeek,
    TeacherScheduleValidationErrors,
    SchedulePeriod
} from '~/types/teacherSchedule'
import { useTeacherSchedules } from '~/composables/useTeacherSchedules'

interface Props {
    isOpen: boolean
    schedule?: TeacherSchedule | null
    availableClassSubjects: ClassSubjectCombination[]
}

interface Emits {
    (e: 'close'): void
    (e: 'saved'): void
}

const props = withDefaults(defineProps<Props>(), {
    schedule: null
})

const emit = defineEmits<Emits>()

const { loading, dayOptions, createSchedule, updateSchedule, validateScheduleForm } = useTeacherSchedules()

const formData = ref<LegacyTeacherScheduleFormData>({ periods: [] })
const errors = ref<TeacherScheduleValidationErrors | null>(null)

const isEditing = computed(() => !!props.schedule)
const modalTitle = computed(() => isEditing.value ? 'Kemaskini Jadual Mengajar' : 'Tambah Jadual Mengajar')

const classSubjectOptions = computed(() => {
    return props.availableClassSubjects.map(cs => ({
        value: `${cs.class_id}_${cs.subject_id}`,
        label: cs.combined_label,
        ...cs
    }))
})

const isFormValid = computed(() => {
    return formData.value.periods.length > 0 &&
        formData.value.periods.every(p => p.class_id && p.subject_id && p.day);
});

const getOptionFromValue = (value: string) => {
    return classSubjectOptions.value.find(opt => opt.value === value)
}

const updatePeriodClassSubject = (index: number, value: string) => {
    const [classId, subjectId] = value.split('_');
    const option = getOptionFromValue(value);
    if (option) {
        formData.value.periods[index].class_id = classId;
        formData.value.periods[index].subject_id = subjectId;
        formData.value.periods[index].class_name = option.class_name;
        formData.value.periods[index].subject_name = option.subject_name;
    }
}

const updatePeriodDay = (index: number, day: DayOfWeek) => {
    formData.value.periods[index].day = day;
}

const addPeriod = () => {
    if (isEditing.value) return; // Prevent adding periods in edit mode
    formData.value.periods.push({
        class_id: '',
        subject_id: '',
        day: 'ISNIN',
        time_slot_start: '08:00',
        time_slot_end: '08:30'
    });
}

const removePeriod = (index: number) => {
    if (isEditing.value) return; // Prevent removing in edit mode
    formData.value.periods.splice(index, 1);
}

const resetForm = () => {
    formData.value = {
        periods: [{
            class_id: '',
            subject_id: '',
            day: 'ISNIN',
            time_slot_start: '08:00',
            time_slot_end: '08:30'
        }]
    }
    errors.value = null
}

const loadScheduleData = () => {
    if (!props.schedule || !props.schedule.schedule_details) {
        resetForm();
        return;
    }
    // Deep copy to prevent direct mutation
    formData.value = JSON.parse(JSON.stringify({ periods: props.schedule.schedule_details.periods }));
}

const handleSubmit = async () => {
    errors.value = validateScheduleForm(formData.value);
    if (errors.value) {
        return;
    }

    try {
        if (isEditing.value && props.schedule) {
            await updateSchedule(props.schedule.id, formData.value);
        } else {
            await createSchedule(formData.value);
        }
        emit('saved');
    } catch (err: any) {
        errors.value = { general: [err.message || 'Ralat semasa menyimpan jadual.'] };
    }
}

watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        loadScheduleData(); // Handles both editing and new
        if (!isEditing.value && formData.value.periods.length === 0) {
            addPeriod();
        }
    }
}, { immediate: true })

watch(() => props.schedule, loadScheduleData, { deep: true });

</script>
