import { ref, computed } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'
import { useTimetable } from '~/composables/useTimetable'
import type { Database } from '~/types/supabase'

export interface CurriculumTask {
  class_id: string
  subject_id: string
  class_name: string
  subject_name: string
  frequency: number
  student_count: number | null
}

export function useCurriculumTasks() {
  const client = useSupabaseClient<Database>()
  const user = useSupabaseUser()
  const { timetableEntries, fetchTimetableEntries } = useTimetable()
  
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed property to generate curriculum tasks from timetable entries
  const curriculumTasks = computed((): CurriculumTask[] => {
    if (!timetableEntries.value || timetableEntries.value.length === 0) {
      return []
    }

    // Filter only CLASS activities with valid class_id and subject_id
    const classEntries = timetableEntries.value.filter(entry => 
      entry.activity_type === 'CLASS' && 
      entry.class_id && 
      entry.subject_id &&
      entry.class_name &&
      entry.subject_name
    )

    if (classEntries.length === 0) {
      return []
    }

    // Group by class_id + subject_id combination
    const groupedEntries = new Map<string, {
      class_id: string
      subject_id: string
      class_name: string
      subject_name: string
      entries: typeof classEntries
    }>()

    classEntries.forEach(entry => {
      const key = `${entry.class_id}_${entry.subject_id}`
      
      if (!groupedEntries.has(key)) {
        groupedEntries.set(key, {
          class_id: entry.class_id!,
          subject_id: entry.subject_id!,
          class_name: entry.class_name!,
          subject_name: entry.subject_name!,
          entries: []
        })
      }
      
      groupedEntries.get(key)!.entries.push(entry)
    })

    // Convert to curriculum tasks array
    return Array.from(groupedEntries.values()).map(group => ({
      class_id: group.class_id,
      subject_id: group.subject_id,
      class_name: group.class_name,
      subject_name: group.subject_name,
      frequency: group.entries.length,
      student_count: null // Will be populated by fetchStudentCounts
    })).sort((a, b) => {
      // Sort by class name first, then by subject name
      const classCompare = a.class_name.localeCompare(b.class_name)
      if (classCompare !== 0) return classCompare
      return a.subject_name.localeCompare(b.subject_name)
    })
  })

  // Fetch student counts from profiles.class_subjects
  const fetchStudentCounts = async (tasks: CurriculumTask[]): Promise<CurriculumTask[]> => {
    if (!user.value || tasks.length === 0) {
      return tasks
    }

    try {
      // Fetch user's profile with class_subjects data
      const { data: profileData, error: profileError } = await client
        .from('profiles')
        .select('class_subjects')
        .eq('id', user.value.id)
        .single()

      if (profileError) {
        console.error('Error fetching profile:', profileError)
        return tasks
      }

      // Parse class_subjects JSONB data
      let classSubjects: any[] = []
      if (Array.isArray(profileData.class_subjects)) {
        classSubjects = profileData.class_subjects
      } else if (profileData.class_subjects && typeof profileData.class_subjects === 'object') {
        classSubjects = [profileData.class_subjects]
      }

      // Create a map for quick lookup of student counts
      const studentCountMap = new Map<string, number>()
      classSubjects.forEach((cs: any) => {
        if (cs.class_id && cs.subject_id && typeof cs.studentCount === 'number') {
          const key = `${cs.class_id}_${cs.subject_id}`
          studentCountMap.set(key, cs.studentCount)
        }
      })

      // Update tasks with student counts
      return tasks.map(task => ({
        ...task,
        student_count: studentCountMap.get(`${task.class_id}_${task.subject_id}`) || null
      }))

    } catch (err) {
      console.error('Error fetching student counts:', err)
      return tasks
    }
  }

  // Enhanced curriculum tasks with student counts
  const curriculumTasksWithStudentCounts = ref<CurriculumTask[]>([])

  // Load curriculum tasks with student counts
  const loadCurriculumTasks = async (): Promise<CurriculumTask[]> => {
    loading.value = true
    error.value = null

    try {
      // First, ensure timetable entries are loaded
      await fetchTimetableEntries()
      
      // Get the computed curriculum tasks
      const tasks = curriculumTasks.value
      
      // Fetch student counts for these tasks
      const tasksWithCounts = await fetchStudentCounts(tasks)
      
      // Update the reactive ref
      curriculumTasksWithStudentCounts.value = tasksWithCounts
      
      return tasksWithCounts
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load curriculum tasks'
      console.error('Error loading curriculum tasks:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // Check if user has any timetable entries
  const hasTimetableEntries = computed(() => {
    return timetableEntries.value && timetableEntries.value.length > 0
  })

  // Check if user has any class activities in timetable
  const hasClassActivities = computed(() => {
    return timetableEntries.value.some(entry => 
      entry.activity_type === 'CLASS' && 
      entry.class_id && 
      entry.subject_id
    )
  })

  // Get summary statistics
  const curriculumSummary = computed(() => {
    const tasks = curriculumTasksWithStudentCounts.value
    return {
      totalClassSubjects: tasks.length,
      totalPeriods: tasks.reduce((sum, task) => sum + task.frequency, 0),
      totalStudents: tasks.reduce((sum, task) => sum + (task.student_count || 0), 0),
      uniqueClasses: new Set(tasks.map(task => task.class_id)).size,
      uniqueSubjects: new Set(tasks.map(task => task.subject_id)).size
    }
  })

  return {
    // State
    loading,
    error,
    curriculumTasks: curriculumTasksWithStudentCounts,
    
    // Computed
    hasTimetableEntries,
    hasClassActivities,
    curriculumSummary,
    
    // Methods
    loadCurriculumTasks,
    fetchStudentCounts
  }
}
