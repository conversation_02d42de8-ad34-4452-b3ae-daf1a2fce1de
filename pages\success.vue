<template>
  <section class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- Success Icon -->
      <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/30">
        <svg class="h-12 w-12 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>

      <!-- Header -->
      <div>
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
          Welcome to RPHMate!
        </h2>
        <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
          Your school account has been created successfully
        </p>
      </div>

      <!-- School Info -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          School Details
        </h3>
        <div class="space-y-2 text-left">
          <div v-if="schoolData" class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">School Name:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ schoolData.schoolName }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">School Code:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ schoolData.schoolCode }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">Plan:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ formatPlanName(schoolData.selectedPlan)
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">Status:</span>
              <span class="font-medium text-green-600 dark:text-green-400">30 days free trial</span>
            </div>
          </div>
          <div v-else class="text-center text-gray-500 dark:text-gray-400">
            Loading school information...
          </div>
        </div>
      </div>

      <!-- School URL -->
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
          Your School Portal
        </h4>
        <div v-if="schoolData" class="flex items-center space-x-2">
          <code
            class="flex-1 text-sm bg-white dark:bg-gray-800 px-3 py-2 rounded border text-blue-600 dark:text-blue-400">
            {{ schoolUrl }}
          </code>
        </div>
        <div v-else class="text-center text-gray-500 dark:text-gray-400">
          Generating school URL...
        </div>
        <p class="text-xs text-blue-700 dark:text-blue-300 mt-2">
          Share this URL with your teachers to give them access to your school
        </p>
      </div>

      <!-- Next Steps -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Next Steps
        </h4>
        <div class="space-y-3 text-left">
          <div class="flex items-start space-x-3">
            <div
              class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              1
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Access your admin dashboard</p>
              <p class="text-xs text-gray-600 dark:text-gray-300">Manage billing and school settings</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div
              class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              2
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Invite your teachers</p>
              <p class="text-xs text-gray-600 dark:text-gray-300">Share the school portal URL with your team</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div
              class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              3
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Start creating lesson plans</p>
              <p class="text-xs text-gray-600 dark:text-gray-300">Begin using RPHMate's educational tools</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-3">
        <button v-if="schoolData" @click="redirectToSchool"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors">
          Access Your School Portal
        </button>

        <a href="http://localhost:3000"
          class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
          Go to Main Dashboard
        </a>
      </div>
    </div>
  </section>


</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Get session ID from URL
const route = useRoute()
const sessionId = route.query.session_id as string

// Reactive data
const schoolData = ref<any>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// Computed properties
const schoolUrl = computed(() => {
  if (!schoolData.value) return ''

  const schoolCode = schoolData.value.schoolCode
  if (process.env.NODE_ENV === 'development') {
    return `http://${schoolCode}.localhost:3000`
  }
  return `https://${schoolCode}.rphmate.com`
})

// Helper function to format plan names
const formatPlanName = (plan: string) => {
  const planNames: Record<string, string> = {
    basic: 'Basic Plan',
    professional: 'Professional Plan',
    enterprise: 'Enterprise Plan'
  }
  return planNames[plan] || plan
}

// Fetch school data from Stripe session
const fetchSchoolData = async () => {
  if (!sessionId) {
    error.value = 'No session ID provided'
    loading.value = false
    return
  }

  try {
    console.log('🔍 Fetching school data for session:', sessionId)

    const response = await $fetch('/api/stripe/get-session-data', {
      method: 'POST',
      body: { sessionId }
    }) as any

    if (response.success && response.schoolData) {
      schoolData.value = response.schoolData
      console.log('✅ School data loaded:', schoolData.value)
    } else {
      throw new Error('Failed to fetch school data')
    }
  } catch (err: any) {
    console.error('❌ Error fetching school data:', err)
    error.value = err.message || 'Failed to load school information'
  } finally {
    loading.value = false
  }
}

// Redirect to school portal
const redirectToSchool = () => {
  if (schoolData.value) {
    console.log('🚀 Redirecting to school portal:', schoolUrl.value)
    window.location.href = schoolUrl.value
  }
}

// Load school data on mount
onMounted(() => {
  fetchSchoolData()
})

// Set page head
useHead({
  title: 'Welcome to RPHMate - Account Created Successfully',
  meta: [
    {
      name: 'description',
      content: 'Your RPHMate school account has been created successfully. Start your educational journey today.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for success page */
</style>
