<template>
    <div class="space-y-6">
        <!-- Dashboard Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Dashboard Refleksi</h2>
            <div class="flex items-center gap-2">
                <UiBaseButton @click="refreshData" variant="outline" size="sm" :disabled="loading"
                    class="flex items-center gap-2">
                    <Icon name="heroicons:arrow-path" :class="{ 'animate-spin': loading }" class="h-4 w-4" />
                    Muat Semula
                </UiBaseButton>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading && !stats" class="text-center py-8">
            <div class="inline-flex items-center gap-2 text-gray-500">
                <Icon name="heroicons:arrow-path" class="h-5 w-5 animate-spin" />
                Memuatkan statistik refleksi...
            </div>
        </div>

        <!-- Error State -->
        <UiBaseAlert v-if="error" type="error" :message="error" @dismiss="error = null" />

        <!-- Statistics Cards -->
        <div v-if="stats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Total Reflections -->
            <UiCompositeCard class="p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Jumlah Refleksi</p>
                        <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ stats.total_reflections }}</p>
                    </div>
                    <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <Icon name="heroicons:chat-bubble-left-ellipsis"
                            class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Completion Rate -->
            <UiCompositeCard class="p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Kadar Penyelesaian</p>
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ Math.round(stats.completion_rate) }}%
                        </p>
                    </div>
                    <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                        <Icon name="heroicons:check-circle" class="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Average Rating -->
            <UiCompositeCard class="p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Penilaian Purata</p>
                        <div class="flex items-center gap-2">
                            <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                                {{ stats.average_rating.toFixed(1) }}
                            </p>
                            <div class="flex items-center">
                                <Icon v-for="i in 5" :key="i" name="heroicons:star-solid"
                                    :class="i <= Math.round(stats.average_rating) ? 'text-orange-400' : 'text-gray-300'"
                                    class="h-4 w-4" />
                            </div>
                        </div>
                    </div>
                    <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                        <Icon name="heroicons:star" class="h-6 w-6 text-orange-600 dark:text-orange-400" />
                    </div>
                </div>
            </UiCompositeCard>

            <!-- Objectives Achievement -->
            <UiCompositeCard class="p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Kadar Siap</p>
                        <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {{ Math.round(stats.completion_rate) }}%
                        </p>
                    </div>
                    <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                        <Icon name="heroicons:trophy" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                </div>
            </UiCompositeCard>
        </div>

        <!-- Recent Reflections Summary -->
        <UiCompositeCard v-if="recentReflections && recentReflections.length > 0" class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Refleksi Terkini</h3>
            <div class="space-y-4">
                <div v-for="reflection in recentReflections" :key="reflection.lesson_plan_id"
                    class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex-1">
                        <p class="font-medium text-gray-900 dark:text-white">{{ reflection.lesson_plan?.file_name ||
                            'RPH Tidak Dikenali' }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ reflection.total_periods }} waktu, {{ reflection.periods_with_reflections }} refleksi
                            terperinci
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                            {{ reflection.overall_rating.toFixed(1) }} ⭐
                        </p>
                    </div>
                </div>
            </div>
        </UiCompositeCard>



        <!-- Empty State -->
        <UiCompositeCard v-if="!loading && stats && stats.total_reflections === 0" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
                <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-full">
                    <Icon name="heroicons:chat-bubble-left-ellipsis" class="h-8 w-8 text-gray-400" />
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Belum Ada Refleksi</h3>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        Mulakan dengan menambah refleksi untuk rancangan pengajaran anda
                    </p>
                </div>
            </div>
        </UiCompositeCard>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useLessonPlanReflections } from '@/composables/useLessonPlanReflections';
import type { ReflectionStats, LessonPlanReflectionSummary } from '@/composables/useLessonPlanReflections';

interface Props {
    weekId?: string;
    autoRefresh?: boolean;
}

const props = defineProps<Props>();

const {
    getReflectionStats,
    fetchReflectionSummaries,
    reflectionSummaries,
    loading
} = useLessonPlanReflections();

const stats = ref<ReflectionStats | null>(null);
const recentReflections = ref<LessonPlanReflectionSummary[]>([]);
const error = ref<string | null>(null);

const refreshData = async () => {
    try {
        error.value = null;

        // Fetch reflection summaries first
        await fetchReflectionSummaries(props.weekId);

        // Get stats from the new composable
        const statsData = await getReflectionStats(props.weekId);
        stats.value = statsData;

        // Use recent reflection summaries
        recentReflections.value = reflectionSummaries.value.slice(0, 5);
    } catch (err: any) {
        error.value = err.message || 'Gagal memuatkan data dashboard';
    }
};

const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Watch for week changes
watch(() => props.weekId, () => {
    refreshData();
}, { immediate: false });

// Auto-refresh if enabled
watch(() => props.autoRefresh, (newValue) => {
    if (newValue) {
        // Refresh every 30 seconds
        const interval = setInterval(refreshData, 30000);

        // Clean up interval when component unmounts or autoRefresh is disabled
        return () => clearInterval(interval);
    }
});

onMounted(() => {
    refreshData();
});
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
