import { ref, computed } from 'vue'
import { useToast } from '~/composables/useToast'

export interface ErrorContext {
  operation: string
  component?: string
  data?: any
  timestamp: Date
}

export interface EnhancedError {
  id: string
  type: 'validation' | 'network' | 'permission' | 'server' | 'client' | 'unknown'
  code?: string
  message: string
  userMessage: string
  technicalDetails?: string
  context: ErrorContext
  suggestions: string[]
  recoverable: boolean
  retryable: boolean
  timestamp: Date
}

export const useErrorHandler = () => {
  const { error: showErrorToast, warning: showWarningToast } = useToast()
  
  // Error state
  const errors = ref<EnhancedError[]>([])
  const lastError = ref<EnhancedError | null>(null)
  
  // Error type mappings
  const errorTypeMap = {
    // Network errors
    'NETWORK_ERROR': 'network',
    'FETCH_ERROR': 'network',
    'TIMEOUT_ERROR': 'network',
    'CONNECTION_ERROR': 'network',
    
    // Permission errors
    'UNAUTHORIZED': 'permission',
    'FORBIDDEN': 'permission',
    'PERMISSION_DENIED': 'permission',
    
    // Validation errors
    'VALIDATION_ERROR': 'validation',
    'INVALID_INPUT': 'validation',
    'REQUIRED_FIELD': 'validation',
    
    // Server errors
    'INTERNAL_SERVER_ERROR': 'server',
    'DATABASE_ERROR': 'server',
    'SERVICE_UNAVAILABLE': 'server',
    
    // Client errors
    'NOT_FOUND': 'client',
    'BAD_REQUEST': 'client',
    'CONFLICT': 'client'
  } as const
  
  // User-friendly messages
  const getUserMessage = (error: any, context: ErrorContext): string => {
    const operation = context.operation.toLowerCase()
    
    // Network errors
    if (error.message?.includes('fetch') || error.code === 'NETWORK_ERROR') {
      return `Gagal menyambung ke pelayan semasa ${operation}. Sila semak sambungan internet anda.`
    }
    
    // Permission errors
    if (error.status === 401 || error.code === 'UNAUTHORIZED') {
      return `Anda tidak mempunyai kebenaran untuk ${operation}. Sila log masuk semula.`
    }
    
    if (error.status === 403 || error.code === 'FORBIDDEN') {
      return `Akses ditolak untuk ${operation}. Hubungi pentadbir sistem jika perlu.`
    }
    
    // Validation errors
    if (error.code === 'VALIDATION_ERROR' || error.status === 422) {
      return `Data yang dimasukkan tidak sah untuk ${operation}. Sila semak dan cuba lagi.`
    }
    
    // Server errors
    if (error.status >= 500) {
      return `Ralat pelayan semasa ${operation}. Sila cuba lagi dalam beberapa minit.`
    }
    
    // Database errors
    if (error.message?.includes('duplicate') || error.code === '23505') {
      return `Data yang sama sudah wujud. Sila gunakan data yang berbeza.`
    }
    
    if (error.message?.includes('foreign key') || error.code === '23503') {
      return `Tidak dapat ${operation} kerana data berkaitan tidak wujud.`
    }
    
    // File upload errors
    if (operation.includes('muat naik') || operation.includes('upload')) {
      if (error.message?.includes('size') || error.message?.includes('large')) {
        return 'Fail terlalu besar. Sila pilih fail yang lebih kecil.'
      }
      if (error.message?.includes('type') || error.message?.includes('format')) {
        return 'Format fail tidak disokong. Sila pilih fail yang sesuai.'
      }
    }
    
    // Default messages
    return `Ralat berlaku semasa ${operation}. Sila cuba lagi.`
  }
  
  // Get suggestions based on error type
  const getSuggestions = (error: any, context: ErrorContext): string[] => {
    const suggestions: string[] = []
    
    if (error.message?.includes('fetch') || error.code === 'NETWORK_ERROR') {
      suggestions.push('Semak sambungan internet anda')
      suggestions.push('Cuba muat semula halaman')
      suggestions.push('Tunggu sebentar dan cuba lagi')
    }
    
    if (error.status === 401) {
      suggestions.push('Log masuk semula ke akaun anda')
      suggestions.push('Semak kelayakan akaun anda')
    }
    
    if (error.status === 403) {
      suggestions.push('Hubungi pentadbir sistem')
      suggestions.push('Semak peranan pengguna anda')
    }
    
    if (error.code === 'VALIDATION_ERROR') {
      suggestions.push('Semak semua medan yang diperlukan')
      suggestions.push('Pastikan format data adalah betul')
      suggestions.push('Lihat mesej ralat untuk butiran lanjut')
    }
    
    if (error.status >= 500) {
      suggestions.push('Cuba lagi dalam beberapa minit')
      suggestions.push('Hubungi sokongan teknikal jika masalah berterusan')
    }
    
    if (context.operation.includes('muat naik')) {
      suggestions.push('Pastikan fail tidak melebihi had saiz')
      suggestions.push('Gunakan format fail yang disokong')
      suggestions.push('Semak sambungan internet untuk fail besar')
    }
    
    return suggestions
  }
  
  // Determine if error is recoverable
  const isRecoverable = (error: any): boolean => {
    // Network errors are usually recoverable
    if (error.message?.includes('fetch') || error.code === 'NETWORK_ERROR') {
      return true
    }
    
    // Validation errors are recoverable
    if (error.code === 'VALIDATION_ERROR' || error.status === 422) {
      return true
    }
    
    // Client errors (4xx) are usually recoverable
    if (error.status >= 400 && error.status < 500) {
      return true
    }
    
    // Server errors might be recoverable
    if (error.status >= 500) {
      return true
    }
    
    return false
  }
  
  // Determine if error is retryable
  const isRetryable = (error: any): boolean => {
    // Network errors are retryable
    if (error.message?.includes('fetch') || error.code === 'NETWORK_ERROR') {
      return true
    }
    
    // Server errors are retryable
    if (error.status >= 500) {
      return true
    }
    
    // Timeout errors are retryable
    if (error.code === 'TIMEOUT_ERROR') {
      return true
    }
    
    return false
  }
  
  // Main error handling function
  const handleError = (error: any, context: ErrorContext): EnhancedError => {
    const enhancedError: EnhancedError = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: getErrorType(error),
      code: error.code || error.status?.toString(),
      message: error.message || 'Unknown error',
      userMessage: getUserMessage(error, context),
      technicalDetails: JSON.stringify(error, null, 2),
      context,
      suggestions: getSuggestions(error, context),
      recoverable: isRecoverable(error),
      retryable: isRetryable(error),
      timestamp: new Date()
    }
    
    // Store error
    errors.value.unshift(enhancedError)
    lastError.value = enhancedError
    
    // Keep only last 50 errors
    if (errors.value.length > 50) {
      errors.value = errors.value.slice(0, 50)
    }
    
    // Show toast notification
    showErrorToast(enhancedError.userMessage)
    
    return enhancedError
  }
  
  // Get error type
  const getErrorType = (error: any): EnhancedError['type'] => {
    if (error.code && errorTypeMap[error.code as keyof typeof errorTypeMap]) {
      return errorTypeMap[error.code as keyof typeof errorTypeMap]
    }
    
    if (error.status) {
      if (error.status === 401 || error.status === 403) return 'permission'
      if (error.status === 422) return 'validation'
      if (error.status >= 400 && error.status < 500) return 'client'
      if (error.status >= 500) return 'server'
    }
    
    if (error.message?.includes('fetch') || error.message?.includes('network')) {
      return 'network'
    }
    
    return 'unknown'
  }
  
  // Clear errors
  const clearErrors = () => {
    errors.value = []
    lastError.value = null
  }
  
  // Clear specific error
  const clearError = (errorId: string) => {
    errors.value = errors.value.filter(e => e.id !== errorId)
    if (lastError.value?.id === errorId) {
      lastError.value = errors.value[0] || null
    }
  }
  
  // Get errors by type
  const getErrorsByType = (type: EnhancedError['type']) => {
    return errors.value.filter(e => e.type === type)
  }
  
  // Get recent errors
  const getRecentErrors = (minutes: number = 5) => {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000)
    return errors.value.filter(e => e.timestamp > cutoff)
  }
  
  // Computed properties
  const hasErrors = computed(() => errors.value.length > 0)
  const errorCount = computed(() => errors.value.length)
  const hasRecoverableErrors = computed(() => errors.value.some(e => e.recoverable))
  const hasRetryableErrors = computed(() => errors.value.some(e => e.retryable))
  
  return {
    // State
    errors: computed(() => errors.value),
    lastError: computed(() => lastError.value),
    hasErrors,
    errorCount,
    hasRecoverableErrors,
    hasRetryableErrors,
    
    // Methods
    handleError,
    clearErrors,
    clearError,
    getErrorsByType,
    getRecentErrors
  }
}
