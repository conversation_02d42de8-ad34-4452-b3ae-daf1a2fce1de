<template>
  <div class="space-y-8">
    <!-- Page Header -->
    <SkeletonPageHeader :title-width="'12rem'" :subtitle-width="'25rem'" :action-count="2" />
    
    <!-- Calendar Navigation -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <SkeletonBox height="2.5rem" width="2.5rem" class="rounded-md" />
          <SkeletonBox height="2.5rem" width="9rem" class="rounded-md" />
          <SkeletonBox height="2.5rem" width="2.5rem" class="rounded-md" />
        </div>
        <SkeletonBox height="2rem" width="6rem" class="rounded-md" />
      </div>
    </div>
    
    <!-- Calendar Grid -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- Calendar Header -->
      <div class="grid grid-cols-7 bg-gray-50 dark:bg-gray-700">
        <div v-for="day in 7" :key="`header-${day}`" class="p-3 text-center">
          <SkeletonBox height="1rem" width="3rem" class="mx-auto" />
        </div>
      </div>
      
      <!-- Calendar Body -->
      <div class="grid grid-cols-7">
        <div v-for="cell in 42" :key="`cell-${cell}`" 
          class="min-h-[120px] p-2 border-r border-b border-gray-200 dark:border-gray-700">
          <!-- Date Number -->
          <SkeletonBox height="1.25rem" width="1.5rem" class="mb-2" />
          
          <!-- Events -->
          <div v-if="Math.random() > 0.6" class="space-y-1">
            <SkeletonBox height="1rem" width="80%" class="rounded-sm" variant="light" />
            <SkeletonBox v-if="Math.random() > 0.7" height="1rem" width="60%" class="rounded-sm" variant="light" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
</script>
