-- Create lesson_plan_reflections table
CREATE TABLE lesson_plan_reflections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    lesson_plan_id UUID NOT NULL REFERENCES lesson_plans(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reflection_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_detailed_mode BOOLEAN NOT NULL DEFAULT false,
    
    -- Quick mode fields (always required)
    overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
    objectives_achieved BOOLEAN NOT NULL DEFAULT false,
    challenges_faced TEXT NOT NULL,
    
    -- Detailed mode fields (optional)
    activity_effectiveness INTEGER CHECK (activity_effectiveness >= 1 AND activity_effectiveness <= 5),
    time_management INTEGER CHECK (time_management >= 1 AND time_management <= 5),
    student_engagement INTEGER CHECK (student_engagement >= 1 AND student_engagement <= 5),
    resource_adequacy INTEGER CHECK (resource_adequacy >= 1 AND resource_adequacy <= 5),
    improvements_needed TEXT,
    successful_strategies TEXT,
    action_items TEXT,
    student_feedback TEXT,
    notes TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_lesson_plan_reflections_lesson_plan_id ON lesson_plan_reflections(lesson_plan_id);
CREATE INDEX idx_lesson_plan_reflections_user_id ON lesson_plan_reflections(user_id);
CREATE INDEX idx_lesson_plan_reflections_reflection_date ON lesson_plan_reflections(reflection_date);

-- Create unique constraint to prevent duplicate reflections per lesson plan
CREATE UNIQUE INDEX idx_lesson_plan_reflections_unique ON lesson_plan_reflections(lesson_plan_id, user_id);

-- Enable RLS
ALTER TABLE lesson_plan_reflections ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own reflections" ON lesson_plan_reflections
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reflections" ON lesson_plan_reflections
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reflections" ON lesson_plan_reflections
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reflections" ON lesson_plan_reflections
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_lesson_plan_reflections_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_lesson_plan_reflections_updated_at
    BEFORE UPDATE ON lesson_plan_reflections
    FOR EACH ROW
    EXECUTE FUNCTION update_lesson_plan_reflections_updated_at();
