<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            School Settings
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Manage your school's configuration and preferences
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="saveAllSettings"
            :disabled="!hasUnsavedChanges || isSaving"
            class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Icon v-if="isSaving" name="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <!-- Tab Navigation -->
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            ]"
          >
            <Icon :name="tab.icon" class="w-4 h-4 mr-2" />
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-6">
        <!-- General Settings -->
        <div v-if="activeTab === 'general'" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- School Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                School Name *
              </label>
              <input
                v-model="settings.general.name"
                type="text"
                required
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="Enter school name"
              >
            </div>

            <!-- School Code -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                School Code
              </label>
              <input
                :value="settings.general.code"
                type="text"
                disabled
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white bg-gray-100 dark:bg-gray-600"
                placeholder="School code (cannot be changed)"
              >
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Your school URL: {{ settings.general.code }}.{{ baseDomain }}
              </p>
            </div>

            <!-- Description -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                v-model="settings.general.description"
                rows="3"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="Brief description of your school"
              ></textarea>
            </div>

            <!-- Location -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Location
              </label>
              <input
                v-model="settings.general.location"
                type="text"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="City, State/Province, Country"
              >
            </div>

            <!-- Established Year -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Established Year
              </label>
              <input
                v-model="settings.general.established"
                type="number"
                min="1800"
                :max="new Date().getFullYear()"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="e.g., 1995"
              >
            </div>

            <!-- Time Zone -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Time Zone
              </label>
              <select
                v-model="settings.general.timezone"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="Asia/Kuala_Lumpur">Asia/Kuala_Lumpur (GMT+8)</option>
                <option value="Asia/Singapore">Asia/Singapore (GMT+8)</option>
                <option value="Asia/Jakarta">Asia/Jakarta (GMT+7)</option>
                <option value="Asia/Bangkok">Asia/Bangkok (GMT+7)</option>
                <option value="UTC">UTC (GMT+0)</option>
              </select>
            </div>

            <!-- Language -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default Language
              </label>
              <select
                v-model="settings.general.language"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="en">English</option>
                <option value="ms">Bahasa Malaysia</option>
                <option value="zh">中文</option>
                <option value="ta">தமிழ்</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Academic Settings -->
        <div v-if="activeTab === 'academic'" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Academic Year -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Current Academic Year
              </label>
              <input
                v-model="settings.academic.currentYear"
                type="text"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="e.g., 2024/2025"
              >
            </div>

            <!-- School Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                School Type
              </label>
              <select
                v-model="settings.academic.schoolType"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="primary">Primary School</option>
                <option value="secondary">Secondary School</option>
                <option value="mixed">Primary & Secondary</option>
                <option value="preschool">Preschool</option>
                <option value="college">College</option>
                <option value="university">University</option>
              </select>
            </div>

            <!-- Curriculum -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Curriculum
              </label>
              <select
                v-model="settings.academic.curriculum"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="kssr">KSSR (Malaysia)</option>
                <option value="kssm">KSSM (Malaysia)</option>
                <option value="cambridge">Cambridge International</option>
                <option value="ib">International Baccalaureate</option>
                <option value="national">National Curriculum</option>
                <option value="custom">Custom Curriculum</option>
              </select>
            </div>

            <!-- Grading System -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Grading System
              </label>
              <select
                v-model="settings.academic.gradingSystem"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="letter">Letter Grades (A, B, C, D, F)</option>
                <option value="percentage">Percentage (0-100%)</option>
                <option value="points">Points (1-10)</option>
                <option value="gpa">GPA (0.0-4.0)</option>
              </select>
            </div>

            <!-- Class Duration -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default Class Duration (minutes)
              </label>
              <input
                v-model.number="settings.academic.classDuration"
                type="number"
                min="15"
                max="180"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="e.g., 40"
              >
            </div>

            <!-- Break Duration -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Default Break Duration (minutes)
              </label>
              <input
                v-model.number="settings.academic.breakDuration"
                type="number"
                min="5"
                max="60"
                class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="e.g., 15"
              >
            </div>
          </div>
        </div>

        <!-- Features Settings -->
        <div v-if="activeTab === 'features'" class="space-y-6">
          <div class="space-y-4">
            <!-- Feature Toggles -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div v-for="feature in featureList" :key="feature.key" class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex-1">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ feature.name }}
                  </h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    {{ feature.description }}
                  </p>
                </div>
                <div class="ml-4">
                  <input
                    v-model="(settings.features as any)[feature.key]"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Notifications Settings -->
        <div v-if="activeTab === 'notifications'" class="space-y-6">
          <div class="space-y-4">
            <div v-for="notification in notificationTypes" :key="notification.key" class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex-1">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ notification.name }}
                </h3>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ notification.description }}
                </p>
              </div>
              <div class="ml-4 flex space-x-4">
                <label class="flex items-center">
                  <input
                    v-model="(settings.notifications as any)[notification.key].email"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">Email</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="(settings.notifications as any)[notification.key].inApp"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">In-App</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccessMessage" class="bg-green-50 border border-green-200 rounded-md p-4">
      <div class="flex">
        <Icon name="heroicons:check-circle" class="h-5 w-5 text-green-400" />
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">Settings saved successfully!</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Layout and meta
definePageMeta({
  layout: 'school',
  // TEMPORARY: Disable middleware for testing subdomain functionality
  // middleware: 'school-auth'
})

// Route params
const route = useRoute()
const schoolCode = route.params.school as string

// Composables
const supabase = useSupabaseClient()
const config = useRuntimeConfig()

// State
const activeTab = ref('general')
const isSaving = ref(false)
const hasUnsavedChanges = ref(false)
const showSuccessMessage = ref(false)

const settings = ref({
  general: {
    name: '',
    code: schoolCode,
    description: '',
    location: '',
    established: null,
    timezone: 'Asia/Kuala_Lumpur',
    language: 'en'
  },
  academic: {
    currentYear: '',
    schoolType: 'primary',
    curriculum: 'kssr',
    gradingSystem: 'letter',
    classDuration: 40,
    breakDuration: 15
  },
  features: {
    lessonPlans: true,
    reflections: true,
    timetable: true,
    analytics: true,
    reports: true,
    calendar: true
  },
  notifications: {
    newTeacher: { email: true, inApp: true },
    lessonPlanShared: { email: false, inApp: true },
    systemUpdates: { email: true, inApp: true },
    weeklyReports: { email: true, inApp: false }
  }
})

// Computed
const baseDomain = computed(() => config.public.baseDomain || 'localhost:3000')

const tabs = [
  { id: 'general', name: 'General', icon: 'heroicons:cog-6-tooth' },
  { id: 'academic', name: 'Academic', icon: 'heroicons:academic-cap' },
  { id: 'features', name: 'Features', icon: 'heroicons:puzzle-piece' },
  { id: 'notifications', name: 'Notifications', icon: 'heroicons:bell' }
]

const featureList = [
  { key: 'lessonPlans', name: 'Lesson Plans', description: 'Create and manage lesson plans' },
  { key: 'reflections', name: 'Reflections', description: 'Teacher reflection system' },
  { key: 'timetable', name: 'Timetable', description: 'Class scheduling and timetables' },
  { key: 'analytics', name: 'Analytics', description: 'Usage analytics and insights' },
  { key: 'reports', name: 'Reports', description: 'Generate various reports' },
  { key: 'calendar', name: 'Calendar', description: 'School calendar and events' }
]

const notificationTypes = [
  { key: 'newTeacher', name: 'New Teacher Joined', description: 'When a new teacher joins the school' },
  { key: 'lessonPlanShared', name: 'Lesson Plan Shared', description: 'When a lesson plan is shared with you' },
  { key: 'systemUpdates', name: 'System Updates', description: 'Important system announcements' },
  { key: 'weeklyReports', name: 'Weekly Reports', description: 'Weekly activity summaries' }
]

// Methods
const fetchSchoolSettings = async () => {
  try {
    const { data: school, error } = await (supabase as any)
      .from('schools')
      .select('*')
      .eq('code', schoolCode)
      .single()

    if (error) throw error

    // Map school data to settings
    settings.value.general = {
      name: school.name || '',
      code: school.code || schoolCode,
      description: school.description || '',
      location: school.location || '',
      established: school.established || null,
      timezone: school.timezone || 'Asia/Kuala_Lumpur',
      language: school.language || 'en'
    }

    // Load other settings from school.settings JSON field if it exists
    if (school.settings) {
      const schoolSettings = typeof school.settings === 'string' 
        ? JSON.parse(school.settings) 
        : school.settings

      if (schoolSettings.academic) {
        settings.value.academic = { ...settings.value.academic, ...schoolSettings.academic }
      }
      if (schoolSettings.features) {
        settings.value.features = { ...settings.value.features, ...schoolSettings.features }
      }
      if (schoolSettings.notifications) {
        settings.value.notifications = { ...settings.value.notifications, ...schoolSettings.notifications }
      }
    }

  } catch (error) {
    console.error('Error fetching school settings:', error)
  }
}

const saveAllSettings = async () => {
  try {
    isSaving.value = true

    const updateData = {
      name: settings.value.general.name,
      description: settings.value.general.description,
      location: settings.value.general.location,
      established: settings.value.general.established,
      timezone: settings.value.general.timezone,
      language: settings.value.general.language,
      settings: JSON.stringify({
        academic: settings.value.academic,
        features: settings.value.features,
        notifications: settings.value.notifications
      })
    }

    const { error } = await (supabase as any)
      .from('schools')
      .update(updateData)
      .eq('code', schoolCode)

    if (error) throw error

    hasUnsavedChanges.value = false
    showSuccessMessage.value = true

    // Hide success message after 3 seconds
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)

  } catch (error) {
    console.error('Error saving settings:', error)
  } finally {
    isSaving.value = false
  }
}

// Watch for changes
watch(settings, () => {
  hasUnsavedChanges.value = true
}, { deep: true })

// Lifecycle
onMounted(() => {
  fetchSchoolSettings()
})
</script>
