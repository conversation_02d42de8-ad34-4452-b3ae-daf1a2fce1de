# Subject Abbreviation Auto-Fill Feature

## Overview

The Subject Abbreviation Auto-Fill feature automatically populates the "Singkatan nama subjek" (subject abbreviation) field when a user selects a subject that already has an abbreviation from previously added class-subject combinations.

## How It Works

### Data Source
The feature looks up existing abbreviations from:
1. **User's existing profile data** - Previously saved class-subject combinations from the user's profile
2. **Current session entries** - Abbreviations from entries added during the current session

### Auto-Fill Logic
1. When a user selects a subject from the dropdown
2. If the abbreviation field is currently empty
3. And the selected subject has a previously used abbreviation
4. The system automatically fills the abbreviation field with the existing value

### User Control
- Users can still override the auto-filled abbreviation by typing a different value
- The feature only auto-fills when the abbreviation field is empty
- When editing existing entries, the original abbreviation is preserved

## Implementation Details

### Key Components

#### `subjectAbbreviationLookup` (Computed Property)
```typescript
const subjectAbbreviationLookup = computed<Map<string, string>>(() => {
    const lookup = new Map<string, string>();
    
    // From initial data (user's existing profile data)
    if (props.initialData && props.initialData.length > 0) {
        props.initialData.forEach(item => {
            if (item.subject_id && (item as any).subject_abbreviation) {
                lookup.set(item.subject_id, (item as any).subject_abbreviation);
            }
        });
    }
    
    // From current session entries (override with more recent entries)
    savedClassSubjects.value.forEach(item => {
        if (item.subject && item.subjectAbbreviation && !item.subject.startsWith('session_')) {
            lookup.set(item.subject, item.subjectAbbreviation);
        }
    });
    
    return lookup;
});
```

#### Enhanced `handleSubjectSelection` Function
The function now includes auto-fill logic:
```typescript
// Auto-fill abbreviation if field is empty and we have a previous abbreviation for this subject
if (value && !formData.value.subjectAbbreviation && subjectAbbreviationLookup.value.has(value)) {
    const existingAbbreviation = subjectAbbreviationLookup.value.get(value);
    if (existingAbbreviation) {
        formData.value.subjectAbbreviation = existingAbbreviation;
        // Validate the auto-filled abbreviation
        validateField('subjectAbbreviation');
    }
}
```

## Usage Examples

### Example 1: Basic Auto-Fill
1. User adds "1A - Bahasa Inggeris" with abbreviation "BI"
2. Later, user adds "1B - Bahasa Inggeris"
3. When selecting "Bahasa Inggeris", the abbreviation field automatically shows "BI"

### Example 2: User Override
1. System auto-fills "BI" for "Bahasa Inggeris"
2. User can change it to "ENG" if preferred
3. The new abbreviation "ENG" will be used for future auto-fills

### Example 3: Session Persistence
1. User adds entry with "Matematik" → "MAT" in current session
2. User adds another entry and selects "Matematik"
3. System auto-fills "MAT" even though it wasn't saved to database yet

## Technical Considerations

### Performance
- Uses Vue computed properties for efficient reactive lookups
- Lookup map is rebuilt only when dependencies change
- O(1) lookup time for abbreviation retrieval

### Data Integrity
- Only auto-fills for real subjects (not session-only temporary subjects)
- Preserves existing abbreviations when editing entries
- Validates auto-filled values using existing validation rules

### Edge Cases Handled
1. **Empty abbreviation field**: Only auto-fills when field is empty
2. **Editing mode**: Preserves original abbreviation, doesn't auto-fill
3. **Session subjects**: Skips auto-fill for temporary session-only subjects
4. **Multiple abbreviations**: Uses most recent abbreviation if subject has different ones
5. **Form reset**: Properly clears abbreviation field for new entries

## Benefits

1. **Improved User Experience**: Reduces repetitive typing
2. **Data Consistency**: Encourages consistent abbreviation usage
3. **Time Saving**: Eliminates need to remember and retype abbreviations
4. **Flexibility**: Users maintain full control to override when needed
5. **Session Awareness**: Works with both saved and current session data

## Future Enhancements

Potential improvements that could be added:
1. Visual indicator when abbreviation is auto-filled
2. Tooltip showing "Previously used: BI" for transparency
3. Suggestion dropdown for subjects with multiple abbreviations
4. Global abbreviation standards for common subjects