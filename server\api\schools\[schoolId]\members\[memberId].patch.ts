// Update school member API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')
    const schoolId = getRouterParam(event, 'schoolId')
    const memberId = getRouterParam(event, 'memberId')

    // Get request body
    const body = await readBody(event)
    const { role, status, notes, permissions } = body

    // Validate input
    if (!schoolId || !memberId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School ID and member ID are required'
      })
    }

    if (role && !['teacher', 'supervisor', 'admin'].includes(role)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid role. Must be teacher, supervisor, or admin'
      })
    }

    if (status && !['active', 'inactive', 'suspended', 'pending'].includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid status'
      })
    }

    // Initialize Supabase client with service role for admin operations
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Check if the requesting user has admin/supervisor access to this school
    const { data: requesterMembership, error: membershipError } = await supabase
      .from('school_memberships')
      .select('role')
      .eq('user_id', user.id)
      .eq('school_id', schoolId)
      .eq('status', 'active')
      .single()

    // Also check if user is the school admin
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .select('admin_user_id')
      .eq('id', schoolId)
      .single()

    if (schoolError || !school) {
      throw createError({
        statusCode: 404,
        statusMessage: 'School not found'
      })
    }

    const isSchoolAdmin = school.admin_user_id === user.id
    const hasPermission = isSchoolAdmin || 
      (requesterMembership && ['admin', 'supervisor'].includes(requesterMembership.role))

    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to update members'
      })
    }

    // Get the current membership to validate the update
    const { data: currentMembership, error: currentError } = await supabase
      .from('school_memberships')
      .select('*')
      .eq('id', memberId)
      .eq('school_id', schoolId)
      .single()

    if (currentError || !currentMembership) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Member not found'
      })
    }

    // Prevent users from modifying their own admin status (unless they're the school owner)
    if (currentMembership.user_id === user.id && !isSchoolAdmin) {
      if (role && role !== currentMembership.role) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Cannot modify your own role'
        })
      }
      if (status && status !== currentMembership.status) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Cannot modify your own status'
        })
      }
    }

    // Prevent non-admins from creating/modifying admin roles
    if (role === 'admin' && !isSchoolAdmin) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Only school owners can assign admin roles'
      })
    }

    // Build update object
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (role !== undefined) updateData.role = role
    if (status !== undefined) updateData.status = status
    if (notes !== undefined) updateData.notes = notes
    if (permissions !== undefined) updateData.permissions = permissions

    // Update the membership
    const { data: updatedMembership, error: updateError } = await supabase
      .from('school_memberships')
      .update(updateData)
      .eq('id', memberId)
      .eq('school_id', schoolId)
      .select('*')
      .single()

    if (updateError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to update membership: ${updateError.message}`
      })
    }

    // Transform the response
    const transformedMembership = {
      id: updatedMembership.id,
      role: updatedMembership.role,
      status: updatedMembership.status,
      joined_at: updatedMembership.joined_at,
      created_at: updatedMembership.created_at,
      updated_at: updatedMembership.updated_at,
      notes: updatedMembership.notes,
      permissions: updatedMembership.permissions,
      user_id: updatedMembership.user_id
    }

    return {
      success: true,
      membership: transformedMembership,
      message: 'Member updated successfully'
    }

  } catch (error: any) {
    console.error('Update member error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during member update'
    })
  }
})
