-- Advanced Indexing Strategy for eRPH System
-- Optimized for common query patterns and high performance

-- =====================================================
-- 1. COMPOSITE INDEXES FOR COMMON QUERY PATTERNS
-- =====================================================

-- Most common query: Get user's lesson plans for a specific week
CREATE INDEX CONCURRENTLY idx_lesson_plans_user_week_optimized 
ON lesson_plans (user_id, week_id, created_at DESC);

-- Get lesson plans by date range (for dashboard/reports)
CREATE INDEX CONCURRENTLY idx_lesson_plans_user_date_range 
ON lesson_plans (user_id, created_at DESC) 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days';

-- =====================================================
-- 2. <PERSON><PERSON><PERSON>L INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Index only recent lesson plans (last 6 months) for faster queries
CREATE INDEX CONCURRENTLY idx_lesson_plans_recent 
ON lesson_plans (user_id, week_id, created_at DESC)
WHERE created_at >= CURRENT_DATE - INTERVAL '6 months';

-- Index only lesson plans with files for file management queries
CREATE INDEX CONCURRENTLY idx_lesson_plans_with_files 
ON lesson_plans (user_id, storage_file_path, created_at DESC)
WHERE storage_file_path IS NOT NULL;

-- =====================================================
-- 3. JSONB INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Index for teacher_schedules JSONB queries
CREATE INDEX CONCURRENTLY idx_teacher_schedules_class_subjects 
ON teacher_schedules USING GIN ((schedule_details->'class_subjects'));

-- Index for specific class_id searches in schedule_details
CREATE INDEX CONCURRENTLY idx_teacher_schedules_class_id 
ON teacher_schedules USING GIN ((schedule_details->'class_subjects'->0->'class_id'));

-- Index for action_items in detailed reflections
CREATE INDEX CONCURRENTLY idx_detailed_reflections_action_items 
ON lesson_plan_detailed_reflections USING GIN (action_items);

-- =====================================================
-- 4. COVERING INDEXES FOR READ-HEAVY QUERIES
-- =====================================================

-- Cover common lesson plan list queries (avoid table lookups)
CREATE INDEX CONCURRENTLY idx_lesson_plans_list_covering 
ON lesson_plans (user_id, created_at DESC) 
INCLUDE (id, week_label, class_subject_ids, days_selected, file_name);

-- Cover reflection summary queries
CREATE INDEX CONCURRENTLY idx_reflections_summary_covering 
ON lesson_plan_reflections (user_id, reflection_date DESC) 
INCLUDE (lesson_plan_id, overall_rating, objectives_achieved);

-- =====================================================
-- 5. SPECIALIZED INDEXES FOR ANALYTICS
-- =====================================================

-- For rating analytics and reporting
CREATE INDEX CONCURRENTLY idx_reflections_ratings_analytics 
ON lesson_plan_reflections (user_id, overall_rating, reflection_date DESC)
WHERE overall_rating IS NOT NULL;

-- For time management analysis
CREATE INDEX CONCURRENTLY idx_detailed_reflections_time_mgmt 
ON lesson_plan_detailed_reflections (user_id, time_management, created_at DESC)
WHERE time_management IS NOT NULL;

-- For engagement tracking
CREATE INDEX CONCURRENTLY idx_detailed_reflections_engagement 
ON lesson_plan_detailed_reflections (user_id, student_engagement, created_at DESC)
WHERE student_engagement IS NOT NULL;

-- =====================================================
-- 6. TIMETABLE OPTIMIZATION
-- =====================================================

-- Optimize timetable queries (most frequent reads)
CREATE INDEX CONCURRENTLY idx_timetable_user_day_time 
ON timetable_entries (user_id, day, time_slot_start, time_slot_end);

-- For class-subject filtering
CREATE INDEX CONCURRENTLY idx_timetable_user_class_subject 
ON timetable_entries (user_id, class_id, subject_id);

-- =====================================================
-- 7. WEEK MANAGEMENT OPTIMIZATION
-- =====================================================

-- Optimize week queries (frequently accessed)
CREATE INDEX CONCURRENTLY idx_rph_weeks_user_number 
ON rph_weeks (user_id, week_number DESC);

-- For week submissions tracking
CREATE INDEX CONCURRENTLY idx_week_submissions_user_status 
ON user_week_submissions (user_id, submission_status, created_at DESC);

-- =====================================================
-- 8. MAINTENANCE INDEXES
-- =====================================================

-- For cleanup operations (old data removal)
CREATE INDEX CONCURRENTLY idx_lesson_plans_cleanup 
ON lesson_plans (created_at) 
WHERE created_at < CURRENT_DATE - INTERVAL '2 years';

-- For file storage cleanup
CREATE INDEX CONCURRENTLY idx_lesson_plans_file_cleanup 
ON lesson_plans (storage_file_path, created_at) 
WHERE storage_file_path IS NOT NULL 
AND created_at < CURRENT_DATE - INTERVAL '1 year';

-- =====================================================
-- 9. STATISTICS AND MONITORING
-- =====================================================

-- Update table statistics for better query planning
ANALYZE lesson_plans;
ANALYZE lesson_plan_reflections;
ANALYZE lesson_plan_detailed_reflections;
ANALYZE teacher_schedules;
ANALYZE timetable_entries;
ANALYZE rph_weeks;

-- =====================================================
-- 10. INDEX MONITORING QUERIES
-- =====================================================

-- Query to monitor index usage
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        WHEN idx_scan < 1000 THEN 'MEDIUM_USAGE'
        ELSE 'HIGH_USAGE'
    END as usage_level
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Query to find missing indexes (slow queries)
CREATE OR REPLACE VIEW potential_missing_indexes AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query LIKE '%lesson_plans%' 
   OR query LIKE '%reflections%'
   OR query LIKE '%teacher_schedules%'
ORDER BY total_time DESC
LIMIT 20;

-- =====================================================
-- 11. AUTOMATED INDEX MAINTENANCE
-- =====================================================

-- Function to rebuild indexes when fragmentation is high
CREATE OR REPLACE FUNCTION maintain_indexes()
RETURNS void AS $$
DECLARE
    rec record;
BEGIN
    -- Reindex tables with high write activity
    FOR rec IN 
        SELECT tablename 
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public' 
        AND (n_tup_ins + n_tup_upd + n_tup_del) > 10000
    LOOP
        EXECUTE 'REINDEX TABLE ' || rec.tablename;
    END LOOP;
    
    -- Update statistics
    EXECUTE 'ANALYZE';
END;
$$ LANGUAGE plpgsql;

-- Schedule weekly index maintenance
SELECT cron.schedule('maintain-indexes', '0 2 * * 0', 'SELECT maintain_indexes();');
