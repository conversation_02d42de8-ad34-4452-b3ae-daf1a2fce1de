<template>
    <div class="w-full sm:w-auto bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
        <div class="flex w-full">
            <button v-for="(option, index) in options" :key="String(option.value)" type="button"
                @click="handleToggle(option.value)" :disabled="disabled" :class="[
                    'flex-1 px-3 py-2 text-sm font-medium transition-colors text-center',
                    'disabled:opacity-50 disabled:cursor-not-allowed',
                    'flex items-center justify-center gap-1',
                    'whitespace-nowrap',
                    // Rounded corners logic
                    index === 0 ? 'rounded-l-md rounded-r-none' : '',
                    index === options.length - 1 ? 'rounded-r-md rounded-l-none' : '',
                    index > 0 && index < options.length - 1 ? 'rounded-none' : '',
                    // Active/inactive states
                    modelValue === option.value
                        ? 'bg-primary dark:bg-primary text-white dark:text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                ]" :aria-pressed="modelValue === option.value" :aria-label="option.ariaLabel || option.label">
                <UiBaseIcon v-if="option.icon" :name="option.icon" class="h-4 w-4" />
                <span>{{ option.label }}</span>
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
export interface ToggleOption {
    value: string | boolean
    label: string
    icon?: string
    ariaLabel?: string
}

interface Props {
    modelValue: string | boolean
    options: ToggleOption[]
    disabled?: boolean
}

interface Emits {
    (e: 'update:modelValue', value: string | boolean): void
    (e: 'change', value: string | boolean): void
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false
})

const emit = defineEmits<Emits>()

const handleToggle = (value: string | boolean) => {
    if (props.disabled) return

    emit('update:modelValue', value)
    emit('change', value)
}
</script>