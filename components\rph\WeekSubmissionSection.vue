<template>
    <!-- Week Submission Status Card Content -->
    <UiCompositeCard>

        <template #header>
            <div class="flex items-center space-x-2">
                <UiBaseIcon name="heroicons:document-plus-solid" class="w-5 h-5 text-primary" />
                <h2 v-if="currentSelectedWeek" class="text-xl font-semibold text-gray-900 dark:text-white">Status
                    Penghantaran RPH
                    untuk
                    {{ currentSelectedWeek?.name || 'Minggu Dipilih' }}</h2>
            </div>
        </template>

        <div class="space-y-2">
            <div v-if="loadingSubmissionStatus && !currentWeekSubmission">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white">Memuatkan status
                    penghantaran
                    RPH...</h2>
            </div>
            <div v-else-if="!loadingSubmissionStatus && currentWeekSubmission">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white">Status Semasa:
                    <span :class="statusBadgeClass(currentWeekSubmission.status)"
                        class="px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ml-2 inline-block">
                        {{ localizeStatus(currentWeekSubmission.status) }}
                    </span>
                </h2>
                <!-- Submission details moved up -->
                <div class="space-y-1">
                    <p v-if="currentWeekSubmission.status === 'Dihantar' && currentWeekSubmission.submitted_at"
                        class="text-sm text-gray-600 dark:text-gray-400">
                        Dihantar pada: {{ formatDate(currentWeekSubmission.submitted_at) }}
                    </p>
                    <p v-if="(currentWeekSubmission.status === 'Disemak' || currentWeekSubmission.status === 'Ditolak') && currentWeekSubmission.reviewed_at"
                        class="text-sm text-gray-600 dark:text-gray-400">
                        Disemak pada: {{ formatDate(currentWeekSubmission.reviewed_at) }}
                        <span v-if="currentWeekSubmission.reviewer_id">oleh Penyelia (ID: {{
                            currentWeekSubmission.reviewer_id.substring(0, 8) }}...)</span>
                    </p>
                    <p v-if="currentWeekSubmission.status === 'Ditolak' && currentWeekSubmission.supervisor_comments"
                        class="text-sm text-red-600 dark:text-red-400">
                        Komen Penyelia: {{ currentWeekSubmission.supervisor_comments }}
                    </p>
                    <p v-if="currentWeekSubmission?.status === 'Disemak'"
                        class="text-sm text-green-600 dark:text-green-400">
                        Minggu ini telah diluluskan. Tiada tindakan lanjut diperlukan.
                    </p>
                </div>
            </div>
            <div v-else-if="!loadingSubmissionStatus && !currentWeekSubmission">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white">Status Semasa:
                    <span :class="statusBadgeClass('Draf')"
                        class="px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ml-2 inline-block">
                        {{ localizeStatus('Draf') }}
                    </span>
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Rancangan pengajaran untuk minggu ini
                    belum
                    dihantar.</p>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end">
                <UiBaseButton @click="determineAndEmitAction" :variant="weekSubmissionButtonVariant" :size="buttonSize"
                    :disabled="isSubmitButtonDisabled" class="w-full sm:w-auto text-center">
                    <span v-if="loadingSubmissionStatus">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 inline" xmlns="http://www.w3.org/2000/svg"
                            fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                        Memproses...
                    </span>
                    <span v-else class="break-words">{{ weekSubmissionButtonText }}</span>
                </UiBaseButton>
            </div>
        </template>
    </UiCompositeCard>

    <div v-if="!currentSelectedWeek && !isLoadingRphWeeks && !isLoadingLessonPlans"
        class="text-center text-gray-500 py-8">
        <p class="text-xl">Sila pilih minggu untuk memuat naik RPH, melihat senarai RPH, dan status penghantaran RPH.
        </p>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { RphWeek } from '~/types/rph';
import type { UserWeekSubmission } from '~/composables/useWeekSubmissions'; // Assuming this path is correct
import UiBaseButton from '~/components/ui/base/Button.vue'; // Ensure path is correct

const props = defineProps<{
    currentSelectedWeek: RphWeek | null;
    currentWeekSubmission: UserWeekSubmission | null;
    loadingSubmissionStatus: boolean;
    isLoadingLessonPlans: boolean; // Used for disabling button during other loading states
    lessonPlansCount: number; // To check if any lesson plans exist for submission
    isLoadingRphWeeks: boolean; // For the placeholder message when no week is selected
}>();

const hasSubmissionDetails = computed(() => {
    if (!props.currentWeekSubmission) {
        return false;
    }
    const { status, submitted_at, reviewed_at, supervisor_comments } = props.currentWeekSubmission;
    return (status === 'Dihantar' && submitted_at) ||
        ((status === 'Disemak' || status === 'Ditolak') && reviewed_at) ||
        (status === 'Ditolak' && supervisor_comments) ||
        (status === 'Disemak');
});

const statusMap: Record<string, string> = {
    'Draf': 'Draf',
    'Dihantar': 'Dihantar',
    'Disemak': 'Disemak',
    'Ditolak': 'Ditolak',
};

const localizeStatus = (statusKey?: string) => {
    if (!statusKey) return 'N/A';
    return statusMap[statusKey] || statusKey;
};

const emit = defineEmits<{
    (e: 'submit-week', action: 'submit' | 'unsubmit' | 'resubmit'): void; // Modified emit
}>();

const weekSubmissionButtonText = computed(() => {
    if (!props.currentWeekSubmission || props.currentWeekSubmission.status === 'Draf') {
        return 'Hantar RPH untuk Semakan';
    }
    if (props.currentWeekSubmission.status === 'Ditolak') {
        return 'Hantar Semula RPH untuk Semakan';
    }
    if (props.currentWeekSubmission.status === 'Dihantar') {
        return 'Batalkan Penghantaran (Kembali ke Draf)';
    }
    if (props.currentWeekSubmission.status === 'Disemak') {
        return 'RPH Telah Diluluskan';
    }
    return 'Tindakan Minggu'; // Fallback
});

const weekSubmissionButtonVariant = computed(() => {
    if (props.currentWeekSubmission?.status === 'Dihantar') {
        return 'alert-error';
    }
    return 'primary';
});

const isSubmitButtonDisabled = computed(() => {
    if (!props.currentSelectedWeek || props.loadingSubmissionStatus || props.isLoadingLessonPlans) {
        return true;
    }
    if (props.currentWeekSubmission?.status === 'Disemak') {
        return true; // Cannot take action if already approved
    }
    // Disable submission if it's not a cancellation and no lesson plans exist
    if (props.currentWeekSubmission?.status !== 'Dihantar' && props.lessonPlansCount === 0) {
        // This is a visual cue; the parent component (rph.vue) will show an alert for this case.
        return true;
    }
    return false;
});

const buttonSize = computed((): "sm" | "md" | "lg" => {
    // Use medium size for better mobile experience
    return 'md';
});

const formatDate = (dateString?: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('ms-MY', { dateStyle: 'medium', timeStyle: 'short' });
};

const statusBadgeClass = (status?: string) => {
    if (!status) return 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-200';
    // Use the English keys for matching against the database/prop values if they haven't been updated yet,
    // but the display will be localized.
    switch (status) {
        case 'Draft':
        case 'Draf':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-700 dark:text-yellow-100';
        case 'Submitted':
        case 'Dihantar':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100';
        case 'Approved':
        case 'Disemak':
            return 'bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-100';
        case 'Rejected':
        case 'Ditolak':
            return 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
};

const determineAndEmitAction = () => {
    if (!props.currentWeekSubmission || props.currentWeekSubmission.status === 'Draf') {
        emit('submit-week', 'submit');
    } else if (props.currentWeekSubmission.status === 'Ditolak') {
        emit('submit-week', 'resubmit');
    } else if (props.currentWeekSubmission.status === 'Dihantar') {
        emit('submit-week', 'unsubmit');
    }
    // No action if 'Disemak'
};
</script>
