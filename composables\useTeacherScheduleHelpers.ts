import type { TimetableEntry, DayOfWeek } from '~/types/timetable'
import type { 
  TeacherScheduleDetails, 
  ClassSubjectSchedule, 
  SchedulePeriod 
} from '~/types/teacherSchedule'

/**
 * Helper functions for converting between timetable entries and teacher schedules
 */
export const useTeacherScheduleHelpers = () => {
  
  /**
   * Convert timetable entries to teacher schedule format for a lesson plan
   */
  const createTeacherScheduleFromTimetable = (
    timetableEntries: TimetableEntry[],
    selectedClassSubjects: string[], // composite IDs like "f5_uuid"
    selectedDays: string[]
  ): TeacherScheduleDetails => {
    const classSubjects: ClassSubjectSchedule[] = []
    
    selectedClassSubjects.forEach(compositeId => {
      const [classId, subjectId] = compositeId.split('_')
      
      // Find all timetable entries for this class-subject combination
      const relevantEntries = timetableEntries.filter(entry => 
        entry.class_id === classId && 
        entry.subject_id === subjectId &&
        selectedDays.includes(entry.day.toLowerCase())
      )
      
      if (relevantEntries.length > 0) {
        // Get class and subject names from the first entry
        const firstEntry = relevantEntries[0]
        
        // Group by day and create periods
        const dayGroups = new Map<DayOfWeek, TimetableEntry[]>()
        relevantEntries.forEach(entry => {
          const day = entry.day as DayOfWeek
          if (!dayGroups.has(day)) {
            dayGroups.set(day, [])
          }
          dayGroups.get(day)!.push(entry)
        })
        
        const periods: SchedulePeriod[] = []
        const daysScheduled: DayOfWeek[] = []
        
        dayGroups.forEach((entries, day) => {
          daysScheduled.push(day)
          entries.forEach(entry => {
            periods.push({
              day: day,
              time_slot_start: entry.time_slot_start,
              time_slot_end: entry.time_slot_end,
              // Legacy fields for backward compatibility
              class_id: classId,
              subject_id: subjectId,
              class_name: entry.class_name || classId,
              subject_name: entry.subject_name || subjectId,
              class_subject_id: compositeId
            })
          })
        })
        
        classSubjects.push({
          class_id: classId,
          class_name: firstEntry.class_name || classId,
          subject_id: subjectId,
          subject_name: firstEntry.subject_name || subjectId,
          subject_abbreviation: undefined, // Will be populated from user profile if available
          days_scheduled: daysScheduled,
          total_periods: periods.length,
          periods: periods
        })
      }
    })
    
    return {
      class_subjects: classSubjects
    }
  }
  
  /**
   * Validate that selected class-subjects and days exist in timetable
   */
  const validateTimetableCompatibility = (
    timetableEntries: TimetableEntry[],
    selectedClassSubjects: string[],
    selectedDays: string[],
    userClassSubjects?: any[] // Optional: for getting proper class/subject names
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (timetableEntries.length === 0) {
      errors.push('Sila lengkapkan jadual waktu anda terlebih dahulu sebelum menambah rancangan pengajaran.')
      return { isValid: false, errors }
    }
    
    // New logic: Check if user teaches ANY of the selected class-subjects on each selected day
    selectedDays.forEach(selectedDay => {
      // Check if user teaches any of the selected class-subjects on this day
      const hasAnyClassSubjectOnDay = selectedClassSubjects.some(compositeId => {
        const [classId, subjectId] = compositeId.split('_')
        return timetableEntries.some(entry =>
          entry.class_id === classId &&
          entry.subject_id === subjectId &&
          entry.day.toLowerCase() === selectedDay.toLowerCase()
        )
      })

      if (!hasAnyClassSubjectOnDay) {
        // Convert day name to proper Malay format
        const dayNames: { [key: string]: string } = {
          'isnin': 'Isnin',
          'selasa': 'Selasa',
          'rabu': 'Rabu',
          'khamis': 'Khamis',
          'jumaat': 'Jumaat',
          'sabtu': 'Sabtu',
          'ahad': 'Ahad'
        }

        const dayName = dayNames[selectedDay.toLowerCase()] || selectedDay

        errors.push(
          `Anda tidak mengajar mana-mana Kelas & Subjek yang dipilih pada hari ${dayName}.`
        )
      }
    })
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * Extract all unique class-subject combinations from timetable
   */
  const getAvailableClassSubjectsFromTimetable = (
    timetableEntries: TimetableEntry[]
  ): Array<{
    class_id: string
    subject_id: string
    class_name: string
    subject_name: string
    combined_label: string
  }> => {
    const uniqueCombinations = new Map<string, any>()
    
    timetableEntries.forEach(entry => {
      if (entry.class_id && entry.subject_id) {
        const key = `${entry.class_id}_${entry.subject_id}`
        if (!uniqueCombinations.has(key)) {
          uniqueCombinations.set(key, {
            class_id: entry.class_id,
            subject_id: entry.subject_id,
            class_name: entry.class_name || entry.class_id,
            subject_name: entry.subject_name || entry.subject_id,
            combined_label: `${entry.class_name || entry.class_id} - ${entry.subject_name || entry.subject_id}`
          })
        }
      }
    })
    
    return Array.from(uniqueCombinations.values())
  }
  
  return {
    createTeacherScheduleFromTimetable,
    validateTimetableCompatibility,
    getAvailableClassSubjectsFromTimetable
  }
}
