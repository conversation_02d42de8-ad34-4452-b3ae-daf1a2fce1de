<template>
    <!-- Quick Copy from Another Day Feature -->
    <div v-if="selectedClassSubject && reflectionsFromOtherDays.length > 0"
        class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800 mb-6"
        data-onboarding="quick-copy">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <Icon name="mdi:content-copy" class="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                    Salin Cepat dari <PERSON>
                </h3>
            </div>
            <Button type="button" variant="outline" size="sm" @click="isExpanded = !isExpanded"
                class="text-green-700 border-green-300 hover:bg-green-100 dark:text-green-300 dark:border-green-600 dark:hover:bg-green-800">
                <Icon :name="isExpanded ? 'mdi:chevron-up' : 'mdi:chevron-down'" class="h-4 w-4" />
            </Button>
        </div>

        <div v-if="isExpanded" class="space-y-3">
            <p class="text-xs text-green-700 dark:text-green-300">
                Pilih refleksi dari hari lain untuk menyalin data ke borang ini:
            </p>

            <div class="space-y-2 max-h-48 overflow-y-auto">
                <div v-for="dayGroup in reflectionsFromOtherDays" :key="dayGroup.day"
                    class="border border-green-200 dark:border-green-700 rounded-md">
                    <div
                        class="bg-green-100 dark:bg-green-800/30 px-3 py-2 border-b border-green-200 dark:border-green-700">
                        <h4 class="text-sm font-medium text-green-800 dark:text-green-200">
                            {{ getDayLabel(dayGroup.day) }}
                        </h4>
                    </div>

                    <div class="p-2 space-y-1">
                        <div v-for="reflection in dayGroup.reflections" :key="reflection.id"
                            class="flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ getClassSubjectLabel(reflection.class_subject_id) }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    Rating: {{ reflection.overall_rating }}/5 •
                                    {{ reflection.objectives_achieved ? 'Objektif tercapai' : 'Objektif tidak tercapai'
                                    }}
                                </p>
                            </div>
                            <Button type="button" variant="outline" size="sm"
                                @click="$emit('copyReflection', reflection)"
                                class="ml-3 text-green-600 border-green-300 hover:bg-green-50 dark:text-green-400 dark:border-green-600 dark:hover:bg-green-800">
                                <Icon name="mdi:content-copy" class="h-4 w-4 mr-1" />
                                Salin
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import type { LessonPlanDetailedReflection } from '~/types/reflections'

interface ReflectionDayGroup {
    day: string
    reflections: LessonPlanDetailedReflection[]
}

interface Props {
    selectedClassSubject: string | null
    reflectionsFromOtherDays: ReflectionDayGroup[]
    getDayLabel: (day: string) => string
    getClassSubjectLabel: (id: string) => string
}

defineProps<Props>()

defineEmits<{
    copyReflection: [reflection: LessonPlanDetailedReflection]
}>()

// Local state for expanded/collapsed
const isExpanded = ref(false)
</script>
