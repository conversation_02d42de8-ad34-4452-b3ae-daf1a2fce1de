@echo off
echo ========================================
echo eRPH+ Database Backup Script
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if SUPABASE_SERVICE_KEY is set
if "%SUPABASE_SERVICE_KEY%"=="" (
    echo Error: SUPABASE_SERVICE_KEY environment variable is not set
    echo.
    echo Please set your Supabase service role key:
    echo set SUPABASE_SERVICE_KEY=your_service_key_here
    echo.
    echo You can find your service key in your Supabase project settings
    echo under API ^> Project API keys ^> service_role
    echo.
    pause
    exit /b 1
)

echo Starting backup process...
echo.

REM Run the backup script
node backup-script.js

echo.
echo Backup process completed!
pause
