[{"column_name": "instance_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "aud", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "role", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "email", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "encrypted_password", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "email_confirmed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "invited_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "confirmation_token", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "confirmation_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "recovery_token", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "recovery_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "email_change_token_new", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "email_change", "data_type": "character varying", "is_nullable": "YES", "column_default": null, "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "email_change_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "last_sign_in_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "raw_app_meta_data", "data_type": "jsonb", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "raw_user_meta_data", "data_type": "jsonb", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "is_super_admin", "data_type": "boolean", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": "NULL::character varying", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "phone_confirmed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "phone_change", "data_type": "text", "is_nullable": "YES", "column_default": "''::character varying", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "phone_change_token", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "phone_change_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "confirmed_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "email_change_token_current", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "email_change_confirm_status", "data_type": "smallint", "is_nullable": "YES", "column_default": "0", "character_maximum_length": null, "numeric_precision": 16, "numeric_scale": 0, "datetime_precision": null}, {"column_name": "banned_until", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "reauthentication_token", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying", "character_maximum_length": 255, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "reauthentication_sent_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "is_sso_user", "data_type": "boolean", "is_nullable": "NO", "column_default": "false", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}, {"column_name": "deleted_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null, "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": 6}, {"column_name": "is_anonymous", "data_type": "boolean", "is_nullable": "NO", "column_default": "false", "character_maximum_length": null, "numeric_precision": null, "numeric_scale": null, "datetime_precision": null}]