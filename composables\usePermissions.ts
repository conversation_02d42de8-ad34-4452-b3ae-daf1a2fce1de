// Role-based access control composable
// Created: 2025-07-13

import type { SchoolMembership } from '~/types/multiTenant'

export type Permission = 
  // School management
  | 'manage_school_settings'
  | 'view_school_analytics'
  | 'manage_billing'
  
  // Member management
  | 'invite_teachers'
  | 'manage_teachers'
  | 'remove_teachers'
  | 'assign_roles'
  
  // Content management
  | 'manage_subjects'
  | 'manage_templates'
  | 'manage_documents'
  
  // Class management
  | 'create_classes'
  | 'manage_classes'
  | 'assign_teachers_to_classes'
  
  // Lesson planning
  | 'create_lesson_plans'
  | 'edit_own_lesson_plans'
  | 'edit_all_lesson_plans'
  | 'delete_lesson_plans'
  | 'approve_lesson_plans'
  
  // Schedule management
  | 'create_schedules'
  | 'edit_own_schedules'
  | 'edit_all_schedules'
  | 'manage_timetables'
  
  // Reporting and analytics
  | 'view_own_reports'
  | 'view_all_reports'
  | 'export_data'
  
  // System administration
  | 'manage_coupons'
  | 'view_system_logs'

export type Role = 'admin' | 'supervisor' | 'teacher'

// Define permissions for each role
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  admin: [
    // School management
    'manage_school_settings',
    'view_school_analytics',
    'manage_billing',
    
    // Member management
    'invite_teachers',
    'manage_teachers',
    'remove_teachers',
    'assign_roles',
    
    // Content management
    'manage_subjects',
    'manage_templates',
    'manage_documents',
    
    // Class management
    'create_classes',
    'manage_classes',
    'assign_teachers_to_classes',
    
    // Lesson planning
    'create_lesson_plans',
    'edit_own_lesson_plans',
    'edit_all_lesson_plans',
    'delete_lesson_plans',
    'approve_lesson_plans',
    
    // Schedule management
    'create_schedules',
    'edit_own_schedules',
    'edit_all_schedules',
    'manage_timetables',
    
    // Reporting and analytics
    'view_own_reports',
    'view_all_reports',
    'export_data'
  ],
  
  supervisor: [
    // Limited school management
    'view_school_analytics',
    
    // Member management (limited)
    'invite_teachers',
    'manage_teachers',
    
    // Content management
    'manage_subjects',
    'manage_templates',
    'manage_documents',
    
    // Class management
    'create_classes',
    'manage_classes',
    'assign_teachers_to_classes',
    
    // Lesson planning
    'create_lesson_plans',
    'edit_own_lesson_plans',
    'edit_all_lesson_plans',
    'approve_lesson_plans',
    
    // Schedule management
    'create_schedules',
    'edit_own_schedules',
    'edit_all_schedules',
    'manage_timetables',
    
    // Reporting and analytics
    'view_own_reports',
    'view_all_reports',
    'export_data'
  ],
  
  teacher: [
    // Content management (limited)
    'manage_subjects',
    
    // Lesson planning
    'create_lesson_plans',
    'edit_own_lesson_plans',
    
    // Schedule management
    'create_schedules',
    'edit_own_schedules',
    
    // Reporting and analytics
    'view_own_reports'
  ]
}

export const usePermissions = () => {
  // Get school context - simplified for now
  const schoolContext = ref<{
    school: any | null
    membership: any | null
    isLoading: boolean
    error: any | null
  }>({
    school: null,
    membership: null,
    isLoading: false,
    error: null
  })

  // TODO: Replace with actual useSchool() when it's properly implemented
  // const { schoolContext } = useSchool()

  // Get current user's role in the current school
  const currentRole = computed((): Role | null => {
    return schoolContext.value.membership?.role as Role || null
  })

  // Get current user's permissions
  const currentPermissions = computed((): Permission[] => {
    if (!currentRole.value) return []

    const rolePermissions = ROLE_PERMISSIONS[currentRole.value] || []
    const customPermissions = schoolContext.value.membership?.permissions || {}

    // Merge role permissions with custom permissions
    const allPermissions = [...rolePermissions]

    // Add custom granted permissions
    Object.entries(customPermissions).forEach(([permission, granted]) => {
      if (granted && !allPermissions.includes(permission as Permission)) {
        allPermissions.push(permission as Permission)
      }
    })

    // Remove custom revoked permissions
    return allPermissions.filter(permission => {
      const customSetting = customPermissions[permission]
      return customSetting !== false
    })
  })
  
  // Check if user has a specific permission
  const hasPermission = (permission: Permission): boolean => {
    return currentPermissions.value.includes(permission)
  }
  
  // Check if user has any of the specified permissions
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }
  
  // Check if user has all of the specified permissions
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }
  
  // Check if user has a specific role
  const hasRole = (role: Role): boolean => {
    return currentRole.value === role
  }
  
  // Check if user has any of the specified roles
  const hasAnyRole = (roles: Role[]): boolean => {
    return currentRole.value ? roles.includes(currentRole.value) : false
  }
  
  // Check if user is admin (school owner or admin role)
  const isAdmin = computed((): boolean => {
    const user = useSupabaseUser()
    const school = schoolContext.value.school

    // Check if user is school owner
    if (user.value && school && school.admin_user_id === user.value.id) {
      return true
    }

    // Check if user has admin role
    return hasRole('admin')
  })
  
  // Check if user is supervisor or higher
  const isSupervisorOrHigher = computed((): boolean => {
    return isAdmin.value || hasRole('supervisor')
  })
  
  // Get permissions for a specific role (utility function)
  const getPermissionsForRole = (role: Role): Permission[] => {
    return ROLE_PERMISSIONS[role] || []
  }
  
  // Get all available permissions
  const getAllPermissions = (): Permission[] => {
    const allPermissions = new Set<Permission>()
    Object.values(ROLE_PERMISSIONS).forEach(permissions => {
      permissions.forEach(permission => allPermissions.add(permission))
    })
    return Array.from(allPermissions)
  }
  
  // Permission-based navigation guards
  const requirePermission = (permission: Permission) => {
    if (!hasPermission(permission)) {
      throw createError({
        statusCode: 403,
        statusMessage: `Access denied. Required permission: ${permission}`
      })
    }
  }
  
  const requireRole = (role: Role) => {
    if (!hasRole(role)) {
      throw createError({
        statusCode: 403,
        statusMessage: `Access denied. Required role: ${role}`
      })
    }
  }
  
  const requireAnyRole = (roles: Role[]) => {
    if (!hasAnyRole(roles)) {
      throw createError({
        statusCode: 403,
        statusMessage: `Access denied. Required roles: ${roles.join(', ')}`
      })
    }
  }
  
  return {
    // State
    currentRole: readonly(currentRole),
    currentPermissions: readonly(currentPermissions),
    isAdmin: readonly(isAdmin),
    isSupervisorOrHigher: readonly(isSupervisorOrHigher),
    
    // Permission checks
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // Role checks
    hasRole,
    hasAnyRole,
    
    // Utilities
    getPermissionsForRole,
    getAllPermissions,
    
    // Guards
    requirePermission,
    requireRole,
    requireAnyRole
  }
}
