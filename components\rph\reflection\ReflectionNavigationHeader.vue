<template>
    <!-- Detailed Mode Navigation Header -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <!-- Day Tabs -->
        <div class="mb-4">
            <h3 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
                <PERSON><PERSON><PERSON>
            </h3>
            <div class="border-b border-blue-200 dark:border-blue-700 -mx-4 px-4">
                <nav class="-mb-px flex space-x-1 overflow-x-auto" data-onboarding="day-tabs">
                    <button v-for="day in dayOptions" :key="day.value" @click="$emit('dayChanged', day.value)" :class="[
                        'whitespace-nowrap border-b-2 px-4 py-2 text-sm font-medium transition-colors duration-200',
                        activeDay === day.value
                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                            : 'border-transparent text-blue-500 dark:text-blue-400 hover:border-blue-300 hover:text-blue-700 dark:hover:text-blue-300'
                    ]">
                        {{ day.label }}
                    </button>
                </nav>
            </div>
        </div> <!-- <PERSON><PERSON>-Subjek Selection -->
        <div v-if="activeDay">
            <SingleSelect :model-value="selectedClassSubject" @update:model-value="$emit('classSubjectChanged', $event)"
                :options="enhancedClassSubjectOptions" variant="standard" label="Kelas & Subjek"
                placeholder="Pilih kelas dan subjek" option-label="label" option-value="id"
                data-onboarding="class-subject-selector" />
            <p v-if="enhancedClassSubjectOptions.length === 0" class="text-xs text-blue-700 dark:text-blue-300 mt-1">
                Tiada kelas-subjek tersedia untuk hari ini.
            </p>

            <!-- Bulk Operations Button -->
            <div v-if="enhancedClassSubjectOptions.length > 0" class="mt-3 flex items-center justify-between">
                <button
                    @click="$emit('openBulkOperations')"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-md hover:bg-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-600 dark:hover:bg-purple-800/30 transition-colors"
                    type="button"
                >
                    <Icon name="heroicons:squares-plus" class="h-4 w-4 mr-2" />
                    Operasi Pukal
                </button>
                <span class="text-xs text-blue-600 dark:text-blue-400">
                    Tandakan beberapa waktu sebagai tidak terlaksana sekaligus
                </span>
            </div>

            <!-- Edit Mode Indicator -->
            <div v-if="editingDetailedReflection && selectedClassSubject"
                class="mt-3 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                <div class="flex items-center">
                    <Icon name="mdi:pencil" class="h-4 w-4 text-amber-600 dark:text-amber-400 mr-2" />
                    <span class="text-sm text-amber-800 dark:text-amber-200">
                        Menyunting refleksi sedia ada untuk {{ getClassSubjectLabel(selectedClassSubject) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Loading state for detailed mode initialization -->
        <div v-if="!dayOptions.length" class="text-center py-4">
            <Icon name="mdi:loading" class="h-5 w-5 animate-spin text-blue-600 mx-auto mb-2" />
            <p class="text-sm text-blue-700 dark:text-blue-300">Memuatkan maklumat hari dan kelas...</p>
        </div>

        <!-- Message when no days are available -->
        <div v-else-if="!activeDay && dayOptions.length === 0" class="text-center py-4">
            <div class="flex justify-center items-center">
                <Icon name="mdi:information" class="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                <div class="text-sm text-yellow-800 dark:text-yellow-200">
                    Tiada hari tersedia untuk RPH ini.
                </div>
            </div>
        </div>

        <!-- Message to select a day if none is active -->
        <div v-else-if="!activeDay && dayOptions.length > 0" class="text-center py-4">
            <div class="flex justify-center items-center">
                <Icon name="mdi:arrow-up" class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                <div class="text-sm text-blue-700 dark:text-blue-300">
                    Sila pilih hari untuk meneruskan.
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Icon from '~/components/ui/base/Icon.vue'
import SingleSelect from '~/components/ui/base/SingleSelect.vue'
import type { DayOption, ClassSubjectOption } from '~/types/reflections'

interface Props {
    dayOptions: DayOption[]
    activeDay: string | null
    selectedClassSubject: string | null
    classSubjectOptions: ClassSubjectOption[]
    enhancedClassSubjectOptions: ClassSubjectOption[]
    editingDetailedReflection: boolean
    getDayLabel: (day: string) => string
    getClassSubjectLabel: (id: string) => string
}

defineProps<Props>()

defineEmits<{
    dayChanged: [day: string]
    classSubjectChanged: [classSubject: string | null]
    openBulkOperations: []
}>()
</script>
