import { defineConfig } from "vitest/config";
import vue from "@vitejs/plugin-vue";
import path from "path"; // Import path

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./tests/setup.ts"], // Added setup file for Vitest
  },
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "."),
      "@": path.resolve(__dirname, "."),
      // '#imports': path.resolve(__dirname, '.nuxt/imports.d.ts'), // Avoid aliasing #imports directly to .d.ts for runtime
      // If other specific Nuxt aliases are needed for tests (e.g. #app), they can be added here,
      // but often mocking is preferred for Nuxt internals.
    },
  },
});
