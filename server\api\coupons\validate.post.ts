// Coupon validation API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'
import type { CouponValidationResponse } from '~/types/multiTenant'

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event)
    const { code } = body

    // Validate input
    if (!code || typeof code !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Coupon code is required'
      })
    }

    const trimmedCode = code.trim().toUpperCase()

    if (trimmedCode.length === 0) {
      return {
        success: false,
        isValid: false,
        error: 'Coupon code cannot be empty'
      }
    }

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    )

    // Call the database function to validate coupon
    const { data: isValid, error: validationError } = await supabase
      .rpc('is_coupon_valid', { coupon_code: trimmedCode })

    if (validationError) {
      console.error('Coupon validation error:', validationError)
      throw createError({
        statusCode: 500,
        statusMessage: 'Error validating coupon'
      })
    }

    if (!isValid) {
      return {
        success: true,
        isValid: false,
        error: 'Invalid, expired, or fully used coupon code'
      }
    }

    // Get coupon details
    const { data: coupon, error: couponError } = await supabase
      .from('coupons')
      .select('*')
      .eq('code', trimmedCode)
      .single()

    if (couponError || !coupon) {
      return {
        success: true,
        isValid: false,
        error: 'Coupon not found'
      }
    }

    // Calculate usage remaining
    const usageRemaining = coupon.usage_limit 
      ? coupon.usage_limit - coupon.used_count 
      : null

    const canUse = usageRemaining === null || usageRemaining > 0

    // Return validation result
    const response: CouponValidationResponse = {
      success: true,
      isValid: true,
      coupon: coupon as any, // Use the full coupon object
      message: canUse
        ? `Valid coupon: ${coupon.name || coupon.description || 'Free access'}`
        : 'Coupon usage limit reached'
    }

    return response

  } catch (error: any) {
    console.error('Coupon validation API error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    return {
      success: false,
      isValid: false,
      error: 'Internal server error during coupon validation'
    }
  }
})
