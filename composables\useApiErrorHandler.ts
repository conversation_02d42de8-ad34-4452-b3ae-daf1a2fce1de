import { ref } from 'vue'
import { useErrorHand<PERSON>, type ErrorContext } from '~/composables/useErrorHandler'
import { useToast } from '~/composables/useToast'

export interface ApiErrorResponse {
  status: number
  statusText: string
  data?: any
  headers?: Record<string, string>
}

export interface ApiError extends Error {
  response?: ApiErrorResponse
  code?: string
  config?: any
}

export const useApiErrorHandler = () => {
  const { handleError } = useErrorHandler()
  const { error: showErrorToast, warning: showWarningToast } = useToast()
  
  // State for tracking API errors
  const isRetrying = ref(false)
  const retryCount = ref(0)
  const maxRetries = 3
  
  // Handle Supabase errors specifically
  const handleSupabaseError = (error: any, context: ErrorContext) => {
    let userMessage = ''
    let suggestions: string[] = []
    
    // PostgreSQL error codes
    if (error.code) {
      switch (error.code) {
        case '23505': // Unique violation
          userMessage = 'Data yang sama sudah wujud dalam sistem.'
          suggestions = [
            'Gunakan data yang berbeza',
            'Semak data yang telah dimasukkan sebelum ini'
          ]
          break
          
        case '23503': // Foreign key violation
          userMessage = 'Data berkaitan tidak wujud atau telah dipadam.'
          suggestions = [
            'Pastikan data rujukan masih wujud',
            'Muat semula halaman untuk mendapatkan data terkini'
          ]
          break
          
        case '23502': // Not null violation
          userMessage = 'Medan yang diperlukan tidak diisi.'
          suggestions = [
            'Pastikan semua medan yang diperlukan diisi',
            'Semak borang untuk medan yang kosong'
          ]
          break
          
        case '42501': // Insufficient privilege
          userMessage = 'Anda tidak mempunyai kebenaran untuk operasi ini.'
          suggestions = [
            'Hubungi pentadbir sistem untuk mendapatkan akses',
            'Semak peranan pengguna anda'
          ]
          break
          
        case 'PGRST116': // Not found
          userMessage = 'Data yang dicari tidak dijumpai.'
          suggestions = [
            'Semak data yang diminta masih wujud',
            'Muat semula halaman untuk mendapatkan data terkini'
          ]
          break
          
        default:
          userMessage = `Ralat pangkalan data: ${error.message}`
          suggestions = [
            'Cuba lagi dalam beberapa saat',
            'Hubungi sokongan teknikal jika masalah berterusan'
          ]
      }
    }
    
    // HTTP status codes
    else if (error.status) {
      switch (error.status) {
        case 400:
          userMessage = 'Permintaan tidak sah. Sila semak data yang dimasukkan.'
          suggestions = [
            'Semak format data yang dimasukkan',
            'Pastikan semua medan diisi dengan betul'
          ]
          break
          
        case 401:
          userMessage = 'Sesi anda telah tamat. Sila log masuk semula.'
          suggestions = [
            'Log masuk semula ke akaun anda',
            'Semak kelayakan akaun anda'
          ]
          break
          
        case 403:
          userMessage = 'Akses ditolak. Anda tidak mempunyai kebenaran.'
          suggestions = [
            'Hubungi pentadbir sistem',
            'Semak peranan pengguna anda'
          ]
          break
          
        case 404:
          userMessage = 'Data yang diminta tidak dijumpai.'
          suggestions = [
            'Semak data yang diminta masih wujud',
            'Muat semula halaman'
          ]
          break
          
        case 409:
          userMessage = 'Konflik data. Data mungkin telah diubah oleh pengguna lain.'
          suggestions = [
            'Muat semula halaman untuk mendapatkan data terkini',
            'Cuba lagi selepas memuat semula'
          ]
          break
          
        case 422:
          userMessage = 'Data yang dimasukkan tidak sah.'
          suggestions = [
            'Semak semua medan yang diperlukan',
            'Pastikan format data adalah betul'
          ]
          break
          
        case 429:
          userMessage = 'Terlalu banyak permintaan. Sila tunggu sebentar.'
          suggestions = [
            'Tunggu beberapa minit sebelum cuba lagi',
            'Kurangkan kekerapan permintaan'
          ]
          break
          
        case 500:
          userMessage = 'Ralat pelayan dalaman. Sila cuba lagi.'
          suggestions = [
            'Cuba lagi dalam beberapa minit',
            'Hubungi sokongan teknikal jika masalah berterusan'
          ]
          break
          
        case 502:
          userMessage = 'Pelayan tidak dapat dihubungi. Sila cuba lagi.'
          suggestions = [
            'Semak sambungan internet anda',
            'Cuba lagi dalam beberapa saat'
          ]
          break
          
        case 503:
          userMessage = 'Perkhidmatan tidak tersedia buat masa ini.'
          suggestions = [
            'Cuba lagi dalam beberapa minit',
            'Semak status sistem'
          ]
          break
          
        default:
          userMessage = `Ralat HTTP ${error.status}: ${error.statusText || 'Unknown error'}`
          suggestions = [
            'Cuba lagi dalam beberapa saat',
            'Hubungi sokongan teknikal jika masalah berterusan'
          ]
      }
    }
    
    // Network errors
    else if (error.message?.includes('fetch') || error.name === 'NetworkError') {
      userMessage = 'Gagal menyambung ke pelayan. Sila semak sambungan internet.'
      suggestions = [
        'Semak sambungan internet anda',
        'Cuba muat semula halaman',
        'Tunggu sebentar dan cuba lagi'
      ]
    }
    
    // Default error
    else {
      userMessage = error.message || 'Ralat tidak diketahui berlaku.'
      suggestions = [
        'Cuba lagi dalam beberapa saat',
        'Muat semula halaman jika masalah berterusan'
      ]
    }
    
    return handleError({
      ...error,
      userMessage,
      suggestions
    }, context)
  }
  
  // Handle API call with automatic retry
  const handleApiCall = async <T>(
    apiCall: () => Promise<T>,
    context: ErrorContext,
    options: {
      maxRetries?: number
      retryDelay?: number
      showToast?: boolean
    } = {}
  ): Promise<T> => {
    const { 
      maxRetries: maxRetriesOption = maxRetries, 
      retryDelay = 1000,
      showToast = true 
    } = options
    
    let lastError: any
    
    for (let attempt = 0; attempt <= maxRetriesOption; attempt++) {
      try {
        retryCount.value = attempt
        isRetrying.value = attempt > 0
        
        const result = await apiCall()
        
        // Reset retry state on success
        retryCount.value = 0
        isRetrying.value = false
        
        return result
      } catch (error) {
        lastError = error
        
        // Don't retry on certain errors
        if ((error as any).status && [400, 401, 403, 404, 422].includes((error as any).status)) {
          break
        }
        
        // Don't retry on last attempt
        if (attempt === maxRetriesOption) {
          break
        }
        
        // Wait before retry
        if (retryDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
        }
      }
    }
    
    // Handle the final error
    isRetrying.value = false
    retryCount.value = 0
    
    const enhancedError = handleSupabaseError(lastError, context)
    
    if (showToast) {
      showErrorToast(enhancedError.userMessage)
    }
    
    throw enhancedError
  }
  
  // Validate response data
  const validateResponse = <T>(data: T, validator: (data: T) => boolean, errorMessage: string): T => {
    if (!validator(data)) {
      throw new Error(errorMessage)
    }
    return data
  }
  
  // Handle file upload errors
  const handleFileUploadError = (error: any, context: ErrorContext) => {
    let userMessage = ''
    let suggestions: string[] = []
    
    if (error.message?.includes('size') || error.message?.includes('large')) {
      userMessage = 'Fail terlalu besar untuk dimuat naik.'
      suggestions = [
        'Pilih fail yang lebih kecil',
        'Kompres fail sebelum muat naik',
        'Semak had saiz fail yang dibenarkan'
      ]
    } else if (error.message?.includes('type') || error.message?.includes('format')) {
      userMessage = 'Format fail tidak disokong.'
      suggestions = [
        'Gunakan format fail yang disokong (PDF, DOC, DOCX, JPG, PNG)',
        'Tukar format fail menggunakan perisian yang sesuai'
      ]
    } else if (error.status === 413) {
      userMessage = 'Fail terlalu besar untuk dimuat naik.'
      suggestions = [
        'Pilih fail yang lebih kecil',
        'Kompres fail sebelum muat naik'
      ]
    } else {
      userMessage = 'Gagal memuat naik fail. Sila cuba lagi.'
      suggestions = [
        'Semak sambungan internet anda',
        'Pastikan fail tidak rosak',
        'Cuba muat naik fail yang berbeza'
      ]
    }
    
    return handleError({
      ...error,
      userMessage,
      suggestions
    }, context)
  }
  
  return {
    // State
    isRetrying: computed(() => isRetrying.value),
    retryCount: computed(() => retryCount.value),
    
    // Methods
    handleSupabaseError,
    handleApiCall,
    handleFileUploadError,
    validateResponse
  }
}
