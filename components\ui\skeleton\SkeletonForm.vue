<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <!-- Form Title -->
    <div class="mb-6">
      <SkeletonBox height="1.5rem" width="40%" class="mb-2" />
      <SkeletonBox height="1rem" width="70%" variant="light" />
    </div>
    
    <!-- Form Fields -->
    <div class="space-y-6">
      <div v-for="field in fields" :key="`field-${field}`" class="space-y-2">
        <!-- Field Label -->
        <SkeletonBox height="0.875rem" :width="getLabelWidth(field)" variant="medium" />
        
        <!-- Field Input -->
        <div v-if="getFieldType(field) === 'input'">
          <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
        </div>
        
        <div v-else-if="getFieldType(field) === 'textarea'">
          <SkeletonBox height="6rem" width="100%" class="rounded-md" variant="light" />
        </div>
        
        <div v-else-if="getFieldType(field) === 'select'">
          <SkeletonBox height="2.5rem" width="100%" class="rounded-md" variant="light" />
        </div>
        
        <div v-else-if="getFieldType(field) === 'checkbox'">
          <div class="flex items-center space-x-3">
            <SkeletonBox height="1rem" width="1rem" class="rounded" />
            <SkeletonBox height="0.875rem" width="60%" variant="light" />
          </div>
        </div>
        
        <div v-else-if="getFieldType(field) === 'radio'">
          <div class="space-y-2">
            <div v-for="option in 3" :key="`option-${option}`" class="flex items-center space-x-3">
              <SkeletonBox height="1rem" width="1rem" class="rounded-full" />
              <SkeletonBox height="0.875rem" :width="getOptionWidth(option)" variant="light" />
            </div>
          </div>
        </div>
        
        <!-- Field Help Text -->
        <SkeletonBox v-if="Math.random() > 0.7" height="0.75rem" width="50%" variant="light" />
      </div>
    </div>
    
    <!-- Form Actions -->
    <div v-if="showButtons" class="flex items-center justify-end space-x-3 pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
      <SkeletonBox height="2.5rem" width="5rem" class="rounded-md" />
      <SkeletonBox height="2.5rem" width="6rem" class="rounded-md" />
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  fields?: number
  showButtons?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  fields: 5,
  showButtons: true
})

const getLabelWidth = (field: number): string => {
  const widths = ['25%', '30%', '20%', '35%', '28%', '22%']
  return widths[(field - 1) % widths.length]
}

const getFieldType = (field: number): string => {
  const types = ['input', 'input', 'textarea', 'select', 'input', 'checkbox', 'radio', 'input']
  return types[(field - 1) % types.length]
}

const getOptionWidth = (option: number): string => {
  const widths = ['40%', '60%', '35%']
  return widths[(option - 1) % widths.length]
}
</script>
