import { ref } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'
import type { Database } from '~/types/supabase'
import type { TimetableEntry, DayOfWeek, ActivityType } from '~/types/timetable'

type TimetableEntryDB = Database['public']['Tables']['timetable_entries']['Row']
type TimetableEntryInsert = Database['public']['Tables']['timetable_entries']['Insert']
type TimetableEntryUpdate = Database['public']['Tables']['timetable_entries']['Update']

// Helper function to normalize time format (remove seconds if present)
const normalizeTimeFormat = (time: string): string => {
  if (time.includes(':')) {
    const parts = time.split(':')
    return `${parts[0]}:${parts[1]}`
  }
  return time
}

// Global state - singleton pattern to ensure all components share the same state
const globalTimetableState = {
  timetableEntries: ref<TimetableEntry[]>([]),
  loading: ref(false),
  error: ref<string | null>(null),
  initialized: false
}

export function useTimetable() {
  const client = useSupabaseClient<Database>()
  const user = useSupabaseUser()
  
  // Use global state
  const timetableEntries = globalTimetableState.timetableEntries
  const loading = globalTimetableState.loading
  const error = globalTimetableState.error

  // Fetch all timetable entries for the current user
  const fetchTimetableEntries = async (): Promise<TimetableEntry[]> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      loading.value = false // Ensure loading is set to false even when not authenticated
      return []
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await client
        .from('timetable_entries')
        .select('*')
        .eq('user_id', user.value.id)
        .order('day')
        .order('time_slot_start')

      if (fetchError) {
        error.value = fetchError.message
        return []
      }      // Convert database format to TimetableEntry format
      const entries: TimetableEntry[] = (data || []).map(entry => ({
        id: entry.id,
        teacher_schedule_id: entry.teacher_schedule_id,
        day: entry.day as DayOfWeek,
        time_slot_start: entry.time_slot_start,
        time_slot_end: entry.time_slot_end,
        activity_type: (entry.activity_type as ActivityType) || 'CLASS',
        activity_title: entry.activity_title,
        activity_description: entry.activity_description,
        class_id: entry.class_id,
        subject_id: entry.subject_id,
        class_name: entry.class_name,
        subject_name: entry.subject_name,
        created_at: entry.created_at,
        updated_at: entry.updated_at
      }))

      timetableEntries.value = entries
      return entries
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return []
    } finally {
      loading.value = false
    }
  }

  // Create a new timetable entry
  const createTimetableEntry = async (entryData: Omit<TimetableEntry, 'id' | 'created_at' | 'updated_at'>): Promise<TimetableEntry | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      loading.value = false // Ensure loading is set to false even when not authenticated
      return null
    }

    loading.value = true
    error.value = null

    try {
      const insertData: TimetableEntryInsert = {
        user_id: user.value.id,
        teacher_schedule_id: entryData.teacher_schedule_id,
        day: entryData.day,
        time_slot_start: entryData.time_slot_start,
        time_slot_end: entryData.time_slot_end,
        class_id: entryData.class_id,
        subject_id: entryData.subject_id,
        class_name: entryData.class_name,
        subject_name: entryData.subject_name
      }

      const { data, error: insertError } = await client
        .from('timetable_entries')
        .insert(insertData)
        .select()
        .single()

      if (insertError) {
        error.value = insertError.message
        return null
      }      const newEntry: TimetableEntry = {
        id: data.id,
        teacher_schedule_id: data.teacher_schedule_id,
        day: data.day as DayOfWeek,
        time_slot_start: data.time_slot_start,
        time_slot_end: data.time_slot_end,
        activity_type: (data.activity_type as ActivityType) || 'CLASS',
        activity_title: data.activity_title,
        activity_description: data.activity_description,
        class_id: data.class_id,
        subject_id: data.subject_id,
        class_name: data.class_name,
        subject_name: data.subject_name,
        created_at: data.created_at,
        updated_at: data.updated_at
      }

      // Add to local state
      timetableEntries.value.push(newEntry)
      
      return newEntry
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return null
    } finally {
      loading.value = false
    }
  }

  // Update an existing timetable entry
  const updateTimetableEntry = async (id: string, updates: Partial<Omit<TimetableEntry, 'id' | 'created_at' | 'updated_at'>>): Promise<TimetableEntry | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      loading.value = false // Ensure loading is set to false even when not authenticated
      return null
    }

    loading.value = true
    error.value = null

    try {
      const updateData: TimetableEntryUpdate = {
        teacher_schedule_id: updates.teacher_schedule_id,
        day: updates.day,
        time_slot_start: updates.time_slot_start,
        time_slot_end: updates.time_slot_end,
        class_id: updates.class_id,
        subject_id: updates.subject_id,
        class_name: updates.class_name,
        subject_name: updates.subject_name
      }

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if ((updateData as any)[key] === undefined) {
          delete (updateData as any)[key]
        }
      })

      const { data, error: updateError } = await client
        .from('timetable_entries')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.value.id)
        .select()
        .single()

      if (updateError) {
        error.value = updateError.message
        return null
      }      const updatedEntry: TimetableEntry = {
        id: data.id,
        teacher_schedule_id: data.teacher_schedule_id,
        day: data.day as DayOfWeek,
        time_slot_start: data.time_slot_start,
        time_slot_end: data.time_slot_end,
        activity_type: (data.activity_type as ActivityType) || 'CLASS',
        activity_title: data.activity_title,
        activity_description: data.activity_description,
        class_id: data.class_id,
        subject_id: data.subject_id,
        class_name: data.class_name,
        subject_name: data.subject_name,
        created_at: data.created_at,
        updated_at: data.updated_at
      }

      // Update local state
      const index = timetableEntries.value.findIndex(entry => entry.id === id)
      if (index >= 0) {
        timetableEntries.value[index] = updatedEntry
      }

      return updatedEntry
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return null
    } finally {
      loading.value = false
    }
  }

  // Delete a timetable entry
  const deleteTimetableEntry = async (id: string): Promise<boolean> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      loading.value = false // Ensure loading is set to false even when not authenticated
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { error: deleteError } = await client
        .from('timetable_entries')
        .delete()
        .eq('id', id)
        .eq('user_id', user.value.id)

      if (deleteError) {
        error.value = deleteError.message
        return false
      }

      // Remove from local state
      const index = timetableEntries.value.findIndex(entry => entry.id === id)
      if (index >= 0) {
        timetableEntries.value.splice(index, 1)
      }

      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return false
    } finally {
      loading.value = false
    }
  }

  // Get entries for a specific day
  const getEntriesForDay = (day: DayOfWeek): TimetableEntry[] => {
    return timetableEntries.value.filter(entry => entry.day === day)
  }
  // Get entry for specific day and time slot
  const getEntryForTimeSlot = (day: DayOfWeek, timeSlotStart: string, timeSlotEnd: string): TimetableEntry | undefined => {
    const normalizedStart = normalizeTimeFormat(timeSlotStart)
    const normalizedEnd = normalizeTimeFormat(timeSlotEnd)
    
    return timetableEntries.value.find(entry => 
      entry.day === day && 
      normalizeTimeFormat(entry.time_slot_start) === normalizedStart && 
      normalizeTimeFormat(entry.time_slot_end) === normalizedEnd
    )
  }

  // Check if a time slot is available
  const isTimeSlotAvailable = (day: DayOfWeek, timeSlotStart: string, timeSlotEnd: string, excludeId?: string): boolean => {
    const normalizedStart = normalizeTimeFormat(timeSlotStart)
    const normalizedEnd = normalizeTimeFormat(timeSlotEnd)
    
    return !timetableEntries.value.some(entry => 
      entry.day === day && 
      normalizeTimeFormat(entry.time_slot_start) === normalizedStart && 
      normalizeTimeFormat(entry.time_slot_end) === normalizedEnd &&
      entry.id !== excludeId
    )
  }

  return {
    timetableEntries,
    loading,
    error,
    fetchTimetableEntries,
    createTimetableEntry,
    updateTimetableEntry,
    deleteTimetableEntry,
    getEntriesForDay,
    getEntryForTimeSlot,
    isTimeSlotAvailable
  }
}
