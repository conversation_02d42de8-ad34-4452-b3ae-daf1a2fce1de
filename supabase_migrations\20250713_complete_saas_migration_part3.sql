-- Complete SaaS Multi-tenant Migration Script - Part 3
-- Created: 2025-07-13
-- Description: Enable RLS and create security policies

-- Log migration start for part 3
INSERT INTO migration_log (migration_name, status, notes) 
VALUES ('20250713_complete_saas_migration_part3', 'started', 'Implementing RLS policies');

BEGIN;

-- =====================================================
-- STEP 8: ENABLE RLS ON ALL TABLES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE school_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;

-- Enable RLS on existing user-data tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plan_detailed_reflections ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE timetable_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE observation_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_observer_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rph_weeks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_week_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_calendar_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE annual_calendar_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE dskp_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE rpt_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE jadual_pencerapan ENABLE ROW LEVEL SECURITY;
ALTER TABLE tidak_terlaksana ENABLE ROW LEVEL SECURITY;
ALTER TABLE tindakan_susulan ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_reflection_template_preferences ENABLE ROW LEVEL SECURITY;

-- Enable RLS on global/school-specific tables
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE reflection_templates ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 9: CREATE RLS POLICIES FOR NEW TABLES
-- =====================================================

-- SCHOOLS TABLE POLICIES
DROP POLICY IF EXISTS "School admins can manage their schools" ON schools;
CREATE POLICY "School admins can manage their schools" ON schools
    FOR ALL USING (admin_user_id = auth.uid());

DROP POLICY IF EXISTS "School members can view school info" ON schools;
CREATE POLICY "School members can view school info" ON schools
    FOR SELECT USING (id = ANY(get_user_school_ids()));

-- SCHOOL MEMBERSHIPS TABLE POLICIES
DROP POLICY IF EXISTS "Users can view their own memberships" ON school_memberships;
CREATE POLICY "Users can view their own memberships" ON school_memberships
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "School admins can manage school memberships" ON school_memberships;
CREATE POLICY "School admins can manage school memberships" ON school_memberships
    FOR ALL USING (
        school_id IN (SELECT id FROM schools WHERE admin_user_id = auth.uid())
    );

DROP POLICY IF EXISTS "School admins and supervisors can view school memberships" ON school_memberships;
CREATE POLICY "School admins and supervisors can view school memberships" ON school_memberships
    FOR SELECT USING (
        school_id IN (
            SELECT school_id FROM school_memberships 
            WHERE user_id = auth.uid() AND status = 'active' AND role IN ('admin', 'supervisor')
        )
    );

-- COUPONS TABLE POLICIES
DROP POLICY IF EXISTS "Users can view active coupons for validation" ON coupons;
CREATE POLICY "Users can view active coupons for validation" ON coupons
    FOR SELECT USING (is_active = true);

-- COUPON USAGE TABLE POLICIES
DROP POLICY IF EXISTS "Users can view their own coupon usage" ON coupon_usage;
CREATE POLICY "Users can view their own coupon usage" ON coupon_usage
    FOR SELECT USING (used_by = auth.uid());

DROP POLICY IF EXISTS "School admins can view school coupon usage" ON coupon_usage;
CREATE POLICY "School admins can view school coupon usage" ON coupon_usage
    FOR SELECT USING (
        school_id IN (SELECT id FROM schools WHERE admin_user_id = auth.uid())
    );

-- =====================================================
-- STEP 10: CREATE RLS POLICIES FOR EXISTING TABLES
-- =====================================================

-- PROFILES
DROP POLICY IF EXISTS "Users can access school profiles" ON profiles;
CREATE POLICY "Users can access school profiles" ON profiles
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- LESSON PLANS
DROP POLICY IF EXISTS "Users can access school lesson plans" ON lesson_plans;
CREATE POLICY "Users can access school lesson plans" ON lesson_plans
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- LESSON PLAN DETAILED REFLECTIONS
DROP POLICY IF EXISTS "Users can access school reflections" ON lesson_plan_detailed_reflections;
CREATE POLICY "Users can access school reflections" ON lesson_plan_detailed_reflections
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TEACHER SCHEDULES
DROP POLICY IF EXISTS "Users can access school schedules" ON teacher_schedules;
CREATE POLICY "Users can access school schedules" ON teacher_schedules
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TIMETABLE ENTRIES
DROP POLICY IF EXISTS "Users can access school timetables" ON timetable_entries;
CREATE POLICY "Users can access school timetables" ON timetable_entries
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- OBSERVATION SCHEDULES
DROP POLICY IF EXISTS "Users can access school observations" ON observation_schedules;
CREATE POLICY "Users can access school observations" ON observation_schedules
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TEACHER ACTIVITIES
DROP POLICY IF EXISTS "Users can access school activities" ON teacher_activities;
CREATE POLICY "Users can access school activities" ON teacher_activities
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TEACHER TASKS
DROP POLICY IF EXISTS "Users can access school tasks" ON teacher_tasks;
CREATE POLICY "Users can access school tasks" ON teacher_tasks
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TEACHER OBSERVER ASSIGNMENTS
DROP POLICY IF EXISTS "Users can access school observer assignments" ON teacher_observer_assignments;
CREATE POLICY "Users can access school observer assignments" ON teacher_observer_assignments
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- RPH WEEKS
DROP POLICY IF EXISTS "Users can access school rph weeks" ON rph_weeks;
CREATE POLICY "Users can access school rph weeks" ON rph_weeks
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- USER WEEK SUBMISSIONS
DROP POLICY IF EXISTS "Users can access school week submissions" ON user_week_submissions;
CREATE POLICY "Users can access school week submissions" ON user_week_submissions
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- ACADEMIC CALENDAR DOCUMENTS
DROP POLICY IF EXISTS "Users can access school academic documents" ON academic_calendar_documents;
CREATE POLICY "Users can access school academic documents" ON academic_calendar_documents
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- ANNUAL CALENDAR EVENTS
DROP POLICY IF EXISTS "Users can access school calendar events" ON annual_calendar_events;
CREATE POLICY "Users can access school calendar events" ON annual_calendar_events
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- DSKP DOCUMENTS
DROP POLICY IF EXISTS "Users can access school dskp documents" ON dskp_documents;
CREATE POLICY "Users can access school dskp documents" ON dskp_documents
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- RPT DOCUMENTS
DROP POLICY IF EXISTS "Users can access school rpt documents" ON rpt_documents;
CREATE POLICY "Users can access school rpt documents" ON rpt_documents
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- ITEMS
DROP POLICY IF EXISTS "Users can access school items" ON items;
CREATE POLICY "Users can access school items" ON items
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- JADUAL PENCERAPAN
DROP POLICY IF EXISTS "Users can access school jadual pencerapan" ON jadual_pencerapan;
CREATE POLICY "Users can access school jadual pencerapan" ON jadual_pencerapan
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TIDAK TERLAKSANA
DROP POLICY IF EXISTS "Users can access school tidak terlaksana" ON tidak_terlaksana;
CREATE POLICY "Users can access school tidak terlaksana" ON tidak_terlaksana
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- TINDAKAN SUSULAN
DROP POLICY IF EXISTS "Users can access school tindakan susulan" ON tindakan_susulan;
CREATE POLICY "Users can access school tindakan susulan" ON tindakan_susulan
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- USER PREFERENCES
DROP POLICY IF EXISTS "Users can access school preferences" ON user_preferences;
CREATE POLICY "Users can access school preferences" ON user_preferences
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- USER REFLECTION TEMPLATE PREFERENCES
DROP POLICY IF EXISTS "Users can access school template preferences" ON user_reflection_template_preferences;
CREATE POLICY "Users can access school template preferences" ON user_reflection_template_preferences
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- SUBJECTS (Global + School-specific)
DROP POLICY IF EXISTS "Users can access global and school subjects" ON subjects;
CREATE POLICY "Users can access global and school subjects" ON subjects
    FOR ALL USING (
        school_id IS NULL OR school_id = ANY(get_user_school_ids())
    );

-- REFLECTION TEMPLATES (Global + School-specific)
DROP POLICY IF EXISTS "Users can access global and school templates" ON reflection_templates;
CREATE POLICY "Users can access global and school templates" ON reflection_templates
    FOR ALL USING (
        school_id IS NULL OR school_id = ANY(get_user_school_ids())
    );

COMMIT;

-- Log successful completion of part 3
UPDATE migration_log 
SET status = 'completed', notes = 'RLS policies implemented successfully'
WHERE migration_name = '20250713_complete_saas_migration_part3' AND status = 'started';
