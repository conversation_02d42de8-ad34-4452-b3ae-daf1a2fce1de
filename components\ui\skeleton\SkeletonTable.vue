<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
    <!-- Table Header -->
    <div v-if="showHeader" class="bg-gray-50 dark:bg-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
      <div class="grid gap-4" :style="{ gridTemplateColumns: `repeat(${columns}, 1fr)` }">
        <SkeletonBox 
          v-for="col in columns" 
          :key="`header-${col}`" 
          height="1rem" 
          :width="getHeaderWidth(col)" 
          variant="medium"
        />
      </div>
    </div>
    
    <!-- Table Rows -->
    <div class="divide-y divide-gray-200 dark:divide-gray-700">
      <div 
        v-for="row in rows" 
        :key="`row-${row}`" 
        class="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50"
      >
        <div class="grid gap-4 items-center" :style="{ gridTemplateColumns: `repeat(${columns}, 1fr)` }">
          <SkeletonBox 
            v-for="col in columns" 
            :key="`row-${row}-col-${col}`" 
            height="1rem" 
            :width="getCellWidth(row, col)" 
            :variant="getCellVariant(col)"
          />
        </div>
      </div>
    </div>
    
    <!-- Table Footer -->
    <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 border-t border-gray-200 dark:border-gray-600">
      <div class="flex items-center justify-between">
        <SkeletonBox height="0.875rem" width="8rem" variant="light" />
        <div class="flex items-center space-x-2">
          <SkeletonBox height="2rem" width="2rem" class="rounded" />
          <SkeletonBox height="2rem" width="2rem" class="rounded" />
          <SkeletonBox height="2rem" width="2rem" class="rounded" />
          <SkeletonBox height="2rem" width="2rem" class="rounded" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  rows?: number
  columns?: number
  showHeader?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  rows: 5,
  columns: 4,
  showHeader: true
})

const getHeaderWidth = (col: number): string => {
  const widths = ['60%', '80%', '70%', '50%', '90%', '65%']
  return widths[(col - 1) % widths.length]
}

const getCellWidth = (row: number, col: number): string => {
  const widths = [
    ['80%', '60%', '70%', '50%'],
    ['70%', '85%', '60%', '75%'],
    ['90%', '55%', '80%', '65%'],
    ['65%', '70%', '75%', '55%'],
    ['75%', '80%', '65%', '70%']
  ]
  
  const rowWidths = widths[(row - 1) % widths.length]
  return rowWidths[(col - 1) % rowWidths.length]
}

const getCellVariant = (col: number): 'light' | 'medium' | 'dark' => {
  // First column usually has primary data (medium)
  // Other columns have secondary data (light)
  return col === 1 ? 'medium' : 'light'
}
</script>
