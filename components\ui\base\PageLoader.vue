<template>
    <div v-if="isVisible" class="h-full bg-transparent z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ title }}</h2>
            <p class="text-gray-600 dark:text-gray-400">{{ subtitle }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Props {
    isVisible: boolean
    title?: string
    subtitle?: string
}

withDefaults(defineProps<Props>(), {
    title: 'Memuatkan',
    subtitle: 'Sila tunggu sebentar...'
})
</script>
