-- Database Partitioning Strategy for eRPH System
-- Optimized for 50+ users with high daily activity

-- =====================================================
-- 1. LESSON PLANS - Partition by created_at (Monthly)
-- =====================================================
-- This is your highest volume table, partition by month for optimal performance

-- Drop existing table and recreate as partitioned (BACKUP DATA FIRST!)
-- ALTER TABLE lesson_plans RENAME TO lesson_plans_backup;

CREATE TABLE lesson_plans (
    id UUID DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    user_id UUID NOT NULL,
    week_id UUID NOT NULL,
    week_label TEXT NOT NULL,
    class_subject_ids TEXT[] NOT NULL,
    days_selected TEXT[] NOT NULL,
    file_name TEXT,
    storage_file_path TEXT,
    file_mime_type TEXT,
    file_size_bytes BIGINT,
    
    PRIMARY KEY (id, created_at),
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
    FOREIGN KEY (week_id) REFERENCES rph_weeks(id) ON DELETE CASCADE
) PARTITION BY RANGE (created_at);

-- Create monthly partitions for current and future months
CREATE TABLE lesson_plans_2024_12 PARTITION OF lesson_plans
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE TABLE lesson_plans_2025_01 PARTITION OF lesson_plans
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE lesson_plans_2025_02 PARTITION OF lesson_plans
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- Add more partitions as needed...

-- =====================================================
-- 2. LESSON PLAN REFLECTIONS - Partition by created_at
-- =====================================================

CREATE TABLE lesson_plan_reflections_new (
    id UUID DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    lesson_plan_id UUID NOT NULL,
    user_id UUID NOT NULL,
    reflection_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_detailed_mode BOOLEAN NOT NULL DEFAULT false,
    overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
    objectives_achieved BOOLEAN NOT NULL DEFAULT false,
    challenges_faced TEXT NOT NULL,
    activity_effectiveness INTEGER CHECK (activity_effectiveness >= 1 AND activity_effectiveness <= 5),
    time_management INTEGER CHECK (time_management >= 1 AND time_management <= 5),
    student_engagement INTEGER CHECK (student_engagement >= 1 AND student_engagement <= 5),
    resource_adequacy INTEGER CHECK (resource_adequacy >= 1 AND resource_adequacy <= 5),
    improvements_needed TEXT,
    successful_strategies TEXT,
    action_items TEXT,
    student_feedback TEXT,
    notes TEXT,
    
    PRIMARY KEY (id, created_at),
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
) PARTITION BY RANGE (created_at);

-- Create monthly partitions
CREATE TABLE lesson_plan_reflections_2024_12 PARTITION OF lesson_plan_reflections_new
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE TABLE lesson_plan_reflections_2025_01 PARTITION OF lesson_plan_reflections_new
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- =====================================================
-- 3. DETAILED REFLECTIONS - Partition by created_at
-- =====================================================

CREATE TABLE lesson_plan_detailed_reflections_new (
    id UUID DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    lesson_plan_id UUID NOT NULL,
    class_subject_id TEXT NOT NULL,
    day TEXT NOT NULL,
    overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
    objectives_achieved BOOLEAN NOT NULL DEFAULT false,
    challenges_faced TEXT NOT NULL,
    activity_effectiveness INTEGER CHECK (activity_effectiveness >= 1 AND activity_effectiveness <= 5),
    time_management TEXT CHECK (time_management IN ('on_time', 'early', 'late')),
    student_engagement INTEGER CHECK (student_engagement >= 1 AND student_engagement <= 5),
    resource_adequacy TEXT CHECK (resource_adequacy IN ('inadequate', 'adequate', 'excellent')),
    improvements_needed TEXT,
    successful_strategies TEXT,
    action_items JSONB,
    additional_notes TEXT,
    
    PRIMARY KEY (id, created_at),
    UNIQUE (lesson_plan_id, class_subject_id, day, created_at)
) PARTITION BY RANGE (created_at);

-- Create monthly partitions
CREATE TABLE lesson_plan_detailed_reflections_2024_12 PARTITION OF lesson_plan_detailed_reflections_new
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- =====================================================
-- 4. TEACHER SCHEDULES - Partition by created_at
-- =====================================================

CREATE TABLE teacher_schedules_new (
    id UUID DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    user_id UUID NOT NULL,
    lesson_plan_id UUID,
    schedule_details JSONB NOT NULL DEFAULT '{}'::jsonb,
    
    PRIMARY KEY (id, created_at),
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_plan_id) REFERENCES lesson_plans(id, created_at) ON DELETE CASCADE,
    UNIQUE (user_id, lesson_plan_id, created_at)
) PARTITION BY RANGE (created_at);

-- Create monthly partitions
CREATE TABLE teacher_schedules_2024_12 PARTITION OF teacher_schedules_new
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- =====================================================
-- 5. OPTIMIZED INDEXES FOR PARTITIONED TABLES
-- =====================================================

-- Lesson Plans Indexes
CREATE INDEX idx_lesson_plans_user_id ON lesson_plans (user_id, created_at);
CREATE INDEX idx_lesson_plans_week_id ON lesson_plans (week_id, created_at);
CREATE INDEX idx_lesson_plans_user_week ON lesson_plans (user_id, week_id, created_at);

-- Reflections Indexes
CREATE INDEX idx_reflections_lesson_plan ON lesson_plan_reflections_new (lesson_plan_id, created_at);
CREATE INDEX idx_reflections_user ON lesson_plan_reflections_new (user_id, created_at);
CREATE INDEX idx_reflections_date ON lesson_plan_reflections_new (reflection_date, created_at);

-- Detailed Reflections Indexes
CREATE INDEX idx_detailed_reflections_lesson_plan ON lesson_plan_detailed_reflections_new (lesson_plan_id, created_at);
CREATE INDEX idx_detailed_reflections_composite ON lesson_plan_detailed_reflections_new (lesson_plan_id, class_subject_id, day, created_at);

-- Teacher Schedules Indexes
CREATE INDEX idx_teacher_schedules_user ON teacher_schedules_new (user_id, created_at);
CREATE INDEX idx_teacher_schedules_lesson_plan ON teacher_schedules_new (lesson_plan_id, created_at);

-- =====================================================
-- 6. AUTOMATED PARTITION MANAGEMENT
-- =====================================================

-- Function to create new monthly partitions automatically
CREATE OR REPLACE FUNCTION create_monthly_partitions()
RETURNS void AS $$
DECLARE
    start_date date;
    end_date date;
    table_name text;
BEGIN
    -- Get next month
    start_date := date_trunc('month', CURRENT_DATE + interval '1 month');
    end_date := start_date + interval '1 month';
    
    -- Create partition names
    table_name := 'lesson_plans_' || to_char(start_date, 'YYYY_MM');
    
    -- Create lesson_plans partition
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF lesson_plans FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
    
    -- Create reflections partition
    table_name := 'lesson_plan_reflections_' || to_char(start_date, 'YYYY_MM');
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF lesson_plan_reflections_new FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
    
    -- Create detailed reflections partition
    table_name := 'lesson_plan_detailed_reflections_' || to_char(start_date, 'YYYY_MM');
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF lesson_plan_detailed_reflections_new FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
    
    -- Create teacher schedules partition
    table_name := 'teacher_schedules_' || to_char(start_date, 'YYYY_MM');
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF teacher_schedules_new FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;

-- Schedule automatic partition creation (run monthly)
-- You can set this up as a cron job or use pg_cron extension
SELECT cron.schedule('create-partitions', '0 0 1 * *', 'SELECT create_monthly_partitions();');
