import { ref, computed } from 'vue';
import type { Database } from '~/types/supabase';
import type {
  AcademicCalendarDocument,
  AcademicCalendarDocumentInput,
  AcademicCalendarStatus,
  FileValidationResult,
  AcademicCalendarUploadResponse,
  AcademicCalendarError
} from '~/types/academicCalendarDocuments';
import {
  MAX_ACADEMIC_CALENDAR_FILE_SIZE_BYTES,
  SUPPORTED_ACADEMIC_CALENDAR_FILE_TYPES,
  validateAcademicCalendarFile
} from '~/types/academicCalendarDocuments';

type GenericSupabaseClient = ReturnType<typeof useSupabaseClient<Database>>;

const ACADEMIC_CALENDAR_FILES_BUCKET = 'academic-calendar-files';

// Storage and utility helpers
function sanitizeFilenameForStorage(originalName: string): string {
  return originalName.replace(/[^a-zA-Z0-9_.-]/g, '_');
}

function generateStorageFilePath(
  userId: string,
  originalFileName: string
): string {
  const sanitizedFileName = sanitizeFilenameForStorage(originalFileName);
  const timestamp = Date.now();
  return `${userId}/${timestamp}_${sanitizedFileName}`;
}

async function uploadFileToStorage(
  supabaseClient: GenericSupabaseClient,
  bucket: string,
  storagePath: string,
  file: File
): Promise<{ path: string }> {
  const { data, error } = await supabaseClient.storage
    .from(bucket)
    .upload(storagePath, file, { cacheControl: '3600', upsert: false });
  
  if (error) {
    console.error(`[Academic Calendar StorageHelper] Supabase Storage upload error:`, error);
    throw error;
  }
  
  if (!data?.path) {
    console.error('[Academic Calendar StorageHelper] File upload failed, no path returned.');
    throw new Error('File upload failed, no path returned.');
  }
  
  return { path: data.path };
}

async function deleteFileFromStorage(
  supabaseClient: GenericSupabaseClient,
  bucket: string,
  filePath: string
): Promise<void> {
  const { error } = await supabaseClient.storage
    .from(bucket)
    .remove([filePath]);
  
  if (error) {
    console.error(`[Academic Calendar StorageHelper] Error deleting file from storage:`, error);
    throw error;
  }
}

export function useAcademicCalendarDocuments() {
  const client = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  // State
  const academicCalendarDocument = ref<AcademicCalendarDocument | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Computed
  const academicCalendarStatus = computed<AcademicCalendarStatus>(() => ({
    hasDocument: !!academicCalendarDocument.value,
    document: academicCalendarDocument.value,
    isLoading: loading.value,
    error: error.value
  }));

  // Fetch academic calendar document for the current user
  const fetchAcademicCalendarDocument = async (): Promise<void> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const { data, error: fetchError } = await client
        .from('academic_calendar_documents')
        .select('*')
        .eq('user_id', user.value.id);

      if (fetchError) {
        console.error('[fetchAcademicCalendarDocument] Database error:', fetchError);
        throw fetchError;
      } else {
        // Handle the array result - take the first item if it exists
        academicCalendarDocument.value = data && data.length > 0 ? data[0] : null;
      }
    } catch (e) {
      console.error('[fetchAcademicCalendarDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to fetch academic calendar document';
    } finally {
      loading.value = false;
    }
  };

  // Upload new academic calendar document
  const uploadAcademicCalendarDocument = async (
    file: File
  ): Promise<AcademicCalendarUploadResponse> => {
    if (!user.value?.id) {
      const errorResponse: AcademicCalendarError = {
        type: 'permission',
        message: 'User not authenticated'
      };
      return { success: false, error: errorResponse };
    }

    // Validate file
    const validation = validateAcademicCalendarFile(file);
    if (!validation.isValid) {
      const errorResponse: AcademicCalendarError = {
        type: 'validation',
        message: validation.errorMessage || 'File validation failed'
      };
      return { success: false, error: errorResponse };
    }

    loading.value = true;
    error.value = null;

    let uploadedStoragePath: string | null = null;

    try {
      const userId = user.value.id;
      const originalFileName = file.name;

      // Generate storage file path
      const storageFilePath = generateStorageFilePath(userId, originalFileName);

      // Upload file to storage
      const uploadResult = await uploadFileToStorage(
        client,
        ACADEMIC_CALENDAR_FILES_BUCKET,
        storageFilePath,
        file
      );

      uploadedStoragePath = uploadResult.path;

      // Create database record
      const newDocumentData: Database['public']['Tables']['academic_calendar_documents']['Insert'] = {
        user_id: userId,
        file_name: originalFileName,
        storage_file_path: uploadedStoragePath,
        file_mime_type: file.type,
        file_size_bytes: file.size,
      };

      const { data: dbData, error: dbError } = await client
        .from('academic_calendar_documents')
        .insert(newDocumentData)
        .select()
        .single();

      if (dbError) {
        // If database insert fails, clean up the uploaded file
        if (uploadedStoragePath) {
          await deleteFileFromStorage(client, ACADEMIC_CALENDAR_FILES_BUCKET, uploadedStoragePath);
        }
        console.error('[uploadAcademicCalendarDocument] Database insert error:', dbError);
        throw dbError;
      }

      academicCalendarDocument.value = dbData;
      return { success: true, document: dbData };

    } catch (e) {
      console.error('[uploadAcademicCalendarDocument] Error:', e);
      const errorResponse: AcademicCalendarError = {
        type: 'upload',
        message: e instanceof Error ? e.message : 'Failed to upload academic calendar document',
        details: e
      };
      error.value = errorResponse.message;
      return { success: false, error: errorResponse };
    } finally {
      loading.value = false;
    }
  };

  // Replace existing academic calendar document
  const replaceAcademicCalendarDocument = async (
    newFile: File
  ): Promise<AcademicCalendarUploadResponse> => {
    if (!user.value?.id) {
      const errorResponse: AcademicCalendarError = {
        type: 'permission',
        message: 'User not authenticated'
      };
      return { success: false, error: errorResponse };
    }

    if (!academicCalendarDocument.value) {
      const errorResponse: AcademicCalendarError = {
        type: 'validation',
        message: 'No existing document to replace'
      };
      return { success: false, error: errorResponse };
    }

    // Validate new file
    const validation = validateAcademicCalendarFile(newFile);
    if (!validation.isValid) {
      const errorResponse: AcademicCalendarError = {
        type: 'validation',
        message: validation.errorMessage || 'File validation failed'
      };
      return { success: false, error: errorResponse };
    }

    loading.value = true;
    error.value = null;

    const existingDocument = academicCalendarDocument.value;
    let newUploadedStoragePath: string | null = null;

    try {
      const userId = user.value.id;
      const originalFileName = newFile.name;

      // Generate new storage file path
      const newStorageFilePath = generateStorageFilePath(userId, originalFileName);

      // Upload new file to storage
      const uploadResult = await uploadFileToStorage(
        client,
        ACADEMIC_CALENDAR_FILES_BUCKET,
        newStorageFilePath,
        newFile
      );

      newUploadedStoragePath = uploadResult.path;

      // Update database record
      const updateData: Database['public']['Tables']['academic_calendar_documents']['Update'] = {
        file_name: originalFileName,
        storage_file_path: newUploadedStoragePath,
        file_mime_type: newFile.type,
        file_size_bytes: newFile.size,
      };

      const { data: dbData, error: dbError } = await client
        .from('academic_calendar_documents')
        .update(updateData)
        .eq('id', existingDocument.id)
        .eq('user_id', userId)
        .select()
        .single();

      if (dbError) {
        console.error('[replaceAcademicCalendarDocument] Database update error:', dbError);
        // Clean up new uploaded file
        if (newUploadedStoragePath) {
          await deleteFileFromStorage(client, ACADEMIC_CALENDAR_FILES_BUCKET, newUploadedStoragePath);
        }
        throw dbError;
      }

      // Delete old file from storage
      try {
        await deleteFileFromStorage(client, ACADEMIC_CALENDAR_FILES_BUCKET, existingDocument.storage_file_path);
      } catch (deleteError) {
        console.warn('[replaceAcademicCalendarDocument] Warning: Failed to delete old file from storage:', deleteError);
        // Don't fail the operation if old file deletion fails
      }

      academicCalendarDocument.value = dbData;
      return { success: true, document: dbData };

    } catch (e) {
      console.error('[replaceAcademicCalendarDocument] Error:', e);
      const errorResponse: AcademicCalendarError = {
        type: 'upload',
        message: e instanceof Error ? e.message : 'Failed to replace academic calendar document',
        details: e
      };
      error.value = errorResponse.message;
      return { success: false, error: errorResponse };
    } finally {
      loading.value = false;
    }
  };

  // Delete academic calendar document
  const deleteAcademicCalendarDocument = async (): Promise<boolean> => {
    if (!user.value?.id) {
      error.value = 'User not authenticated';
      return false;
    }

    if (!academicCalendarDocument.value) {
      error.value = 'No document to delete';
      return false;
    }

    loading.value = true;
    error.value = null;

    const documentToDelete = academicCalendarDocument.value;

    try {
      const userId = user.value.id;

      // Delete from database first
      const { error: dbError } = await client
        .from('academic_calendar_documents')
        .delete()
        .eq('id', documentToDelete.id)
        .eq('user_id', userId);

      if (dbError) {
        console.error('[deleteAcademicCalendarDocument] Database delete error:', dbError);
        throw dbError;
      }

      // Delete file from storage
      try {
        await deleteFileFromStorage(client, ACADEMIC_CALENDAR_FILES_BUCKET, documentToDelete.storage_file_path);
      } catch (storageError) {
        console.warn('[deleteAcademicCalendarDocument] Warning: Failed to delete file from storage:', storageError);
        // Don't fail the operation if storage deletion fails
      }

      academicCalendarDocument.value = null;
      return true;

    } catch (e) {
      console.error('[deleteAcademicCalendarDocument] Error:', e);
      error.value = e instanceof Error ? e.message : 'Failed to delete academic calendar document';
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Get temporary public URL for file preview
  const getTemporaryPublicUrl = async (
    filePath: string,
    expiresInSeconds: number = 3600
  ): Promise<string | null> => {
    if (!filePath) {
      error.value = 'File path is required';
      return null;
    }

    try {
      const { data, error: signedUrlError } = await client.storage
        .from(ACADEMIC_CALENDAR_FILES_BUCKET)
        .createSignedUrl(filePath, expiresInSeconds);

      if (signedUrlError) {
        console.error('[getTemporaryPublicUrl] Supabase error creating signed URL:', signedUrlError);
        throw signedUrlError;
      }

      return data?.signedUrl || null;
    } catch (e) {
      console.error('[getTemporaryPublicUrl] Error getting temporary public URL:', e);
      error.value = e instanceof Error ? e.message : 'Failed to get temporary public URL';
      return null;
    }
  };

  // Validate file before upload
  const validateFile = (file: File): FileValidationResult => {
    return validateAcademicCalendarFile(file);
  };

  // Clear error state
  const clearError = () => {
    error.value = null;
  };

  // Reset state
  const resetState = () => {
    academicCalendarDocument.value = null;
    loading.value = false;
    error.value = null;
  };

  return {
    // State
    academicCalendarDocument: readonly(academicCalendarDocument),
    loading: readonly(loading),
    error: readonly(error),

    // Computed
    academicCalendarStatus,

    // Methods
    fetchAcademicCalendarDocument,
    uploadAcademicCalendarDocument,
    replaceAcademicCalendarDocument,
    deleteAcademicCalendarDocument,
    getTemporaryPublicUrl,
    validateFile,
    clearError,
    resetState
  };
}
