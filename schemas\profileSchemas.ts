import { z } from 'zod';

// =====================================================
// VALIDATION MESSAGES
// =====================================================

const PROFILE_VALIDATION_MESSAGES = {
  IC_NUMBER_INVALID: 'Format No. Kad Pengenalan tidak sah',
  DATE_INVALID: 'Format tarikh tidak sah',
  DATE_FUTURE: 'Tarikh tidak boleh pada masa hadapan',
  DATE_ORDER: 'Tarikh pelantikan mesti sebelum tarikh pengesahan jawatan',
  OPTION_NAME_REQUIRED: 'Nama opsyen diperlukan',
  QUALIFICATION_NAME_REQUIRED: 'Nama kelulusan diperlukan',
  QUALIFICATION_YEAR_INVALID: 'Tahun kelulusan tidak sah',
} as const;

// =====================================================
// REUSABLE VALIDATION SCHEMAS
// =====================================================

const OptionalString = z.string().optional().nullable();
const OptionalDate = z.string().optional().nullable();

// IC Number validation (basic Malaysian IC format)
const ICNumberSchema = z
  .string()
  .optional()
  .nullable()
  .refine(
    (val) => !val || /^\d{6}-\d{2}-\d{4}$|^\d{12}$/.test(val),
    { message: PROFILE_VALIDATION_MESSAGES.IC_NUMBER_INVALID }
  );

// Date validation
const DateSchema = z
  .string()
  .optional()
  .nullable()
  .refine(
    (val) => !val || !isNaN(Date.parse(val)),
    { message: PROFILE_VALIDATION_MESSAGES.DATE_INVALID }
  );

// Future date validation
const PastDateSchema = z
  .string()
  .optional()
  .nullable()
  .refine(
    (val) => !val || new Date(val) <= new Date(),
    { message: PROFILE_VALIDATION_MESSAGES.DATE_FUTURE }
  );

// =====================================================
// DYNAMIC FIELD SCHEMAS
// =====================================================

// Schema for user-defined options
export const OptionItemSchema = z.object({
  id: z.string(),
  name: z.string().min(1, PROFILE_VALIDATION_MESSAGES.OPTION_NAME_REQUIRED),
  created_at: z.string().optional(),
});

// Schema for academic qualifications
export const AcademicQualificationSchema = z.object({
  id: z.string(),
  name: z.string().min(1, PROFILE_VALIDATION_MESSAGES.QUALIFICATION_NAME_REQUIRED),
  institution: OptionalString,
  year: z
    .number()
    .optional()
    .nullable()
    .refine(
      (val) => !val || (val >= 1950 && val <= new Date().getFullYear()),
      { message: PROFILE_VALIDATION_MESSAGES.QUALIFICATION_YEAR_INVALID }
    ),
  created_at: z.string().optional(),
});

// =====================================================
// MAIN PROFILE SCHEMA
// =====================================================

export const ExtendedProfileSchema = z.object({
  // 1. Maklumat Peribadi (Personal Information)
  ic_number: ICNumberSchema,
  teacher_type: OptionalString,
  religion: OptionalString,
  date_of_birth: PastDateSchema,

  // 2. Opsyen (Options) - Dynamic array
  options: z.array(OptionItemSchema).default([]),

  // 3. Kelulusan Akademik (Academic Qualifications) - Dynamic array
  academic_qualifications: z.array(AcademicQualificationSchema).default([]),

  // 4. Nombor Rujukan (Reference Numbers)
  file_number: OptionalString,
  spp_reference_number: OptionalString,
  salary_number: OptionalString,
  epf_number: OptionalString,
  income_tax_number: OptionalString,

  // 5. Maklumat Pelantikan (Appointment Information)
  appointment_date: DateSchema,
  position_confirmation_date: DateSchema,
  pensionable_position_date: DateSchema,
  retirement_date: DateSchema,
}).refine(
  (data) => {
    // Validate appointment date order
    if (data.appointment_date && data.position_confirmation_date) {
      return new Date(data.appointment_date) <= new Date(data.position_confirmation_date);
    }
    return true;
  },
  {
    message: PROFILE_VALIDATION_MESSAGES.DATE_ORDER,
    path: ['position_confirmation_date'],
  }
).refine(
  (data) => {
    // Validate position confirmation and pensionable position date order
    if (data.position_confirmation_date && data.pensionable_position_date) {
      return new Date(data.position_confirmation_date) <= new Date(data.pensionable_position_date);
    }
    return true;
  },
  {
    message: 'Tarikh pengesahan jawatan mesti sebelum tarikh masuk ke jawatan berpencen',
    path: ['pensionable_position_date'],
  }
).refine(
  (data) => {
    // Validate pensionable position and retirement date order
    if (data.pensionable_position_date && data.retirement_date) {
      return new Date(data.pensionable_position_date) <= new Date(data.retirement_date);
    }
    return true;
  },
  {
    message: 'Tarikh masuk ke jawatan berpencen mesti sebelum tarikh pencen',
    path: ['retirement_date'],
  }
);

// =====================================================
// TYPE EXPORTS
// =====================================================

export type ExtendedProfile = z.infer<typeof ExtendedProfileSchema>;
export type OptionItem = z.infer<typeof OptionItemSchema>;
export type AcademicQualification = z.infer<typeof AcademicQualificationSchema>;

// =====================================================
// DROPDOWN OPTIONS
// =====================================================

export const RELIGION_OPTIONS = [
  { value: 'islam', label: 'Islam' },
  { value: 'kristian', label: 'Kristian' },
  { value: 'buddha', label: 'Buddha' },
  { value: 'hindu', label: 'Hindu' },
  { value: 'lain-lain', label: 'Lain-lain' },
] as const;

// Teacher type options will be derived from the role column + butir-asas peranan options
export const getTeacherTypeOptions = (roleData?: { code: string; label: string } | null) => {
  const baseOptions = [
    { value: 'gb', label: 'Guru Besar / Pengetua' },
    { value: 'gpk', label: 'Guru Penolong Kanan' },
    { value: 'gp', label: 'Guru Penyelaras' },
    { value: 'gc', label: 'Guru Cemerlang' },
    { value: 'ga', label: 'Guru Akademik' },
  ];

  // Add role data if available and not already in base options
  if (roleData && roleData.code && roleData.label) {
    const existingOption = baseOptions.find(opt => opt.value === roleData.code);
    if (!existingOption) {
      // Add the user's current role as an option
      baseOptions.unshift({ value: roleData.code, label: roleData.label });
    }
  }

  return baseOptions;
};
