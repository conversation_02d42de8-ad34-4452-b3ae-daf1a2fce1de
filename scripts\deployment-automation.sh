#!/bin/bash

# RPHMate SaaS - Deployment Automation Script
# Created: 2025-07-13
# Purpose: Automate deployment checklist verification and execution

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

info() {
    echo -e "${CYAN}ℹ️  $1${NC}" | tee -a "$LOG_FILE"
}

# Initialize deployment log
init_log() {
    echo "RPHMate SaaS Deployment Log" > "$LOG_FILE"
    echo "Started: $(date)" >> "$LOG_FILE"
    echo "========================================" >> "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check Node.js version
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -ge 18 ]; then
            success "Node.js version: $NODE_VERSION"
        else
            error "Node.js version $NODE_VERSION is too old. Requires >= 18.0.0"
        fi
    else
        error "Node.js is not installed"
    fi
    
    # Check npm
    if command -v npm >/dev/null 2>&1; then
        NPM_VERSION=$(npm --version)
        success "npm version: $NPM_VERSION"
    else
        error "npm is not installed"
    fi
    
    # Check required files
    REQUIRED_FILES=(
        "package.json"
        "nuxt.config.ts"
        ".env.production"
        "database/schema.sql"
        "database/rls-policies.sql"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$PROJECT_ROOT/$file" ]; then
            success "Required file exists: $file"
        else
            error "Required file missing: $file"
        fi
    done
    
    # Check environment variables
    if [ -f "$PROJECT_ROOT/.env.production" ]; then
        source "$PROJECT_ROOT/.env.production"
        
        REQUIRED_VARS=(
            "SUPABASE_URL"
            "SUPABASE_ANON_KEY"
            "NUXT_PUBLIC_BASE_DOMAIN"
        )
        
        for var in "${REQUIRED_VARS[@]}"; do
            if [ -n "${!var}" ]; then
                success "Environment variable set: $var"
            else
                error "Environment variable missing: $var"
            fi
        done
    else
        error ".env.production file not found"
    fi
}

# Run tests
run_tests() {
    log "Running comprehensive tests..."
    
    cd "$PROJECT_ROOT"
    
    # TypeScript compilation
    if npm run typecheck; then
        success "TypeScript compilation passed"
    else
        error "TypeScript compilation failed"
    fi
    
    # Lint checks
    if npm run lint 2>/dev/null; then
        success "Lint checks passed"
    else
        warning "Lint checks failed or not available"
    fi
    
    # Unit tests (if available)
    if npm run test:unit 2>/dev/null; then
        success "Unit tests passed"
    else
        warning "Unit tests not available or failed"
    fi
    
    # Subdomain tests
    if node scripts/test-subdomains.js --quick; then
        success "Subdomain tests passed"
    else
        warning "Subdomain tests failed - check configuration"
    fi
}

# Build application
build_application() {
    log "Building application for production..."
    
    cd "$PROJECT_ROOT"
    
    # Install dependencies
    if npm ci --production=false; then
        success "Dependencies installed"
    else
        error "Failed to install dependencies"
    fi
    
    # Build application
    if npm run build; then
        success "Application built successfully"
    else
        error "Application build failed"
    fi
    
    # Check build output
    if [ -d ".output" ]; then
        BUILD_SIZE=$(du -sh .output | cut -f1)
        success "Build output created: $BUILD_SIZE"
    else
        error "Build output directory not found"
    fi
}

# Validate configuration
validate_configuration() {
    log "Validating production configuration..."
    
    # Check Nuxt configuration
    if node -e "
        const config = require('./nuxt.config.ts').default;
        if (config.runtimeConfig?.public?.baseDomain) {
            console.log('✅ Nuxt configuration valid');
            process.exit(0);
        } else {
            console.log('❌ Nuxt configuration invalid');
            process.exit(1);
        }
    " 2>/dev/null; then
        success "Nuxt configuration validated"
    else
        error "Nuxt configuration validation failed"
    fi
    
    # Check database schema
    if [ -f "database/schema.sql" ]; then
        SCHEMA_SIZE=$(wc -l < database/schema.sql)
        success "Database schema validated: $SCHEMA_SIZE lines"
    else
        error "Database schema file not found"
    fi
    
    # Check RLS policies
    if [ -f "database/rls-policies.sql" ]; then
        RLS_SIZE=$(wc -l < database/rls-policies.sql)
        success "RLS policies validated: $RLS_SIZE lines"
    else
        error "RLS policies file not found"
    fi
}

# Security checks
security_checks() {
    log "Running security checks..."
    
    # Check for sensitive data in code
    if grep -r "password\|secret\|key" --include="*.vue" --include="*.ts" --include="*.js" . | grep -v node_modules | grep -v ".git" | grep -v "// TODO" | head -5; then
        warning "Potential sensitive data found in code - review manually"
    else
        success "No obvious sensitive data in code"
    fi
    
    # Check npm audit
    if npm audit --audit-level high; then
        success "No high-severity vulnerabilities found"
    else
        warning "Security vulnerabilities found - review npm audit output"
    fi
    
    # Check environment file permissions
    if [ -f ".env.production" ]; then
        PERMS=$(stat -c "%a" .env.production 2>/dev/null || stat -f "%A" .env.production 2>/dev/null)
        if [ "$PERMS" = "600" ] || [ "$PERMS" = "644" ]; then
            success "Environment file permissions secure"
        else
            warning "Environment file permissions may be too open: $PERMS"
        fi
    fi
}

# Generate deployment report
generate_report() {
    log "Generating deployment report..."
    
    REPORT_FILE="$PROJECT_ROOT/deployment-report.md"
    
    cat > "$REPORT_FILE" << EOF
# RPHMate SaaS - Deployment Report

**Generated:** $(date)
**Version:** $(node -p "require('./package.json').version" 2>/dev/null || echo "Unknown")

## Pre-Deployment Checklist Status

### ✅ Prerequisites
- Node.js version: $(node --version)
- npm version: $(npm --version)
- Build size: $(du -sh .output 2>/dev/null | cut -f1 || echo "Not built")

### ✅ Configuration
- Environment variables: Configured
- Database schema: Available
- RLS policies: Available
- Nuxt configuration: Valid

### ✅ Testing
- TypeScript compilation: Passed
- Build process: Successful
- Basic functionality: Verified

### ⚠️ Manual Steps Required
- [ ] DNS configuration
- [ ] SSL certificate installation
- [ ] Production server setup
- [ ] Database deployment
- [ ] Email service configuration
- [ ] Payment service configuration

## Next Steps

1. **Deploy to production server:**
   \`\`\`bash
   ./scripts/deploy-production.sh
   \`\`\`

2. **Verify deployment:**
   \`\`\`bash
   curl -f https://yourdomain.com/health
   \`\`\`

3. **Test subdomain functionality:**
   \`\`\`bash
   node scripts/test-subdomains.js
   \`\`\`

## Support

- Documentation: \`docs/deployment-checklist.md\`
- Production setup: \`docs/production-subdomain-setup.md\`
- Troubleshooting: Check deployment logs

---
*Report generated by deployment automation script*
EOF

    success "Deployment report generated: $REPORT_FILE"
}

# Main deployment automation
main() {
    echo -e "${CYAN}"
    echo "🚀 RPHMate SaaS - Deployment Automation"
    echo "==========================================="
    echo -e "${NC}"
    
    init_log
    
    case "${1:-all}" in
        "prerequisites")
            check_prerequisites
            ;;
        "test")
            run_tests
            ;;
        "build")
            build_application
            ;;
        "validate")
            validate_configuration
            ;;
        "security")
            security_checks
            ;;
        "report")
            generate_report
            ;;
        "all")
            check_prerequisites
            run_tests
            build_application
            validate_configuration
            security_checks
            generate_report
            
            echo -e "${GREEN}"
            echo "🎉 Pre-deployment automation completed successfully!"
            echo ""
            echo "📋 Next steps:"
            echo "1. Review the deployment report: deployment-report.md"
            echo "2. Follow the deployment checklist: docs/deployment-checklist.md"
            echo "3. Deploy to production: ./scripts/deploy-production.sh"
            echo -e "${NC}"
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [prerequisites|test|build|validate|security|report|all|help]"
            echo ""
            echo "Commands:"
            echo "  prerequisites - Check deployment prerequisites"
            echo "  test         - Run comprehensive tests"
            echo "  build        - Build application for production"
            echo "  validate     - Validate configuration"
            echo "  security     - Run security checks"
            echo "  report       - Generate deployment report"
            echo "  all          - Run all checks (default)"
            echo "  help         - Show this help message"
            ;;
        *)
            error "Unknown command: $1. Use 'help' for usage information."
            ;;
    esac
}

# Run main function
main "$@"
