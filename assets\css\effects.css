.ripple-effect-container {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(
    255,
    255,
    255,
    0.4
  ); /* Default ripple color, can be customized */
  transform: scale(0);
  animation: ripple-animation 600ms linear;
  pointer-events: none;
  z-index: 0; /* Ensure ripple is behind content if content has z-index */
}

@keyframes ripple-animation {
  to {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes spinSlow {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.animate-spin-slow {
  animation: spinSlow 3s linear infinite;
}