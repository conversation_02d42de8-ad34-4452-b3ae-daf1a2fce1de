<template>
    <UiCompositeCard class="mb-6">
        <template #header>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <UiBaseIcon name="heroicons:funnel-solid" class="w-5 h-5 text-primary" />
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Tapis <PERSON>car<PERSON></h3>
                </div>
                <UiBaseButton variant="flat" size="sm" @click="closeFilters">
                    Tutup
                </UiBaseButton>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Categories -->
            <div>
                <h4 class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Kategori Acara
                </h4>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                    <div v-for="category in EVENT_CATEGORIES" :key="category.value"
                        class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors"
                        :class="{
                            'ring-2 ring-primary bg-primary/5': localFilters.categories.includes(category.value),
                        }" @click="toggleCategory(category.value)">
                        <UiBaseCheckbox :id="`category-${category.value}`"
                            :model-value="localFilters.categories.includes(category.value)"
                            @update:model-value="toggleCategory(category.value)" />
                        <div class="flex items-center space-x-2 flex-1 min-w-0">
                            <div class="w-3 h-3 rounded-full flex-shrink-0" :class="`bg-${category.color}`"></div>
                            <div class="min-w-0 flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ category.label }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                    {{ category.description }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search -->
            <div>
                <UiBaseInput v-model="localFilters.searchQuery" variant="normal" label="Cari Acara"
                    prepend-icon="heroicons:magnifying-glass-solid" @keyup.enter="handleSearchEnter" />
                <p v-if="localFilters.searchQuery && localFilters.searchQuery.length < 3"
                    class="text-xs text-amber-600 dark:text-amber-400 mt-1">
                    Sila masukkan sekurang-kurangnya 3 aksara untuk carian.
                </p>
            </div>
        </div>

        <template #footer>
            <div class="flex items-center justify-between">
                <UiBaseButton variant="outline" @click="closeAndResetFilters">
                    Tutup
                </UiBaseButton>
                <div class="flex space-x-2">
                    <UiBaseButton variant="outline" @click="resetFilters">
                        Reset
                    </UiBaseButton>
                    <UiBaseButton variant="primary" @click="applyFilters" :disabled="isSearchDisabled">
                        Cari
                    </UiBaseButton>
                </div>
            </div>
        </template>
    </UiCompositeCard>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { CalendarFilters, EventCategory } from '~/types/calendar'
import { EVENT_CATEGORIES } from '~/types/calendar'



// Props
interface Props {
    filters: CalendarFilters
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'update:filters': [filters: CalendarFilters]
    'apply': [filters: CalendarFilters]
    'reset': []
    'close': []
    'close-and-reset': []
}>()

// Local state
const localFilters = ref<CalendarFilters>({
    categories: [...props.filters.categories],
    searchQuery: props.filters.searchQuery || ''
})

// Computed
const isSearchDisabled = computed(() => {
    const hasValidSearchQuery = localFilters.value.searchQuery.trim().length >= 3
    return !hasValidSearchQuery
})

// Methods
const toggleCategory = (category: EventCategory) => {
    const index = localFilters.value.categories.indexOf(category)
    if (index > -1) {
        localFilters.value.categories.splice(index, 1)
    } else {
        localFilters.value.categories.push(category)
    }
    applyFilters()
}

const applyFilters = () => {
    const filters: CalendarFilters = {
        categories: [...localFilters.value.categories],
        searchQuery: localFilters.value.searchQuery
    }

    emit('update:filters', filters)
    emit('apply', filters)
}

const resetFilters = () => {
    localFilters.value = {
        categories: [],
        searchQuery: ''
    }
    emit('reset')
    applyFilters()
}

const closeFilters = () => {
    emit('close')
}

const closeAndResetFilters = () => {
    localFilters.value = {
        categories: [],
        searchQuery: ''
    }
    emit('close-and-reset')
}

const handleSearchEnter = () => {
    // Only trigger search if validation passes
    if (!isSearchDisabled.value) {
        applyFilters()
    }
}

// Watch for external filter changes
watch(() => props.filters, (newFilters) => {
    localFilters.value = {
        categories: [...newFilters.categories],
        searchQuery: newFilters.searchQuery || ''
    }
}, { deep: true })
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
