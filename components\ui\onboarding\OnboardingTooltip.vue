<template>
  <div class="relative inline-block">
    <!-- Trigger element -->
    <div ref="triggerRef" @click="handleTriggerClick">
      <slot name="trigger" :show="show" :isVisible="isVisible" />
    </div>
    
    <!-- Tooltip overlay -->
    <Teleport to="body">
      <transition
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 scale-95"
        enter-to-class="opacity-100 scale-100"
        leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 scale-100"
        leave-to-class="opacity-0 scale-95"
      >
        <div
          v-if="isVisible"
          class="fixed inset-0 z-[9999] flex items-center justify-center"
          @click="handleOverlayClick"
        >
          <!-- Backdrop -->
          <div class="absolute inset-0 bg-black bg-opacity-50"></div>
          
          <!-- Tooltip content -->
          <div
            ref="tooltipRef"
            :style="tooltipStyle"
            class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 max-w-sm mx-4"
            @click.stop
          >
            <!-- Arrow -->
            <div :class="arrowClasses"></div>
            
            <!-- Header -->
            <div class="px-4 pt-4 pb-2">
              <div class="flex items-start justify-between">
                <div class="flex items-center space-x-2">
                  <Icon v-if="icon" :name="icon" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ title }}
                  </h3>
                </div>
                <button
                  @click="hide"
                  class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                  type="button"
                >
                  <Icon name="heroicons:x-mark" class="h-5 w-5" />
                </button>
              </div>
            </div>
            
            <!-- Content -->
            <div class="px-4 pb-4">
              <div class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                <slot name="content">
                  {{ content }}
                </slot>
              </div>
              
              <!-- Actions -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <!-- Step indicator -->
                  <div v-if="showSteps" class="flex items-center space-x-1">
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ currentStep }} of {{ totalSteps }}
                    </span>
                    <div class="flex space-x-1">
                      <div
                        v-for="step in totalSteps"
                        :key="step"
                        :class="[
                          'w-2 h-2 rounded-full',
                          step <= currentStep
                            ? 'bg-blue-600 dark:bg-blue-400'
                            : 'bg-gray-300 dark:bg-gray-600'
                        ]"
                      ></div>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2">
                  <!-- Skip button -->
                  <button
                    v-if="showSkip"
                    @click="skip"
                    class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                    type="button"
                  >
                    Skip
                  </button>
                  
                  <!-- Previous button -->
                  <button
                    v-if="showPrevious && currentStep > 1"
                    @click="previous"
                    class="px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors"
                    type="button"
                  >
                    Previous
                  </button>
                  
                  <!-- Next/Done button -->
                  <button
                    @click="next"
                    class="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-md transition-colors"
                    type="button"
                  >
                    {{ isLastStep ? 'Done' : 'Next' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import Icon from '../base/Icon.vue'

interface Props {
  title: string
  content: string
  icon?: string
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  currentStep?: number
  totalSteps?: number
  showSteps?: boolean
  showSkip?: boolean
  showPrevious?: boolean
  persistent?: boolean
  autoShow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  position: 'auto',
  currentStep: 1,
  totalSteps: 1,
  showSteps: false,
  showSkip: true,
  showPrevious: true,
  persistent: false,
  autoShow: false
})

const emit = defineEmits<{
  show: []
  hide: []
  next: []
  previous: []
  skip: []
  done: []
}>()

// State
const isVisible = ref(false)
const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const tooltipStyle = ref({})

// Computed
const isLastStep = computed(() => props.currentStep >= props.totalSteps)

const arrowClasses = computed(() => {
  // Arrow positioning will be calculated based on position
  return [
    'absolute w-3 h-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transform rotate-45',
    // Position will be set dynamically
  ]
})

// Methods
const show = () => {
  isVisible.value = true
  emit('show')
  nextTick(calculatePosition)
}

const hide = () => {
  isVisible.value = false
  emit('hide')
}

const next = () => {
  if (isLastStep.value) {
    emit('done')
    hide()
  } else {
    emit('next')
  }
}

const previous = () => {
  emit('previous')
}

const skip = () => {
  emit('skip')
  hide()
}

const handleTriggerClick = () => {
  if (!props.persistent) {
    show()
  }
}

const handleOverlayClick = () => {
  if (!props.persistent) {
    hide()
  }
}

const calculatePosition = () => {
  if (!triggerRef.value || !tooltipRef.value) return
  
  const trigger = triggerRef.value
  const tooltip = tooltipRef.value
  const triggerRect = trigger.getBoundingClientRect()
  const tooltipRect = tooltip.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  
  let position = props.position
  
  // Auto-calculate best position
  if (position === 'auto') {
    const spaceTop = triggerRect.top
    const spaceBottom = viewportHeight - triggerRect.bottom
    const spaceLeft = triggerRect.left
    const spaceRight = viewportWidth - triggerRect.right
    
    if (spaceBottom >= tooltipRect.height + 20) {
      position = 'bottom'
    } else if (spaceTop >= tooltipRect.height + 20) {
      position = 'top'
    } else if (spaceRight >= tooltipRect.width + 20) {
      position = 'right'
    } else {
      position = 'left'
    }
  }
  
  let top = 0
  let left = 0
  
  switch (position) {
    case 'top':
      top = triggerRect.top - tooltipRect.height - 12
      left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      break
    case 'bottom':
      top = triggerRect.bottom + 12
      left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.left - tooltipRect.width - 12
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.right + 12
      break
  }
  
  // Keep tooltip within viewport with padding
  const padding = 16
  left = Math.max(padding, Math.min(left, viewportWidth - tooltipRect.width - padding))
  top = Math.max(padding, Math.min(top, viewportHeight - tooltipRect.height - padding))
  
  tooltipStyle.value = {
    top: `${top}px`,
    left: `${left}px`
  }
}

// Handle escape key
const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isVisible.value) {
    hide()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleEscape)
  window.addEventListener('resize', calculatePosition)
  window.addEventListener('scroll', calculatePosition)
  
  if (props.autoShow) {
    nextTick(show)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
  window.removeEventListener('resize', calculatePosition)
  window.removeEventListener('scroll', calculatePosition)
})

// Expose methods
defineExpose({
  show,
  hide
})
</script>
