# Copilot Instructions for Nuxt 3 Web App with TypeScript, Tailwind CSS, Supabase, and Google Fonts

These instructions guide the development of a web app using Nuxt 3, TypeScript, Tailwind CSS, custom UI components, Supabase, and @nuxtjs/google-fonts with <PERSON><PERSON><PERSON> as the default font. Follow each step to ensure a scalable, maintainable, and performant application.

## General Guidelines

When writing code:

- Do not assume any existing files or configurations unless specified. Each step should be self-contained, allowing for incremental development and testing.
- DRY (Don't Repeat Yourself) principles should be followed. Avoid duplicating code or logic across components and pages.
- Consistency in naming conventions, file structures, and coding styles is crucial. Use camelCase for variables and functions, PascalCase for components, and kebab-case for file names.
- Maintainability is key. Write clear, concise code with appropriate comments and documentation. Use TypeScript interfaces and types to define data structures.
- Follow a modular approach. Each component, page, and utility should be self-contained and reusable.
- Ensure that all components are designed to be responsive and accessible, adhering to best practices for web development.
- Use Pinia for state management.
- UX is priority

## Mobile Layout Standards

For consistent mobile and desktop layouts across all pages:

- **Page Container Structure**: Use `<div class="space-y-8">` as the root container for all pages instead of `container mx-auto p-4`
- **Mobile Padding**: Do not apply padding at the page root level. Let the layout wrapper handle edge spacing
- **Content Spacing**: Use `space-y-8` (32px vertical gaps) between major page sections for consistent vertical rhythm
- **Component Padding**: Individual components (Cards, Modals, etc.) should handle their own internal padding
- **Responsive Design**: Components should be designed to work from mobile-first, extending to larger screens
- **Edge-to-Edge Content**: Content can extend to screen edges on mobile, with components providing their own spacing

This approach ensures:

- Consistent spacing across all pages
- Better mobile experience with components controlling their own padding
- Cleaner responsive design patterns
- Easier maintenance of layout standards

## 1. Plan and Build UI Components

1. Create a component library structure in the `/components/ui` directory:
   - `/components/ui/base`: For atomic components (Button, Input, Icon).
   - `/components/ui/composite`: For complex components (Card, Modal, Form).
2. Initialize Tailwind CSS:

   - Install `@nuxtjs/tailwindcss` with `npm install --save-dev @nuxtjs/tailwindcss`.
   - Add `@nuxtjs/tailwindcss` to the `modules` array in `nuxt.config.ts`.
   - Create `tailwind.config.ts` in the project root:

     ```typescript
     import type { Config } from "tailwindcss";

     export default <Config>{
       content: [
         "./components/**/*.{vue,ts}",
         "./pages/**/*.vue",
         "./layouts/**/*.vue",
         "./app.vue",
       ],
       theme: {
         extend: {
           colors: {
             primary: "#2563EB",
             secondary: "#1F2937",
             accent: "#F59E0B",
           },
           fontFamily: {
             sans: ["Poppins", "sans-serif"],
           },
         },
       },
       plugins: [],
     };
     ```

3. Initialize Google Fonts:

   - Install `@nuxtjs/google-fonts` with `npm install --save-dev @nuxtjs/google-fonts`.
   - Add `@nuxtjs/google-fonts` to `nuxt.config.ts`:

     ```typescript
     import { defineNuxtConfig } from "nuxt/config";

     export default defineNuxtConfig({
       modules: [
         "@pinia/nuxt",
         "@nuxtjs/tailwindcss",
         "@nuxtjs/supabase",
         "@nuxtjs/google-fonts",
       ],
       googleFonts: {
         families: {
           Poppins: [400, 500, 600, 700],
         },
         preload: true,
         display: "swap",
       },
       typescript: {
         strict: true,
       },
     });
     ```

4. Build base components in `/components/ui/base`:
   - Create `Button.vue` with props for `variant` (primary, secondary, outline) and `size` (sm, md, lg). Use Tailwind classes like `bg-primary`, `text-white`, `px-4`, `py-2`. Define props with TypeScript:
     ```typescript
     defineProps<{
       variant?: "primary" | "secondary" | "outline";
       size?: "sm" | "md" | "lg";
     }>();
     ```
   - Create `Input.vue` with props for `type` (text, email, password) and `placeholder`. Use Tailwind classes like `border`, `rounded-md`, `p-2`. Define props with TypeScript:
     ```typescript
     defineProps<{
       type?: "text" | "email" | "password";
       placeholder?: string;
     }>();
     ```
5. Build composite components in `/components/ui/composite`:
   - Create `Card.vue` with slots for header, body, and footer. Use Tailwind classes like `shadow-md`, `rounded-lg`, `p-4`.
   - Create `Modal.vue` with props for `isOpen` and `title`. Use Tailwind classes like `fixed`, `inset-0`, `bg-opacity-50`. Define props with TypeScript:
     ```typescript
     defineProps<{
       isOpen: boolean;
       title: string;
     }>();
     ```
6. Test components in isolation using Nuxt’s dev environment (`npm run dev`).
7. Document components in `/docs/components.md` with their props, slots, and usage examples.

## 2. Set Up State and Mock Data

1. Install Pinia with `npm install pinia @pinia/nuxt`.
2. Add Pinia to `nuxt.config.ts`:

   ```typescript
   import { defineNuxtConfig } from "nuxt/config";

   export default defineNuxtConfig({
     modules: [
       "@pinia/nuxt",
       "@nuxtjs/tailwindcss",
       "@nuxtjs/supabase",
       "@nuxtjs/google-fonts",
     ],
     googleFonts: {
       families: {
         Poppins: [400, 500, 600, 700],
       },
       preload: true,
       display: "swap",
     },
     typescript: {
       strict: true,
     },
   });
   ```

3. Create a Pinia store in `/stores/data.ts`:

   ```typescript
   import { defineStore } from "pinia";

   interface Item {
     id: number;
     title: string;
     description: string;
   }

   export const useDataStore = defineStore("data", {
     state: (): { items: Item[] } => ({
       items: [
         { id: 1, title: "Item 1", description: "Description 1" },
         { id: 2, title: "Item 2", description: "Description 2" },
       ],
     }),
     actions: {
       addItem(item: Item) {
         this.items.push(item);
       },
     },
   });
   ```

4. Create a mock API in `/server/api/mock.ts` to simulate Supabase responses:

   ```typescript
   import type { H3Event } from "h3";

   interface Item {
     id: number;
     title: string;
     description: string;
   }

   export default defineEventHandler((_event: H3Event): Item[] => {
     return [
       { id: 1, title: "Item 1", description: "Description 1" },
       { id: 2, title: "Item 2", description: "Description 2" },
     ];
   });
   ```

5. Use `useFetch` in components to fetch mock data from `/api/mock`.

## 3. Develop Pages Iteratively

1. Create pages in the `/pages` directory:
   - Create `index.vue` for the homepage.
   - Create `items.vue` for a list of items.
   - Create `item/[id].vue` for a single item view.
2. Build `index.vue`:
   - Use `Card.vue` and `Button.vue` to display a welcome message and a link to `items.vue`.
   - Apply Tailwind classes like `container`, `mx-auto`, `py-8`.
3. Build `items.vue`:
   - Fetch mock data using `useFetch('/api/mock')`.
   - Display items in a grid using `Card.vue` and Tailwind’s `grid`, `gap-4`.
   - Add a `Button.vue` to trigger adding a new item via the Pinia store.
4. Build `item/[id].vue`:
   - Fetch a single item using `useFetch('/api/mock')` filtered by `id` from `useRoute().params.id`.
   - Display item details in a `Card.vue`.
5. Apply Tailwind styles and custom components to each page.
6. Test page functionality and responsiveness in the browser (`npm run dev`).

## 4. Test Components and Pages

1. Install Vitest with `npm install --save-dev vitest @vitejs/plugin-vue`.
2. Configure Vitest in `vitest.config.ts`:

   ```typescript
   import { defineConfig } from "vitest/config";
   import vue from "@vitejs/plugin-vue";

   export default defineConfig({
     plugins: [vue()],
     test: {
       environment: "jsdom",
     },
   });
   ```

3. Write unit tests for `Button.vue` in `/components/ui/base/__tests__/Button.spec.ts`:

   ```typescript
   import { mount } from "@vue/test-utils";
   import Button from "../Button.vue";

   describe("Button", () => {
     it("renders primary variant", () => {
       const wrapper = mount(Button, { props: { variant: "primary" } });
       expect(wrapper.classes()).toContain("bg-primary");
     });
   });
   ```

4. Write integration tests for `items.vue` in `/pages/__tests__/items.spec.ts` to verify data rendering.
5. Run tests with `npm run test`.

## 5. Integrate Supabase

1. Install `@nuxtjs/supabase` with `npm install @nuxtjs/supabase`.
2. Add `@nuxtjs/supabase` to `nuxt.config.ts`:

   ```typescript
   import { defineNuxtConfig } from "nuxt/config";

   export default defineNuxtConfig({
     modules: [
       "@pinia/nuxt",
       "@nuxtjs/tailwindcss",
       "@nuxtjs/supabase",
       "@nuxtjs/google-fonts",
     ],
     supabase: {
       url: process.env.SUPABASE_URL,
       key: process.env.SUPABASE_KEY,
     },
     googleFonts: {
       families: {
         Poppins: [400, 500, 600, 700],
       },
       preload: true,
       display: "swap",
     },
     typescript: {
       strict: true,
     },
   });
   ```

3. Create a `.env` file in the project root:
   ```
   SUPABASE_URL=your-supabase-url
   SUPABASE_KEY=your-supabase-key
   ```
4. Create a Supabase composable in `/composables/useSupabase.ts`:

   ```typescript
   import { useSupabaseClient } from "#imports";
   import type { SupabaseClient } from "@supabase/supabase-js";

   export const useSupabase = (): { client: SupabaseClient } => {
     const client = useSupabaseClient();
     return { client };
   };
   ```

5. Implement authentication:

   - Create `/pages/login.vue` using `useSupabase().client.auth.signInWithPassword` for email/password login.
   - Create a middleware in `/middleware/auth.ts` to protect routes:

     ```typescript
     import type { NuxtMiddleware } from "@nuxt/types";
     import { useSupabase } from "~/composables/useSupabase";

     export default defineNuxtMiddleware(async (to, from) => {
       const { client } = useSupabase();
       const {
         data: { session },
       } = await client.auth.getSession();
       if (!session && to.path !== "/login") {
         return navigateTo("/login");
       }
     }) as NuxtMiddleware;
     ```

   - Apply middleware to `items.vue` and `item/[id].vue` in `definePageMeta({ middleware: 'auth' })`.

6. Replace mock data in `items.vue` and `item/[id].vue`:
   - Update `useFetch` to query Supabase tables using `useSupabase().client.from('items').select('*')`.
7. Implement real-time subscriptions in `items.vue`:

   ```typescript
   import { useSupabase } from "~/composables/useSupabase";
   import { useDataStore } from "~/stores/data";

   const { client } = useSupabase();
   client
     .channel("items")
     .on(
       "postgres_changes",
       { event: "INSERT", schema: "public", table: "items" },
       (payload) => {
         useDataStore().addItem(payload.new);
       }
     )
     .subscribe();
   ```

8. Test Supabase integration in the browser.

## 6. Polish and Deploy

1. Optimize performance:
   - Use `defineAsyncComponent` for heavy components.
   - Enable static site generation in `nuxt.config.ts` with `ssr: false` if applicable.
2. Ensure accessibility:
   - Add ARIA attributes to `Button.vue` and `Input.vue` (e.g., `aria-label`).
   - Test with screen readers.
3. Deploy to Vercel:
   - Push code to a GitHub repository.
   - Connect the repository to Vercel and deploy with `vercel deploy`.
4. Test the deployed app for functionality, responsiveness, and Supabase connectivity.

## Notes

- Run `npm run dev` to test locally after each major change.
- Use `npm run build` and `npm run preview` before deployment to verify the production build.
- Maintain a `/docs` directory for component and API documentation.
- Commit changes frequently with clear messages (e.g., `Add Button component`, `Integrate Supabase auth`).
