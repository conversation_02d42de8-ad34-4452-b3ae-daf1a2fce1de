import type { Database } from './supabase'

// Base types from database
type TeacherScheduleRow = Database['public']['Tables']['teacher_schedules']['Row']
type TeacherScheduleInsert = Database['public']['Tables']['teacher_schedules']['Insert']
type TeacherScheduleUpdate = Database['public']['Tables']['teacher_schedules']['Update']

// Days enum for type safety
export type DayOfWeek = 'ISNIN' | 'SELASA' | 'RABU' | 'KHAMIS' | 'JUMAAT' | 'AHAD'

// Enhanced teacher schedule interface - NEW STRUCTURE
// Single row per lesson plan with aggregated class-subject data
export interface TeacherScheduleDetails extends Record<string, any> {
  class_subjects: ClassSubjectSchedule[]
}

export interface ClassSubjectSchedule {
  class_id: string
  class_name: string
  subject_id: string
  subject_name: string
  subject_abbreviation?: string
  days_scheduled: DayOfWeek[]
  total_periods: number
  periods: SchedulePeriod[]
}

export interface SchedulePeriod extends Record<string, any> {
  day: DayOfWeek
  time_slot_start: string
  time_slot_end: string
  class_id?: string // For backward compatibility
  subject_id?: string // For backward compatibility
  class_name?: string // For backward compatibility
  subject_name?: string // For backward compatibility
  class_subject_id?: string // For backward compatibility
}

export interface TeacherSchedule extends TeacherScheduleRow {
  schedule_details: TeacherScheduleDetails | null
  // Additional computed properties can be added here
  total_periods?: number
}

// Form data for creating/editing teacher schedules
// This will now map to the new aggregated structure
export interface TeacherScheduleFormData {
  class_subjects: ClassSubjectSchedule[]
}

// Legacy form data for backward compatibility
export interface LegacyTeacherScheduleFormData {
  periods: SchedulePeriod[]
}

// For displaying schedules with class/subject details
export interface TeacherScheduleWithDetails extends TeacherSchedule {
  class_name: string
  subject_name: string
  days_scheduled: DayOfWeek[] // Add this back for the grouped view
  total_periods: number
}

// For rating calculations
export interface PeriodRating {
  period: SchedulePeriod
  rating: number // From detailed reflection or default 5
  has_reflection: boolean
}

// Summary for lesson plan rating calculation
export interface LessonPlanRatingSummary {
  lesson_plan_id: string
  total_periods: number
  total_stars: number
  calculated_rating: number // Rounded to nearest 0.1
  periods_with_reflections: number
  periods_using_default: number
  period_ratings: PeriodRating[]
}

// For validation and form handling
export interface TeacherScheduleValidationErrors {
  periods?: string[]
  general?: string[]
}

// Class and Subject reference data (may come from user profile or separate tables)
export interface ClassReference {
  id: string
  name: string
  code: string
}

export interface SubjectReference {
  id: string
  name: string
  code?: string
}

// Combined class-subject for dropdowns and UI
export interface ClassSubjectCombination {
  class_id: string
  subject_id: string
  class_name: string
  subject_name: string
  combined_label: string // e.g., "1A - Matematik"
}

// Export database types for direct use
export type {
  TeacherScheduleRow,
  TeacherScheduleInsert,
  TeacherScheduleUpdate
}
