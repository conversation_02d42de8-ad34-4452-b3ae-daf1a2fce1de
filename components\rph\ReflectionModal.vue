<template>
    <Modal :is-open="isOpen" :title="modalTitle" @update:is-open="handleModalClose" :size="modalSize">
        <template #header>
            <!-- Mode Selection in Header (Full Width and Sticky) -->
            <div class="w-full">
                <ReflectionModeSelector :current-mode="currentMode" @mode-changed="switchToMode"
                    data-onboarding="mode-tabs" />
            </div>
        </template>

        <!-- Loading Overlay -->
        <div v-if="loading"
            class="absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex items-center justify-center z-50 rounded-lg">
            <div class="flex items-center space-x-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-lg">
                <Icon name="mdi:loading" class="h-5 w-5 animate-spin text-primary" />
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {{ isEditing ? 'Mengemaskini' : 'Menyimpan' }} refleksi...
                </span>
            </div>
        </div>

        <!-- Content that grows with content but respects viewport limits -->
        <div ref="modalContentArea" class="flex flex-col max-h-[85vh] reflection-modal">
            <!-- Error Display -->
            <ErrorDisplay v-if="lastError" :error="lastError" variant="banner" :show-technical-details="false"
                @retry="retryLastOperation" @dismiss="clearLastError" class="mb-6" />

            <!-- Form Errors -->
            <FormErrorDisplay v-if="hasFormErrors" :errors="formErrors" :field-labels="fieldLabels" class="mb-6" />
            <!-- Detailed Mode Navigation Header -->
            <div v-if="currentMode === 'detailed'" class="flex-shrink-0 mb-6">
                <ReflectionNavigationHeader :day-options="dayOptions" :active-day="activeDay"
                    :selected-class-subject="selectedClassSubject" :class-subject-options="classSubjectOptionsForDay"
                    :enhanced-class-subject-options="enhancedClassSubjectOptions"
                    :editing-detailed-reflection="!!editingDetailedReflection" :get-day-label="getDayLabel"
                    :get-class-subject-label="getClassSubjectLabel" @day-changed="switchToDay"
                    @class-subject-changed="handleClassSubjectChange" @open-bulk-operations="openBulkOperations" />
            </div><!-- Quick Mode -->
            <div v-if="currentMode === 'quick'" data-onboarding="quick-mode">
                <ReflectionCalculatedQuickForm :lesson-plan="lessonPlan" :form-data="formData" :errors="formErrors"
                    :loading="loading" :is-editing="isEditing" @update:form-data="formData = $event" />
            </div><!-- Detailed Mode -->
            <div v-if="currentMode === 'detailed'" data-onboarding="detailed-mode">
                <!-- Bulk Operations Panel -->
                <BulkOperationsPanel v-if="showBulkOperations" :available-periods="availablePeriodsForBulk"
                    :loading="bulkOperationLoading" @close="closeBulkOperations" @apply="applyBulkTidakTerlaksana" />
                <!-- Quick Copy from Another Day Feature -->
                <ReflectionQuickCopy :selected-class-subject="selectedClassSubject"
                    :reflections-from-other-days="reflectionsFromOtherDays" :get-day-label="getDayLabel"
                    :get-class-subject-label="getClassSubjectLabel" @copy-reflection="copyFromReflection" />

                <!-- Template Selector -->
                <ReflectionTemplateSelector v-if="selectedClassSubject && activeDay" v-model="selectedTemplateId"
                    @template-applied="applyTemplateToForm" />

                <!-- Detailed Reflection Form -->
                <SkeletonReflection v-if="loading && selectedClassSubject && activeDay" />
                <ReflectionDetailedForm v-else-if="selectedClassSubject && activeDay" :form-data="detailedFormData"
                    :errors="detailedFormErrors" :time-management-options="timeManagementOptions"
                    :resource-options="resourceOptions" :loading="loading" :is-editing="!!editingDetailedReflection"
                    :student-count="currentStudentCount" @update:form-data="detailedFormData = $event"
                    @update-activity-effectiveness="setDetailedActivityEffectiveness"
                    @update-student-engagement="setDetailedStudentEngagement" />

                <!-- Spacing between components -->
                <div v-if="selectedClassSubject && activeDay && currentMode === 'detailed'"></div>

                <!-- Existing Detailed Reflections List -->
                <ReflectionExistingList v-if="currentMode === 'detailed' && activeDay"
                    :reflections="existingReflectionsForDay" :day-label="getDayLabel(activeDay)"
                    :show-no-reflections-message="!selectedClassSubject" :get-class-subject-label="getClassSubjectLabel"
                    @edit="editDetailedReflection" />
            </div>
        </div>

        <template #footer>
            <div class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3">
                <Button type="button" @click="handleCancelButton" variant="outline" class="w-full sm:w-auto">
                    {{ cancelButtonText }}
                </Button>
                <Button v-if="currentMode === 'detailed' && selectedClassSubject" type="button"
                    @click="handleSaveReflection" variant="primary" :disabled="loading" class="w-full sm:w-auto">
                    <Icon v-if="loading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                    Simpan Refleksi
                </Button>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { z } from 'zod';
import Modal from '~/components/ui/composite/Modal.vue';
import Button from '~/components/ui/base/Button.vue';
import Icon from '~/components/ui/base/Icon.vue';

import ReflectionModeSelector from '~/components/rph/reflection/ReflectionModeSelector.vue';
import ReflectionCalculatedQuickForm from '~/components/rph/reflection/ReflectionCalculatedQuickForm.vue';
import ReflectionNavigationHeader from '~/components/rph/reflection/ReflectionNavigationHeader.vue';
import ReflectionQuickCopy from '~/components/rph/reflection/ReflectionQuickCopy.vue';
import ReflectionTemplateSelector from '~/components/rph/reflection/ReflectionTemplateSelector.vue';
import ReflectionDetailedForm from '~/components/rph/reflection/ReflectionDetailedForm.vue';
import ReflectionExistingList from '~/components/rph/reflection/ReflectionExistingList.vue';
import BulkOperationsPanel from '~/components/rph/reflection/BulkOperationsPanel.vue';
import SkeletonReflection from '~/components/ui/skeleton/SkeletonReflection.vue';
import type { LessonPlan } from '~/types/lessonPlans';
import type {
    LessonPlanReflection,
    ReflectionFormData,
    ReflectionMode,
    LessonPlanDetailedReflection,
    DetailedReflectionFormData,
    ClassSubjectOption,
    DayOption,
    TemplateApplicationResult
} from '~/types/reflections';
import { useDetailedReflections } from '~/composables/useDetailedReflections';
import { useApiErrorHandler } from '~/composables/useApiErrorHandler';
import ErrorDisplay from '~/components/ui/error/ErrorDisplay.vue';
import FormErrorDisplay from '~/components/ui/error/FormErrorDisplay.vue';

interface Props {
    isOpen: boolean;
    lessonPlan: LessonPlan | null;
    existingReflection?: LessonPlanReflection | null;
    loading?: boolean;
    userClassSubjects?: any[];
}

interface Emits {
    (e: 'update:is-open', value: boolean): void;
    (e: 'close'): void;
    (e: 'saved'): void;
    (e: 'success', message: string): void;
    (e: 'error', message: string): void;
}

const props = withDefaults(defineProps<Props>(), {
    existingReflection: null,
    loading: false,
    userClassSubjects: () => []
});

const emit = defineEmits<Emits>();

// Reflection composables - using single-row JSONB system
const {
    fetchDetailedReflections,
    getPeriodReflection,
    updatePeriodReflection,
    getClassSubjectOptions,
    getDayOptions,
    loading: detailedLoading
} = useDetailedReflections();

// Error handling
const { handleApiCall, handleSupabaseError } = useApiErrorHandler();

// Form state
const currentMode = ref<ReflectionMode>('quick');
const isEditing = computed(() => !!props.existingReflection);
const loading = computed(() => props.loading || detailedLoading.value);

// Cancel button text logic
const cancelButtonText = computed(() => {
    if (currentMode.value === 'detailed' && selectedClassSubject.value) {
        return 'Batal';
    }
    return 'Tutup';
});

// Detailed mode state
const activeDay = ref<string>('');
const selectedClassSubject = ref<string>('');
const editingDetailedReflection = ref<LessonPlanDetailedReflection | null>(null);
const dayOptions = ref<DayOption[]>([]);
const classSubjectOptions = ref<ClassSubjectOption[]>([]);

// Bulk operations state
const showBulkOperations = ref(false);
const bulkOperationLoading = ref(false);

// Error handling state
const lastError = ref<any>(null);
const formErrorsState = ref<Record<string, string[]>>({});
const lastOperation = ref<(() => Promise<void>) | null>(null);
const classSubjectOptionsForDay = ref<ClassSubjectOption[]>([]);

// Modal refs
const modalContentArea = ref<HTMLElement | null>(null);

// Template state
const selectedTemplateId = ref<string | null>(null);

// Form data for quick mode
const formData = ref<ReflectionFormData>({
    overall_rating: 3,
    objectives_achieved: false,
    challenges_faced: '',
    activity_effectiveness: 3,
    time_management: 'on_time',
    student_engagement: 3,
    resource_adequacy: 'adequate',
    improvements_needed: '',
    successful_strategies: '',
    action_items: [''],
    additional_notes: ''
});

// Form data for detailed mode
const detailedFormData = ref<DetailedReflectionFormData>({
    overall_rating: 5,
    objectives_achieved: false,
    challenges_faced: '',
    activity_effectiveness: 5,
    time_management: 'on_time',
    student_engagement: 5,
    resource_adequacy: 'adequate',
    improvements_needed: '',
    successful_strategies: '',
    action_items: [''],
    additional_notes: '',
    jumlah_murid_mencapai_objektif: 0,
    tindakan_susulan: [],
    tidak_terlaksana: null
});

// Form validation
const ReflectionSchema = z.object({
    overall_rating: z.number().min(0, 'Penilaian tidak sah').max(5),
    objectives_achieved: z.boolean(),
    challenges_faced: z.string().optional(), // Made optional - no longer required
    // Detailed mode fields are optional
    activity_effectiveness: z.number().min(0).max(5).optional(),
    time_management: z.enum(['on_time', 'early', 'late', 'not_applicable']).optional(),
    student_engagement: z.number().min(0).max(5).optional(),
    resource_adequacy: z.enum(['inadequate', 'adequate', 'excellent', 'not_applicable']).optional(),
    improvements_needed: z.string().optional(),
    successful_strategies: z.string().optional(),
    action_items: z.array(z.string()).optional(),
    additional_notes: z.string().optional(),
});

// formErrors moved to error handling state section
const detailedFormErrors = ref<Record<string, string[]> | null>(null);

// Options for selects
const timeManagementOptions = [
    { value: 'early', label: 'Siap Awal' },
    { value: 'on_time', label: 'Tepat Masa' },
    { value: 'late', label: 'Lewat' },
    { value: 'not_applicable', label: 'Tidak Berkenaan' }
];

const resourceOptions = [
    { value: 'inadequate', label: 'Tidak Mencukupi' },
    { value: 'adequate', label: 'Mencukupi' },
    { value: 'excellent', label: 'Sangat Baik' },
    { value: 'not_applicable', label: 'Tidak Berkenaan' }
];

const modalTitle = computed(() => {
    return currentMode.value === 'quick' ? 'Refleksi Keseluruhan' : 'Refleksi Terperinci';
});

// Dynamic modal size based on mode
const modalSize = computed(() => {
    return currentMode.value === 'detailed' ? '2xl' : 'lg';
});



// Rating management for detailed mode
const setDetailedActivityEffectiveness = (rating: number) => {
    detailedFormData.value.activity_effectiveness = rating;
};

const setDetailedStudentEngagement = (rating: number) => {
    detailedFormData.value.student_engagement = rating;
};

// Mode switching
const switchToMode = (mode: ReflectionMode) => {
    currentMode.value = mode;
    formErrorsState.value = {};
    detailedFormErrors.value = null;

    if (mode === 'detailed') {
        // Initialize detailed mode data
        initializeDetailedMode();
    }
};

// Day switching for detailed mode
const switchToDay = async (day: string) => {
    activeDay.value = day;
    selectedClassSubject.value = '';
    editingDetailedReflection.value = null;
    resetDetailedForm();
    detailedFormErrors.value = null;

    // Update class-subject options for the selected day
    if (props.lessonPlan) {
        try {
            classSubjectOptionsForDay.value = await getClassSubjectOptions(props.lessonPlan.id, day);
        } catch (error) {
            classSubjectOptionsForDay.value = [];
        }
    }
};

// Get day label helper
const getDayLabel = (day: string): string => {
    const dayLabels: Record<string, string> = {
        'ISNIN': 'Isnin',
        'SELASA': 'Selasa',
        'RABU': 'Rabu',
        'KHAMIS': 'Khamis',
        'JUMAAT': 'Jumaat',
        'AHAD': 'Ahad'
    };
    return dayLabels[day] || day;
};

// Initialize detailed mode
const initializeDetailedMode = async () => {
    if (!props.lessonPlan) {
        return;
    }

    try {
        // Get day and class-subject options
        dayOptions.value = await getDayOptions(props.lessonPlan.id);
        classSubjectOptions.value = await getClassSubjectOptions(props.lessonPlan.id); // All combinations for reference

        // Set first day as active if not set and load its class-subject options
        if (!activeDay.value && dayOptions.value.length > 0) {
            const firstDay = dayOptions.value[0].value;
            activeDay.value = firstDay;
            // Load class-subject options for the first day only
            classSubjectOptionsForDay.value = await getClassSubjectOptions(props.lessonPlan.id, firstDay);
        }

        // Fetch existing detailed reflections for this lesson plan
        await fetchDetailedReflections(props.lessonPlan.id);

        // If there are active selections, load the reflection data immediately
        // Add a small delay to ensure child components (like TindakanSusulanMultiSelect) are mounted
        if (selectedClassSubject.value && activeDay.value) {
            await nextTick();
            await loadPeriodReflectionData(selectedClassSubject.value, activeDay.value);
        }
    } catch (error) {
        // Handle error silently or show user-friendly message
    }
};

// Reset detailed form
const resetDetailedForm = () => {
    detailedFormData.value = {
        overall_rating: 5,
        objectives_achieved: false,
        challenges_faced: '',
        activity_effectiveness: 5,
        time_management: 'on_time',
        student_engagement: 5,
        resource_adequacy: 'adequate',
        improvements_needed: '',
        successful_strategies: '',
        action_items: [''],
        additional_notes: '',
        jumlah_murid_mencapai_objektif: currentStudentCount.value || 0,
        tindakan_susulan: [],
        tidak_terlaksana: null
    };
};

// Form validation
const validateForm = (): boolean => {
    const dataToValidate = currentMode.value === 'detailed' ? formData.value : {
        overall_rating: formData.value.overall_rating,
        objectives_achieved: formData.value.objectives_achieved,
        challenges_faced: formData.value.challenges_faced
    };

    const result = ReflectionSchema.safeParse(dataToValidate);

    if (!result.success) {
        formErrorsState.value = result.error.flatten().fieldErrors as Record<string, string[]>;
        return false;
    }

    formErrorsState.value = {};
    return true;
};

// Validate detailed form
const validateDetailedForm = (): boolean => {
    const result = ReflectionSchema.safeParse(detailedFormData.value);

    if (!result.success) {
        detailedFormErrors.value = result.error.flatten().fieldErrors as Record<string, string[]>;
        return false;
    }

    detailedFormErrors.value = null;
    return true;
};

// Computed property for enhanced class-subject options with reflection status
const enhancedClassSubjectOptions = computed(() => {
    if (!classSubjectOptionsForDay.value.length || !activeDay.value) return classSubjectOptionsForDay.value;

    return classSubjectOptionsForDay.value.map(option => {
        // Since reflections are auto-generated, all options have reflections
        // No need to show "Sudah ada refleksi" label
        return option;
    });
});

// Computed property for current student count based on selected class-subject
const currentStudentCount = computed(() => {
    if (!selectedClassSubject.value || !props.userClassSubjects?.length) return 0;

    // Extract class_id and subject_id from the selected class-subject composite ID
    const [classId, subjectId] = selectedClassSubject.value.split('_');

    // Find the student count from the user's class-subjects data
    const matchingClassSubject = props.userClassSubjects.find(cs =>
        cs.class_id === classId && cs.subject_id === subjectId
    );

    if (matchingClassSubject?.studentCount) {
        return matchingClassSubject.studentCount;
    }

    // Fallback: try to get from any class with the same class_id
    const matchingClass = props.userClassSubjects.find(cs => cs.class_id === classId);
    return matchingClass?.studentCount || 0;
});

// Computed property for existing reflections for the current day
const existingReflectionsForDay = computed(() => {
    // In single-row structure, we don't have separate reflections per day
    // This is now handled differently
    return [];
});

// Helper to get class-subject label
const getClassSubjectLabel = (classSubjectId: string): string => {
    // First try to find in day-specific options, then fall back to all options
    let option = classSubjectOptionsForDay.value.find(opt => opt.id === classSubjectId);
    if (!option) {
        option = classSubjectOptions.value.find(opt => opt.id === classSubjectId);
    }
    return option ? option.label : classSubjectId;
};

// Computed property for reflections from other days (for quick copy feature)
const reflectionsFromOtherDays = computed(() => {
    // In single-row structure, this feature is simplified
    return [];
});

// Computed property for available periods for bulk operations
const availablePeriodsForBulk = computed(() => {
    if (!props.lessonPlan) return [];

    const periods: Array<{
        id: string;
        dayLabel: string;
        classSubjectLabel: string;
        timeSlot: string;
        alreadyTidakTerlaksana: boolean;
        classSubjectId: string;
        day: string;
    }> = [];

    // Generate periods for all class-subject combinations across all days
    for (const day of props.lessonPlan.days_selected) {
        const dayLabel = getDayLabel(day);

        for (const classSubjectId of props.lessonPlan.class_subject_ids) {
            const classSubjectLabel = getClassSubjectLabel(classSubjectId);
            const periodId = `${classSubjectId}-${day}`;

            // Check if this period already has "tidak terlaksana" marked
            const alreadyTidakTerlaksana = checkIfPeriodTidakTerlaksana(classSubjectId, day);

            periods.push({
                id: periodId,
                dayLabel,
                classSubjectLabel,
                timeSlot: 'Sepanjang hari', // Could be enhanced with actual time slots
                alreadyTidakTerlaksana,
                classSubjectId,
                day
            });
        }
    }

    return periods;
});

// Error handling computed properties
const hasFormErrors = computed(() => {
    return Object.keys(formErrorsState.value).length > 0;
});

const formErrors = computed(() => formErrorsState.value);

const fieldLabels = computed(() => ({
    overall_rating: 'Penilaian Keseluruhan',
    objectives_achieved: 'Objektif Tercapai',
    challenges_faced: 'Cabaran Dihadapi',
    improvements_needed: 'Penambahbaikan Diperlukan',
    successful_strategies: 'Strategi Berjaya',
    action_items: 'Tindakan Susulan',
    additional_notes: 'Catatan Tambahan',
    activity_effectiveness: 'Keberkesanan Aktiviti',
    student_engagement: 'Penglibatan Murid',
    time_management: 'Pengurusan Masa',
    resource_adequacy: 'Kecukupan Sumber',
    jumlah_murid_mencapai_objektif: 'Jumlah Murid Mencapai Objektif',
    tidak_terlaksana: 'Sebab Tidak Terlaksana',
    tindakan_susulan: 'Tindakan Susulan'
}));

// Copy reflection data from another day
const copyFromReflection = (reflection: LessonPlanDetailedReflection) => {
    if (!selectedClassSubject.value) {
        detailedFormErrors.value = {
            general: ['Sila pilih kelas-subjek terlebih dahulu sebelum menyalin data.']
        };
        return;
    }

    // Copy reflection data (simplified for single-row structure)
    Object.assign(detailedFormData.value, {
        overall_rating: reflection.overall_rating || 5,
        objectives_achieved: reflection.objectives_achieved || true,
        challenges_faced: reflection.challenges_faced || '',
        activity_effectiveness: reflection.activity_effectiveness || 5,
        student_engagement: reflection.student_engagement || 5
    });
    detailedFormErrors.value = null;
};

// Template application method
const applyTemplateToForm = (result: TemplateApplicationResult) => {
    if (!selectedClassSubject.value) {
        detailedFormErrors.value = {
            general: ['Sila pilih kelas-subjek terlebih dahulu sebelum menggunakan template.']
        };
        return;
    }

    // Apply template default values to form
    if (result.applied_defaults) {
        Object.assign(detailedFormData.value, {
            ...detailedFormData.value,
            ...result.applied_defaults
        });
    }

    // Clear any existing errors
    detailedFormErrors.value = {};

    // Show success message (optional - could emit to parent for toast)
    console.log(`Template "${result.template_name}" applied successfully`);
};

// Bulk operations methods
const openBulkOperations = () => {
    showBulkOperations.value = true;
};

const closeBulkOperations = () => {
    showBulkOperations.value = false;
};

const checkIfPeriodTidakTerlaksana = (classSubjectId: string, day: string): boolean => {
    // Check if this period already has "tidak terlaksana" marked
    // This would need to be implemented based on your data structure
    // For now, return false as a placeholder
    return false;
};

const applyBulkTidakTerlaksana = async (periodIds: string[], reason: string) => {
    if (!props.lessonPlan) return;

    try {
        bulkOperationLoading.value = true;

        // Process each selected period
        for (const periodId of periodIds) {
            const [classSubjectId, day] = periodId.split('-');

            // Create or update reflection with "tidak terlaksana" status
            await updatePeriodReflection(
                props.lessonPlan.id,
                classSubjectId,
                day,
                {
                    overall_rating: 0,
                    objectives_achieved: false,
                    tidak_terlaksana: reason,
                    activity_effectiveness: 0,
                    student_engagement: 0,
                    time_management: 'not_applicable',
                    resource_adequacy: 'not_applicable',
                    jumlah_murid_mencapai_objektif: 0,
                    challenges_faced: 'Tidak terlaksana',
                    improvements_needed: '',
                    successful_strategies: '',
                    action_items: [],
                    additional_notes: `Tidak terlaksana: ${reason}`,
                    tindakan_susulan: []
                }
            );
        }

        // Show success message
        emit('success', `${periodIds.length} waktu berjaya ditandakan sebagai tidak terlaksana`);

        // Close bulk operations panel
        closeBulkOperations();

        // Refresh data if needed
        if (selectedClassSubject.value && activeDay.value) {
            await loadPeriodReflectionData(selectedClassSubject.value, activeDay.value);
        }

    } catch (error) {
        console.error('Error applying bulk tidak terlaksana:', error);
        emit('error', 'Gagal menandakan waktu sebagai tidak terlaksana');
    } finally {
        bulkOperationLoading.value = false;
    }
};

// Error handling methods
const clearLastError = () => {
    lastError.value = null;
};

const clearFormErrors = () => {
    formErrorsState.value = {};
};

const retryLastOperation = async () => {
    if (lastOperation.value) {
        try {
            await lastOperation.value();
            clearLastError();
        } catch (error) {
            // Error will be handled by the operation itself
        }
    }
};

const handleOperationError = (error: any, operation: () => Promise<void>, context: any = {}) => {
    lastOperation.value = operation;
    lastError.value = handleSupabaseError(error, {
        operation: operation.name || 'unknown operation',
        component: 'ReflectionModal',
        data: context,
        timestamp: new Date()
    });
};

// Edit detailed reflection
const editDetailedReflection = (reflection: LessonPlanDetailedReflection) => {
    editingDetailedReflection.value = reflection;
    selectedClassSubject.value = reflection.class_subject_id;

    // Load reflection data into form
    detailedFormData.value = {
        overall_rating: reflection.overall_rating,
        objectives_achieved: reflection.objectives_achieved,
        challenges_faced: reflection.challenges_faced || '', // Convert null/undefined to empty string for form
        activity_effectiveness: reflection.activity_effectiveness || 5,
        time_management: (reflection.time_management as 'early' | 'on_time' | 'late') || 'on_time',
        student_engagement: reflection.student_engagement || 5,
        resource_adequacy: (reflection.resource_adequacy as 'inadequate' | 'adequate' | 'excellent') || 'adequate',
        improvements_needed: reflection.improvements_needed || '',
        successful_strategies: reflection.successful_strategies || '',
        action_items: Array.isArray(reflection.action_items) ? reflection.action_items : [''],
        additional_notes: reflection.additional_notes || '',
        jumlah_murid_mencapai_objektif: (reflection as any).jumlah_murid_mencapai_objektif || 0,
        tindakan_susulan: Array.isArray((reflection as any).tindakan_susulan) ? (reflection as any).tindakan_susulan : [],
        tidak_terlaksana: (reflection as any).tidak_terlaksana || null
    };
};





// Handle save reflection from footer
const handleSaveReflection = async () => {
    if (currentMode.value === 'detailed') {
        await submitDetailedReflection()

        // Reload the period data to show updated values
        // Add delay to ensure database update is complete and components are ready
        if (selectedClassSubject.value && activeDay.value && props.lessonPlan) {
            await nextTick();
            await loadPeriodReflectionData(selectedClassSubject.value, activeDay.value);
        }

        // Smooth scroll to top of main content area (day tabs) after saving
        await nextTick()
        const scrollableContent = document.querySelector('.reflection-modal .overflow-y-auto');
        if (scrollableContent) {
            scrollableContent.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        }
    }
}

// Submit detailed reflection
const submitDetailedReflection = async () => {
    if (!validateDetailedForm()) {
        nextTick(() => {
            // Focus first error field
            const firstErrorField = Object.keys(detailedFormErrors.value || {})[0];
            if (firstErrorField) {
                const element = document.getElementById(firstErrorField);
                element?.focus();
            }
        });
        return;
    }

    if (!props.lessonPlan || !selectedClassSubject.value || !activeDay.value) {
        detailedFormErrors.value = {
            general: ['Sila pilih kelas-subjek dan hari untuk refleksi.']
        };
        return;
    }

    try {
        // Find the selected class-subject data
        const selectedOption = classSubjectOptionsForDay.value.find(
            option => option.id === selectedClassSubject.value
        );

        if (!selectedOption) {
            detailedFormErrors.value = {
                general: ['Kelas-subjek yang dipilih tidak sah.']
            };
            return;
        }

        // Prepare submission data
        const submissionData = {
            ...detailedFormData.value,
            action_items: detailedFormData.value.action_items.filter(item => item.trim() !== '')
        };

        // Update the specific period reflection using single-row JSONB system
        const updatedReflection = await updatePeriodReflection(
            props.lessonPlan.id,
            selectedClassSubject.value,
            activeDay.value,
            submissionData
        );

        if (!updatedReflection) {
            throw new Error('Failed to update period reflection');
        }

        // Don't reset form - let the data reload handle the refresh
        // Keep selectedClassSubject.value to maintain kelas & subjek selection
        editingDetailedReflection.value = null;

        // Emit saved event
        emit('saved');
    } catch (error) {
        detailedFormErrors.value = {
            general: ['Ralat semasa menyimpan refleksi. Sila cuba lagi.']
        };
    }
};



// Modal management
const handleModalClose = (isOpen: boolean) => {
    emit('update:is-open', isOpen);
    if (!isOpen) {
        emit('close');
    }
};

const handleCancelButton = () => {
    if (currentMode.value === 'detailed' && selectedClassSubject.value) {
        // Clear kelas & subjek selection but keep active day selected
        selectedClassSubject.value = '';
        editingDetailedReflection.value = null;
        // Note: activeDay.value is kept to maintain day tab selection
    } else {
        // Close modal
        handleModalClose(false);
    }
};

// Reset form when modal opens/closes
const resetForm = () => {
    if (props.existingReflection) {
        // Load existing reflection data
        formData.value = {
            overall_rating: props.existingReflection.overall_rating,
            objectives_achieved: props.existingReflection.objectives_achieved,
            challenges_faced: props.existingReflection.challenges_faced || '', // Convert null/undefined to empty string for form
            activity_effectiveness: props.existingReflection.activity_effectiveness || 3,
            time_management: (props.existingReflection.time_management as 'early' | 'on_time' | 'late' | 'not_applicable') || 'on_time',
            student_engagement: props.existingReflection.student_engagement || 3,
            resource_adequacy: (props.existingReflection.resource_adequacy as 'inadequate' | 'adequate' | 'excellent' | 'not_applicable') || 'adequate',
            improvements_needed: props.existingReflection.improvements_needed || '',
            successful_strategies: props.existingReflection.successful_strategies || '',
            action_items: (props.existingReflection.action_items as string[]) || [''],
            additional_notes: props.existingReflection.additional_notes || ''
        };
        currentMode.value = props.existingReflection.is_detailed_mode ? 'detailed' : 'quick';
    } else {
        // Reset to defaults
        formData.value = {
            overall_rating: 3,
            objectives_achieved: false,
            challenges_faced: '',
            activity_effectiveness: 3,
            time_management: 'on_time',
            student_engagement: 3,
            resource_adequacy: 'adequate',
            improvements_needed: '',
            successful_strategies: '',
            action_items: [''],
            additional_notes: ''
        };
        currentMode.value = 'quick';
    }
    formErrorsState.value = {};
    // Only reset detailed form if we don't have active selections
    // This prevents clearing saved data when modal reopens
    if (!selectedClassSubject.value || !activeDay.value) {
        resetDetailedForm();
    }
    // Note: If we have active selections, the data will be loaded by initializeDetailedMode
    detailedFormErrors.value = null;
};

// Watch for modal open/close
watch(() => props.isOpen, async (isOpen) => {
    if (isOpen) {
        resetForm();
        // Initialize detailed mode if needed
        if (currentMode.value === 'detailed') {
            await nextTick();
            await initializeDetailedMode();
        }
        // Note: Removed quick mode calculation trigger to prevent infinite loops
        // The ReflectionCalculatedQuickForm will handle its own calculation on mount
    }
});

// Watch for existing reflection changes
watch(() => props.existingReflection, async () => {
    if (props.isOpen) {
        resetForm();
        // Initialize detailed mode if needed
        if (currentMode.value === 'detailed') {
            await nextTick();
            await initializeDetailedMode();
        }
        // Note: Removed quick mode calculation trigger to prevent infinite loops
    }
});

// Note: Removed lesson plan watcher to prevent infinite loops
// The ReflectionCalculatedQuickForm will handle lesson plan changes internally

// Watch for mode changes and scroll to top
watch(currentMode, async () => {
    if (currentMode.value === 'detailed' && props.isOpen) {
        await initializeDetailedMode();
    }
    // Note: Removed quick mode calculation trigger - let the component handle it naturally
    nextTick(() => {
        const scrollableContent = document.querySelector('.reflection-modal .overflow-y-auto');
        if (scrollableContent) {
            scrollableContent.scrollTo({ top: 0, behavior: 'smooth' });
        }
    });
});

// Load period reflection data for a specific class-subject and day
const loadPeriodReflectionData = async (classSubjectId: string, day: string) => {
    if (!props.lessonPlan) return;

    try {
        const periodReflection = await getPeriodReflection(props.lessonPlan.id, classSubjectId, day);

        if (periodReflection) {
            // Load existing reflection data into form with proper null checking
            const formData = {
                overall_rating: periodReflection.overall_rating ?? 5,
                objectives_achieved: periodReflection.objectives_achieved ?? true,
                challenges_faced: periodReflection.challenges_faced ?? '',
                activity_effectiveness: periodReflection.activity_effectiveness ?? 5,
                time_management: periodReflection.time_management ?? 'on_time',
                student_engagement: periodReflection.student_engagement ?? 5,
                resource_adequacy: periodReflection.resource_adequacy ?? 'adequate',
                improvements_needed: periodReflection.improvements_needed ?? '',
                successful_strategies: periodReflection.successful_strategies ?? '',
                action_items: periodReflection.action_items ?? [],
                additional_notes: periodReflection.additional_notes ?? '',
                jumlah_murid_mencapai_objektif: periodReflection.jumlah_murid_mencapai_objektif ?? 0,
                tindakan_susulan: periodReflection.tindakan_susulan ?? [],
                tidak_terlaksana: periodReflection.tidak_terlaksana ?? null
            };

            // Set the form data and ensure reactivity
            detailedFormData.value = { ...formData };

            // Mark as editing existing reflection
            editingDetailedReflection.value = {
                id: `${props.lessonPlan.id}-${classSubjectId}-${day}`,
                lesson_plan_id: props.lessonPlan.id,
                class_subject_id: classSubjectId,
                day: day,
                overall_rating: periodReflection.overall_rating ?? 5,
                objectives_achieved: periodReflection.objectives_achieved ?? true,
                challenges_faced: periodReflection.challenges_faced ?? '',
                activity_effectiveness: periodReflection.activity_effectiveness ?? 5,
                time_management: (periodReflection.time_management === 'not_applicable') ? 'on_time' : (periodReflection.time_management ?? 'on_time'),
                student_engagement: periodReflection.student_engagement ?? 5,
                resource_adequacy: periodReflection.resource_adequacy ?? 'adequate',
                improvements_needed: periodReflection.improvements_needed ?? '',
                successful_strategies: periodReflection.successful_strategies ?? '',
                action_items: periodReflection.action_items ?? [],
                additional_notes: periodReflection.additional_notes ?? '',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            } as any;
        } else {
            // No existing data, use defaults
            resetDetailedForm();
            editingDetailedReflection.value = null;
        }
    } catch (error) {
        resetDetailedForm();
        editingDetailedReflection.value = null;
    }
};

// Handle class subject change
const handleClassSubjectChange = (newValue: string | null) => {
    const oldValue = selectedClassSubject.value;

    if (newValue !== oldValue) {
        // Update the selected class subject
        selectedClassSubject.value = newValue || '';

        // Load existing reflection data for this class-subject + day combination
        if (newValue && activeDay.value && props.lessonPlan) {
            loadPeriodReflectionData(newValue, activeDay.value);
        } else {
            // No class-subject selected, reset form
            editingDetailedReflection.value = null;
            resetDetailedForm();
        }
    }
};

// Watch for selected class-subject changes to auto-load existing reflection or clear editing state
watch(selectedClassSubject, (newValue, oldValue) => {
    if (newValue !== oldValue) {
        // Load existing reflection data for this class-subject + day combination
        if (newValue && activeDay.value && props.lessonPlan) {
            loadPeriodReflectionData(newValue, activeDay.value);
        } else {
            // No class-subject selected, reset form
            editingDetailedReflection.value = null;
            resetDetailedForm();
        }
    }
});

// Watch for active day changes to reload reflection data for current class-subject
watch(activeDay, (newDay, oldDay) => {
    if (newDay !== oldDay && selectedClassSubject.value && props.lessonPlan) {
        loadPeriodReflectionData(selectedClassSubject.value, newDay);
    }
});

// Watch for "Tidak Terlaksana" changes to auto-update form values
watch(() => detailedFormData.value.tidak_terlaksana, (newValue, oldValue) => {
    if (newValue !== oldValue && newValue) {
        // When "Tidak Terlaksana" is selected, auto-set values
        detailedFormData.value = {
            ...detailedFormData.value,
            overall_rating: 0,
            objectives_achieved: false,
            activity_effectiveness: 0,
            student_engagement: 0,
            time_management: 'not_applicable',
            resource_adequacy: 'not_applicable',
            jumlah_murid_mencapai_objektif: 0
        };
    }
});

// Watch for student count changes to update default value
watch(currentStudentCount, (newCount) => {
    // Only update if the current value is 0 (default) and we're not in "tidak terlaksana" mode
    if (detailedFormData.value.jumlah_murid_mencapai_objektif === 0 &&
        !detailedFormData.value.tidak_terlaksana &&
        newCount > 0) {
        detailedFormData.value.jumlah_murid_mencapai_objektif = newCount;
    }
});

// Note: Removed conflicting activeDay watcher that was clearing selectedClassSubject
// The watcher on lines 726-730 already handles activeDay changes properly

// Keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
    if (props.isOpen) {
        if (event.key === 'Escape') {
            handleModalClose(false);
        }
    }
};

// Initialize modal data when mounted
onMounted(async () => {
    document.addEventListener('keydown', handleKeydown);

    // Initialize reflection data when modal opens
    if (props.lessonPlan) {
        await initializeReflectionData();
    }
});

// Initialize reflection data for the lesson plan
const initializeReflectionData = async () => {
    if (!props.lessonPlan) return;

    try {
        // Fetch existing reflection data for this lesson plan
        const existingReflections = await fetchDetailedReflections(props.lessonPlan.id);

        if (existingReflections) {
            // If there's a selected class-subject and active day, load that period's data
            if (selectedClassSubject.value && activeDay.value) {
                await loadPeriodReflectionData(selectedClassSubject.value, activeDay.value);
            }
        }
    } catch (error) {
        // Handle error silently
    }
};

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
/* Allow modal to grow with content but respect viewport limits */
.reflection-modal :deep(.modal-content) {
    max-height: 90vh;
    max-width: 95vw;
    width: auto;
    min-width: 0;
    overflow: visible;
}

/* Ensure modal overlay allows scrolling if needed */
.reflection-modal :deep(.modal-overlay) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    min-height: 100vh;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
    .reflection-modal :deep(.modal-content) {
        max-height: 95vh;
        max-width: 98vw;
        margin: 2.5vh 1vw;
    }

    /* Reduce modal padding on mobile */
    .reflection-modal :deep(#modal-content-area) {
        padding: 1rem;
    }
}

/* Smooth transitions for mode switching */
.reflection-modal .space-y-6 {
    transition: all 0.3s ease-in-out;
}

/* Ensure form sections don't break layout */
.reflection-modal form>div {
    min-height: fit-content;
}

/* Fix for very tall content - allow modal to be scrollable within viewport */
@media (max-height: 600px) {
    .reflection-modal :deep(.modal-content) {
        max-height: 95vh;
        margin: 1vh auto;
    }
}

/* Ensure content area can scroll when modal reaches max height */
.reflection-modal .overflow-y-auto {
    /* Only apply scroll when needed - content will determine height */
    overflow-y: auto;
    max-height: calc(85vh - 100px);
    /* Account for mode buttons and spacing */
}

/* Text clamp utility for truncating text */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
