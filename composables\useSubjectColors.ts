import { SUBJECT_COLORS, DYNAMIC_COLORS, ACTIVITY_TYPES, isClassActivity } from '~/types/timetable'
import type { TimetableEntry, SubjectColor } from '~/types/timetable'

// Exact subject to color key mapping
const EXACT_MAPPING = {
  // STEM Sciences
  'matematik tambahan': 'additional_mathematics',
  'addmath': 'additional_mathematics',
  'add math': 'additional_mathematics',
  'matematik': 'mathematics',
  'math': 'mathematics',
  'fizik': 'physics',
  'physics': 'physics',
  'kimia': 'chemistry',
  'chemistry': 'chemistry',
  'biologi': 'biology',
  'biology': 'biology',
  'sains': 'science',
  'science': 'science',

  // Languages
  'bahasa melayu': 'malay',
  'melayu': 'malay',
  'bm': 'malay',
  'bahasa inggeris': 'english',
  'inggeris': 'english',
  'english': 'english',
  'bi': 'english',
  'bahasa arab': 'arabic',
  'arab': 'arabic',
  'ba': 'arabic',
  'bahasa cina': 'chinese',
  'cina': 'chinese',
  'chinese': 'chinese',
  'bc': 'chinese',
  'mandarin': 'chinese',
  'bahasa tamil': 'tamil',
  'tamil': 'tamil',
  'bt': 'tamil',
  'bahasa kadazandusun': 'kadazandusun',
  'kadazandusun': 'kadazandusun',
  'bkd': 'kadazandusun',
  'kadazan': 'kadazandusun',
  'dusun': 'kadazandusun',
  'bahasa iban': 'iban',
  'iban': 'iban',
  'bib': 'iban',
  'bahasa semai': 'semai',
  'semai': 'semai',
  'bs': 'semai',

  // Social Sciences
  'sejarah': 'history',
  'history': 'history',
  'geografi': 'geography',
  'geography': 'geography',
  'ekonomi': 'economics',
  'economics': 'economics',
  'pendidikan islam': 'islamic',
  'islam': 'islamic',
  'agama islam': 'islamic',
  'agama': 'islamic',
  'pi': 'islamic',

  // Arts & Physical
  'pendidikan seni visual': 'visual_arts',
  'seni visual': 'visual_arts',
  'visual arts': 'visual_arts',
  'psv': 'visual_arts',
  'pendidikan muzik': 'music',
  'muzik': 'music',
  'music': 'music',
  'pendidikan jasmani dan kesihatan': 'physical',
  'pendidikan jasmani': 'physical',
  'jasmani dan kesihatan': 'physical',
  'jasmani': 'physical',
  'sukan': 'physical',
  'physical education': 'physical',
  'physical': 'physical',
  'pjk': 'physical',
  'pe': 'physical',
  'pendidikan moral': 'moral',
  'moral': 'moral',
  'pm': 'moral',

  // Business & Technology
  'perniagaan': 'business',
  'niaga': 'business',
  'business studies': 'business',
  'business': 'business',
  'prinsip perakaunan': 'accounting',
  'perakaunan': 'accounting',
  'akaun': 'accounting',
  'accounting': 'accounting',
  'principles of accounting': 'accounting',
  'reka bentuk dan teknologi': 'design_technology',
  'reka bentuk': 'design_technology',
  'design and technology': 'design_technology',
  'rbt': 'design_technology',
  'design': 'design_technology',
  'teknologi': 'design_technology',
  'dt': 'design_technology',

  // Legacy mappings
  'seni': 'visual_arts',
  'art': 'visual_arts',
  'arts': 'visual_arts'
} as const

// Priority-ordered partial match keywords
const PARTIAL_MATCH_KEYWORDS = [
  { keyword: 'matematik tambahan', colorKey: 'additional_mathematics' },
  { keyword: 'pendidikan seni visual', colorKey: 'visual_arts' },
  { keyword: 'pendidikan jasmani dan kesihatan', colorKey: 'physical' },
  { keyword: 'reka bentuk dan teknologi', colorKey: 'design_technology' },
  { keyword: 'prinsip perakaunan', colorKey: 'accounting' },
  { keyword: 'pendidikan islam', colorKey: 'islamic' },
  { keyword: 'pendidikan moral', colorKey: 'moral' },
  { keyword: 'pendidikan muzik', colorKey: 'music' },
  { keyword: 'pendidikan jasmani', colorKey: 'physical' },
  { keyword: 'bahasa kadazandusun', colorKey: 'kadazandusun' },
  { keyword: 'bahasa inggeris', colorKey: 'english' },
  { keyword: 'bahasa melayu', colorKey: 'malay' },
  { keyword: 'bahasa tamil', colorKey: 'tamil' },
  { keyword: 'bahasa semai', colorKey: 'semai' },
  { keyword: 'bahasa cina', colorKey: 'chinese' },
  { keyword: 'bahasa iban', colorKey: 'iban' },
  { keyword: 'bahasa arab', colorKey: 'arabic' },
  { keyword: 'seni visual', colorKey: 'visual_arts' },
  { keyword: 'reka bentuk', colorKey: 'design_technology' },
  { keyword: 'jasmani dan kesihatan', colorKey: 'physical' },
  { keyword: 'kadazandusun', colorKey: 'kadazandusun' },
  { keyword: 'matematik', colorKey: 'mathematics' },
  { keyword: 'perakaunan', colorKey: 'accounting' },
  { keyword: 'perniagaan', colorKey: 'business' },
  { keyword: 'inggeris', colorKey: 'english' },
  { keyword: 'ekonomi', colorKey: 'economics' },
  { keyword: 'geografi', colorKey: 'geography' },
  { keyword: 'sejarah', colorKey: 'history' },
  { keyword: 'biologi', colorKey: 'biology' },
  { keyword: 'chemistry', colorKey: 'chemistry' },
  { keyword: 'physics', colorKey: 'physics' },
  { keyword: 'jasmani', colorKey: 'physical' },
  { keyword: 'teknologi', colorKey: 'design_technology' },
  { keyword: 'melayu', colorKey: 'malay' },
  { keyword: 'english', colorKey: 'english' },
  { keyword: 'chinese', colorKey: 'chinese' },
  { keyword: 'mandarin', colorKey: 'chinese' },
  { keyword: 'tamil', colorKey: 'tamil' },
  { keyword: 'arabic', colorKey: 'arabic' },
  { keyword: 'semai', colorKey: 'semai' },
  { keyword: 'iban', colorKey: 'iban' },
  { keyword: 'kimia', colorKey: 'chemistry' },
  { keyword: 'fizik', colorKey: 'physics' },
  { keyword: 'sains', colorKey: 'science' },
  { keyword: 'science', colorKey: 'science' },
  { keyword: 'history', colorKey: 'history' },
  { keyword: 'geography', colorKey: 'geography' },
  { keyword: 'economics', colorKey: 'economics' },
  { keyword: 'islam', colorKey: 'islamic' },
  { keyword: 'agama', colorKey: 'islamic' },
  { keyword: 'muzik', colorKey: 'music' },
  { keyword: 'music', colorKey: 'music' },
  { keyword: 'moral', colorKey: 'moral' },
  { keyword: 'business', colorKey: 'business' },
  { keyword: 'niaga', colorKey: 'business' },
  { keyword: 'accounting', colorKey: 'accounting' },
  { keyword: 'akaun', colorKey: 'accounting' },
  { keyword: 'design', colorKey: 'design_technology' },
  { keyword: 'sukan', colorKey: 'physical' },
  { keyword: 'physical', colorKey: 'physical' },
  { keyword: 'seni', colorKey: 'visual_arts' },
  { keyword: 'visual', colorKey: 'visual_arts' },
  { keyword: 'art', colorKey: 'visual_arts' },
  { keyword: 'bahasa', colorKey: 'malay' },
  { keyword: 'pendidikan', colorKey: 'default' }
] as const

export default function useSubjectColors() {
  const getSubjectColor = (subjectId?: string | null, subjectName?: string | null): SubjectColor => {
    if (!subjectId || !subjectName) return SUBJECT_COLORS.default
    
    const name = subjectName.toLowerCase().trim()
    
    // Phase 1: Exact matches
    for (const [keyword, colorKey] of Object.entries(EXACT_MAPPING)) {
      if (name === keyword) {
        return SUBJECT_COLORS[colorKey] || SUBJECT_COLORS.default
      }
    }
    
    // Phase 2: Partial matches with conflict resolution
    for (const { keyword, colorKey } of PARTIAL_MATCH_KEYWORDS) {
      if (name.includes(keyword)) {
        // Conflict resolution logic
        if (keyword === 'sains' && (
          name.includes('kimia') || name.includes('biologi') ||
          name.includes('fizik') || name.includes('matematik') ||
          name.includes('chemistry') || name.includes('biology') ||
          name.includes('physics') || name.includes('math')
        )) continue

        if (keyword === 'bahasa' && (
          name.includes('inggeris') || name.includes('arab') ||
          name.includes('melayu') || name.includes('cina') ||
          name.includes('tamil') || name.includes('kadazandusun') ||
          name.includes('semai') || name.includes('iban') ||
          name.includes('english') || name.includes('arabic') ||
          name.includes('chinese') || name.includes('mandarin')
        )) continue

        if (keyword === 'seni' && (
          name.includes('visual') || name.includes('muzik') ||
          name.includes('music')
        )) continue

        if (keyword === 'pendidikan' && (
          name.includes('jasmani') || name.includes('islam') ||
          name.includes('moral') || name.includes('seni') ||
          name.includes('muzik') || name.includes('physical') ||
          name.includes('music') || name.includes('visual')
        )) continue

        return SUBJECT_COLORS[colorKey] || SUBJECT_COLORS.default
      }
    }
    
    // Phase 3: Generate unique color for unmapped subjects
    let hash = 0
    for (let i = 0; i < subjectId.length; i++) {
      const char = subjectId.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    const colorIndex = Math.abs(hash) % DYNAMIC_COLORS.length
    const dynamicColor = DYNAMIC_COLORS[colorIndex]
    
    return {
      subject_id: subjectId,
      subject_name: subjectName,
      color: dynamicColor.color,
      bg_color: dynamicColor.bg_color,
      text_color: dynamicColor.text_color
    }
  }

  const getEntryColor = (entry: TimetableEntry): SubjectColor => {
    if (isClassActivity(entry)) {
      return getSubjectColor(entry.subject_id, entry.subject_name)
    }
    
    const activityConfig = ACTIVITY_TYPES[entry.activity_type]
    return {
      subject_id: entry.activity_type,
      subject_name: entry.activity_title || activityConfig.label,
      color: activityConfig.color,
      bg_color: activityConfig.bg_color,
      text_color: activityConfig.text_color
    }
  }

  return {
    getSubjectColor,
    getEntryColor
  }
}