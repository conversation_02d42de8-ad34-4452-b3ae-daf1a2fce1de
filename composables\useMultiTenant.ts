// Multi-tenant data access composable
// Created: 2025-07-13

import { ref, computed } from 'vue'
import type { Database } from '~/types/supabase'

export const useMultiTenant = (schoolId?: string) => {
  const supabase = useSupabaseClient<Database>()
  const route = useRoute()

  // Helper to get school ID from parameter or route
  const getSchoolId = (): string => {
    if (schoolId) return schoolId

    // Try to get from route params
    const routeSchoolCode = route.params.school as string
    if (routeSchoolCode) {
      // For now, we'll need to fetch the school ID from the code
      // This is a simplified approach - in practice, you'd want to cache this
      throw new Error('School ID must be provided or use useSchool composable')
    }

    throw new Error('No school context available')
  }

  // Helper to ensure school context is available
  const ensureSchoolContext = () => {
    return getSchoolId()
  }

  // Generic query builder with automatic school filtering
  const createSchoolQuery = (tableName: string) => {
    const schoolId = ensureSchoolContext()

    return (supabase as any)
      .from(tableName)
      .select('*')
      .eq('school_id', schoolId)
  }

  // Advanced query builder with data pattern support
  const query = <T = any>(table: string, options: {
    includeGlobal?: boolean    // Include global data (school_id IS NULL)
    globalOnly?: boolean       // Only global data
    schoolOnly?: boolean       // Only school-specific data
    userOwned?: boolean        // Only user's own data
  } = {}) => {
    const user = useSupabaseUser()

    return {
      // Select with automatic filtering based on data patterns
      select: (columns = '*') => {
        let query = (supabase as any).from(table).select(columns)

        if (options.globalOnly) {
          // Only global data (school_id IS NULL)
          query = query.is('school_id', null)
        } else if (options.includeGlobal) {
          // Both global and school-specific data
          const schoolId = ensureSchoolContext()
          query = query.or(`school_id.is.null,school_id.eq.${schoolId}`)
        } else if (!options.schoolOnly) {
          // Default: school-specific data only
          const schoolId = ensureSchoolContext()
          query = query.eq('school_id', schoolId)
        }

        // Add user filtering if requested
        if (options.userOwned && user.value) {
          query = query.eq('user_id', user.value.id)
        }

        return query
      },

      // Insert with automatic school_id and user_id
      insert: (data: any) => {
        const insertData = Array.isArray(data) ? data : [data]

        // Add school_id and user_id to all records
        const dataWithContext = insertData.map(record => {
          const contextData: any = { ...record }

          // Add school_id unless it's global data
          if (!options.globalOnly) {
            contextData.school_id = record.school_id || ensureSchoolContext()
          }

          // Add user_id if not present and user is authenticated
          if (!record.user_id && user.value) {
            contextData.user_id = user.value.id
          }

          return contextData
        })

        return (supabase as any).from(table).insert(dataWithContext)
      },

      // Update with proper filtering
      update: (data: any) => {
        let updateQuery = (supabase as any).from(table).update(data)

        if (options.globalOnly) {
          updateQuery = updateQuery.is('school_id', null)
        } else if (!options.schoolOnly) {
          const schoolId = ensureSchoolContext()
          updateQuery = updateQuery.eq('school_id', schoolId)
        }

        // Add user filtering if requested
        if (options.userOwned && user.value) {
          updateQuery = updateQuery.eq('user_id', user.value.id)
        }

        return updateQuery
      },

      // Delete with proper filtering
      delete: () => {
        let deleteQuery = (supabase as any).from(table).delete()

        if (options.globalOnly) {
          deleteQuery = deleteQuery.is('school_id', null)
        } else if (!options.schoolOnly) {
          const schoolId = ensureSchoolContext()
          deleteQuery = deleteQuery.eq('school_id', schoolId)
        }

        // Add user filtering if requested
        if (options.userOwned && user.value) {
          deleteQuery = deleteQuery.eq('user_id', user.value.id)
        }

        return deleteQuery
      }
    }
  }

  // Lesson Plans
  const getLessonPlans = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('lesson_plans')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })
  }

  const createLessonPlan = async (lessonPlan: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('lesson_plans')
      .insert({
        ...lessonPlan,
        school_id: schoolId
      })
  }

  // Teacher Schedules
  const getTeacherSchedules = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('teacher_schedules')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })
  }

  const createTeacherSchedule = async (schedule: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('teacher_schedules')
      .insert({
        ...schedule,
        school_id: schoolId
      })
  }

  // Timetable Entries
  const getTimetableEntries = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('timetable_entries')
      .select('*')
      .eq('school_id', schoolId)
      .order('day', { ascending: true })
      .order('time_slot_start', { ascending: true })
  }

  const createTimetableEntry = async (entry: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('timetable_entries')
      .insert({
        ...entry,
        school_id: schoolId
      })
  }

  // Subjects (Global + School-specific)
  const getSubjects = async () => {
    const schoolId = ensureSchoolContext()

    // Get both global subjects (school_id IS NULL) and school-specific subjects
    return await (supabase as any)
      .from('subjects')
      .select('*')
      .or(`school_id.is.null,school_id.eq.${schoolId}`)
      .order('sort_order', { ascending: true })
      .order('name', { ascending: true })
  }

  const createSchoolSubject = async (subject: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('subjects')
      .insert({
        ...subject,
        school_id: schoolId,
        is_custom: true
      })
  }

  // Reflection Templates (Global + School-specific)
  const getReflectionTemplates = async () => {
    const schoolId = ensureSchoolContext()

    // Get both global templates (school_id IS NULL) and school-specific templates
    return await (supabase as any)
      .from('reflection_templates')
      .select('*')
      .or(`school_id.is.null,school_id.eq.${schoolId}`)
      .order('is_system_template', { ascending: false })
      .order('name', { ascending: true })
  }

  const createSchoolTemplate = async (template: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('reflection_templates')
      .insert({
        ...template,
        school_id: schoolId,
        is_system_template: false
      })
  }

  // User Profiles (School-specific)
  const getSchoolProfiles = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('profiles')
      .select('*')
      .eq('school_id', schoolId)
      .order('full_name', { ascending: true })
  }

  const updateUserProfile = async (profileData: any) => {
    const schoolId = ensureSchoolContext()
    const user = useSupabaseUser()

    if (!user.value) throw new Error('User not authenticated')

    return await (supabase as any)
      .from('profiles')
      .update({
        ...profileData,
        school_id: schoolId
      })
      .eq('id', user.value.id)
      .eq('school_id', schoolId)
  }

  // Teacher Activities
  const getTeacherActivities = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('teacher_activities')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })
  }

  const createTeacherActivity = async (activity: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('teacher_activities')
      .insert({
        ...activity,
        school_id: schoolId
      })
  }

  // Documents (Academic Calendar, DSKP, RPT)
  const getAcademicCalendarDocuments = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('academic_calendar_documents')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })
  }

  const getDskpDocuments = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('dskp_documents')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })
  }

  const getRptDocuments = async () => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from('rpt_documents')
      .select('*')
      .eq('school_id', schoolId)
      .order('created_at', { ascending: false })
  }

  // Generic CRUD operations with automatic school filtering
  const createRecord = async (tableName: string, data: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from(tableName)
      .insert({
        ...data,
        school_id: schoolId
      })
  }

  const updateRecord = async (tableName: string, id: string, data: any) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from(tableName)
      .update(data)
      .eq('id', id)
      .eq('school_id', schoolId)
  }

  const deleteRecord = async (tableName: string, id: string) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from(tableName)
      .delete()
      .eq('id', id)
      .eq('school_id', schoolId)
  }

  const getRecord = async (tableName: string, id: string) => {
    const schoolId = ensureSchoolContext()

    return await (supabase as any)
      .from(tableName)
      .select('*')
      .eq('id', id)
      .eq('school_id', schoolId)
      .single()
  }

  return {
    // Helpers
    ensureSchoolContext,
    createSchoolQuery,
    query, // Advanced query builder with data pattern support

    // Specific data access methods
    getLessonPlans,
    createLessonPlan,
    getTeacherSchedules,
    createTeacherSchedule,
    getTimetableEntries,
    createTimetableEntry,
    getSubjects,
    createSchoolSubject,
    getReflectionTemplates,
    createSchoolTemplate,
    getSchoolProfiles,
    updateUserProfile,
    getTeacherActivities,
    createTeacherActivity,
    getAcademicCalendarDocuments,
    getDskpDocuments,
    getRptDocuments,

    // Generic CRUD operations
    createRecord,
    updateRecord,
    deleteRecord,
    getRecord
  }
}
