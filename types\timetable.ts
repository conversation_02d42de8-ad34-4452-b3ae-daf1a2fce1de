// Extended types for timetable view
import type { TeacherSchedule, DayOfWeek } from './teacherSchedule'

// Re-export DayOfWeek for convenience
export type { DayOfWeek } from './teacherSchedule'

// Activity type enum for different kinds of timetable entries
export type ActivityType = 'CLASS' | 'ASSEMBLY' | 'COCURRICULAR' | 'BREAK' | 'OTHER'

// Activity type configuration for UI
export interface ActivityTypeConfig {
  value: ActivityType
  label: string
  icon: string
  color: string
  bg_color: string
  text_color: string
  description: string
}

// Predefined activity types with their UI configurations
export const ACTIVITY_TYPES: Record<ActivityType, ActivityTypeConfig> = {
  CLASS: {
    value: 'CLASS',
    label: 'Kelas',
    icon: 'mdi:school',
    color: 'blue-600',
    bg_color: 'bg-blue-100 dark:bg-blue-900/20',
    text_color: 'text-blue-800 dark:text-blue-200',
    description: '<PERSON><PERSON> biasa mengikut subjek'
  },
  ASSEMBLY: {
    value: 'ASSEMBLY',
    label: 'Perhimpunan',
    icon: 'mdi:account-group',
    color: 'purple-600',
    bg_color: 'bg-purple-100 dark:bg-purple-900/20',
    text_color: 'text-purple-800 dark:text-purple-200',
    description: 'Perhimpunan sekolah'
  },
  COCURRICULAR: {
    value: 'COCURRICULAR',
    label: 'Kokurikulum',
    icon: 'mdi:run',
    color: 'green-600',
    bg_color: 'bg-green-100 dark:bg-green-900/20',
    text_color: 'text-green-800 dark:text-green-200',
    description: 'Aktiviti kokurikulum'
  },
  BREAK: {
    value: 'BREAK',
    label: 'Rehat',
    icon: 'mdi:coffee',
    color: 'yellow-600',
    bg_color: 'bg-yellow-100 dark:bg-yellow-900/20',
    text_color: 'text-yellow-800 dark:text-yellow-200',
    description: 'Masa rehat'
  },
  OTHER: {
    value: 'OTHER',
    label: 'Lain-lain',
    icon: 'mdi:calendar-star',
    color: 'gray-600',
    bg_color: 'bg-gray-100 dark:bg-gray-900/20',
    text_color: 'text-gray-800 dark:text-gray-200',
    description: 'Aktiviti lain yang tidak dikategorikan'
  }
}

// Time slot definition
export interface TimeSlot {
  id: string
  start_time: string // "08:00"
  end_time: string   // "09:00"
  label: string      // "8:00 AM - 9:00 AM"
  period_number: number // 1, 2, 3, etc.
}

// Timetable entry - represents a specific class at a specific time
export interface TimetableEntry {
  id: string
  teacher_schedule_id?: string | null
  day: DayOfWeek
  time_slot_start: string  // "08:00"
  time_slot_end: string    // "08:30"
  
  // Activity type and details
  activity_type: ActivityType
  activity_title?: string | null      // For non-class activities
  activity_description?: string | null // Additional details
  
  // Class-specific fields (nullable for non-class activities)
  class_id?: string | null
  subject_id?: string | null
  class_name?: string | null
  subject_name?: string | null
  
  // UI-specific fields
  is_dummy?: boolean  // For preview/placeholder entries
  
  // Common fields
  created_at: string
  updated_at: string
}

// For backward compatibility with TimeSlot-based components
export interface TimeSlot {
  id: string
  start_time: string // "08:00"
  end_time: string   // "09:00"
  label: string      // "8:00 AM - 9:00 AM"
  period_number: number // 1, 2, 3, etc.
}

// Helper function to convert TimetableEntry to work with TimeSlot-based components
export function getTimeSlotId(entry: TimetableEntry): string {
  return `${entry.time_slot_start}-${entry.time_slot_end}`
}

// Helper function to create TimeSlot from time strings
export function createTimeSlot(start: string, end: string, periodNumber: number): TimeSlot {
  return {
    id: `${start}-${end}`,
    start_time: start,
    end_time: end,
    label: `${start} - ${end}`,
    period_number: periodNumber
  }
}

// For displaying in the timetable grid
export interface TimetableSlot {
  day: DayOfWeek
  time_slot: TimeSlot
  entry?: TimetableEntry
  is_available: boolean
}

// Form data for creating/editing timetable entries
export interface TimetableEntryFormData {
  day: DayOfWeek
  time_slot_start: string  // "08:00"
  time_slot_end: string    // "08:30"
  class_subject_id: string // Composite ID: "class_id_subject_id"
}

// Subject color mapping for visual consistency
export interface SubjectColor {
  subject_id: string
  subject_name: string
  color: string
  bg_color: string
  text_color: string
}

// Predefined time slots for Malaysian school system
export const DEFAULT_TIME_SLOTS: TimeSlot[] = [
  { id: '1', start_time: '07:30', end_time: '08:00', label: '7:30 AM - 8:00 AM', period_number: 0 },
  { id: '2', start_time: '08:00', end_time: '08:30', label: '8:00 AM - 8:30 AM', period_number: 1 },
  { id: '3', start_time: '08:30', end_time: '09:00', label: '8:30 AM - 9:00 AM', period_number: 2 },
  { id: '4', start_time: '09:00', end_time: '09:30', label: '9:00 AM - 9:30 AM', period_number: 3 },
  { id: '5', start_time: '09:30', end_time: '10:00', label: '9:30 AM - 10:00 AM', period_number: 4 },
  { id: '6', start_time: '10:20', end_time: '10:50', label: '10:20 AM - 10:50 AM', period_number: 5 },
  { id: '7', start_time: '10:50', end_time: '11:20', label: '10:50 AM - 11:20 AM', period_number: 6 },
  { id: '8', start_time: '11:20', end_time: '11:50', label: '11:20 AM - 11:50 AM', period_number: 7 },
  { id: '9', start_time: '11:50', end_time: '12:20', label: '11:50 AM - 12:20 PM', period_number: 8 },
  { id: '10', start_time: '13:00', end_time: '13:30', label: '1:00 PM - 1:30 PM', period_number: 9 },
  { id: '11', start_time: '13:30', end_time: '14:00', label: '1:30 PM - 2:00 PM', period_number: 10 },
  { id: '12', start_time: '14:00', end_time: '14:30', label: '2:00 PM - 2:30 PM', period_number: 11 },
  { id: '13', start_time: '14:30', end_time: '15:00', label: '2:30 PM - 3:00 PM', period_number: 12 }
]

// Subject color palette - comprehensive coverage for all 25+ Malaysian subjects
export const SUBJECT_COLORS: Record<string, SubjectColor> = {
  // STEM Sciences (Cool colors - blues, teals, purples)
  mathematics: {
    subject_id: 'mathematics',
    subject_name: 'Matematik',
    color: 'border-l-blue-500',
    bg_color: 'bg-blue-100 dark:bg-blue-900/30',
    text_color: 'text-blue-900 dark:text-blue-100'
  },
  additional_mathematics: {
    subject_id: 'additional_mathematics',
    subject_name: 'Matematik Tambahan',
    color: 'border-l-blue-600',
    bg_color: 'bg-blue-200 dark:bg-blue-800/30',
    text_color: 'text-blue-900 dark:text-blue-100'
  },
  physics: {
    subject_id: 'physics',
    subject_name: 'Fizik',
    color: 'border-l-indigo-500',
    bg_color: 'bg-indigo-100 dark:bg-indigo-900/30',
    text_color: 'text-indigo-900 dark:text-indigo-100'
  },
  chemistry: {
    subject_id: 'chemistry',
    subject_name: 'Kimia',
    color: 'border-l-cyan-500',
    bg_color: 'bg-cyan-100 dark:bg-cyan-900/30',
    text_color: 'text-cyan-900 dark:text-cyan-100'
  },
  biology: {
    subject_id: 'biology',
    subject_name: 'Biologi',
    color: 'border-l-teal-500',
    bg_color: 'bg-teal-100 dark:bg-teal-900/30',
    text_color: 'text-teal-900 dark:text-teal-100'
  },
  science: {
    subject_id: 'science',
    subject_name: 'Sains',
    color: 'border-l-emerald-500',
    bg_color: 'bg-emerald-100 dark:bg-emerald-900/30',
    text_color: 'text-emerald-900 dark:text-emerald-100'
  },

  // Languages (Warm colors - reds, oranges, yellows)
  malay: {
    subject_id: 'malay',
    subject_name: 'Bahasa Melayu',
    color: 'border-l-red-500',
    bg_color: 'bg-red-100 dark:bg-red-900/30',
    text_color: 'text-red-900 dark:text-red-100'
  },
  english: {
    subject_id: 'english',
    subject_name: 'Bahasa Inggeris',
    color: 'border-l-purple-500',
    bg_color: 'bg-purple-100 dark:bg-purple-900/30',
    text_color: 'text-purple-900 dark:text-purple-100'
  },
  arabic: {
    subject_id: 'arabic',
    subject_name: 'Bahasa Arab',
    color: 'border-l-orange-500',
    bg_color: 'bg-orange-100 dark:bg-orange-900/30',
    text_color: 'text-orange-900 dark:text-orange-100'
  },
  chinese: {
    subject_id: 'chinese',
    subject_name: 'Bahasa Cina',
    color: 'border-l-yellow-500',
    bg_color: 'bg-yellow-100 dark:bg-yellow-900/30',
    text_color: 'text-yellow-900 dark:text-yellow-100'
  },
  tamil: {
    subject_id: 'tamil',
    subject_name: 'Bahasa Tamil',
    color: 'border-l-orange-600',
    bg_color: 'bg-orange-200 dark:bg-orange-800/30',
    text_color: 'text-orange-900 dark:text-orange-100'
  },
  kadazandusun: {
    subject_id: 'kadazandusun',
    subject_name: 'Bahasa Kadazandusun',
    color: 'border-l-red-600',
    bg_color: 'bg-red-200 dark:bg-red-800/30',
    text_color: 'text-red-900 dark:text-red-100'
  },
  iban: {
    subject_id: 'iban',
    subject_name: 'Bahasa Iban',
    color: 'border-l-amber-600',
    bg_color: 'bg-amber-200 dark:bg-amber-800/30',
    text_color: 'text-amber-900 dark:text-amber-100'
  },
  semai: {
    subject_id: 'semai',
    subject_name: 'Bahasa Semai',
    color: 'border-l-yellow-600',
    bg_color: 'bg-yellow-200 dark:bg-yellow-800/30',
    text_color: 'text-yellow-900 dark:text-yellow-100'
  },

  // Social Sciences (Earth tones - greens, stones, limes)
  history: {
    subject_id: 'history',
    subject_name: 'Sejarah',
    color: 'border-l-amber-500',
    bg_color: 'bg-amber-100 dark:bg-amber-900/30',
    text_color: 'text-amber-900 dark:text-amber-100'
  },
  geography: {
    subject_id: 'geography',
    subject_name: 'Geografi',
    color: 'border-l-green-500',
    bg_color: 'bg-green-100 dark:bg-green-900/30',
    text_color: 'text-green-900 dark:text-green-100'
  },
  economics: {
    subject_id: 'economics',
    subject_name: 'Ekonomi',
    color: 'border-l-lime-500',
    bg_color: 'bg-lime-100 dark:bg-lime-900/30',
    text_color: 'text-lime-900 dark:text-lime-100'
  },
  islamic: {
    subject_id: 'islamic',
    subject_name: 'Pendidikan Islam',
    color: 'border-l-stone-500',
    bg_color: 'bg-stone-100 dark:bg-stone-900/30',
    text_color: 'text-stone-900 dark:text-stone-100'
  },

  // Arts & Physical (Vibrant colors - pinks, purples, indigos)
  visual_arts: {
    subject_id: 'visual_arts',
    subject_name: 'Pendidikan Seni Visual',
    color: 'border-l-rose-500',
    bg_color: 'bg-rose-100 dark:bg-rose-900/30',
    text_color: 'text-rose-900 dark:text-rose-100'
  },
  music: {
    subject_id: 'music',
    subject_name: 'Pendidikan Muzik',
    color: 'border-l-fuchsia-500',
    bg_color: 'bg-fuchsia-100 dark:bg-fuchsia-900/30',
    text_color: 'text-fuchsia-900 dark:text-fuchsia-100'
  },
  physical: {
    subject_id: 'physical',
    subject_name: 'Pendidikan Jasmani',
    color: 'border-l-pink-500',
    bg_color: 'bg-pink-100 dark:bg-pink-900/30',
    text_color: 'text-pink-900 dark:text-pink-100'
  },
  moral: {
    subject_id: 'moral',
    subject_name: 'Pendidikan Moral',
    color: 'border-l-violet-500',
    bg_color: 'bg-violet-100 dark:bg-violet-900/30',
    text_color: 'text-violet-900 dark:text-violet-100'
  },

  // Business & Technology (Professional colors - grays, slates)
  business: {
    subject_id: 'business',
    subject_name: 'Perniagaan',
    color: 'border-l-slate-500',
    bg_color: 'bg-slate-100 dark:bg-slate-900/30',
    text_color: 'text-slate-900 dark:text-slate-100'
  },
  accounting: {
    subject_id: 'accounting',
    subject_name: 'Prinsip Perakaunan',
    color: 'border-l-gray-500',
    bg_color: 'bg-gray-100 dark:bg-gray-900/30',
    text_color: 'text-gray-900 dark:text-gray-100'
  },
  design_technology: {
    subject_id: 'design_technology',
    subject_name: 'Reka Bentuk dan Teknologi',
    color: 'border-l-zinc-500',
    bg_color: 'bg-zinc-100 dark:bg-zinc-900/30',
    text_color: 'text-zinc-900 dark:text-zinc-100'
  },

  // Legacy/Generic (kept for backward compatibility)
  art: {
    subject_id: 'art',
    subject_name: 'Seni',
    color: 'border-l-pink-500',
    bg_color: 'bg-pink-100 dark:bg-pink-900/30',
    text_color: 'text-pink-900 dark:text-pink-100'
  },
  default: {
    subject_id: 'default',
    subject_name: 'Lain-lain',
    color: 'border-l-gray-500',
    bg_color: 'bg-gray-100 dark:bg-gray-900/30',
    text_color: 'text-gray-900 dark:text-gray-100'
  }
}

// Dynamic color palette for generating unique colors (fallback only - should not be needed with 100% coverage)
// Removed colors that are now used in predefined SUBJECT_COLORS to avoid conflicts
export const DYNAMIC_COLORS = [
  { color: 'border-l-neutral-500', bg_color: 'bg-neutral-100 dark:bg-neutral-900/30', text_color: 'text-neutral-900 dark:text-neutral-100' },
  { color: 'border-l-stone-600', bg_color: 'bg-stone-200 dark:bg-stone-800/30', text_color: 'text-stone-900 dark:text-stone-100' },
  { color: 'border-l-sky-500', bg_color: 'bg-sky-100 dark:bg-sky-900/30', text_color: 'text-sky-900 dark:text-sky-100' },
  { color: 'border-l-emerald-600', bg_color: 'bg-emerald-200 dark:bg-emerald-800/30', text_color: 'text-emerald-900 dark:text-emerald-100' },
  { color: 'border-l-indigo-600', bg_color: 'bg-indigo-200 dark:bg-indigo-800/30', text_color: 'text-indigo-900 dark:text-indigo-100' }
]

// Helper functions for activity management
export function isClassActivity(entry: TimetableEntry): boolean {
  return entry.activity_type === 'CLASS'
}

export function isNonClassActivity(entry: TimetableEntry): boolean {
  return entry.activity_type !== 'CLASS'
}

export function getActivityDisplayName(entry: TimetableEntry): string {
  if (isClassActivity(entry)) {
    return `${entry.class_name} - ${entry.subject_name}`
  }
  return entry.activity_title || ACTIVITY_TYPES[entry.activity_type].label
}

export function getActivityConfig(activityType: ActivityType): ActivityTypeConfig {
  return ACTIVITY_TYPES[activityType]
}

export function createClassEntry(
  timeSlot: { start: string; end: string },
  day: DayOfWeek,
  classId: string,
  subjectId: string,
  className: string,
  subjectName: string
): Omit<TimetableEntry, 'id' | 'created_at' | 'updated_at'> {
  return {
    day,
    time_slot_start: timeSlot.start,
    time_slot_end: timeSlot.end,
    activity_type: 'CLASS',
    class_id: classId,
    subject_id: subjectId,
    class_name: className,
    subject_name: subjectName
  }
}

export function createActivityEntry(
  timeSlot: { start: string; end: string },
  day: DayOfWeek,
  activityType: ActivityType,
  activityTitle: string,
  activityDescription?: string
): Omit<TimetableEntry, 'id' | 'created_at' | 'updated_at'> {
  return {
    day,
    time_slot_start: timeSlot.start,
    time_slot_end: timeSlot.end,
    activity_type: activityType,
    activity_title: activityTitle,
    activity_description: activityDescription
  }
}
