import { useSupabaseClient } from "#imports";
// Import the Database type using a relative path
import type { Database } from "../types/supabase"; // Adjust path if necessary

// Update the return type to use the generic SupabaseClient
export const useSupabase = (): {
  client: ReturnType<typeof useSupabaseClient<Database>>;
} => {
  const client = useSupabaseClient<Database>();
  return { client };
};
