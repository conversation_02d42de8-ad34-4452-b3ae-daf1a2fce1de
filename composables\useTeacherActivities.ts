import { ref, computed } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'
import type { Database } from '~/types/supabase'

export interface TeacherActivity {
  id: string
  user_id: string
  category: 'sukan' | 'pertubuhan' | 'sumbangan'
  activity_description: string
  is_active: boolean
  start_date: string | null
  end_date: string | null
  location: string | null
  notes: string | null
  created_at: string
  updated_at: string
}

export interface ActivitiesByCategory {
  sukan: TeacherActivity[]
  pertubuhan: TeacherActivity[]
  sumbangan: TeacherActivity[]
}

export function useTeacherActivities() {
  const client = useSupabaseClient<Database>()
  const user = useSupabaseUser()
  
  const activities = ref<TeacherActivity[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed properties for different activity categories
  const activitiesByCategory = computed((): ActivitiesByCategory => ({
    sukan: activities.value.filter(activity => activity.category === 'sukan'),
    pertubuhan: activities.value.filter(activity => activity.category === 'pertubuhan'),
    sumbangan: activities.value.filter(activity => activity.category === 'sumbangan')
  }))

  // Fetch all activities for the current user
  const fetchActivities = async (): Promise<TeacherActivity[]> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return []
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await client
        .from('teacher_activities')
        .select('*')
        .eq('user_id', user.value.id)
        .order('created_at', { ascending: false })

      if (fetchError) {
        error.value = fetchError.message
        return []
      }

      const fetchedActivities = (data || []) as TeacherActivity[]
      activities.value = fetchedActivities
      return fetchedActivities

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch activities'
      console.error('Error fetching activities:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // Create a new activity
  const createActivity = async (
    category: TeacherActivity['category'], 
    description: string
  ): Promise<TeacherActivity | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return null
    }

    if (!description.trim()) {
      error.value = 'Activity description is required'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: insertError } = await client
        .from('teacher_activities')
        .insert({
          user_id: user.value.id,
          category,
          activity_description: description.trim(),
          is_active: true
        })
        .select()
        .single()

      if (insertError) {
        error.value = insertError.message
        return null
      }

      const newActivity = data as TeacherActivity
      activities.value.unshift(newActivity) // Add to beginning of array
      return newActivity

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create activity'
      console.error('Error creating activity:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // Update an existing activity
  const updateActivity = async (
    activityId: string, 
    updates: Partial<Pick<TeacherActivity, 'activity_description' | 'is_active' | 'start_date' | 'end_date' | 'location' | 'notes'>>
  ): Promise<TeacherActivity | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const { data, error: updateError } = await client
        .from('teacher_activities')
        .update(updates)
        .eq('id', activityId)
        .eq('user_id', user.value.id) // Ensure user can only update their own activities
        .select()
        .single()

      if (updateError) {
        error.value = updateError.message
        return null
      }

      const updatedActivity = data as TeacherActivity
      
      // Update local state
      const index = activities.value.findIndex(activity => activity.id === activityId)
      if (index >= 0) {
        activities.value[index] = updatedActivity
      }

      return updatedActivity

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update activity'
      console.error('Error updating activity:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // Delete an activity
  const deleteActivity = async (activityId: string): Promise<boolean> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { error: deleteError } = await client
        .from('teacher_activities')
        .delete()
        .eq('id', activityId)
        .eq('user_id', user.value.id) // Ensure user can only delete their own activities

      if (deleteError) {
        error.value = deleteError.message
        return false
      }

      // Update local state
      activities.value = activities.value.filter(activity => activity.id !== activityId)
      return true

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete activity'
      console.error('Error deleting activity:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // Toggle activity active status
  const toggleActivityStatus = async (activityId: string): Promise<boolean> => {
    const activity = activities.value.find(a => a.id === activityId)
    if (!activity) {
      error.value = 'Activity not found'
      return false
    }

    const updatedActivity = await updateActivity(activityId, { 
      is_active: !activity.is_active 
    })
    
    return updatedActivity !== null
  }

  // Get activities by category
  const getActivitiesByCategory = (category: TeacherActivity['category']) => {
    return computed(() => activities.value.filter(activity => activity.category === category))
  }

  // Get activity statistics
  const activityStats = computed(() => {
    const total = activities.value.length
    const active = activities.value.filter(activity => activity.is_active).length
    const inactive = total - active
    
    const byCategory = {
      sukan: activities.value.filter(activity => activity.category === 'sukan').length,
      pertubuhan: activities.value.filter(activity => activity.category === 'pertubuhan').length,
      sumbangan: activities.value.filter(activity => activity.category === 'sumbangan').length
    }

    return {
      total,
      active,
      inactive,
      activeRate: total > 0 ? Math.round((active / total) * 100) : 0,
      byCategory
    }
  })

  return {
    // State
    activities,
    loading,
    error,
    
    // Computed
    activitiesByCategory,
    activityStats,
    
    // Methods
    fetchActivities,
    createActivity,
    updateActivity,
    deleteActivity,
    toggleActivityStatus,
    getActivitiesByCategory
  }
}
