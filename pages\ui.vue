<template>
    <div class="space-y-8">
        <!-- Page Header -->
        <div class="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-8 border border-primary/20">
            <div class="flex items-center space-x-4 mb-4">
                <div
                    class="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center">
                    <UiBaseIcon name="heroicons:paint-brush-solid" class="w-6 h-6 text-white" />
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">UI Component Library</h1>
                    <p class="text-gray-600 dark:text-gray-300">Complete showcase of RPHMate components</p>
                </div>
            </div>
            <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm font-medium">Tailwind CSS</span>
                <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">TypeScript</span>
                <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">Vue 3</span>
                <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">Nuxt 3</span>
            </div>
        </div>

        <!-- Component Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Buttons Section -->
            <UiCompositeCard class="h-fit">
                <template #header>
                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:cursor-arrow-ripple-solid" class="w-5 h-5 text-primary" />
                        <h2 class="text-xl font-semibold">Buttons</h2>
                    </div>
                </template>
                <template #default>
                    <div class="space-y-6">
                        <!-- Primary Buttons -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Primary Variants</h3>
                            <div class="flex flex-wrap gap-3">
                                <UiBaseButton variant="primary" size="sm">Small</UiBaseButton>
                                <UiBaseButton variant="primary" size="md">Medium</UiBaseButton>
                                <UiBaseButton variant="primary" size="lg">Large</UiBaseButton>
                            </div>
                        </div>

                        <!-- Secondary Buttons -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Secondary Variants
                            </h3>
                            <div class="flex flex-wrap gap-3">
                                <UiBaseButton variant="secondary" size="sm">Small</UiBaseButton>
                                <UiBaseButton variant="secondary" size="md">Medium</UiBaseButton>
                                <UiBaseButton variant="secondary" size="lg">Large</UiBaseButton>
                            </div>
                        </div>

                        <!-- Outline Buttons -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Outline Variants</h3>
                            <div class="flex flex-wrap gap-3">
                                <UiBaseButton variant="outline" size="sm">Small</UiBaseButton>
                                <UiBaseButton variant="outline" size="md">Medium</UiBaseButton>
                                <UiBaseButton variant="outline" size="lg">Large</UiBaseButton>
                            </div>
                        </div>

                        <!-- Icon Buttons -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">With Icons</h3>
                            <div class="flex flex-wrap gap-3">
                                <UiBaseButton variant="primary" size="md" prepend-icon="heroicons:plus-solid">
                                    Add Item
                                </UiBaseButton>
                                <UiBaseButton variant="outline" size="md"
                                    prepend-icon="heroicons:arrow-down-tray-solid">
                                    Download
                                </UiBaseButton>
                            </div>
                        </div>
                    </div>
                </template>
            </UiCompositeCard> <!-- Inputs Section -->
            <UiCompositeCard class="h-fit">
                <template #header>
                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:pencil-square-solid" class="w-5 h-5 text-primary" />
                        <h2 class="text-xl font-semibold">Form Inputs</h2>
                    </div>
                </template>
                <template #default>
                    <div class="space-y-4">
                        <UiBaseInput type="text" placeholder="Enter your name" v-model="formData.name" />
                        <UiBaseInput type="email" placeholder="Enter your email" v-model="formData.email" />
                        <UiBaseInput type="password" placeholder="Enter your password" v-model="formData.password" />

                        <!-- Form Values Display -->
                        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Form Values:</h4>
                            <div class="space-y-1 text-sm">
                                <p><span class="font-medium">Name:</span> {{ formData.name || 'Empty' }}</p>
                                <p><span class="font-medium">Email:</span> {{ formData.email || 'Empty' }}</p>
                                <p><span class="font-medium">Password:</span> {{ formData.password ?
                                    '•'.repeat(formData.password.length) : 'Empty' }}</p>
                            </div>
                        </div>
                    </div>
                </template>
            </UiCompositeCard> <!-- Cards Section -->
            <UiCompositeCard>
                <template #header>
                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:rectangle-stack-solid" class="w-5 h-5 text-primary" />
                        <h2 class="text-xl font-semibold">Card Components</h2>
                    </div>
                </template>
                <template #default>
                    <div class="space-y-6">
                        <!-- Simple Card -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Basic Card</h3>
                            <UiCompositeCard class="max-w-sm">
                                <template #default>
                                    <div class="text-center">
                                        <div
                                            class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                                            <UiBaseIcon name="heroicons:star-solid" class="w-8 h-8 text-white" />
                                        </div>
                                        <h4 class="text-lg font-semibold mb-2">Premium Feature</h4>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">This is a sample card
                                            content that demonstrates the card component styling.</p>
                                    </div>
                                </template>
                            </UiCompositeCard>
                        </div>

                        <!-- Card with Header -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Card with Header</h3>
                            <UiCompositeCard class="max-w-sm">
                                <template #header>
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-semibold">User Profile</h4>
                                        <UiBaseIcon name="heroicons:ellipsis-horizontal-solid"
                                            class="w-5 h-5 text-gray-400" />
                                    </div>
                                </template>
                                <template #default>
                                    <div class="flex items-center space-x-3">
                                        <div
                                            class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                                            <UiBaseIcon name="heroicons:user-solid" class="w-6 h-6 text-white" />
                                        </div>
                                        <div>
                                            <p class="font-medium">John Doe</p>
                                            <p class="text-sm text-gray-500"><EMAIL></p>
                                        </div>
                                    </div>
                                </template>
                            </UiCompositeCard>
                        </div>
                    </div>
                </template>
            </UiCompositeCard>

            <!-- Interactive Elements -->
            <UiCompositeCard>
                <template #header>
                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:cursor-arrow-rays-solid" class="w-5 h-5 text-primary" />
                        <h2 class="text-xl font-semibold">Interactive Elements</h2>
                    </div>
                </template>
                <template #default>
                    <div class="space-y-6">
                        <!-- Checkboxes -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Checkboxes</h3>
                            <div class="space-y-2"> <label class="flex items-center">
                                    <UiBaseCheckbox id="option1" v-model="checkboxes.option1" />
                                    <span class="ml-2 text-sm">Option 1</span>
                                </label>
                                <label class="flex items-center">
                                    <UiBaseCheckbox id="option2" v-model="checkboxes.option2" />
                                    <span class="ml-2 text-sm">Option 2</span>
                                </label>
                                <label class="flex items-center">
                                    <UiBaseCheckbox id="option3" v-model="checkboxes.option3" />
                                    <span class="ml-2 text-sm">Option 3</span>
                                </label>
                            </div>
                        </div>

                        <!-- Alerts -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Alerts</h3>
                            <div class="space-y-3">
                                <UiBaseAlert type="info" no-appear>
                                    This is an informational alert message.
                                </UiBaseAlert>
                                <UiBaseAlert type="success" no-appear>
                                    Success! Your action was completed.
                                </UiBaseAlert>
                                <UiBaseAlert type="warning" no-appear>
                                    Warning: Please check your input.
                                </UiBaseAlert>
                                <UiBaseAlert type="error" no-appear>
                                    Error: Something went wrong.
                                </UiBaseAlert>
                            </div>
                        </div>

                        <!-- Modal Demo -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Modal</h3>
                            <UiBaseButton @click="showModal = true" variant="primary">
                                Open Modal
                            </UiBaseButton>

                            <UiCompositeModal :is-open="showModal" title="Demo Modal" @close="showModal = false">
                                <div class="py-4">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        This is a demonstration of the modal component. You can add any content here.
                                    </p>
                                    <div class="mt-4 flex justify-end space-x-2">
                                        <UiBaseButton variant="outline" @click="showModal = false">
                                            Cancel
                                        </UiBaseButton>
                                        <UiBaseButton variant="primary" @click="showModal = false">
                                            Confirm
                                        </UiBaseButton>
                                    </div>
                                </div>
                            </UiCompositeModal>
                        </div>
                    </div>
                </template>
            </UiCompositeCard>
        </div>

        <!-- Status Section -->
        <UiCompositeCard>
            <template #header>
                <div class="flex items-center space-x-2">
                    <UiBaseIcon name="heroicons:chart-bar-solid" class="w-5 h-5 text-primary" />
                    <h2 class="text-xl font-semibold">Component Status</h2>
                </div>
            </template>
            <template #default>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div
                        class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                            <UiBaseIcon name="heroicons:check-solid" class="w-6 h-6 text-white" />
                        </div>
                        <h3 class="font-semibold text-green-800 dark:text-green-300">Completed</h3>
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">12</p>
                        <p class="text-sm text-green-600 dark:text-green-400">Components</p>
                    </div>

                    <div
                        class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-3">
                            <UiBaseIcon name="heroicons:clock-solid" class="w-6 h-6 text-white" />
                        </div>
                        <h3 class="font-semibold text-yellow-800 dark:text-yellow-300">In Progress</h3>
                        <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mt-1">3</p>
                        <p class="text-sm text-yellow-600 dark:text-yellow-400">Components</p>
                    </div>

                    <div
                        class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                            <UiBaseIcon name="heroicons:light-bulb-solid" class="w-6 h-6 text-white" />
                        </div>
                        <h3 class="font-semibold text-blue-800 dark:text-blue-300">Planned</h3>
                        <p class="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-1">5</p>
                        <p class="text-sm text-blue-600 dark:text-blue-400">Components</p>
                    </div>
                </div>
            </template>
        </UiCompositeCard>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';

// Meta tags for SEO
useHead({
    title: 'UI Components - RPHMate',
    meta: [
        { name: 'description', content: 'Complete showcase of RPHMate UI components built with Vue 3, Nuxt 3, and Tailwind CSS.' }
    ]
});

// Form data
const formData = reactive({
    name: '',
    email: '',
    password: ''
});

// Checkbox states
const checkboxes = reactive({
    option1: false,
    option2: true,
    option3: false
});

// Modal state
const showModal = ref(false);
</script>
