// Helper utilities for teacher schedule development and testing

import type { LegacyTeacherScheduleFormData, DayOfWeek, SchedulePeriod } from '~/types/teacherSchedule'

// Sample class-subject combinations for testing
export const sampleClassSubjects = [
  {
    class_id: 'f1',
    subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825',
    class_name: '1A',
    subject_name: 'Matemati<PERSON>',
    combined_label: '1A - Matematik'
  },
  {
    class_id: 'f2',
    subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825',
    class_name: '1B',
    subject_name: 'Matematik',
    combined_label: '1B - Matematik'
  },
  {
    class_id: 'f1',
    subject_id: 'a3b9ea9f-fd85-4ca8-86d3-6817baded25a',
    class_name: '1A',
    subject_name: 'Sains',
    combined_label: '1A - Sains'
  },
  {
    class_id: 'f3',
    subject_id: '0fae9dc9-5a6b-4de6-97dd-9a1ff2fc5b2e',
    class_name: '1C',
    subject_name: 'Bahasa Inggeris',
    combined_label: '1C - Bahasa Inggeris'
  }
]

// Sample schedule data for development (updated for new structure)
export const sampleSchedules: LegacyTeacherScheduleFormData[] = [
    {
        periods: [
            { class_id: 'f1', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', day: 'ISNIN', time_slot_start: '08:00', time_slot_end: '08:30' },
            { class_id: 'f1', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', day: 'RABU', time_slot_start: '08:00', time_slot_end: '08:30' },
            { class_id: 'f1', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', day: 'JUMAAT', time_slot_start: '08:00', time_slot_end: '08:30' },
        ]
    },
    {
        periods: [
            { class_id: 'f2', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', day: 'SELASA', time_slot_start: '08:00', time_slot_end: '08:30' },
            { class_id: 'f2', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', day: 'KHAMIS', time_slot_start: '08:00', time_slot_end: '08:30' },
        ]
    },
    {
        periods: [
            { class_id: 'f1', subject_id: 'a3b9ea9f-fd85-4ca8-86d3-6817baded25a', day: 'SELASA', time_slot_start: '08:00', time_slot_end: '08:30' },
            { class_id: 'f1', subject_id: 'a3b9ea9f-fd85-4ca8-86d3-6817baded25a', day: 'KHAMIS', time_slot_start: '08:00', time_slot_end: '08:30' },
            { class_id: 'f1', subject_id: 'a3b9ea9f-fd85-4ca8-86d3-6817baded25a', day: 'JUMAAT', time_slot_start: '08:00', time_slot_end: '08:30' },
        ]
    }
]

// Utility to create mock user profile with class subjects
export const createMockUserProfile = () => {
  return {
    id: 'mock-user-id',
    class_subjects: JSON.stringify([
      { class_id: 'f1', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', className: '1A' },
      { class_id: 'f2', subject_id: 'd21dcaa8-d036-4e0e-8717-bc587bed7825', className: '1B' },
      { class_id: 'f1', subject_id: 'a3b9ea9f-fd85-4ca8-86d3-6817baded25a', className: '1A' },
      { class_id: 'f3', subject_id: '0fae9dc9-5a6b-4de6-97dd-9a1ff2fc5b2e', className: '1C' }
    ])
  }
}

// Groups periods by class and subject to get the days scheduled
const getDaysForClassSubject = (periods: SchedulePeriod[]): Map<string, DayOfWeek[]> => {
    const classSubjectDayMap = new Map<string, Set<DayOfWeek>>();
    periods.forEach(p => {
        const key = `${p.class_id}_${p.subject_id}`;
        if (!classSubjectDayMap.has(key)) {
            classSubjectDayMap.set(key, new Set());
        }
        classSubjectDayMap.get(key)!.add(p.day);
    });

    const resultMap = new Map<string, DayOfWeek[]>();
    classSubjectDayMap.forEach((daysSet, key) => {
        resultMap.set(key, Array.from(daysSet));
    });

    return resultMap;
}

// Calculate total periods across all schedules
export const calculateTotalPeriods = (schedules: LegacyTeacherScheduleFormData[]): number => {
  return schedules.reduce((total, schedule) => total + (schedule.periods?.length || 0), 0)
}

// Get unique subjects from schedules
export const getUniqueSubjects = (schedules: LegacyTeacherScheduleFormData[]): string[] => {
    const subjectIds = new Set<string>();
    schedules.forEach(schedule => schedule.periods.forEach((period: any) => subjectIds.add(period.subject_id)));
    return Array.from(subjectIds);
}

// Get unique classes from schedules
export const getUniqueClasses = (schedules: LegacyTeacherScheduleFormData[]): string[] => {
    const classIds = new Set<string>();
    schedules.forEach(schedule => schedule.periods.forEach((period: any) => classIds.add(period.class_id)));
    return Array.from(classIds);
}
