// Vitest setup file
import { vi } from "vitest";
import { setActive<PERSON><PERSON>, createPinia } from "pinia";

// Mock Pinia testing utilities if they are causing type issues
// This is a workaround if TypeScript isn't picking up the types correctly.
vi.mock("@pinia/testing", () => ({
  // Mock other exports from @pinia/testing if needed
}));

// Initialize Pinia for testing
beforeEach(() => {
  setActivePinia(createPinia());
});

// You can add other global mocks or configurations here
