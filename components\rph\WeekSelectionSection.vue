<template>
  <div class="space-y-3">
    <div class="flex flex-col sm:flex-row gap-3">
      <div class="flex-1">
        <UiBaseSingleSelect v-if="weeks.length > 0" v-model="selectedWeekId" :options="weekOptions" option-label="name"
          option-value="id" placeholder="<PERSON>lih <PERSON>" id="weekSelectorDropdown" class="w-full" />
        <div v-else-if="!loading && weeks.length === 0"
          class="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <div class="text-center">
            <UiBaseIcon name="heroicons:calendar-solid" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">Tiada minggu ditemui</p>
            <UiBaseButton @click="$emit('openAddWeekModal')" variant="primary" size="sm"
              prepend-icon="heroicons:plus-solid">
              <PERSON><PERSON>gu Pertama
            </UiBaseButton>
          </div>
        </div>
      </div>

      <!-- Mobile Action Buttons -->
      <div class="flex sm:hidden gap-2">
        <UiBaseButton @click="$emit('openAddWeekModal')" variant="primary" size="md" class="flex-1 h-12"
          aria-label="Tambah Minggu">
          <div class="w-full h-full flex items-center justify-center">
            <UiBaseIcon name="heroicons:plus-solid" class="w-5 h-5" />
          </div>
        </UiBaseButton>
        <UiBaseButton @click="$emit('openManageModal')" variant="outline" size="md" class="flex-1 h-12"
          aria-label="Urus Minggu">
          <div class="w-full h-full flex items-center justify-center">
            <UiBaseIcon name="heroicons:cog-6-tooth-solid" class="w-5 h-5" />
          </div>
        </UiBaseButton>
      </div>
    </div>

    <!-- Selected Week Info -->
    <div v-if="selectedWeek" class="bg-primary/5 border border-primary/20 rounded-lg p-3">
      <div class="flex items-center space-x-2">
        <UiBaseIcon name="heroicons:check-circle-solid" class="w-4 h-4 text-primary" />
        <span class="text-sm font-medium text-primary">Minggu Aktif:</span>
        <span class="text-sm text-gray-700 dark:text-gray-300">{{ selectedWeek.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RphWeek } from '~/types/rph'
import UiBaseButton from '~/components/ui/base/Button.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'
import UiBaseSingleSelect from '~/components/ui/base/SingleSelect.vue'

interface Props {
  weeks: RphWeek[]
  selectedWeekId: string | null
  selectedWeek: RphWeek | null
  loading: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  openAddWeekModal: []
  openManageModal: []
  'update:selectedWeekId': [value: string | null]
}>()

const selectedWeekId = computed({
  get: () => props.selectedWeekId,
  set: (value) => emit('update:selectedWeekId', value)
})

const weekOptions = computed(() =>
  props.weeks
    .slice()
    .sort((a, b) => b.week_number - a.week_number)
    .map(w => ({ id: w.id, name: w.name }))
)
</script>
