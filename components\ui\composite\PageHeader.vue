<template>
    <div class="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-4 sm:p-6 border border-primary/20">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div class="flex items-center space-x-3 sm:space-x-4">
                <div
                    class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center">
                    <UiBaseIcon :name="icon" class="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
                    <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300">{{ subtitle }}</p>
                </div>
            </div>
            <div v-if="$slots.actions" class="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                <slot name="actions" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Props {
    title: string
    subtitle: string
    icon: string
}

defineProps<Props>()
</script>
