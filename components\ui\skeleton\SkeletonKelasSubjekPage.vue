<template>
  <div class="space-y-8">
    <!-- <PERSON> Header Skeleton -->
    <SkeletonPageHeader 
      title-width="15rem" 
      subtitle-width="25rem" 
      :show-actions="true" 
      :action-count="1" 
    />

    <!-- Search Card Skeleton -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="p-6">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          </div>
          <SkeletonBox height="2.5rem" width="6rem" class="rounded-md" />
        </div>
      </div>
    </div>

    <!-- Data Table Skeleton -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <!-- Table Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <SkeletonBox height="1.5rem" width="12rem" />
          </div>
          <SkeletonBox height="1rem" width="8rem" variant="light" />
        </div>
      </div>

      <!-- Desktop Table Content -->
      <div class="hidden md:block overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <!-- Checkbox Column -->
              <th class="px-6 py-3 text-center">
                <SkeletonBox height="1rem" width="1rem" variant="dark" />
              </th>
              <!-- Tahap Column -->
              <th class="px-6 py-3">
                <SkeletonBox height="1rem" width="3rem" variant="dark" />
              </th>
              <!-- Nama Kelas Column -->
              <th class="px-6 py-3">
                <SkeletonBox height="1rem" width="6rem" variant="dark" />
              </th>
              <!-- Subjek Column -->
              <th class="px-6 py-3">
                <SkeletonBox height="1rem" width="4rem" variant="dark" />
              </th>
              <!-- Singkatan Column -->
              <th class="px-6 py-3">
                <SkeletonBox height="1rem" width="5rem" variant="dark" />
              </th>
              <!-- Bil. Murid Column -->
              <th class="px-6 py-3">
                <SkeletonBox height="1rem" width="5rem" variant="dark" />
              </th>
              <!-- Tindakan Column -->
              <th class="px-6 py-3">
                <SkeletonBox height="1rem" width="5rem" variant="dark" />
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
            <tr v-for="i in 5" :key="`desktop-row-${i}`">
              <!-- Checkbox -->
              <td class="px-6 py-4 text-center">
                <SkeletonBox height="1rem" width="1rem" />
              </td>
              <!-- Tahap -->
              <td class="px-6 py-4">
                <SkeletonBox height="1rem" width="4rem" />
              </td>
              <!-- Nama Kelas -->
              <td class="px-6 py-4">
                <SkeletonBox height="1rem" width="6rem" />
              </td>
              <!-- Subjek -->
              <td class="px-6 py-4">
                <SkeletonBox height="1rem" width="8rem" />
              </td>
              <!-- Singkatan -->
              <td class="px-6 py-4">
                <SkeletonBox height="1rem" width="3rem" />
              </td>
              <!-- Bil. Murid -->
              <td class="px-6 py-4">
                <SkeletonBox height="1rem" width="2rem" />
              </td>
              <!-- Actions -->
              <td class="px-6 py-4">
                <div class="flex space-x-2">
                  <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" />
                  <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mobile Cards Content -->
      <div class="md:hidden space-y-4 p-6">
        <div v-for="i in 3" :key="`mobile-card-${i}`" 
             class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
          <div class="flex justify-between items-start">
            <div class="flex items-start space-x-3">
              <!-- Mobile Checkbox -->
              <SkeletonBox height="1rem" width="1rem" class="mt-1" />
              <div class="space-y-1">
                <SkeletonBox height="1.25rem" width="8rem" />
                <SkeletonBox height="0.875rem" width="5rem" variant="light" />
              </div>
            </div>
            <div class="flex space-x-2">
              <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" />
              <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" />
            </div>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-1">
              <SkeletonBox height="0.875rem" width="3rem" variant="light" />
              <SkeletonBox height="1rem" width="6rem" />
            </div>
            <div class="space-y-1">
              <SkeletonBox height="0.875rem" width="4rem" variant="light" />
              <SkeletonBox height="1rem" width="3rem" />
            </div>
            <div class="space-y-1">
              <SkeletonBox height="0.875rem" width="4rem" variant="light" />
              <SkeletonBox height="1rem" width="2rem" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
</script>
