// Main types index - centralized exports for all type definitions

// Core database types
export type { Database } from './supabase'

// Multi-tenant SaaS types
export type * from './multiTenant'

// Lesson plan related types
export type * from './lessonPlans'

// Reflection system types (explicit exports to avoid conflicts)
export type {
  LessonPlanReflection,
  ReflectionFormData,
  ReflectionStats,
  WeeklyReflectionTrend,
  ReflectionWithLessonPlan,
  ReflectionMode,
  LessonPlanDetailedReflection,
  DetailedReflectionFormData,
  ClassSubjectOption,
  CalculatedRating,
  QuickReflectionFormData,
  RatingBreakdown,
  LessonPlanReflectionWithCalculation,
  // Template types
  ReflectionTemplateCategory,
  ReflectionTemplate,
  UserReflectionTemplatePreference,
  ReflectionTemplateWithPreference,
  TemplateOption,
  TemplateApplicationResult,
  ReflectionTemplateFormData,
  TemplateUsageStats
} from './reflections'

// Reflection-specific types (with prefix to avoid conflicts)
export type {
  DayOption as ReflectionDayOption,
  SubmissionStatus as ReflectionSubmissionStatus
} from './reflections'

// RPH (Rancangan Pengajaran Harian) types
export type * from './rph'

// Teacher schedule types (new)
export type * from './teacherSchedule'

// Rating calculation types (new)
export type * from './ratingCalculation'
