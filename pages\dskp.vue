<template>
    <!-- Page Loading State -->
    <SkeletonLoader v-if="isPageLoading" variant="document-cards" :card-count="6" :title-width="'8rem'"
        :subtitle-width="'25rem'" />

    <div v-if="!isPageLoading" class="space-y-8">
        <!-- <PERSON> Header -->
        <UiCompositePageHeader title="Pengurusan Dokumen Standard Kokurikulum (DSKP)"
            subtitle="Urus dokumen DSKP untuk setiap kelas dan subjek yang anda ajar"
            icon="heroicons:document-text-solid">
            <template #actions>
                <div class="text-right">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ uploadedCount }}/{{ totalClassSubjects }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        DSKP Lengkap
                    </div>
                </div>
            </template>
        </UiCompositePageHeader>



        <!-- Empty State (No Class Subjects) -->
        <UiCompositeCard v-if="userClassSubjects.length === 0 && !isLoadingClassSubjects">
            <div class="text-center py-12">
                <Icon name="heroicons:academic-cap-solid" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Tiada Kelas & Subjek
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    Anda perlu menetapkan kelas dan subjek yang anda ajar terlebih dahulu sebelum boleh menguruskan
                    DSKP.
                </p>
                <UiBaseButton variant="primary" @click="navigateToProfile">
                    <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 mr-2" />
                    Pergi ke Kelas & Subjek
                </UiBaseButton>
            </div>
        </UiCompositeCard>

        <!-- DSKP Cards Grid -->
        <div v-else-if="dskpStatusList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <DskpCard v-for="dskpStatus in dskpStatusList" :key="`${dskpStatus.class_id}_${dskpStatus.subject_id}`"
                :dskp-status="dskpStatus" :is-loading="isLoadingDskp" @upload="handleUpload" @replace="handleReplace"
                @preview="handlePreview" @delete="handleDeleteClick" />
        </div>

        <!-- Loading State -->
        <div v-else-if="isLoadingClassSubjects || isLoadingDskp"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <SkeletonDocumentCard v-for="card in 6" :key="`card-${card}`" />
        </div>
    </div>

    <!-- Modals -->
    <UiCompositeDeleteConfirmationModal :is-open="showDeleteConfirmationModal" item-type="DSKP"
        :item-name="dskpToDelete?.file_name || 'dokumen'" :item-subtitle="deleteModalSubtitle"
        warning-message="DSKP ini akan dipadam secara kekal. Tindakan ini tidak boleh dibatalkan."
        @confirm="confirmDeleteDskp" @cancel="showDeleteConfirmationModal = false" />

    <FilePreviewModal :is-open="showPreviewModal" :title="previewModalTitle" :preview-url="previewModalUrl"
        :preview-type="previewModalType" :is-loading="isPreviewLoading" :raw-file-url="newTabPreviewUrl"
        @close="showPreviewModal = false" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useDskpDocuments } from '~/composables/useDskpDocuments';
import { useSubjects } from '~/composables/useSubjects';
import type { DskpDocument } from '~/types/dskpDocuments';
import { getPreviewType } from '~/types/dskpDocuments';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas';
import { getClassLevelName } from '~/utils/classLevelMapping';
import UiCompositeCard from '~/components/ui/composite/Card.vue';
import UiCompositePageHeader from '~/components/ui/composite/PageHeader.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import { useToast } from '~/composables/useToast';
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue';
import FilePreviewModal from '~/components/rph/FilePreviewModal.vue';
import DskpCard from '~/components/dskp/DskpCard.vue';
import Icon from '~/components/ui/base/Icon.vue';
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue';
import SkeletonDocumentCard from '~/components/ui/skeleton/SkeletonDocumentCard.vue';

// Set page metadata
definePageMeta({
    layout: 'default'
});

// Composables
const router = useRouter();
const supabase = useSupabaseClient();
const user = useSupabaseUser();

const {
    loading: isLoadingDskp,
    error: dskpError,
    fetchDskpDocuments,
    getDskpStatusForClassSubjects,
    uploadDskpDocument,
    updateDskpDocument,
    deleteDskpDocument,
    getTemporaryPublicUrl,
    validateFile,
} = useDskpDocuments();

const { subjects, fetchSubjects } = useSubjects();

// State
const isPageLoading = ref(true);
const isLoadingClassSubjects = ref(false);
const userClassSubjects = ref<UserClassSubjectEntry[]>([]);

// Toast notifications
const { success: showSuccessToast, error: showErrorToast } = useToast();

// Modal states
const showDeleteConfirmationModal = ref(false);
const dskpToDelete = ref<DskpDocument | null>(null);

const showPreviewModal = ref(false);
const previewModalTitle = ref('Pratonton Fail');
const previewModalUrl = ref<string | null>(null);
const previewModalType = ref<'image' | 'office' | null>(null);
const isPreviewLoading = ref(false);
const newTabPreviewUrl = ref<string | null>(null);

// Computed
const dskpStatusList = computed(() => {
    const statusList = getDskpStatusForClassSubjects(userClassSubjects.value);

    // Enhance with subject names from subjects store
    return statusList.map(status => {
        const subject = subjects.value.find(s => s.id === status.subject_id);
        return {
            ...status,
            subject_name: subject?.name || status.subject_abbreviation || 'Subjek',
        };
    });
});

const totalClassSubjects = computed(() => userClassSubjects.value.length);
const uploadedCount = computed(() => dskpStatusList.value.filter(status => status.has_dskp).length);

// Computed property for delete modal subtitle with class level name
const deleteModalSubtitle = computed(() => {
    if (!dskpToDelete.value) return '';
    const classLevelName = getClassLevelName(dskpToDelete.value.class_id);
    return `${classLevelName} - ${dskpToDelete.value.subject_name}`;
});

// Methods
const loadUserClassSubjects = async () => {
    if (!user.value) return;

    isLoadingClassSubjects.value = true;
    try {
        const { data: profile, error: fetchError } = await supabase
            .from('profiles')
            .select('class_subjects')
            .eq('id', user.value.id)
            .single();

        if (fetchError) {
            console.error('Error fetching profile:', fetchError);
            userClassSubjects.value = [];
            return;
        }

        const classSubjects = ((profile as any)?.class_subjects as UserClassSubjectEntry[]) || [];
        userClassSubjects.value = classSubjects;
    } catch (error) {
        console.error('Error loading user class subjects:', error);
        userClassSubjects.value = [];
    } finally {
        isLoadingClassSubjects.value = false;
    }
};

const initializePage = async () => {
    isPageLoading.value = true;

    try {
        // Load data in parallel
        await Promise.all([
            loadUserClassSubjects(),
            fetchSubjects(),
            fetchDskpDocuments(),
        ]);
    } catch (error) {
        console.error('Error initializing DSKP page:', error);
        showErrorToast('Terdapat masalah semasa memuatkan data. Sila cuba lagi.');
    } finally {
        isPageLoading.value = false;
    }
};

const navigateToProfile = () => {
    router.push('/kelas-subjek');
};

// Handle file upload for new DSKP
const handleUpload = async (classId: string, subjectId: string, file: File) => {
    // Validate file first
    const validation = validateFile(file);
    if (!validation.isValid) {
        showErrorToast(validation.errorMessage || 'Fail yang dipilih tidak sah.');
        return;
    }

    // Find the class-subject entry to get names
    const classSubject = userClassSubjects.value.find(
        entry => entry.class_id === classId && entry.subject_id === subjectId
    );

    if (!classSubject) {
        showErrorToast('Kelas dan subjek tidak dijumpai.');
        return;
    }

    const subject = subjects.value.find(s => s.id === subjectId);
    const subjectName = subject?.name || classSubject.subject_abbreviation || 'Subjek';
    const classLevelName = getClassLevelName(classId);

    const dskpInput = {
        class_id: classId,
        subject_id: subjectId,
        class_name: classLevelName, // Use class level name instead of specific class name
        subject_name: subjectName,
    };

    const result = await uploadDskpDocument(dskpInput, file);

    if (result) {
        showSuccessToast(`DSKP untuk ${classLevelName} - ${subjectName} telah berjaya dimuat naik.`);
    } else {
        showErrorToast(dskpError.value || 'Gagal memuat naik DSKP. Sila cuba lagi.');
    }
};

// Handle file replacement for existing DSKP
const handleReplace = async (dskpDocument: DskpDocument, file: File) => {
    // Validate file first
    const validation = validateFile(file);
    if (!validation.isValid) {
        showErrorToast(validation.errorMessage || 'Fail yang dipilih tidak sah.');
        return;
    }

    const result = await updateDskpDocument(dskpDocument, file);

    if (result) {
        showSuccessToast(`DSKP untuk ${dskpDocument.class_name} - ${dskpDocument.subject_name} telah berjaya dikemas kini.`);
    } else {
        showErrorToast(dskpError.value || 'Gagal mengemaskini DSKP. Sila cuba lagi.');
    }
};

const handlePreview = async (dskpDocument: DskpDocument) => {
    previewModalTitle.value = `Pratonton: ${dskpDocument.file_name}`;
    previewModalType.value = getPreviewType(dskpDocument.file_mime_type);
    previewModalUrl.value = null;
    newTabPreviewUrl.value = null;
    isPreviewLoading.value = true;
    showPreviewModal.value = true;

    try {
        const signedUrl = await getTemporaryPublicUrl(dskpDocument.storage_file_path);

        if (signedUrl) {
            if (previewModalType.value === 'office') {
                if (dskpDocument.file_mime_type === 'application/pdf') {
                    // Use Google Docs viewer for PDF files in iframe
                    previewModalUrl.value = `https://docs.google.com/gview?url=${encodeURIComponent(signedUrl)}&embedded=true`;
                    // For new tab, open the PDF directly
                    newTabPreviewUrl.value = signedUrl;
                } else {
                    // Use Microsoft Office Online Viewer for other office documents (docx, xlsx)
                    previewModalUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(signedUrl)}`;
                    // For new tab, use the non-embedded viewer which has more controls
                    newTabPreviewUrl.value = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(signedUrl)}`;
                }
            } else {
                // For images, both URLs are the same
                previewModalUrl.value = signedUrl;
                newTabPreviewUrl.value = signedUrl;
            }
        } else {
            throw new Error('Failed to get file URL');
        }
    } catch (error) {
        console.error('Error setting up preview:', error);
        showErrorToast('Gagal memuatkan pratonton fail. Sila cuba lagi.');
        showPreviewModal.value = false;
    } finally {
        isPreviewLoading.value = false;
    }
};

// Handle delete click
const handleDeleteClick = (dskpDocument: DskpDocument) => {
    dskpToDelete.value = dskpDocument;
    showDeleteConfirmationModal.value = true;
};

const confirmDeleteDskp = async () => {
    if (!dskpToDelete.value) return;

    const result = await deleteDskpDocument(dskpToDelete.value);

    if (result) {
        // Use class level name for display
        const classLevelName = getClassLevelName(dskpToDelete.value.class_id);
        showSuccessToast(`DSKP untuk ${classLevelName} - ${dskpToDelete.value.subject_name} telah berjaya dipadam.`);
    } else {
        showErrorToast(dskpError.value || 'Gagal memadam DSKP. Sila cuba lagi.');
    }

    showDeleteConfirmationModal.value = false;
    dskpToDelete.value = null;
};

// Lifecycle
onMounted(() => {
    initializePage();
});

// Watch for authentication changes
watch(user, (newUser) => {
    if (newUser) {
        initializePage();
    }
}, { immediate: true });
</script>
