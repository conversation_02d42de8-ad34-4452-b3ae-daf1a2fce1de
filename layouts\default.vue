<template>
    <div
        class="flex h-screen bg-light-background dark:bg-dark-background text-light-foreground dark:text-dark-foreground">
        <!-- Sidebar -->
        <UiLayoutSidebar :is-mobile-open="isMobileSidebarOpen" @close="closeMobileSidebar" @logout="handleLogout" />

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <!-- Header -->
            <UiLayoutHeader @toggle-mobile-sidebar="toggleMobileSidebar" />

            <!-- Main Content -->
            <main id="main-content-area" class="flex-1 overflow-x-hidden overflow-y-auto p-4 md:p-6 lg:p-8">
                <slot />
            </main>
        </div>

        <!-- Toast Container -->
        <ClientOnly>
            <UiToastContainer />
        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Mobile sidebar state
const isMobileSidebarOpen = ref(false);

const toggleMobileSidebar = () => {
    isMobileSidebarOpen.value = !isMobileSidebarOpen.value;
};

const closeMobileSidebar = () => {
    isMobileSidebarOpen.value = false;
};

const handleLogout = async () => {
    const supabaseClient = useSupabaseClient();
    const router = useRouter();

    if (!supabaseClient) {
        console.error('Supabase client is not available.');
        return;
    }

    // Close mobile sidebar
    closeMobileSidebar();

    const { error } = await supabaseClient.auth.signOut();
    if (error) {
        console.error('Error logging out:', error.message);
    } else {
        // Redirect to the login page after successful logout
        await router.push('/auth/login');
    }
};
</script>

<style scoped>
/* Scoped styles for the layout if needed */
</style>
