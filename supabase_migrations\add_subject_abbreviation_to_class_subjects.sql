-- Migration to add subject_abbreviation field to existing class_subjects data in profiles table
-- This updates existing profiles to add empty subject_abbreviation field for backward compatibility

UPDATE profiles
SET class_subjects = (
    SELECT json_agg(
        CASE 
            WHEN element->>'subject_abbreviation' IS NULL 
            THEN element || jsonb_build_object('subject_abbreviation', '')
            ELSE element
        END
    )
    FROM jsonb_array_elements(class_subjects) AS element
)
WHERE class_subjects IS NOT NULL 
  AND class_subjects != 'null'::jsonb 
  AND jsonb_array_length(class_subjects) > 0;
