<template>
  <div class="space-y-8">
    <!-- Page Header -->
    <SkeletonPageHeader :title-width="'15rem'" :subtitle-width="'25rem'" :show-actions="false" />

    <!-- Document Display Section (when document exists) -->
    <div class="space-y-4">
      <!-- Document Preview Card -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Card Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
              <SkeletonBox height="1.125rem" width="12rem" />
            </div>
            <SkeletonBox height="2rem" width="10rem" class="rounded-md" />
          </div>
        </div>

        <!-- Document Viewer Area -->
        <div class="p-6">
          <div class="relative">
            <SkeletonBox height="56rem" width="100%" class="rounded-md border border-gray-200 dark:border-gray-700" variant="light" />
          </div>
        </div>
      </div>
    </div>

    <!-- Upload/Document Management Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <!-- Card Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
          <SkeletonBox height="1.125rem" width="18rem" />
        </div>
      </div>

      <!-- Card Content -->
      <div class="p-6 space-y-6">
        <!-- Document Info Section -->
        <div class="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <SkeletonBox height="2rem" width="2rem" class="rounded" />
          <div class="flex-1 space-y-2">
            <SkeletonBox height="1rem" width="16rem" />
            <SkeletonBox height="0.875rem" width="12rem" variant="light" />
            <SkeletonBox height="0.75rem" width="10rem" variant="light" />
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
          <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
        </div>

        <!-- Info Message -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div class="flex items-start space-x-3">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <div class="flex-1 space-y-2">
              <SkeletonBox height="0.875rem" width="6rem" />
              <SkeletonBox height="0.875rem" width="100%" variant="light" />
              <SkeletonBox height="0.875rem" width="80%" variant="light" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
</script>
