-- Row Level Security (RLS) Policies for Multi-Tenant Data Isolation
-- Created: 2025-07-13
-- Purpose: Ensure users can only access data from their associated schools

-- =============================================================================
-- HELPER FUNCTIONS
-- =============================================================================

-- Function to get user's accessible school IDs
CREATE OR REPLACE FUNCTION get_user_school_ids(user_id UUID)
RETURNS UUID[] AS $$
BEGIN
  -- Return array of school IDs that the user has access to
  RETURN ARRAY(
    SELECT DISTINCT school_id 
    FROM school_memberships 
    WHERE user_id = $1 
    AND status = 'active'
    
    UNION
    
    SELECT DISTINCT id 
    FROM schools 
    WHERE admin_user_id = $1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has access to a specific school
CREATE OR REPLACE FUNCTION user_has_school_access(user_id UUID, school_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is school admin
  IF EXISTS (
    SELECT 1 FROM schools 
    WHERE id = school_id 
    AND admin_user_id = user_id
  ) THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user has active membership
  IF EXISTS (
    SELECT 1 FROM school_memberships 
    WHERE user_id = $1 
    AND school_id = $2 
    AND status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- ENABLE RLS ON TABLES
-- =============================================================================

-- Enable RLS on all school-specific tables
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE school_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE teaching_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE timetables ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE reflections ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_calendars ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- SCHOOLS TABLE POLICIES
-- =============================================================================

-- Users can view schools they have access to
CREATE POLICY "Users can view accessible schools" ON schools
  FOR SELECT
  USING (
    admin_user_id = auth.uid() OR
    id = ANY(get_user_school_ids(auth.uid()))
  );

-- Only school admins can update their schools
CREATE POLICY "School admins can update their schools" ON schools
  FOR UPDATE
  USING (admin_user_id = auth.uid())
  WITH CHECK (admin_user_id = auth.uid());

-- Only authenticated users can create schools (handled by API)
CREATE POLICY "Authenticated users can create schools" ON schools
  FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL);

-- Only school admins can delete their schools
CREATE POLICY "School admins can delete their schools" ON schools
  FOR DELETE
  USING (admin_user_id = auth.uid());

-- =============================================================================
-- SCHOOL MEMBERSHIPS TABLE POLICIES
-- =============================================================================

-- Users can view memberships for schools they have access to
CREATE POLICY "Users can view school memberships" ON school_memberships
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    user_has_school_access(auth.uid(), school_id)
  );

-- School admins and supervisors can insert memberships
CREATE POLICY "Admins can manage memberships" ON school_memberships
  FOR INSERT
  WITH CHECK (
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM school_memberships sm
      JOIN schools s ON sm.school_id = s.id
      WHERE sm.user_id = auth.uid()
      AND sm.school_id = school_memberships.school_id
      AND (sm.role IN ('admin', 'supervisor') OR s.admin_user_id = auth.uid())
      AND sm.status = 'active'
    )
  );

-- School admins and supervisors can update memberships
CREATE POLICY "Admins can update memberships" ON school_memberships
  FOR UPDATE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM school_memberships sm
      JOIN schools s ON sm.school_id = s.id
      WHERE sm.user_id = auth.uid()
      AND sm.school_id = school_memberships.school_id
      AND (sm.role IN ('admin', 'supervisor') OR s.admin_user_id = auth.uid())
      AND sm.status = 'active'
    )
  )
  WITH CHECK (
    user_has_school_access(auth.uid(), school_id)
  );

-- School admins can delete memberships
CREATE POLICY "Admins can delete memberships" ON school_memberships
  FOR DELETE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM schools s
      WHERE s.id = school_memberships.school_id
      AND s.admin_user_id = auth.uid()
    )
  );

-- =============================================================================
-- LESSON PLANS TABLE POLICIES
-- =============================================================================

-- Users can view lesson plans from their schools
CREATE POLICY "Users can view school lesson plans" ON lesson_plans
  FOR SELECT
  USING (user_has_school_access(auth.uid(), school_id));

-- Users can create lesson plans for their schools
CREATE POLICY "Users can create lesson plans" ON lesson_plans
  FOR INSERT
  WITH CHECK (
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- Users can update their own lesson plans, admins/supervisors can update all
CREATE POLICY "Users can update lesson plans" ON lesson_plans
  FOR UPDATE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = lesson_plans.school_id
        AND sm.role IN ('admin', 'supervisor')
        AND sm.status = 'active'
      )
    )
  )
  WITH CHECK (user_has_school_access(auth.uid(), school_id));

-- Users can delete their own lesson plans, admins can delete all
CREATE POLICY "Users can delete lesson plans" ON lesson_plans
  FOR DELETE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = lesson_plans.school_id
        AND sm.role = 'admin'
        AND sm.status = 'active'
      )
    )
  );

-- =============================================================================
-- TEACHING SCHEDULES TABLE POLICIES
-- =============================================================================

-- Users can view schedules from their schools
CREATE POLICY "Users can view school schedules" ON teaching_schedules
  FOR SELECT
  USING (user_has_school_access(auth.uid(), school_id));

-- Users can create schedules for their schools
CREATE POLICY "Users can create schedules" ON teaching_schedules
  FOR INSERT
  WITH CHECK (
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- Users can update their own schedules, admins/supervisors can update all
CREATE POLICY "Users can update schedules" ON teaching_schedules
  FOR UPDATE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = teaching_schedules.school_id
        AND sm.role IN ('admin', 'supervisor')
        AND sm.status = 'active'
      )
    )
  )
  WITH CHECK (user_has_school_access(auth.uid(), school_id));

-- Users can delete their own schedules, admins can delete all
CREATE POLICY "Users can delete schedules" ON teaching_schedules
  FOR DELETE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = teaching_schedules.school_id
        AND sm.role = 'admin'
        AND sm.status = 'active'
      )
    )
  );

-- =============================================================================
-- TIMETABLES TABLE POLICIES
-- =============================================================================

-- Users can view timetables from their schools
CREATE POLICY "Users can view school timetables" ON timetables
  FOR SELECT
  USING (user_has_school_access(auth.uid(), school_id));

-- Admins and supervisors can manage timetables
CREATE POLICY "Admins can manage timetables" ON timetables
  FOR ALL
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM school_memberships sm
      WHERE sm.user_id = auth.uid()
      AND sm.school_id = timetables.school_id
      AND sm.role IN ('admin', 'supervisor')
      AND sm.status = 'active'
    )
  )
  WITH CHECK (user_has_school_access(auth.uid(), school_id));

-- =============================================================================
-- CLASSES TABLE POLICIES
-- =============================================================================

-- Users can view classes from their schools
CREATE POLICY "Users can view school classes" ON classes
  FOR SELECT
  USING (user_has_school_access(auth.uid(), school_id));

-- Admins and supervisors can manage classes
CREATE POLICY "Admins can manage classes" ON classes
  FOR ALL
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM school_memberships sm
      WHERE sm.user_id = auth.uid()
      AND sm.school_id = classes.school_id
      AND sm.role IN ('admin', 'supervisor')
      AND sm.status = 'active'
    )
  )
  WITH CHECK (user_has_school_access(auth.uid(), school_id));

-- =============================================================================
-- SUBJECTS TABLE POLICIES
-- =============================================================================

-- Users can view subjects from their schools and global subjects
CREATE POLICY "Users can view subjects" ON subjects
  FOR SELECT
  USING (
    school_id IS NULL OR -- Global subjects
    user_has_school_access(auth.uid(), school_id)
  );

-- Users can create school-specific subjects
CREATE POLICY "Users can create school subjects" ON subjects
  FOR INSERT
  WITH CHECK (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id)
  );

-- Users can update school-specific subjects they have access to
CREATE POLICY "Users can update school subjects" ON subjects
  FOR UPDATE
  USING (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id)
  )
  WITH CHECK (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id)
  );

-- Only admins can delete school-specific subjects
CREATE POLICY "Admins can delete school subjects" ON subjects
  FOR DELETE
  USING (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM school_memberships sm
      WHERE sm.user_id = auth.uid()
      AND sm.school_id = subjects.school_id
      AND sm.role = 'admin'
      AND sm.status = 'active'
    )
  );

-- =============================================================================
-- TEMPLATES TABLE POLICIES
-- =============================================================================

-- Users can view templates from their schools and global templates
CREATE POLICY "Users can view templates" ON templates
  FOR SELECT
  USING (
    school_id IS NULL OR -- Global templates
    user_has_school_access(auth.uid(), school_id)
  );

-- Users can create school-specific templates
CREATE POLICY "Users can create school templates" ON templates
  FOR INSERT
  WITH CHECK (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- Users can update their own templates, admins can update all school templates
CREATE POLICY "Users can update templates" ON templates
  FOR UPDATE
  USING (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = templates.school_id
        AND sm.role IN ('admin', 'supervisor')
        AND sm.status = 'active'
      )
    )
  )
  WITH CHECK (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id)
  );

-- Users can delete their own templates, admins can delete all school templates
CREATE POLICY "Users can delete templates" ON templates
  FOR DELETE
  USING (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = templates.school_id
        AND sm.role = 'admin'
        AND sm.status = 'active'
      )
    )
  );

-- =============================================================================
-- REFLECTIONS TABLE POLICIES
-- =============================================================================

-- Users can view reflections from their schools
CREATE POLICY "Users can view school reflections" ON reflections
  FOR SELECT
  USING (user_has_school_access(auth.uid(), school_id));

-- Users can create reflections for their schools
CREATE POLICY "Users can create reflections" ON reflections
  FOR INSERT
  WITH CHECK (
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- Users can update their own reflections, supervisors can view all
CREATE POLICY "Users can update reflections" ON reflections
  FOR UPDATE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  )
  WITH CHECK (
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- Users can delete their own reflections
CREATE POLICY "Users can delete reflections" ON reflections
  FOR DELETE
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- =============================================================================
-- ACADEMIC CALENDARS TABLE POLICIES
-- =============================================================================

-- Users can view academic calendars from their schools
CREATE POLICY "Users can view school calendars" ON academic_calendars
  FOR SELECT
  USING (user_has_school_access(auth.uid(), school_id));

-- Admins and supervisors can manage academic calendars
CREATE POLICY "Admins can manage calendars" ON academic_calendars
  FOR ALL
  USING (
    user_has_school_access(auth.uid(), school_id) AND
    EXISTS (
      SELECT 1 FROM school_memberships sm
      WHERE sm.user_id = auth.uid()
      AND sm.school_id = academic_calendars.school_id
      AND sm.role IN ('admin', 'supervisor')
      AND sm.status = 'active'
    )
  )
  WITH CHECK (user_has_school_access(auth.uid(), school_id));

-- =============================================================================
-- DOCUMENTS TABLE POLICIES
-- =============================================================================

-- Users can view documents from their schools and global documents
CREATE POLICY "Users can view documents" ON documents
  FOR SELECT
  USING (
    school_id IS NULL OR -- Global documents
    user_has_school_access(auth.uid(), school_id)
  );

-- Users can create school-specific documents
CREATE POLICY "Users can create school documents" ON documents
  FOR INSERT
  WITH CHECK (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    user_id = auth.uid()
  );

-- Users can update their own documents, admins can update all school documents
CREATE POLICY "Users can update documents" ON documents
  FOR UPDATE
  USING (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = documents.school_id
        AND sm.role IN ('admin', 'supervisor')
        AND sm.status = 'active'
      )
    )
  )
  WITH CHECK (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id)
  );

-- Users can delete their own documents, admins can delete all school documents
CREATE POLICY "Users can delete documents" ON documents
  FOR DELETE
  USING (
    school_id IS NOT NULL AND
    user_has_school_access(auth.uid(), school_id) AND
    (
      user_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM school_memberships sm
        WHERE sm.user_id = auth.uid()
        AND sm.school_id = documents.school_id
        AND sm.role = 'admin'
        AND sm.status = 'active'
      )
    )
  );

-- =============================================================================
-- PROFILES TABLE POLICIES (User profiles are global, not school-specific)
-- =============================================================================

-- Users can view and update their own profiles
CREATE POLICY "Users can manage their own profiles" ON profiles
  FOR ALL
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- =============================================================================
-- COUPONS TABLE POLICIES (Global, admin-only)
-- =============================================================================

-- Only super admins can manage coupons (handled by application logic)
-- Regular users cannot access coupons table directly

-- =============================================================================
-- COUPON USAGE TABLE POLICIES
-- =============================================================================

-- School admins can view their school's coupon usage
CREATE POLICY "School admins can view coupon usage" ON coupon_usage
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM schools s
      WHERE s.id = coupon_usage.school_id
      AND s.admin_user_id = auth.uid()
    )
  );

-- =============================================================================
-- GRANT PERMISSIONS TO AUTHENTICATED USERS
-- =============================================================================

-- Grant usage on helper functions
GRANT EXECUTE ON FUNCTION get_user_school_ids(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_school_access(UUID, UUID) TO authenticated;

-- =============================================================================
-- REFRESH POLICIES (Run this after any policy changes)
-- =============================================================================

-- Force refresh of RLS policies
SELECT pg_reload_conf();
