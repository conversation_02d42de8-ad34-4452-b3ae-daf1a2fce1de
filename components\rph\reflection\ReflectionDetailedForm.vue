<template>
    <!-- Detailed Reflection Form -->
    <div class="space-y-6">
        <!-- General <PERSON><PERSON>r Message -->
        <div v-if="errors?.general"
            class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
                <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5" />
                <div class="text-sm text-red-800 dark:text-red-200">
                    {{ errors.general[0] }}
                </div>
            </div>
        </div> <!-- Quick Fields Component -->
        <ReflectionQuickFields :model-value="{
            overall_rating: formData.overall_rating,
            objectives_achieved: formData.objectives_achieved,
            challenges_faced: formData.challenges_faced
        }" @update:model-value="updateQuickFields" :errors="errors" :disabled="isNotImplemented" />

        <!-- Detailed Mode Fields -->
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">
                <PERSON><PERSON><PERSON><PERSON>
            </h3>

            <!-- 1. Keberkesanan Aktiviti -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Keberkesanan Aktiviti (1-5)
                </label>
                <div class="flex space-x-2">
                    <button v-for="level in 5" :key="level" type="button"
                        @click="$emit('updateActivityEffectiveness', level)"
                        class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:scale-105"
                        :class="level <= formData.activity_effectiveness
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'">
                        {{ level }}
                    </button>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Penilaian: {{ formData.activity_effectiveness }}/5
                </p>
            </div>

            <!-- 2. Penglibatan Murid -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Penglibatan Murid (1-5)
                </label>
                <div class="flex space-x-2">
                    <button v-for="level in 5" :key="level" type="button"
                        @click="$emit('updateStudentEngagement', level)"
                        class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:scale-105"
                        :class="level <= formData.student_engagement
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'">
                        {{ level }}
                    </button>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Penilaian: {{ formData.student_engagement }}/5
                </p>
            </div>

            <!-- 3. Pengurusan Masa -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Pengurusan Masa
                </label>
                <SingleSelect :model-value="formData.time_management"
                    @update:model-value="updateFormField('time_management', $event)" :options="timeManagementOptions"
                    option-label="label" option-value="value" placeholder="Pilih pengurusan masa" variant="standard"
                    :disabled="isNotImplemented" />
            </div>

            <!-- 4. Kecukupan Sumber & Bahan -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Kecukupan Sumber & Bahan
                </label>
                <SingleSelect :model-value="formData.resource_adequacy"
                    @update:model-value="updateFormField('resource_adequacy', $event)" :options="resourceOptions"
                    option-label="label" option-value="value" placeholder="Pilih tahap kecukupan" variant="standard"
                    :disabled="isNotImplemented" />
            </div>

            <!-- 5. Jumlah Murid Mencapai Objektif -->
            <div class="mb-6">
                <NumberStepper :model-value="formData.jumlah_murid_mencapai_objektif ?? studentCount"
                    @update:model-value="updateFormField('jumlah_murid_mencapai_objektif', $event)"
                    label="Jumlah Murid Mencapai Objektif" :description="`Jumlah keseluruhan murid: ${studentCount}`"
                    :min="0" :max="studentCount" :disabled="isNotImplemented" />
            </div>

            <!-- 6. Tindakan Susulan -->
            <div class="mb-6">
                <TindakanSusulanMultiSelect :model-value="formData.tindakan_susulan || []"
                    @update:model-value="updateFormField('tindakan_susulan', $event)" />
            </div>

            <!-- 7. Tidak Terlaksana -->
            <div class="mb-6" data-onboarding="tidak-terlaksana">
                <TidakTerlaksanaSelect :model-value="formData.tidak_terlaksana || null"
                    @update:model-value="updateFormField('tidak_terlaksana', $event)" />
            </div>

            <!-- 8. Catatan Tambahan -->
            <div class="mb-6">
                <label for="detailed-additional-notes"
                    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Catatan Tambahan
                </label>
                <textarea id="detailed-additional-notes" :value="formData.additional_notes"
                    @input="updateFormField('additional_notes', ($event.target as HTMLTextAreaElement).value)" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Catatan tambahan mengenai refleksi ini..." />
            </div>

            <!-- 9. Perincian Tambahan (Collapsible) -->
            <div class="mb-6">
                <div class="flex justify-center">
                    <Button type="button" @click="toggleAdditionalDetails" variant="outline" size="sm"
                        class="flex items-center">
                        Perincian Tambahan
                        <Icon :name="showAdditionalDetails ? 'heroicons:chevron-up' : 'heroicons:chevron-down'"
                            class="ml-2 h-4 w-4 transition-transform" />
                    </Button>
                </div>

                <!-- Collapsible Additional Details -->
                <div v-if="showAdditionalDetails"
                    class="mt-4 space-y-4 border-t border-gray-200 dark:border-gray-600 pt-4">
                    <!-- Penambahbaikan yang diperlukan -->
                    <div>
                        <label for="detailed-improvements-needed"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Penambahbaikan yang diperlukan
                        </label>
                        <textarea id="detailed-improvements-needed" :value="formData.improvements_needed"
                            @input="updateFormField('improvements_needed', ($event.target as HTMLTextAreaElement).value)"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                            placeholder="Nyatakan penambahbaikan yang boleh dibuat..." />
                    </div>

                    <!-- Strategi yang berjaya -->
                    <div>
                        <label for="detailed-successful-strategies"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Strategi yang berjaya
                        </label>
                        <textarea id="detailed-successful-strategies" :value="formData.successful_strategies"
                            @input="updateFormField('successful_strategies', ($event.target as HTMLTextAreaElement).value)"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                            placeholder="Nyatakan strategi pengajaran yang berkesan..." />
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import SingleSelect from '~/components/ui/base/SingleSelect.vue'
import NumberStepper from '~/components/ui/base/NumberStepper.vue'
import ReflectionQuickFields from '~/components/rph/ReflectionQuickFields.vue'
import TindakanSusulanMultiSelect from './TindakanSusulanMultiSelect.vue'
import TidakTerlaksanaSelect from './TidakTerlaksanaSelect.vue'
import type { DetailedReflectionFormData } from '~/types/reflections'

interface Props {
    formData: DetailedReflectionFormData
    errors: Record<string, string[]> | null
    timeManagementOptions: { label: string; value: string }[]
    resourceOptions: { label: string; value: string }[]
    loading: boolean
    isEditing: boolean
    studentCount: number
}

const props = defineProps<Props>()

// State
const showAdditionalDetails = ref(false)

// Computed
const isNotImplemented = computed(() => {
    return props.formData.tidak_terlaksana !== null && props.formData.tidak_terlaksana !== ''
})

// Methods
const toggleAdditionalDetails = async () => {
    showAdditionalDetails.value = !showAdditionalDetails.value

    // If opening the section, scroll to bottom smoothly
    if (showAdditionalDetails.value) {
        await nextTick()

        // Wait a bit more for the content to fully expand
        setTimeout(async () => {
            // Try multiple selectors to find the scrollable container
            const selectors = [
                '[role="dialog"] .overflow-y-auto',
                '.overflow-y-auto',
                '.reflection-modal .overflow-y-auto',
                '.modal-content',
                '[data-scroll-container]'
            ]

            let scrollContainer = null
            for (const selector of selectors) {
                const element = document.querySelector(selector)
                if (element) {
                    const style = window.getComputedStyle(element)
                    if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
                        scrollContainer = element
                        break
                    }
                }
            }

            // Fallback: find any scrollable parent of the form
            if (!scrollContainer) {
                const detailedForm = document.querySelector('.space-y-6')
                if (detailedForm) {
                    let parent = detailedForm.parentElement
                    while (parent && parent !== document.body) {
                        const style = window.getComputedStyle(parent)
                        if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
                            scrollContainer = parent
                            break
                        }
                        parent = parent.parentElement
                    }
                }
            }

            if (scrollContainer) {
                scrollContainer.scrollTo({
                    top: scrollContainer.scrollHeight,
                    behavior: 'smooth'
                })
            }
        }, 100) // Small delay to ensure content is fully expanded
    }
}

const emit = defineEmits<{
    'update:formData': [formData: DetailedReflectionFormData]
    updateActivityEffectiveness: [level: number]
    updateTimeManagement: [value: string]
    updateStudentEngagement: [level: number]
    updateResourceAdequacy: [value: string]
    updateImprovementsNeeded: [value: string]
    updateSuccessfulStrategies: [value: string]
    updateActionItem: [data: { index: number; value: string }]
    addActionItem: []
    removeActionItem: [index: number]
    updateAdditionalNotes: [value: string]
}>()

// Handle updates from ReflectionQuickFields
const updateQuickFields = (quickFields: {
    overall_rating: number
    objectives_achieved: boolean
    challenges_faced: string
}) => {
    emit('update:formData', {
        ...props.formData,
        ...quickFields
    })
}

// Generic method to update any form field
const updateFormField = (fieldName: keyof DetailedReflectionFormData, value: any) => {
    emit('update:formData', {
        ...props.formData,
        [fieldName]: value
    })
}
</script>
