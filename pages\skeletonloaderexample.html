<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skeleton Loader Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .panel {
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }

        .panel h3 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #34495e;
            text-align: center;
            font-weight: 600;
        }

        .skeleton-panel {
            border-color: #3498db;
            background: #f8fbff;
        }

        .content-panel {
            border-color: #27ae60;
            background: #f8fff8;
        }

        /* Skeleton Styles */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* Card Layout */
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
        }

        .avatar.skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-info h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
            color: #2c3e50;
        }

        .user-info p {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .skeleton-text {
            height: 16px;
            margin-bottom: 8px;
        }

        .skeleton-text.short {
            width: 60%;
        }

        .skeleton-text.medium {
            width: 80%;
        }

        .skeleton-text.long {
            width: 100%;
        }

        .skeleton-text.title {
            height: 20px;
            width: 70%;
            margin-bottom: 10px;
        }

        .content-text {
            color: #34495e;
            margin-bottom: 15px;
        }

        .content-text h4 {
            color: #2c3e50;
            margin-bottom: 8px;
        }

        /* List Layout */
        .list-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            margin-right: 15px;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .list-icon.skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        .list-content {
            flex: 1;
        }

        .list-content h5 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .list-content p {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        /* Controls */
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-btn:hover {
            background: #2980b9;
        }

        .explanation {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .explanation h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .explanation ul {
            color: #34495e;
            padding-left: 20px;
        }

        .explanation li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1 class="demo-title">Skeleton Loader vs Actual Content</h1>
            
            <div class="controls">
                <button class="toggle-btn" onclick="toggleView()">Toggle Between Views</button>
            </div>

            <div class="comparison">
                <div class="panel skeleton-panel">
                    <h3>🔄 Skeleton Loader</h3>
                    <div id="skeleton-content">
                        <!-- Card Skeleton -->
                        <div class="card">
                            <div class="card-header">
                                <div class="avatar skeleton"></div>
                                <div class="user-info">
                                    <div class="skeleton skeleton-text title"></div>
                                    <div class="skeleton skeleton-text short"></div>
                                </div>
                            </div>
                            <div class="skeleton skeleton-text long"></div>
                            <div class="skeleton skeleton-text medium"></div>
                            <div class="skeleton skeleton-text long"></div>
                        </div>

                        <!-- List Skeleton -->
                        <div class="card">
                            <div class="list-item">
                                <div class="list-icon skeleton"></div>
                                <div class="list-content">
                                    <div class="skeleton skeleton-text medium"></div>
                                    <div class="skeleton skeleton-text short"></div>
                                </div>
                            </div>
                            <div class="list-item">
                                <div class="list-icon skeleton"></div>
                                <div class="list-content">
                                    <div class="skeleton skeleton-text medium"></div>
                                    <div class="skeleton skeleton-text short"></div>
                                </div>
                            </div>
                            <div class="list-item">
                                <div class="list-icon skeleton"></div>
                                <div class="list-content">
                                    <div class="skeleton skeleton-text medium"></div>
                                    <div class="skeleton skeleton-text short"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel content-panel">
                    <h3>✅ Actual Content</h3>
                    <div id="actual-content">
                        <!-- Actual Card -->
                        <div class="card">
                            <div class="card-header">
                                <div class="avatar">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiMzNDk4ZGIiLz4KPHN2ZyB4PSIxNSIgeT0iMTIiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgxOVYxNkgxNFYxOS41SDEyVjE0SDE5VjlIMjFaTTUgOVYyMkg3VjE2SDEyVjE5LjVIMTRWMTRIN1Y5SDVaIi8+Cjwvc3ZnPgo8L3N2Zz4K" alt="User Avatar">
                                </div>
                                <div class="user-info">
                                    <h4>Sarah Johnson</h4>
                                    <p>2 hours ago</p>
                                </div>
                            </div>
                            <div class="content-text">
                                <p>Just finished implementing a new feature for our dashboard! The user experience is so much smoother now. Really excited to see how the team responds to these changes.</p>
                            </div>
                        </div>

                        <!-- Actual List -->
                        <div class="card">
                            <div class="list-item">
                                <div class="list-icon">📊</div>
                                <div class="list-content">
                                    <h5>Analytics Dashboard</h5>
                                    <p>View your performance metrics</p>
                                </div>
                            </div>
                            <div class="list-item">
                                <div class="list-icon">⚙️</div>
                                <div class="list-content">
                                    <h5>Settings & Configuration</h5>
                                    <p>Customize your experience</p>
                                </div>
                            </div>
                            <div class="list-item">
                                <div class="list-icon">👥</div>
                                <div class="list-content">
                                    <h5>Team Management</h5>
                                    <p>Manage users and permissions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="explanation">
                <h4>Key Principles of Skeleton Loaders:</h4>
                <ul>
                    <li><strong>Mirror the Layout:</strong> Skeleton loaders should closely match the structure and dimensions of the actual content</li>
                    <li><strong>Maintain Proportions:</strong> Text blocks should be similar widths to expected content, with varying lengths for realism</li>
                    <li><strong>Include All Elements:</strong> Show placeholders for avatars, icons, images, and text in their proper positions</li>
                    <li><strong>Use Appropriate Shapes:</strong> Circular placeholders for avatars, rectangular for text, square for icons</li>
                    <li><strong>Smooth Animation:</strong> The shimmer effect should be subtle and not distracting</li>
                    <li><strong>Consistent Spacing:</strong> Maintain the same margins and padding as the real content</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let showingSkeleton = true;

        function toggleView() {
            const skeletonContent = document.getElementById('skeleton-content');
            const actualContent = document.getElementById('actual-content');
            const button = document.querySelector('.toggle-btn');

            if (showingSkeleton) {
                skeletonContent.style.display = 'none';
                actualContent.style.display = 'block';
                button.textContent = 'Show Skeleton Loader';
                showingSkeleton = false;
            } else {
                skeletonContent.style.display = 'block';
                actualContent.style.display = 'none';
                button.textContent = 'Show Actual Content';
                showingSkeleton = true;
            }
        }

        // Initialize - show skeleton by default
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('actual-content').style.display = 'none';
        });
    </script>
</body>
</html>