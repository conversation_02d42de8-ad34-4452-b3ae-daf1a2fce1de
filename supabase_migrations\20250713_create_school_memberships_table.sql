-- Migration: Create school_memberships table for user-school relationships
-- Created: 2025-07-13
-- Description: Manage user-school relationships with roles and status tracking for multi-tenant access

BEGIN;

-- =====================================================
-- CREATE SCHOOL_MEMBERSHIPS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS school_memberships (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Foreign key relationships
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    
    -- Role within the school
    role TEXT NOT NULL CHECK (role IN ('admin', 'supervisor', 'teacher')),
    
    -- Membership status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    
    -- Membership metadata
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    invitation_token TEXT, -- For email invitations
    invitation_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional metadata
    notes TEXT, -- Admin notes about this membership
    permissions JSONB DEFAULT '{}'::jsonb, -- Custom permissions if needed
    
    -- Constraints
    CONSTRAINT school_memberships_user_school_unique UNIQUE (user_id, school_id),
    CONSTRAINT school_memberships_invitation_token_unique UNIQUE (invitation_token)
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================

-- Index for user lookups (most common query)
CREATE INDEX IF NOT EXISTS idx_school_memberships_user_id ON school_memberships (user_id);

-- Index for school lookups
CREATE INDEX IF NOT EXISTS idx_school_memberships_school_id ON school_memberships (school_id);

-- Composite index for user-school-status queries
CREATE INDEX IF NOT EXISTS idx_school_memberships_user_school_status ON school_memberships (user_id, school_id, status);

-- Index for role-based queries
CREATE INDEX IF NOT EXISTS idx_school_memberships_role ON school_memberships (role);

-- Index for status queries
CREATE INDEX IF NOT EXISTS idx_school_memberships_status ON school_memberships (status);

-- Index for invitation token lookups
CREATE INDEX IF NOT EXISTS idx_school_memberships_invitation_token ON school_memberships (invitation_token) 
WHERE invitation_token IS NOT NULL;

-- Index for invitation expiry cleanup
CREATE INDEX IF NOT EXISTS idx_school_memberships_invitation_expires_at ON school_memberships (invitation_expires_at) 
WHERE invitation_expires_at IS NOT NULL;

-- GIN index for permissions JSONB column
CREATE INDEX IF NOT EXISTS idx_school_memberships_permissions ON school_memberships USING GIN (permissions);

-- =====================================================
-- CREATE TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_school_memberships_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row changes
DROP TRIGGER IF EXISTS trigger_school_memberships_updated_at ON school_memberships;
CREATE TRIGGER trigger_school_memberships_updated_at
    BEFORE UPDATE ON school_memberships
    FOR EACH ROW
    EXECUTE FUNCTION update_school_memberships_updated_at();

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE school_memberships IS 'User-school relationships with roles and permissions for multi-tenant access';
COMMENT ON COLUMN school_memberships.id IS 'Unique identifier for the membership';
COMMENT ON COLUMN school_memberships.user_id IS 'Reference to the user in auth.users';
COMMENT ON COLUMN school_memberships.school_id IS 'Reference to the school';
COMMENT ON COLUMN school_memberships.role IS 'User role within the school (admin, supervisor, teacher)';
COMMENT ON COLUMN school_memberships.status IS 'Membership status (active, inactive, suspended, pending)';
COMMENT ON COLUMN school_memberships.joined_at IS 'When the user joined the school';
COMMENT ON COLUMN school_memberships.invited_by IS 'User who invited this member (if applicable)';
COMMENT ON COLUMN school_memberships.invitation_token IS 'Unique token for email invitations';
COMMENT ON COLUMN school_memberships.invitation_expires_at IS 'When the invitation expires';
COMMENT ON COLUMN school_memberships.permissions IS 'Custom permissions in JSON format';

-- =====================================================
-- CREATE RLS POLICIES
-- =====================================================

-- Enable RLS on school_memberships table
ALTER TABLE school_memberships ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own memberships
CREATE POLICY "Users can view their own memberships" ON school_memberships
    FOR SELECT USING (user_id = auth.uid());

-- Policy: School admins can manage memberships for their schools
CREATE POLICY "School admins can manage school memberships" ON school_memberships
    FOR ALL USING (
        school_id IN (
            SELECT id FROM schools WHERE admin_user_id = auth.uid()
        )
    );

-- Policy: School admins and supervisors can view all memberships in their schools
CREATE POLICY "School admins and supervisors can view school memberships" ON school_memberships
    FOR SELECT USING (
        school_id IN (
            SELECT school_id FROM school_memberships 
            WHERE user_id = auth.uid() 
            AND status = 'active' 
            AND role IN ('admin', 'supervisor')
        )
    );

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 'School memberships table created successfully' as status;

-- Show table structure
\d school_memberships;
