import { ref, computed, watch, type Ref } from "vue";
import type { RphWeek } from "~/types/rph";

export const useWeekSelection = (weeks: Ref<RphWeek[]>) => {
  // Main selection state
  const selectedWeekId = ref<string | null>(null);
  const internalSelectedWeek = ref<RphWeek | null>(null);

  // Multi-select state for bulk operations
  const selectedWeeksForDeletion = ref<Set<string>>(new Set());

  // Computed properties
  const sortedWeeks = computed(() => {
    return [...weeks.value].sort((a, b) => {
      const numA = typeof a.week_number === "number" ? a.week_number : 0;
      const numB = typeof b.week_number === "number" ? b.week_number : 0;
      return numB - numA;
    });
  });

  const weekOptions = computed(() =>
    sortedWeeks.value.map((w) => ({ id: w.id, name: w.name }))
  );

  // Auto-selection logic
  const autoSelectWeek = (weekIdToSelect?: string) => {
    if (weekIdToSelect) {
      const weekExists = sortedWeeks.value.find((w) => w.id === weekIdToSelect);
      if (weekExists) {
        selectedWeekId.value = weekIdToSelect;
        return;
      }
    }

    // Auto-select the first available week if none is selected
    if (sortedWeeks.value.length > 0) {
      selectedWeekId.value = sortedWeeks.value[0].id;
    } else {
      selectedWeekId.value = null;
    }
  };

  // Watch for changes in selected week ID
  watch(selectedWeekId, (newId) => {
    if (newId) {
      const foundWeek = sortedWeeks.value.find((w) => w.id === newId);
      internalSelectedWeek.value = foundWeek || null;
    } else {
      internalSelectedWeek.value = null;
    }
  });

  // Watch for changes in weeks list and handle auto-selection
  watch(
    weeks,
    (newWeeksList, oldWeeksList) => {
      if (
        newWeeksList.length !== oldWeeksList.length ||
        !selectedWeekId.value
      ) {
        if (
          selectedWeekId.value &&
          !newWeeksList.find((w) => w.id === selectedWeekId.value)
        ) {
          autoSelectWeek();
        } else if (!selectedWeekId.value && sortedWeeks.value.length > 0) {
          autoSelectWeek();
        }
      }
    },
    { deep: true }
  );

  // Multi-select operations
  const clearSelection = () => {
    selectedWeeksForDeletion.value = new Set();
  };

  const selectWeekById = (weekId: string) => {
    const weekToSelect = sortedWeeks.value.find((w) => w.id === weekId);
    if (weekToSelect) {
      selectedWeekId.value = weekId;
    } else {
      selectedWeekId.value = null;
      console.warn(`Week with ID ${weekId} not found in WeekSelector.`);
    }
  };

  return {
    // Main selection
    selectedWeekId,
    internalSelectedWeek,

    // Multi-select for bulk operations
    selectedWeeksForDeletion,

    // Computed
    sortedWeeks,
    weekOptions,

    // Actions
    autoSelectWeek,
    clearSelection,
    selectWeekById,
  };
};
