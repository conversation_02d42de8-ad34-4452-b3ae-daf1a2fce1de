<template>
    <div
        class="bg-white dark:bg-dark-card rounded-lg shadow-sm border border-gray-200 dark:border-dark-border overflow-hidden">
        <!-- Calendar Header -->
        <div class="grid grid-cols-7 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-dark-border">
            <div v-for="day in dayHeaders" :key="day"
                class="p-3 text-center text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ day }}
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="grid grid-cols-7 gap-0 overflow-hidden rounded-lg border border-gray-200 dark:border-dark-border">
            <div v-for="day in calendarDays" :key="`${day.dateString}`"
                class="min-h-[80px] sm:min-h-[120px] border-r border-b border-gray-200 dark:border-dark-border last:border-r-0 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                :class="{
                    'bg-gray-50 dark:bg-gray-800/30': !day.isCurrentMonth,
                    'bg-blue-50 dark:bg-blue-900/20': day.isToday,
                }" @click="handleDateClick(day)">
                <!-- Date Number -->
                <div class="p-1 sm:p-2">
                    <div class="text-xs sm:text-sm font-medium w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center rounded-full"
                        :class="{
                            'text-gray-400 dark:text-gray-600': !day.isCurrentMonth,
                            'text-gray-900 dark:text-white': day.isCurrentMonth && !day.isToday,
                            'bg-primary text-white': day.isToday,
                        }">
                        {{ day.date.getDate() }}
                    </div>
                </div>

                <!-- Events -->
                <div class="px-1 sm:px-2 pb-1 sm:pb-2 space-y-1">
                    <div v-for="(event, index) in day.events.slice(0, 2)" :key="event.id"
                        class="text-xs px-1 sm:px-2 py-1 rounded-md cursor-pointer hover:opacity-80 transition-opacity truncate border-l-2"
                        :class="[getEventClasses(event), `border-l-${getCategoryConfig(event.category).color}`]"
                        @click.stop="handleEventClick(event)" :title="event.title">
                        <div class="flex items-center space-x-1">
                            <UiBaseIcon :name="getCategoryConfig(event.category).icon"
                                class="w-2 h-2 sm:w-3 sm:h-3 flex-shrink-0" />
                            <span class="truncate text-xs">{{ event.title }}</span>
                        </div>
                    </div>

                    <!-- More events indicator -->
                    <div v-if="day.events.length > 2"
                        class="text-xs text-gray-500 dark:text-gray-400 px-1 sm:px-2 py-1 hover:text-gray-700 dark:hover:text-gray-300 cursor-pointer"
                        @click.stop="showMoreEvents(day)">
                        +{{ day.events.length - 2 }} lagi
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- More Events Modal -->
    <UiCompositeModal :is-open="moreEventsModalOpen"
        :title="`Acara pada ${selectedDayForMore?.date.toLocaleDateString('ms-MY')}`"
        @update:is-open="moreEventsModalOpen = false">
        <div class="space-y-3">
            <div v-for="event in selectedDayForMore?.events || []" :key="event.id"
                class="p-3 rounded-lg border border-gray-200 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors"
                @click="handleEventClick(event)">
                <div class="flex items-start space-x-3">
                    <div class="w-3 h-3 rounded-full flex-shrink-0 mt-1"
                        :class="`bg-${getCategoryConfig(event.category).color}`"></div>
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-gray-900 dark:text-white truncate">
                            {{ event.title }}
                        </h4>
                        <p v-if="event.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {{ event.description }}
                        </p>
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <span class="flex items-center space-x-1">
                                <UiBaseIcon name="heroicons:clock-solid" class="w-3 h-3" />
                                <span>{{ formatEventTime(event) }}</span>
                            </span>
                            <span v-if="event.location" class="flex items-center space-x-1">
                                <UiBaseIcon name="heroicons:map-pin-solid" class="w-3 h-3" />
                                <span>{{ event.location }}</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </UiCompositeModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { CalendarEvent, CalendarDay } from '~/types/calendar'
import { getCategoryConfig, formatEventDate, isEventOnDate, MALAYSIAN_DAYS } from '~/types/calendar'
import { formatDateToLocalString } from '~/utils/dateUtils'

// Props
interface Props {
    month: number
    year: number
    events: readonly CalendarEvent[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'event-click': [event: CalendarEvent]
    'date-click': [date: string]
}>()

// Reactive state
const moreEventsModalOpen = ref(false)
const selectedDayForMore = ref<CalendarDay | null>(null)



// Computed
const dayHeaders = computed(() => {
    return MALAYSIAN_DAYS.map(day => day.substring(0, 3)) // Shortened day names
})

const calendarDays = computed(() => {
    const days: CalendarDay[] = []
    const firstDay = new Date(props.year, props.month, 1)
    const lastDay = new Date(props.year, props.month + 1, 0)

    // Get the first day of the week (Sunday = 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
        const currentDate = new Date(startDate)
        currentDate.setDate(startDate.getDate() + i)

        const dateString = formatDateToLocalString(currentDate)
        const isCurrentMonth = currentDate.getMonth() === props.month
        const isToday = dateString === formatDateToLocalString(new Date())

        // Filter events for this date
        const dayEvents = props.events.filter(event => isEventOnDate(event, dateString))

        days.push({
            date: currentDate,
            dateString,
            isCurrentMonth,
            isToday,
            events: dayEvents
        })
    }

    return days
})

// Methods
const handleDateClick = (day: CalendarDay) => {
    emit('date-click', day.dateString)
}

const handleEventClick = (event: CalendarEvent) => {
    emit('event-click', event)
    moreEventsModalOpen.value = false
}

const showMoreEvents = (day: CalendarDay) => {
    selectedDayForMore.value = day
    moreEventsModalOpen.value = true
}

const getEventClasses = (event: CalendarEvent) => {
    const config = getCategoryConfig(event.category)
    return `${config.bgColor} ${config.textColor}`
}

const formatEventTime = (event: CalendarEvent) => {
    // Since we removed time fields, just return empty string
    return ''
}
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
