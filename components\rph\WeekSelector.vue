<template>
  <UiCompositeCard>
    <template #header>
      <WeekSelectorHeader @open-add-week-modal="modals.openAddWeekModal" @open-manage-modal="modals.openManageModal" />
    </template>

    <div class="space-y-4">
      <!-- Week Selection Section -->
      <WeekSelectionSection :weeks="weeks" :selected-week-id="weekSelection.selectedWeekId.value"
        :selected-week="weekSelection.internalSelectedWeek.value" :loading="rphWeeksLoading"
        @update:selected-week-id="weekSelection.selectedWeekId.value = $event"
        @open-add-week-modal="modals.openAddWeekModal" @open-manage-modal="modals.openManageModal" />

      <!-- Loading State -->
      <div v-if="rphWeeksLoading" class="flex items-center justify-center py-8">
        <div class="flex items-center space-x-3">
          <svg class="animate-spin h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          <span class="text-sm text-gray-600 dark:text-gray-400">Memuatkan minggu...</span>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="rphWeeksError"
        class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-center space-x-2">
          <UiBaseIcon name="heroicons:exclamation-triangle-solid" class="w-5 h-5 text-red-600" />
          <div>
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Ralat Memuatkan Data</h3>
            <p class="text-sm text-red-600 dark:text-red-400 mt-1">{{ rphWeeksError.message }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <AddWeekModal :is-open="modals.isAddWeekModalOpen.value" :initial-week-number="nextWeekNumber"
      @close="modals.closeAddWeekModal" @week-added="handleWeekAdded" />
    <UiCompositeDeleteConfirmationModal :is-open="modals.isDeleteWeekModalOpen.value" item-type="Minggu"
      :item-name="weekToDelete?.name || ''" danger-level="high" impact-severity="high"
      impact-message="<strong>Semua item berikut akan dipadam secara kekal:</strong><br>• Semua rancangan pengajaran untuk minggu ini<br>• Semua refleksi tradisional yang berkaitan<br>• Semua refleksi terperinci yang berkaitan<br>• Semua fail yang dimuat naik"
      :z-index="70" @cancel="modals.closeDeleteWeekModal" @confirm="confirmDeleteWeek" />
    <WeekManagementModal :is-open="modals.isManageModalOpen.value" :sorted-weeks="weekSelection.sortedWeeks.value"
      :selected-week-id="weekSelection.selectedWeekId.value"
      :selected-weeks-for-deletion="weekSelection.selectedWeeksForDeletion.value"
      :search-query="modals.searchQuery.value" :z-index="50" @close="modals.closeManageModal"
      @open-add-week-modal="modals.openAddWeekModal" @open-bulk-delete-modal="modals.openBulkDeleteModal"
      @open-delete-week-modal="openDeleteWeekModal" @update:search-query="modals.searchQuery.value = $event"
      @update:selected-weeks-for-deletion="weekSelection.selectedWeeksForDeletion.value = $event" />
    <UiCompositeDeleteConfirmationModal :is-open="modals.isBulkDeleteModalOpen.value" title="Pengesahan Padam Pukal"
      item-type="minggu" :item-count="bulkDelete.selectedWeekCount.value"
      :item-list="bulkDelete.selectedWeekNames.value" :loading="bulkDelete.isDeletingWeeks.value"
      loading-text="Memadam minggu..." danger-level="high" impact-severity="high"
      impact-message="<strong>Semua item berikut akan dipadam secara kekal:</strong><br>• Semua rancangan pengajaran untuk setiap minggu<br>• Semua refleksi yang berkaitan<br>• Semua fail yang dimuat naik"
      :error-message="bulkDelete.bulkDeleteError.value || ''" :z-index="70" @cancel="handleBulkDeleteModalClose"
      @confirm="confirmBulkDelete" />
  </UiCompositeCard>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRphWeeks } from '~/composables/useRphWeeks'
import { useWeekSelection } from '~/composables/useWeekSelection'
import { useWeekModals } from '~/composables/useWeekModals'
import { useBulkDelete } from '~/composables/useBulkDelete'
import type { RphWeek, RphWeekUserInput } from '~/types/rph'

// Child components
import WeekSelectorHeader from './WeekSelectorHeader.vue'
import WeekSelectionSection from './WeekSelectionSection.vue'
import WeekManagementModal from './WeekManagementModal.vue'
import AddWeekModal from './AddWeekModal.vue'
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue'
import UiCompositeCard from '~/components/ui/composite/Card.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'

const emit = defineEmits<{
  (e: 'weekSelected', week: RphWeek | null): void
  (e: 'week-deleted', weekId: string): void
}>()

// Core week functionality
const {
  weeks,
  loading: rphWeeksLoading,
  error: rphWeeksError,
  fetchWeeks,
  addWeek,
  deleteWeek,
  nextWeekNumber,
} = useRphWeeks()

// Week selection logic
const weekSelection = useWeekSelection(weeks)

// Modal management
const modals = useWeekModals()

// Bulk delete functionality
const bulkDelete = useBulkDelete(weekSelection.selectedWeeksForDeletion, weeks)

// Single week deletion
const weekToDelete = ref<RphWeek | null>(null)

// Initialize weeks on mount
onMounted(async () => {
  await fetchWeeks()
  if (weekSelection.sortedWeeks.value.length > 0) {
    weekSelection.autoSelectWeek()
  }
})

// Watch for week selection changes and emit to parent
watch(weekSelection.internalSelectedWeek, (newWeek) => {
  emit('weekSelected', newWeek)
})

// Modal handlers
async function handleWeekAdded(newWeekData: Pick<RphWeekUserInput, 'week_number' | 'name'>) {
  const addedWeek = await addWeek(newWeekData)
  if (addedWeek) {
    await fetchWeeks()
    weekSelection.autoSelectWeek(addedWeek.id)
  } else {
    console.error("Failed to add week from WeekSelector")
  }
}

function openDeleteWeekModal(week: RphWeek) {
  weekToDelete.value = week
  modals.openDeleteWeekModal()
}

async function confirmDeleteWeek() {
  if (weekToDelete.value) {
    const weekIdToDelete = weekToDelete.value.id
    const success = await deleteWeek(weekIdToDelete)
    if (success) {
      emit('week-deleted', weekIdToDelete)
      modals.closeDeleteWeekModal()
      weekToDelete.value = null

      // If the deleted week was the currently selected week, clear selection
      if (weekSelection.selectedWeekId.value === weekIdToDelete) {
        weekSelection.selectedWeekId.value = null
      }

      // Refresh weeks after deletion
      await fetchWeeks()

      // Auto-select the first available week if none is selected
      weekSelection.autoSelectWeek()
    } else {
      console.error("Failed to delete week from WeekSelector")
    }
  }
}

function handleBulkDeleteModalClose() {
  if (!bulkDelete.isDeletingWeeks.value) {
    modals.closeBulkDeleteModal()
  }
}

async function confirmBulkDelete() {
  bulkDelete.resetError()

  const result = await bulkDelete.executeBulkDelete()

  if (result.success) {
    // Emit events for deleted weeks
    const deletedWeekIds = Array.from(weekSelection.selectedWeeksForDeletion.value)
    deletedWeekIds.forEach(weekId => emit('week-deleted', weekId))

    // Close modals
    modals.closeBulkDeleteModal()
    modals.closeManageModal()

    // If the currently selected week was deleted, clear selection
    if (weekSelection.selectedWeekId.value && deletedWeekIds.includes(weekSelection.selectedWeekId.value)) {
      weekSelection.selectedWeekId.value = null
    }

    // Refresh weeks after bulk deletion
    await fetchWeeks()

    // Auto-select the first available week if none is selected
    weekSelection.autoSelectWeek()
  }
}

// Expose for parent component interaction
defineExpose({
  weeks: weekSelection.weekOptions,
  refreshWeeks: fetchWeeks,
  selectWeekById: weekSelection.selectWeekById
})
</script>

<style scoped>
/* Add any specific styles for WeekSelector here */
</style>
