@echo off
REM RPHMate SaaS Development Start Script

echo 🚀 Starting RPHMate SaaS Development Environment
echo ==============================================

REM Check if .env.local exists
if not exist .env.local (
    echo ❌ .env.local file not found!
    echo Please run: node scripts/setup-dev-environment.js
    exit /b 1
)

REM Start the development server
echo 📡 Starting Nuxt development server...
start cmd /k npm run dev

timeout /t 3 /nobreak > nul

echo.
echo ✅ Development environment ready!
echo.
echo 🌐 Available URLs:
echo    Main Domain: http://localhost:3000
echo    Test School: http://xba1224.localhost:3000
echo.
echo 📋 Next Steps:
echo    1. Open http://localhost:3000 in your browser
echo    2. Test the complete registration flow
echo    3. Use Stripe test cards for payment testing
echo.
echo 🔧 For webhook testing, run in another terminal:
echo    stripe listen --forward-to localhost:3000/api/webhooks/stripe
echo.
pause
