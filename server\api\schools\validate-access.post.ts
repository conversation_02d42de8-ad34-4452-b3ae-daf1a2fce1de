// School access validation API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')

    // Get request body
    const body = await readBody(event)
    const { schoolCode } = body

    if (!schoolCode) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School code is required'
      })
    }

    // Initialize Supabase client with the user's token
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Get school by code
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .select('*')
      .eq('code', schoolCode.toLowerCase())
      .single()

    if (schoolError || !school) {
      return {
        hasAccess: false,
        error: 'School not found',
        school: null,
        membership: null
      }
    }

    // Check if user is the school admin
    if (school.admin_user_id === user.id) {
      return {
        hasAccess: true,
        school,
        membership: {
          role: 'admin',
          status: 'active',
          permissions: {}
        },
        isAdmin: true
      }
    }

    // Check if user has active membership in this school
    const { data: membership, error: membershipError } = await supabase
      .from('school_memberships')
      .select('*')
      .eq('user_id', user.id)
      .eq('school_id', school.id)
      .eq('status', 'active')
      .single()

    if (membershipError || !membership) {
      return {
        hasAccess: false,
        error: 'No access to this school',
        school,
        membership: null
      }
    }

    // User has access
    return {
      hasAccess: true,
      school,
      membership: {
        id: membership.id,
        role: membership.role,
        status: membership.status,
        joined_at: membership.joined_at,
        permissions: membership.permissions
      },
      isAdmin: membership.role === 'admin'
    }

  } catch (error: any) {
    console.error('School access validation error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during access validation'
    })
  }
})
