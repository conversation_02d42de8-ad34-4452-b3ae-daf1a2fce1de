<template>
  <div>
    <!-- Error State -->
    <div v-if="hasError" class="min-h-[400px] flex items-center justify-center p-8">
      <div class="max-w-md w-full text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
          <Icon name="heroicons:exclamation-triangle" class="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>
        
        <!-- Error Title -->
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          {{ errorTitle }}
        </h2>
        
        <!-- Error Message -->
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {{ errorMessage }}
        </p>
        
        <!-- Error Details (Expandable) -->
        <div v-if="showTechnicalDetails && error" class="mb-6">
          <button
            @click="showDetails = !showDetails"
            class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            type="button"
          >
            <Icon :name="showDetails ? 'heroicons:chevron-up' : 'heroicons:chevron-down'" class="h-4 w-4 mr-1" />
            {{ showDetails ? 'Sembunyikan' : 'Tunjukkan' }} butiran teknikal
          </button>
          
          <div v-if="showDetails" class="mt-3 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left">
            <div class="text-xs font-mono text-gray-700 dark:text-gray-300 overflow-x-auto">
              <div class="mb-2">
                <span class="font-semibold">Mesej:</span> {{ error.message }}
              </div>
              <div v-if="error.stack" class="mb-2">
                <span class="font-semibold">Stack Trace:</span>
                <pre class="mt-1 whitespace-pre-wrap">{{ error.stack }}</pre>
              </div>
              <div class="mb-2">
                <span class="font-semibold">Komponen:</span> {{ componentName }}
              </div>
              <div>
                <span class="font-semibold">Masa:</span> {{ formatTime(errorTime) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            @click="retry"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            type="button"
          >
            <Icon name="heroicons:arrow-path" class="h-4 w-4 mr-2" />
            Cuba Lagi
          </button>
          
          <button
            @click="goHome"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors"
            type="button"
          >
            <Icon name="heroicons:home" class="h-4 w-4 mr-2" />
            Kembali ke Laman Utama
          </button>
        </div>
        
        <!-- Report Error -->
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">
            Jika masalah ini berterusan, sila laporkan kepada pentadbir sistem.
          </p>
          <button
            @click="reportError"
            class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
            type="button"
          >
            Laporkan Ralat Ini
          </button>
        </div>
      </div>
    </div>
    
    <!-- Normal Content -->
    <div v-else>
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, computed } from 'vue'
import { useRouter } from 'vue-router'
import Icon from '~/components/ui/base/Icon.vue'
import { useErrorHandler } from '~/composables/useErrorHandler'

interface Props {
  componentName?: string
  fallbackTitle?: string
  fallbackMessage?: string
  showTechnicalDetails?: boolean
  onRetry?: () => void
  onReport?: (error: Error) => void
}

const props = withDefaults(defineProps<Props>(), {
  componentName: 'Unknown Component',
  fallbackTitle: 'Ralat Tidak Dijangka',
  fallbackMessage: 'Maaf, berlaku ralat yang tidak dijangka. Sila cuba lagi atau hubungi sokongan teknikal.',
  showTechnicalDetails: false
})

const router = useRouter()
const { handleError } = useErrorHandler()

// State
const hasError = ref(false)
const error = ref<Error | null>(null)
const errorTime = ref<Date>(new Date())
const showDetails = ref(false)

// Computed
const errorTitle = computed(() => {
  if (error.value?.name === 'ChunkLoadError') {
    return 'Ralat Memuatkan Aplikasi'
  }
  if (error.value?.name === 'TypeError') {
    return 'Ralat Jenis Data'
  }
  if (error.value?.name === 'ReferenceError') {
    return 'Ralat Rujukan'
  }
  return props.fallbackTitle
})

const errorMessage = computed(() => {
  if (error.value?.name === 'ChunkLoadError') {
    return 'Gagal memuatkan sebahagian aplikasi. Ini mungkin disebabkan oleh sambungan internet yang tidak stabil atau kemas kini aplikasi.'
  }
  if (error.value?.name === 'TypeError') {
    return 'Berlaku ralat dalam pemprosesan data. Sila cuba lagi atau muat semula halaman.'
  }
  if (error.value?.name === 'ReferenceError') {
    return 'Berlaku ralat dalam kod aplikasi. Sila laporkan masalah ini kepada pentadbir sistem.'
  }
  return props.fallbackMessage
})

// Error capture
onErrorCaptured((err: Error, instance, info) => {
  console.error('Error captured by ErrorBoundary:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  hasError.value = true
  error.value = err
  errorTime.value = new Date()
  
  // Handle error with enhanced error handler
  handleError(err, {
    operation: 'render component',
    component: props.componentName,
    data: { info },
    timestamp: new Date()
  })
  
  // Prevent the error from propagating further
  return false
})

// Methods
const retry = () => {
  if (props.onRetry) {
    props.onRetry()
  } else {
    // Default retry: reset error state and reload
    hasError.value = false
    error.value = null
    
    // Force component re-render
    nextTick(() => {
      window.location.reload()
    })
  }
}

const goHome = () => {
  router.push('/')
}

const reportError = () => {
  if (props.onReport && error.value) {
    props.onReport(error.value)
  } else {
    // Default error reporting
    const errorReport = {
      message: error.value?.message,
      stack: error.value?.stack,
      component: props.componentName,
      timestamp: errorTime.value.toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    // Copy error report to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2)).then(() => {
      alert('Laporan ralat telah disalin ke papan keratan. Sila hantar kepada pentadbir sistem.')
    }).catch(() => {
      // Fallback: show error report in alert
      alert(`Laporan Ralat:\n\n${JSON.stringify(errorReport, null, 2)}`)
    })
  }
}

const formatTime = (date: Date): string => {
  return new Intl.DateTimeFormat('ms-MY', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

// Expose methods for parent components
defineExpose({
  retry,
  hasError: computed(() => hasError.value),
  error: computed(() => error.value)
})
</script>
