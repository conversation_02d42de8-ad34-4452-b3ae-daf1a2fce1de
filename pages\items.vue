<template>
  <div class="container mx-auto py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Items</h1>
      <UiBaseButton @click="addNewItem" variant="primary">Add New Item</UiBaseButton>
    </div>
    <div v-if="pending" class="text-center">Loading items...</div>
    <div v-else-if="error" class="text-center text-red-500">Error loading items: {{ error.message }}</div>
    <div v-else-if="items && items.length" class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
      <UiCompositeCard v-for="item in items" :key="item.id">
        <template #header>
          <h2 class="text-xl font-semibold">{{ item.title }}</h2>
        </template>
        <p>{{ item.description }}</p>
        <template #footer>
          <NuxtLink :to="`/item/${item.id}`">
            <UiBaseButton variant="secondary" size="sm">View Details</UiBaseButton>
          </NuxtLink>
        </template>
      </UiCompositeCard>
    </div>
    <div v-else class="text-center">No items found.</div>
  </div>
</template>

<script setup lang="ts">
import { useDataStore } from '~/stores/data';
import { useSupabase } from '~/composables/useSupabase';
import { ref, onMounted, onUnmounted } from 'vue';
import type { RealtimeChannel } from '@supabase/supabase-js';

// Define page meta to apply auth middleware
definePageMeta({});

interface Item {
  id: number;
  title: string;
  description: string;
  // Add other fields from your Supabase table if necessary
}

const { client } = useSupabase();
const dataStore = useDataStore();

const items = ref<Item[]>([]);
const pending = ref(true);
const error = ref<any>(null);

let channel: RealtimeChannel | null = null;

async function fetchItems() {
  pending.value = true;
  error.value = null;
  try {
    const { data, error: fetchError } = await client
      .from('items')
      .select('id, title, description') // Explicitly select columns
      .order('id');

    if (fetchError) throw fetchError;

    // Ensure data is correctly typed as Item[]
    // If 'id' can be string from DB and needs to be number in Item interface
    if (data) {
      items.value = data.map(d => ({ ...d, id: Number(d.id) })) as Item[];
      dataStore.items = data.map(d => ({ ...d, id: Number(d.id) })) as Item[];
    } else {
      items.value = [];
      dataStore.items = [];
    }

  } catch (e) {
    error.value = e;
    items.value = [];
    dataStore.items = []; // Clear store on error too
  } finally {
    pending.value = false;
  }
}

const addNewItem = async () => {
  const newItemScaffold = {
    title: `New Supabase Item ${Date.now()}`,
    description: 'Added directly via Supabase client'
    // id will be auto-generated by Supabase
  };
  try {
    // When inserting, Supabase typically expects column names that exist in the table.
    // If your 'items' table auto-generates 'id', you don't need to provide it.
    // If 'title' and 'description' are the only other columns (or others have defaults/are nullable), this is fine.
    const { data: insertedData, error: insertError } = await client
      .from('items')
      .insert([newItemScaffold])
      .select('id, title, description') // Select the inserted row to get its full data including id
      .single<Item>(); // Expect a single Item back

    if (insertError) throw insertError;

    // The real-time subscription should ideally handle adding to the local 'items' ref and Pinia store.
    // However, if you want immediate feedback or if the subscription has a delay,
    // you might add the 'insertedData' to the local state here.
    // For now, relying on the subscription as per original logic.
    // if (insertedData) {
    // dataStore.addItem(insertedData); // Pinia store might be updated by subscription
    // items.value.push(insertedData); // Local ref might be updated by subscription
    // }

  } catch (e) {
    console.error("Error adding new item:", e);
  }
};

onMounted(() => {
  fetchItems();

  channel = client
    .channel('items-realtime-channel')
    .on(
      'postgres_changes',
      { event: '*', schema: 'public', table: 'items' },
      (payload) => {
        console.log('Supabase change received:', payload);
        if (payload.eventType === 'INSERT') {
          // Ensure the new payload is correctly typed as Item
          const newItem = payload.new as Omit<Item, 'id'> & { id: string | number };
          const correctlyTypedNewItem: Item = {
            ...newItem,
            id: Number(newItem.id)
          };
          dataStore.addItem(correctlyTypedNewItem);
          // To keep local 'items' ref in sync if not already handled by reactivity from Pinia
          // or if you want to manage it independently:
          const existingItemIndex = items.value.findIndex(i => i.id === correctlyTypedNewItem.id);
          if (existingItemIndex === -1) {
            items.value.push(correctlyTypedNewItem);
          } else {
            items.value[existingItemIndex] = correctlyTypedNewItem; // Or update if it was a placeholder
          }

        } else if (payload.eventType === 'UPDATE') {
          const updatedItem = payload.new as Omit<Item, 'id'> & { id: string | number };
          const correctlyTypedUpdatedItem: Item = {
            ...updatedItem,
            id: Number(updatedItem.id)
          };
          const index = items.value.findIndex(i => i.id === correctlyTypedUpdatedItem.id);
          if (index !== -1) {
            items.value[index] = correctlyTypedUpdatedItem;
          }
          const storeIndex = dataStore.items.findIndex(i => i.id === correctlyTypedUpdatedItem.id);
          if (storeIndex !== -1) {
            dataStore.items[storeIndex] = correctlyTypedUpdatedItem;
          }
        } else if (payload.eventType === 'DELETE') {
          const deletedItem = payload.old as Partial<Item>; // OLD record might only have id
          if (deletedItem.id) {
            const numericId = Number(deletedItem.id);
            items.value = items.value.filter(i => i.id !== numericId);
            dataStore.items = dataStore.items.filter(i => i.id !== numericId);
          }
        }
      }
    )
    .subscribe();
});

onUnmounted(() => {
  if (channel) {
    client.removeChannel(channel);
    channel = null;
    console.log('Unsubscribed from items channel');
  }
});

// To make the template directly reactive to Pinia store changes for the list:
// import { storeToRefs } from 'pinia';
// const { items: storeItems } = storeToRefs(dataStore);
// And use `storeItems` in the template instead of the local `items` ref.
// This simplifies state management as updates from subscriptions directly modify the store,
// and the component's view updates automatically.
// For this iteration, `items.value = [...dataStore.items];` in the subscription handler
// manually syncs the local ref with the store.

</script>

<style scoped>
/* Add any page-specific styles here if needed */
</style>
