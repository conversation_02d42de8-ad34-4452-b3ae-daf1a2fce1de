import { mount } from "@vue/test-utils";
import { describe, it, expect, vi } from "vitest";
import UiPage from "../ui.vue";

// Stubs with v-model and custom v-model support, matching local import names in ui.vue
const Button = {
  props: ["variant", "size"], // Added props used in ui.vue
  template: "<div><slot /></div>",
};
const Card = {
  props: ["variant"], // Added prop used in ui.vue
  template: '<div><slot name="header"/><slot /><slot name="footer"/></div>',
};

const Input = {
  props: ["modelValue", "type", "placeholder", "autocomplete"],
  emits: ["update:modelValue"],
  template: "<input />",
};

const Modal = {
  props: ["isOpen", "title"],
  emits: ["update:isOpen"],
  template: "<div><slot /></div>",
};

const Alert = {
  props: [
    "modelValue",
    "type",
    "message",
    "dismissible",
    "showIcon",
    "duration",
  ],
  emits: ["update:modelValue"],
  template: "<div><slot /></div>",
};

const SingleSelect = {
  props: [
    "modelValue",
    "options",
    "placeholder",
    "optionLabel",
    "optionValue",
    "disabled",
    "allowClear",
    "showSearch",
    "searchPlaceholder",
    "noResultsText",
    "ariaLabel", // Added from ui.vue usage
  ],
  emits: ["update:modelValue"],
  template: "<div></div>",
};

const MultiSelect = {
  props: ["modelValue", "options", "placeholder", "disabled", "ariaLabel"], // Added from ui.vue usage
  emits: ["update:modelValue"],
  template: "<div></div>",
};

const Tooltip = {
  props: ["text", "position"], // Added props used in ui.vue
  template: "<div><slot /></div>",
};
const Icon = {
  props: ["name", "class"], // Added class prop
  template: "<div></div>",
};

// Stubs for Nuxt specific components
const NuxtLink = { template: "<a><slot /></a>" };
const NuxtPage = { template: "<div></div>" };
const NuxtLayout = { template: "<div><slot /></div>" };

describe("UI Showcase Page (ui.vue)", () => {
  let wrapper: any;

  beforeEach(() => {
    vi.stubGlobal("definePageMeta", vi.fn());
    wrapper = mount(UiPage, {
      global: {
        stubs: {
          // Use the renamed stubs
          Button,
          Card,
          Input,
          Modal,
          Alert,
          SingleSelect,
          MultiSelect,
          Tooltip,
          Icon,
          // Nuxt specific stubs
          NuxtLink,
          NuxtPage,
          NuxtLayout,
        },
      },
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it("renders the main page title", () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.html()).toContain("UI Component Showcase");
  });

  it("renders section titles for major component showcases", () => {
    expect(wrapper.exists()).toBe(true);
    const html = wrapper.html();
    expect(html).toContain("Buttons");
    expect(html).toContain("Inputs");
    expect(html).toContain("Card Variants");
    expect(html).toContain("Modal");
    expect(html).toContain("Alerts");
    expect(html).toContain("Selects");
    expect(html).toContain("MultiSelect");
    expect(html).toContain("Tooltips");
  });

  it("renders Button components in the Button showcase section", () => {
    expect(wrapper.exists()).toBe(true);
    // Since Button stub is '<div><slot /></div>', we check if its instances are rendered.
    // A more specific test would require a more detailed stub or finding by test-id.
    const buttonSection = wrapper.findAllComponents(Button);
    expect(buttonSection.length).toBeGreaterThan(0);
  });

  it("renders Input components in the Input showcase section", () => {
    expect(wrapper.exists()).toBe(true);
    // Similar to Button, checking for Input stub instances.
    const inputSection = wrapper.findAllComponents(Input);
    expect(inputSection.length).toBeGreaterThan(0);
  });

  describe("Modal Interaction", () => {
    it("opens and closes the modal when respective buttons are clicked", async () => {
      expect(wrapper.exists()).toBe(true);

      // Find the "Open Modal" button. We need a way to identify it.
      // Let's assume the button component renders its slot content.
      // We'll find the button with the text "Open Modal".
      const openModalButton = wrapper
        .findAllComponents(Button)
        .find((b: any) => b.text().includes("Open Modal"));

      expect(openModalButton).toBeTruthy();
      if (!openModalButton) return; // Guard for type safety

      // Modal should initially be closed. The Modal stub doesn't directly expose visibility,
      // so we'll rely on the component's internal state if possible, or assume it's not rendered/hidden.
      // For this test, we'll check if the Modal component with the title "Sample Modal" exists but might not be "visible"
      // The stub for Modal is just a div, so we can't check for visibility easily without more complex stubs.
      // We will check for its presence after the button click.

      await openModalButton.trigger("click");

      // After clicking, the Modal component instance should be findable and its 'isOpen' prop should be true.
      // However, our stub doesn't reflect props in a way we can easily check.
      // We'll check if a Modal component with the title "Sample Modal" is now "active"
      // This requires the Modal stub to render its title prop if we want to find it by title.
      // Let's update the Modal stub to render its title.
      // For now, we assume the click triggers the state change in the parent (UiPage).
      // The parent UiPage component's `isModalOpen` ref should become true.
      // We can't directly test `isModalOpen` in UiPage from here without emitting an event from the stub
      // or having a more complex stub.

      // Let's assume the modal is rendered when isModalOpen is true.
      // We'll look for the modal content.
      // The Modal stub is `<div><slot /></div>`. The slot contains a paragraph and a close button.
      let modalComponent = wrapper.findComponent(Modal);
      expect(modalComponent.exists()).toBe(true);
      // Check if the modal title is rendered (assuming the stub passes it through or the real component does)
      // Since our Modal stub is simple, we'll check for content passed to its slot.
      expect(modalComponent.text()).toContain(
        "This is the content of the modal."
      );
      expect(modalComponent.props("isOpen")).toBe(true); // This relies on the stub passing props correctly

      // Find the "Close" button within the modal content.
      // The close button is a <Button variant="primary">Close</Button>
      const closeButton = modalComponent
        .findAllComponents(Button)
        .find((b: any) => b.text().includes("Close"));

      expect(closeButton).toBeTruthy();
      if (!closeButton) return;

      await closeButton.trigger("click");

      // After clicking close, the modal should not be "visible" or `isOpen` should be false.
      // We'll check the prop on the Modal component again.
      // Vue Test Utils might re-render, so we might need to re-find the component or check its updated state.
      // Re-finding the modal component to get its updated props
      modalComponent = wrapper.findComponent(Modal);
      expect(modalComponent.props("isOpen")).toBe(false);
    });

    it("modal displays the correct title", async () => {
      const openModalButton = wrapper
        .findAllComponents(Button)
        .find((b: any) => b.text().includes("Open Modal"));
      if (!openModalButton) throw new Error("Open Modal button not found");

      await openModalButton.trigger("click");

      const modalComponent = wrapper.findComponent(Modal);
      expect(modalComponent.exists()).toBe(true);
      expect(modalComponent.props("title")).toBe("Sample Modal");
    });
  });
});
