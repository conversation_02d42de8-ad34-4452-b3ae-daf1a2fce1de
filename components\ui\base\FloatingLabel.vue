<template>
    <label :for="forInput"
        class="absolute text-sm duration-300 transform origin-[0] start-2.5 pointer-events-none z-10 rounded-md" :class="{
            'scale-90 -translate-y-6 top-4 px-1 text-white bg-primary dark:bg-gray-800': isFloated, // Adjusted Y translation to move label higher
            'scale-100 translate-y-0 top-1/2 -mt-2.5': !isFloated, // Centered when not floated
            'text-primary': isFocused,
            'text-gray-500 dark:text-gray-400': !isFocused,
        }">
        {{ label }}
    </label>
</template>

<script setup lang="ts">
// defineProps<{
//     forInput: string;
//     label?: string;
//     isFloated: boolean;
//     isFocused: boolean;
// }>();

withDefaults(defineProps<{
    forInput: string;
    label?: string;
    isFloated: boolean;
    isFocused: boolean;
}>(), {
    label: '',
});
</script>

<style scoped>
/* Ensure the label is positioned correctly relative to the input/select field.
   The parent component (Input.vue or Select.vue) should have `position: relative`.
   The input/select itself will need appropriate padding-top to make space for the label.

   The top-1/2 -mt-2.5 classes for non-floated state aim to vertically center a typical text line.
   Adjust if your text size or line height differs significantly.
   The `top-4` for floated state is based on the previous implementation.
*/
</style>
