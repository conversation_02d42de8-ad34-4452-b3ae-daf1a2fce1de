-- Migration: Drop and recreate teacher_schedules table with new structure
-- Date: 2024-12-28
-- Description: Fresh start with optimized single-row per lesson plan structure

BEGIN;

-- Step 1: Create backup of existing data (if any exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'teacher_schedules') THEN
        EXECUTE 'CREATE TABLE teacher_schedules_backup_' || to_char(NOW(), 'YYYYMMDD_HH24MISS') || ' AS SELECT * FROM teacher_schedules';
        RAISE NOTICE 'Backup created: teacher_schedules_backup_%', to_char(NOW(), 'YYYYMMDD_HH24MISS');
    END IF;
END $$;

-- Step 2: Drop the existing teacher_schedules table completely
DROP TABLE IF EXISTS teacher_schedules CASCADE;

-- Step 3: Create the new teacher_schedules table with optimized structure
CREATE TABLE teacher_schedules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    user_id UUID NOT NULL,
    lesson_plan_id UUID,
    schedule_details JSONB NOT NULL DEFAULT '{}'::jsonb,

    -- Constraints
    CONSTRAINT teacher_schedules_user_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
    CONSTRAINT teacher_schedules_lesson_plan_fkey
        FOREIGN KEY (lesson_plan_id) REFERENCES lesson_plans(id) ON DELETE CASCADE,
    CONSTRAINT unique_lesson_plan_schedule
        UNIQUE (user_id, lesson_plan_id)
);

-- Step 4: Create optimized indexes for performance
CREATE INDEX idx_teacher_schedules_user_id ON teacher_schedules (user_id, created_at DESC);
CREATE INDEX idx_teacher_schedules_lesson_plan_id ON teacher_schedules (lesson_plan_id);
CREATE INDEX idx_teacher_schedules_user_lesson_plan ON teacher_schedules (user_id, lesson_plan_id);

-- JSONB indexes for schedule_details queries
CREATE INDEX idx_teacher_schedules_class_subjects ON teacher_schedules USING GIN ((schedule_details->'class_subjects'));
CREATE INDEX idx_teacher_schedules_class_id ON teacher_schedules USING GIN ((schedule_details->'class_subjects'->0->'class_id'));

-- Step 5: Add updated_at trigger for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_teacher_schedules_updated_at
    BEFORE UPDATE ON teacher_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 6: Create a function to validate schedule_details structure
CREATE OR REPLACE FUNCTION validate_schedule_details(details JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if details has class_subjects array
    IF NOT (details ? 'class_subjects') THEN
        RETURN FALSE;
    END IF;

    -- Check if class_subjects is an array
    IF jsonb_typeof(details->'class_subjects') != 'array' THEN
        RETURN FALSE;
    END IF;

    -- Validate each class_subject has required fields
    IF NOT (
        SELECT bool_and(
            cs ? 'class_id' AND
            cs ? 'subject_id' AND
            cs ? 'days_scheduled' AND
            cs ? 'total_periods'
        )
        FROM jsonb_array_elements(details->'class_subjects') AS cs
    ) THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Add check constraint for schedule_details validation
ALTER TABLE teacher_schedules
ADD CONSTRAINT check_schedule_details_structure
CHECK (validate_schedule_details(schedule_details));

-- Step 8: Set up Row Level Security (RLS) policies
ALTER TABLE teacher_schedules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for secure access
CREATE POLICY "Users can view their own teacher schedules"
ON teacher_schedules FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own teacher schedules"
ON teacher_schedules FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own teacher schedules"
ON teacher_schedules FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own teacher schedules"
ON teacher_schedules FOR DELETE
USING (auth.uid() = user_id);

-- Step 9: Add table and column comments for documentation
COMMENT ON TABLE teacher_schedules IS 'Teacher schedules linked to lesson plans with aggregated class-subject data';
COMMENT ON COLUMN teacher_schedules.id IS 'Primary key UUID';
COMMENT ON COLUMN teacher_schedules.user_id IS 'Foreign key to auth.users table';
COMMENT ON COLUMN teacher_schedules.lesson_plan_id IS 'Foreign key to lesson_plans table (nullable for standalone schedules)';
COMMENT ON COLUMN teacher_schedules.schedule_details IS 'JSONB containing class_subjects array with aggregated schedule data';
COMMENT ON COLUMN teacher_schedules.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN teacher_schedules.updated_at IS 'Timestamp when record was last updated (auto-updated via trigger)';

COMMIT;

-- =====================================================
-- EXAMPLE OF THE NEW STRUCTURE
-- =====================================================
/*
schedule_details format:
{
  "class_subjects": [
    {
      "class_id": "f5",
      "class_name": "5B",
      "subject_id": "8fe00a97-5456-4db7-8d2b-fa6646c32ae3",
      "subject_name": "Pendidikan Jasmani dan Kesihatan",
      "subject_abbreviation": "PJK",
      "days_scheduled": ["ISNIN", "SELASA"],
      "total_periods": 2,
      "periods": [
        {
          "day": "ISNIN",
          "time_slot_start": "08:00",
          "time_slot_end": "08:30"
        },
        {
          "day": "SELASA",
          "time_slot_start": "09:00",
          "time_slot_end": "09:30"
        }
      ]
    }
  ]
}

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

-- Insert a new teacher schedule
INSERT INTO teacher_schedules (user_id, lesson_plan_id, schedule_details)
VALUES (
  'user-uuid-here',
  'lesson-plan-uuid-here',
  '{
    "class_subjects": [
      {
        "class_id": "f5",
        "class_name": "5B",
        "subject_id": "8fe00a97-5456-4db7-8d2b-fa6646c32ae3",
        "subject_name": "Pendidikan Jasmani dan Kesihatan",
        "subject_abbreviation": "PJK",
        "days_scheduled": ["ISNIN", "SELASA"],
        "total_periods": 2,
        "periods": [
          {"day": "ISNIN", "time_slot_start": "08:00", "time_slot_end": "08:30"},
          {"day": "SELASA", "time_slot_start": "09:00", "time_slot_end": "09:30"}
        ]
      }
    ]
  }'::jsonb
);

-- Query teacher schedules with class subjects
SELECT
  ts.id,
  ts.lesson_plan_id,
  cs.value->>'class_name' as class_name,
  cs.value->>'subject_name' as subject_name,
  cs.value->'days_scheduled' as days_scheduled,
  cs.value->>'total_periods' as total_periods
FROM teacher_schedules ts,
     jsonb_array_elements(ts.schedule_details->'class_subjects') as cs
WHERE ts.user_id = 'user-uuid-here';
*/
