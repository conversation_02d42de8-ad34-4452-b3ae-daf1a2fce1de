# Teacher Schedules Table Partitioning

This document explains the partitioning scheme implemented for the `teacher_schedules` table in the Supabase database. Partitioning is used to improve query performance, simplify data management, and enhance scalability for large datasets.

## Table Structure

After partitioning, the `teacher_schedules` table acts as a **parent table**. It does not store any data itself but defines the schema and serves as a single point of access for all its partitions. Data is physically stored in individual **child tables** (partitions).

### Parent Table: `teacher_schedules`

The `teacher_schedules` table is partitioned by `created_at` using a `RANGE` partitioning strategy.

```sql
CREATE TABLE teacher_schedules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  created_at timestamptz NOT NULL DEFAULT timezone('utc', now()),
  updated_at timestamptz NOT NULL DEFAULT timezone('utc', now()),
  user_id uuid NOT NULL,
  class_id uuid NOT NULL,
  subject_id uuid NOT NULL,
  days_scheduled text[] NOT NULL DEFAULT '{}'::text[],
  PRIMARY KEY (id, created_at),
  CONSTRAINT unique_class_subject_per_creation UNIQUE (user_id, class_id, subject_id, created_at)
) PARTITION BY RANGE (created_at);
```

**Important Note on Primary Key and Unique Constraints:**
For partitioned tables in PostgreSQL, any `PRIMARY KEY` or `UNIQUE` constraint must include all columns that are part of the partitioning key. In this case, `created_at` is part of the partitioning key, so it must be included in the `PRIMARY KEY` and `UNIQUE` constraints.

### Child Tables (Partitions)

Individual partitions are created for specific date ranges. Data is automatically routed to the correct partition based on the `created_at` column.

#### `teacher_schedules_2025`

This partition stores all `teacher_schedules` records where `created_at` falls within the year 2025.

```sql
CREATE TABLE teacher_schedules_2025
PARTITION OF teacher_schedules
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

#### `teacher_schedules_historical`

This partition stores all `teacher_schedules` records created before 2025. This is useful for migrating existing data and handling older records.

```sql
CREATE TABLE teacher_schedules_historical
PARTITION OF teacher_schedules
FOR VALUES FROM ('1970-01-01') TO ('2025-01-01');
```

## Benefits of Partitioning

*   **Improved Query Performance:** Queries that filter by `created_at` (e.g., `WHERE created_at BETWEEN '2025-01-01' AND '2025-12-31'`) will only scan the relevant partition (`teacher_schedules_2025`), significantly reducing the amount of data to process.
*   **Easier Data Management:**
    *   **Archiving/Deletion:** Old data can be easily archived or deleted by detaching and dropping old partitions without affecting the main table or other partitions.
    *   **Maintenance:** Maintenance tasks (e.g., `VACUUM`, `ANALYZE`) can be performed on individual partitions, reducing the impact on the entire table.
*   **Enhanced Scalability:** New partitions can be added as needed (e.g., for each new year), allowing the table to grow without performance degradation.

## How to Manage Partitions

### Adding a New Yearly Partition

To add a partition for a new year (e.g., 2026), execute the following SQL command:

```sql
CREATE TABLE teacher_schedules_2026
PARTITION OF teacher_schedules
FOR VALUES FROM ('2026-01-01') TO ('2027-01-01');
```

This should typically be done before the start of the new year to ensure new data is routed correctly.

### Detaching an Old Partition (Archiving)

To detach an old partition (e.g., `teacher_schedules_historical`) for archiving or separate storage:

```sql
ALTER TABLE teacher_schedules
DETACH PARTITION teacher_schedules_historical;
```

After detaching, `teacher_schedules_historical` becomes an independent table. You can then move it, back it up, or drop it if no longer needed.

### Querying Data

All queries should be directed to the parent `teacher_schedules` table. PostgreSQL's query planner will automatically route the query to the appropriate partitions.

```sql
-- Query all teacher schedules
SELECT * FROM teacher_schedules;

-- Query schedules for a specific year (e.g., 2025)
SELECT * FROM teacher_schedules
WHERE created_at BETWEEN '2025-01-01' AND '2025-12-31';
```

## Foreign Key Considerations

The `timetable_entries` table has a foreign key constraint referencing `teacher_schedules`. Due to the composite primary key on the partitioned table (`id`, `created_at`), the foreign key in `timetable_entries` must also reference both columns.

The `timetable_entries` table was modified to include a `teacher_schedule_created_at` column to support this composite foreign key.

```sql
ALTER TABLE timetable_entries
ADD CONSTRAINT timetable_entries_teacher_schedule_fkey
FOREIGN KEY (teacher_schedule_id, teacher_schedule_created_at)
REFERENCES teacher_schedules(id, created_at)
ON DELETE CASCADE;