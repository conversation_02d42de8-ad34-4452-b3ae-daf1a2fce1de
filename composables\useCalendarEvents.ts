import { ref, computed } from 'vue'
import type { CalendarEvent, CalendarEventFormData, CalendarFilters } from '~/types/calendar'
import { formatDateToLocalString } from '~/utils/dateUtils'

export const useCalendarEvents = () => {
  const supabase = useSupabaseClient()
  const user = useSupabaseUser()

  // Reactive state
  const events = ref<CalendarEvent[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const eventsCount = computed(() => events.value.length)
  
  const eventsByCategory = computed(() => {
    const grouped = events.value.reduce((acc, event) => {
      if (!acc[event.category]) {
        acc[event.category] = []
      }
      acc[event.category].push(event)
      return acc
    }, {} as Record<string, CalendarEvent[]>)
    
    return grouped
  })

  // Methods
  const fetchEvents = async (filters?: CalendarFilters) => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return
    }

    loading.value = true
    error.value = null

    try {
      let query = (supabase as any)
        .from('annual_calendar_events')
        .select('*')
        .eq('user_id', user.value.id)
        .order('start_date', { ascending: true })

      // Apply filters
      if (filters?.categories && filters.categories.length > 0) {
        query = query.in('category', filters.categories)
      }



      if (filters?.searchQuery) {
        query = query.or(`title.ilike.%${filters.searchQuery}%,description.ilike.%${filters.searchQuery}%,location.ilike.%${filters.searchQuery}%`)
      }

      const { data, error: fetchError } = await query

      if (fetchError) {
        throw fetchError
      }

      events.value = data || []
    } catch (err) {
      console.error('Error fetching events:', err)
      error.value = err instanceof Error ? err.message : 'Failed to fetch events'
    } finally {
      loading.value = false
    }
  }

  const fetchEventsByMonth = async (year: number, month: number) => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return
    }

    loading.value = true
    error.value = null

    try {
      // Get first and last day of the month
      const startDate = formatDateToLocalString(new Date(year, month, 1))
      const endDate = formatDateToLocalString(new Date(year, month + 1, 0))

      const { data, error: fetchError } = await (supabase as any)
        .from('annual_calendar_events')
        .select('*')
        .eq('user_id', user.value.id)
        .or(`and(start_date.gte.${startDate},start_date.lte.${endDate}),and(end_date.gte.${startDate},end_date.lte.${endDate}),and(start_date.lte.${startDate},end_date.gte.${endDate})`)
        .order('start_date', { ascending: true })

      if (fetchError) {
        throw fetchError
      }

      events.value = data || []
    } catch (err) {
      console.error('Error fetching events by month:', err)
      error.value = err instanceof Error ? err.message : 'Failed to fetch events'
    } finally {
      loading.value = false
    }
  }

  const fetchEventsByYear = async (year: number) => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return
    }

    loading.value = true
    error.value = null

    try {
      // Get first and last day of the year
      const startDate = formatDateToLocalString(new Date(year, 0, 1))
      const endDate = formatDateToLocalString(new Date(year, 11, 31))

      const { data, error: fetchError } = await (supabase as any)
        .from('annual_calendar_events')
        .select('*')
        .eq('user_id', user.value.id)
        .or(`and(start_date.gte.${startDate},start_date.lte.${endDate}),and(end_date.gte.${startDate},end_date.lte.${endDate}),and(start_date.lte.${startDate},end_date.gte.${endDate})`)
        .order('start_date', { ascending: true })

      if (fetchError) {
        throw fetchError
      }

      events.value = data || []
    } catch (err) {
      console.error('Error fetching events by year:', err)
      error.value = err instanceof Error ? err.message : 'Failed to fetch events'
    } finally {
      loading.value = false
    }
  }

  const createEvent = async (eventData: CalendarEventFormData): Promise<CalendarEvent | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const newEvent = {
        user_id: user.value.id,
        title: eventData.title,
        description: eventData.description || null,
        category: eventData.category,
        start_date: eventData.start_date,
        end_date: eventData.end_date || null,
        location: eventData.location || null
      }

      const { data, error: createError } = await (supabase as any)
        .from('annual_calendar_events')
        .insert(newEvent)
        .select()
        .single()

      if (createError) {
        throw createError
      }

      // Add to local state
      events.value.push(data)
      
      return data
    } catch (err) {
      console.error('Error creating event:', err)
      error.value = err instanceof Error ? err.message : 'Failed to create event'
      return null
    } finally {
      loading.value = false
    }
  }

  const updateEvent = async (eventId: string, eventData: Partial<CalendarEventFormData>): Promise<CalendarEvent | null> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const updateData = {
        ...eventData,
        // Convert empty strings to null for optional fields
        end_date: eventData.end_date === '' ? null : eventData.end_date,
        location: eventData.location === '' ? null : eventData.location,
        description: eventData.description === '' ? null : eventData.description,
        updated_at: new Date().toISOString()
      }

      const { data, error: updateError } = await (supabase as any)
        .from('annual_calendar_events')
        .update(updateData)
        .eq('id', eventId)
        .eq('user_id', user.value.id)
        .select()
        .single()

      if (updateError) {
        throw updateError
      }

      // Update local state
      const index = events.value.findIndex(event => event.id === eventId)
      if (index !== -1) {
        events.value[index] = data
      }

      return data
    } catch (err) {
      console.error('Error updating event:', err)
      error.value = err instanceof Error ? err.message : 'Failed to update event'
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteEvent = async (eventId: string): Promise<boolean> => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const { error: deleteError } = await (supabase as any)
        .from('annual_calendar_events')
        .delete()
        .eq('id', eventId)
        .eq('user_id', user.value.id)

      if (deleteError) {
        throw deleteError
      }

      // Remove from local state
      events.value = events.value.filter(event => event.id !== eventId)
      
      return true
    } catch (err) {
      console.error('Error deleting event:', err)
      error.value = err instanceof Error ? err.message : 'Failed to delete event'
      return false
    } finally {
      loading.value = false
    }
  }

  const getEventById = (eventId: string): CalendarEvent | undefined => {
    return events.value.find(event => event.id === eventId)
  }

  const getEventsByDate = (date: string): CalendarEvent[] => {
    return events.value.filter(event => {
      const eventStart = event.start_date
      const eventEnd = event.end_date || event.start_date
      return date >= eventStart && date <= eventEnd
    })
  }

  const getEventsByCategory = (category: string): CalendarEvent[] => {
    return events.value.filter(event => event.category === category)
  }

  const clearEvents = () => {
    events.value = []
    error.value = null
  }

  const refreshEvents = async (filters?: CalendarFilters) => {
    await fetchEvents(filters)
  }

  const searchEvents = async (filters: CalendarFilters, page: number = 0, pageSize: number = 20): Promise<{ events: CalendarEvent[], totalCount: number, hasMore: boolean }> => {
    if (!user.value) {
      throw new Error('User not authenticated')
    }

    loading.value = true
    error.value = null

    try {
      // First, get the total count
      let countQuery = supabase
        .from('annual_calendar_events')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.value.id)

      // Apply search query if provided
      if (filters.searchQuery && filters.searchQuery.trim()) {
        const searchTerm = filters.searchQuery.trim()
        countQuery = countQuery.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%`)
      }

      // Apply category filters if provided
      if (filters.categories && filters.categories.length > 0) {
        countQuery = countQuery.in('category', filters.categories)
      }

      const { count, error: countError } = await countQuery

      if (countError) {
        console.error('Error counting events:', countError)
        error.value = countError.message
        return { events: [], totalCount: 0, hasMore: false }
      }

      const totalCount = count || 0

      // Then get the actual data with pagination
      let dataQuery = supabase
        .from('annual_calendar_events')
        .select('*')
        .eq('user_id', user.value.id)
        .order('start_date', { ascending: true })
        .range(page * pageSize, (page + 1) * pageSize - 1)

      // Apply search query if provided
      if (filters.searchQuery && filters.searchQuery.trim()) {
        const searchTerm = filters.searchQuery.trim()
        dataQuery = dataQuery.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%`)
      }

      // Apply category filters if provided
      if (filters.categories && filters.categories.length > 0) {
        dataQuery = dataQuery.in('category', filters.categories)
      }

      const { data, error: fetchError } = await dataQuery

      if (fetchError) {
        console.error('Error searching events:', fetchError)
        error.value = fetchError.message
        return { events: [], totalCount: 0, hasMore: false }
      }

      const events = data || []
      const hasMore = (page + 1) * pageSize < totalCount

      return { events, totalCount, hasMore }
    } catch (err) {
      console.error('Error searching events:', err)
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return { events: [], totalCount: 0, hasMore: false }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    events: readonly(events),
    loading: readonly(loading),
    error: readonly(error),
    
    // Computed
    eventsCount,
    eventsByCategory,
    
    // Methods
    fetchEvents,
    fetchEventsByMonth,
    fetchEventsByYear,
    createEvent,
    updateEvent,
    deleteEvent,
    getEventById,
    getEventsByDate,
    getEventsByCategory,
    clearEvents,
    refreshEvents,
    searchEvents
  }
}
