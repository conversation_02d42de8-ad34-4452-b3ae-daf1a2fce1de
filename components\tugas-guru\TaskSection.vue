<template>
  <div>
    <!-- Add Task Form (shown when add button is clicked) -->
    <div v-if="showAddForm" class="mb-4">
      <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
        <div class="flex items-center space-x-3">
          <Input v-model="newTaskDescription" placeholder="Taipkan tugas anda disini" class="flex-1" variant="normal"
            @keyup.enter="handleAddTask" @keyup.escape="handleCancel" />
          <UiBaseButton type="button" @click="handleAddTask" variant="primary" size="sm"
            :disabled="!newTaskDescription.trim()">
            Simpan
          </UiBaseButton>
          <UiBaseButton type="button" @click="handleCancel" variant="outline" size="sm">
            Batal
          </UiBaseButton>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="tasks.length === 0 && !showAddForm" class="text-center py-8 text-gray-500 dark:text-gray-400">
      <UiBaseIcon name="heroicons:clipboard-document-list-solid" class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <p>Tiada tugas disimpan. Klik "Tambah Tugas" untuk menambah tugas baru.</p>
    </div>

    <!-- Task List -->
    <div v-else-if="tasks.length > 0" class="space-y-3">
      <div v-for="(task, index) in tasks" :key="task.id"
        class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
        <!-- Task Content -->
        <div class="flex items-center space-x-3 flex-1">
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-[1.5rem]">
            {{ index + 1 }}.
          </span>

          <!-- Edit Mode -->
          <div v-if="editingTaskId === task.id" class="flex items-center space-x-3 flex-1">
            <Input v-model="editTaskDescription" class="flex-1" variant="normal" @keyup.enter="handleSaveEdit"
              @keyup.escape="handleCancelEdit" ref="editInput" />
            <UiBaseButton type="button" @click="handleSaveEdit" variant="primary" size="sm"
              :disabled="!editTaskDescription.trim()">
              Simpan
            </UiBaseButton>
            <UiBaseButton type="button" @click="handleCancelEdit" variant="outline" size="sm">
              Batal
            </UiBaseButton>
          </div>

          <!-- View Mode -->
          <span v-else class="text-sm text-gray-900 dark:text-white flex-1">
            {{ task.task_description }}
          </span>
        </div>

        <!-- Actions (only shown in view mode) -->
        <div v-if="editingTaskId !== task.id" class="flex items-center space-x-2">
          <button @click="handleEditTask(task)"
            class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
            Edit
          </button>
          <span class="text-gray-300 dark:text-gray-600">|</span>
          <button @click="handleDeleteTask(task.id)"
            class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors">
            Padam
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import Input from '~/components/ui/base/Input.vue'
import UiBaseButton from '~/components/ui/base/Button.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'

// =====================================================
// TYPES
// =====================================================

interface Task {
  id: string
  task_description: string
  category: string
  created_at: string
  updated_at: string
}

// =====================================================
// PROPS & EMITS
// =====================================================

interface Props {
  tasks: Task[]
  showAddForm: boolean
  category: string
}

interface Emits {
  (e: 'add-task', category: string, description: string): void
  (e: 'edit-task', taskId: string, description: string): void
  (e: 'delete-task', taskId: string): void
  (e: 'cancel-add', category: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// =====================================================
// STATE
// =====================================================

const newTaskDescription = ref('')
const editingTaskId = ref<string | null>(null)
const editTaskDescription = ref('')
const editInput = ref<InstanceType<typeof Input> | null>(null)

// =====================================================
// METHODS
// =====================================================

const handleAddTask = () => {
  const description = newTaskDescription.value.trim()
  if (!description) return

  emit('add-task', props.category, description)
  newTaskDescription.value = ''
}

const handleCancel = () => {
  newTaskDescription.value = ''
  emit('cancel-add', props.category)
}

const handleEditTask = async (task: Task) => {
  editingTaskId.value = task.id
  editTaskDescription.value = task.task_description

  // Focus the edit input after DOM update
  await nextTick()
  if (editInput.value && editInput.value.$el) {
    editInput.value.$el.querySelector('input')?.focus()
  }
}

const handleSaveEdit = () => {
  const description = editTaskDescription.value.trim()
  if (!description || !editingTaskId.value) return

  emit('edit-task', editingTaskId.value, description)
  handleCancelEdit()
}

const handleCancelEdit = () => {
  editingTaskId.value = null
  editTaskDescription.value = ''
}

const handleDeleteTask = (taskId: string) => {
  emit('delete-task', taskId)
}
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
