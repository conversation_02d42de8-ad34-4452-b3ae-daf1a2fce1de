import { ref, computed } from 'vue'
import type { 
  RatingCalculationInput,
  RatingCalculationResult,
  RatingSystemConfig 
} from '~/types/ratingCalculation'
import type { 
  CalculatedRating,
  RatingBreakdown 
} from '~/types/reflections'
import type { 
  LessonPlan 
} from '~/types/lessonPlans'
import type { 
  DayOfWeek,
  SchedulePeriod 
} from '~/types/teacherSchedule'
import { useTeacherSchedules } from './useTeacherSchedules'
import { useDetailedReflections } from './useDetailedReflections'

// Default configuration for the rating system
const DEFAULT_CONFIG: RatingSystemConfig = {
  default_rating: 5,
  rounding_precision: 0.1
}

export const useRatingCalculation = () => {
  const config = ref<RatingSystemConfig>(DEFAULT_CONFIG)
  const { getScheduledDaysForClassSubject } = useTeacherSchedules()

  // Helper function to round to nearest precision
  const roundToPrecision = (value: number, precision: number = 0.1): number => {
    return Math.round(value / precision) * precision
  }

  // Helper function to check if a reflection has been edited from defaults
  const isReflectionEdited = (reflection: any): boolean => {
    if (!reflection) return false;

    // Default values for comparison
    const defaults = {
      overall_rating: 5,
      objectives_achieved: true,
      challenges_faced: '',
      activity_effectiveness: 5,
      time_management: 'on_time',
      student_engagement: 5,
      resource_adequacy: 'adequate',
      improvements_needed: '',
      successful_strategies: '',
      action_items: [],
      additional_notes: '',
      jumlah_murid_mencapai_objektif: 0,
      tindakan_susulan: [],
      tidak_terlaksana: null
    };

    // Check if any field differs from default
    for (const [key, defaultValue] of Object.entries(defaults)) {
      const reflectionValue = reflection[key];

      // Handle array comparisons
      if (Array.isArray(defaultValue)) {
        if (!Array.isArray(reflectionValue) || reflectionValue.length !== defaultValue.length) {
          return true;
        }
        if (reflectionValue.some((item, index) => item !== defaultValue[index])) {
          return true;
        }
      }
      // Handle other value comparisons
      else if (reflectionValue !== defaultValue) {
        return true;
      }
    }

    return false;
  }

  // Helper function to get class-subject ID from lesson plan format
  const getClassSubjectId = (classId: string, subjectId: string): string => {
    return `${classId}_${subjectId}`
  }

  // Extract scheduled periods from teacher schedules for a lesson plan
  const getScheduledPeriodsForLessonPlan = (
    lessonPlan: LessonPlan,
    teacherSchedules: Array<{
      class_id: string
      subject_id: string
      days_scheduled: DayOfWeek[]
      class_name?: string
      subject_name?: string
    }>
  ): SchedulePeriod[] => {
    const periods: SchedulePeriod[] = []
    const seenCombinations = new Set<string>()

    // For each class-subject combination in the lesson plan
    lessonPlan.class_subject_ids.forEach(classSubjectId => {
      const [classId, subjectId] = classSubjectId.split('_')

      // Find the corresponding teacher schedule
      const schedule = teacherSchedules.find(s =>
        s.class_id === classId && s.subject_id === subjectId
      )

      if (schedule) {
        // For each scheduled day, create a period (but deduplicate by class-subject-day)
        schedule.days_scheduled.forEach(day => {
          // Only include days that are also in the lesson plan
          if (lessonPlan.days_selected.map(d => d.toUpperCase()).includes(day)) {
            // Create unique key for class-subject-day combination
            const combinationKey = `${classSubjectId}_${day}`

            // Only add if we haven't seen this combination before
            if (!seenCombinations.has(combinationKey)) {
              seenCombinations.add(combinationKey)
              periods.push({
                day: day,
                time_slot_start: '08:00', // Default time slot
                time_slot_end: '08:30',   // Default time slot
                class_id: classId,
                subject_id: subjectId,
                class_name: schedule.class_name || classId,
                subject_name: schedule.subject_name || subjectId,
                class_subject_id: classSubjectId
              })
            }
          }
        })
      }
    })

    return periods
  }

  // Calculate overall rating for a lesson plan
  const calculateLessonPlanRating = async (
    lessonPlan: LessonPlan,
    teacherSchedules: Array<{
      class_id: string
      subject_id: string
      days_scheduled: DayOfWeek[]
      class_name?: string
      subject_name?: string
    }>
  ): Promise<RatingCalculationResult> => {
    // Get all scheduled periods for this lesson plan
    const scheduledPeriods = getScheduledPeriodsForLessonPlan(lessonPlan, teacherSchedules)

    if (scheduledPeriods.length === 0) {
      // No teacher schedules match this lesson plan
      return {
        total_periods: 0,
        total_stars: 0,
        calculated_rating: config.value.default_rating,
        periods_with_reflections: 0,
        periods_using_default: 0,
        periods_tidak_terlaksana: 0,
        period_details: []
      }
    }

    // Fetch detailed reflections directly from database
    const { fetchDetailedReflections } = useDetailedReflections()
    const detailedReflections = await fetchDetailedReflections(lessonPlan.id)

    let totalStars = 0
    let periodsWithReflections = 0
    let periodsUsingDefault = 0
    let periodsTidakTerlaksana = 0
    const periodDetails: RatingCalculationResult['period_details'] = []

    // Calculate rating for each period
    scheduledPeriods.forEach(period => {
      // Look for a detailed reflection for this specific period in the JSONB structure
      const periodKey = `${period.class_subject_id}_${period.day.toLowerCase()}`;
      const reflection = detailedReflections?.reflections?.[periodKey];

      let rating: number
      let hasReflection: boolean

      if (reflection && isReflectionEdited(reflection)) {
        // Reflection exists and has been edited from defaults
        if (reflection.tidak_terlaksana !== null && reflection.tidak_terlaksana !== '') {
          // Lesson was not delivered - count as 0 stars
          rating = 0
          hasReflection = true
          periodsWithReflections++
          periodsTidakTerlaksana++
        } else {
          // Normal reflection with actual rating
          rating = reflection.overall_rating
          hasReflection = true
          periodsWithReflections++
        }
      } else {
        // No reflection or reflection uses all default values
        rating = config.value.default_rating
        hasReflection = false
        periodsUsingDefault++
      }

      totalStars += rating

      periodDetails.push({
        class_id: period.class_id || '',
        subject_id: period.subject_id || '',
        day: period.day,
        class_name: period.class_name ?? '',
        subject_name: period.subject_name ?? '',
        class_subject_id: period.class_subject_id ?? '',
        rating: rating,
        has_reflection: hasReflection
      })
    })

    // Calculate average and round to precision
    const averageRating = totalStars / scheduledPeriods.length
    const calculatedRating = roundToPrecision(averageRating, config.value.rounding_precision)

    return {
      total_periods: scheduledPeriods.length,
      total_stars: totalStars,
      calculated_rating: calculatedRating,
      periods_with_reflections: periodsWithReflections,
      periods_using_default: periodsUsingDefault,
      periods_tidak_terlaksana: periodsTidakTerlaksana,
      period_details: periodDetails
    }
  }

  // Create a CalculatedRating object for the reflection system
  const createCalculatedRating = (
    lessonPlanId: string,
    calculationResult: RatingCalculationResult
  ): CalculatedRating => {
    return {
      lesson_plan_id: lessonPlanId,
      total_periods: calculationResult.total_periods,
      total_stars: calculationResult.total_stars,
      calculated_rating: calculationResult.calculated_rating,
      periods_with_reflections: calculationResult.periods_with_reflections,
      periods_using_default: calculationResult.periods_using_default,
      periods_tidak_terlaksana: calculationResult.periods_tidak_terlaksana,
      is_calculated: true
    }
  }

  // Create rating breakdown for UI display
  const createRatingBreakdown = (
    calculationResult: RatingCalculationResult
  ): RatingBreakdown[] => {
    return calculationResult.period_details.map(period => ({
      class_subject_label: `${period.class_name} - ${period.subject_name}`,
      day_label: getDayLabel(period.day),
      rating: period.rating,
      has_reflection: period.has_reflection,
      is_default: !period.has_reflection
    }))
  }
  // Helper to get day label in Malay
  const getDayLabel = (day: DayOfWeek): string => {
    const dayLabels: Record<DayOfWeek, string> = {
      'ISNIN': 'Isnin',
      'SELASA': 'Selasa', 
      'RABU': 'Rabu',
      'KHAMIS': 'Khamis',
      'JUMAAT': 'Jumaat',
      'AHAD': 'Ahad'
    }
    return dayLabels[day] || day
  }

  // Check if a lesson plan has any teacher schedules
  const hasCompatibleSchedules = (
    lessonPlan: LessonPlan,
    teacherSchedules: Array<{
      class_id: string
      subject_id: string
      days_scheduled: DayOfWeek[]
      class_name?: string
      subject_name?: string
    }>
  ): boolean => {
    return lessonPlan.class_subject_ids.some(classSubjectId => {
      const [classId, subjectId] = classSubjectId.split('_')
      return teacherSchedules.some(schedule =>
        schedule.class_id === classId && schedule.subject_id === subjectId
      )
    })
  }



  // Update configuration
  const updateConfig = (newConfig: Partial<RatingSystemConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  return {
    // State
    config: computed(() => config.value),
    
    // Main calculation functions
    calculateLessonPlanRating,
    createCalculatedRating,
    createRatingBreakdown,
    
    // Helper functions
    getScheduledPeriodsForLessonPlan,
    hasCompatibleSchedules,
    roundToPrecision,
    getDayLabel,
    
    // Configuration
    updateConfig
  }
}
