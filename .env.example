# RPHMate SaaS - Environment Configuration Example
# Copy this file to .env and fill in your actual values

# ===========================================
# APPLICATION SETTINGS
# ===========================================
NODE_ENV=development
NUXT_PUBLIC_BASE_DOMAIN=localhost:3000
NUXT_PUBLIC_ENABLE_SUBDOMAINS=true

# ===========================================
# SUPABASE CONFIGURATION
# ===========================================
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# ===========================================
# AUTHENTICATION
# ===========================================
NUXT_SECRET_KEY=your-secret-key-for-sessions
JWT_SECRET=your-jwt-secret-key

# ===========================================
# EMAIL CONFIGURATION (Optional for local testing)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=noreply@localhost

# ===========================================
# PAYMENT CONFIGURATION (Optional for local testing)
# ===========================================
# Use Stripe test keys for local development
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_test_...

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Enable debug logging
DEBUG=true
LOG_LEVEL=debug

# ===========================================
# OPTIONAL SERVICES
# ===========================================
# Error tracking (optional)
SENTRY_DSN=your-sentry-dsn

# Analytics (optional)
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
