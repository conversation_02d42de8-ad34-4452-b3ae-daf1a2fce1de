import { ref, computed } from 'vue';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import type { Database } from '~/types/supabase';
import type { LessonPlan } from '~/types/lessonPlans';

// Types for the new reflection system based on detailed reflections
export interface LessonPlanReflectionSummary {
  lesson_plan_id: string;
  overall_rating: number;
  total_periods: number;
  periods_with_reflections: number;
  periods_using_default: number;
  reflection_date: string;
  lesson_plan?: {
    file_name: string;
    week_id: string | null;
    class_subject_ids: string[];
  };
}

export interface ReflectionStats {
  total_reflections: number;
  average_rating: number;
  completion_rate: number;
  total_periods: number;
  periods_with_reflections: number;
}

// Global state
const globalReflectionSummaries = ref<LessonPlanReflectionSummary[]>([]);
const globalLoading = ref(false);
const globalError = ref<string | null>(null);

export const useLessonPlanReflections = () => {
  const supabase = useSupabaseClient<Database>();
  const user = useSupabaseUser();

  // Calculate lesson plan reflection summary from detailed reflections
  const calculateLessonPlanSummary = async (lessonPlanId: string): Promise<LessonPlanReflectionSummary | null> => {
    try {
      // Fetch detailed reflections for this lesson plan
      const { data: detailedReflections, error: detailedError } = await supabase
        .from('lesson_plan_detailed_reflections')
        .select('*')
        .eq('lesson_plan_id', lessonPlanId);

      if (detailedError) throw detailedError;

      // Fetch lesson plan info
      const { data: lessonPlan, error: lessonPlanError } = await supabase
        .from('lesson_plans')
        .select('file_name, week_id, class_subject_ids, days_selected, created_at')
        .eq('id', lessonPlanId)
        .single();

      if (lessonPlanError) throw lessonPlanError;

      // Calculate total periods (unique class-subject-day combinations)
      const totalPeriods = new Set(
        lessonPlan.class_subject_ids.flatMap(classSubjectId =>
          lessonPlan.days_selected.map(day => `${classSubjectId}_${day.toUpperCase()}`)
        )
      ).size;

      const periodsWithReflections = detailedReflections?.length || 0;

      // Calculate overall rating - simple average of all detailed reflections
      // Since all periods have auto-generated reflections, just average them
      let overallRating = 5; // Default rating
      if (periodsWithReflections > 0) {
        const totalStars = (detailedReflections || []).reduce((sum, reflection) => sum + reflection.overall_rating, 0);
        overallRating = Number((totalStars / periodsWithReflections).toFixed(1));
      }

      return {
        lesson_plan_id: lessonPlanId,
        overall_rating: overallRating,
        total_periods: totalPeriods,
        periods_with_reflections: periodsWithReflections,
        periods_using_default: totalPeriods - periodsWithReflections,
        reflection_date: lessonPlan.created_at,
        lesson_plan: {
          file_name: lessonPlan.file_name || `RPH ${lessonPlanId.slice(0, 8)}`,
          week_id: lessonPlan.week_id,
          class_subject_ids: lessonPlan.class_subject_ids
        }
      };
    } catch (error) {
      console.error('Error calculating lesson plan summary:', error);
      return null;
    }
  };

  // Fetch all lesson plan reflection summaries
  const fetchReflectionSummaries = async (weekId?: string) => {
    if (!user.value) return;

    globalLoading.value = true;
    globalError.value = null;

    try {
      // Fetch all lesson plans for the user (optionally filtered by week)
      let query = supabase
        .from('lesson_plans')
        .select('id, file_name, week_id, class_subject_ids, days_selected, created_at')
        .eq('user_id', user.value.id)
        .order('created_at', { ascending: false });

      if (weekId) {
        query = query.eq('week_id', weekId);
      }

      const { data: lessonPlans, error: lessonPlansError } = await query;
      if (lessonPlansError) throw lessonPlansError;

      // Calculate summaries for all lesson plans
      const summaries = await Promise.all(
        (lessonPlans || []).map(async (lessonPlan) => {
          return await calculateLessonPlanSummary(lessonPlan.id);
        })
      );

      // Filter out null results
      globalReflectionSummaries.value = summaries.filter(Boolean) as LessonPlanReflectionSummary[];
    } catch (err: any) {
      globalError.value = err.message;
      globalReflectionSummaries.value = [];
    } finally {
      globalLoading.value = false;
    }
  };

  // Get reflection stats
  const getReflectionStats = async (weekId?: string): Promise<ReflectionStats> => {
    const summaries = globalReflectionSummaries.value;
    
    if (summaries.length === 0) {
      return {
        total_reflections: 0,
        average_rating: 0,
        completion_rate: 0,
        total_periods: 0,
        periods_with_reflections: 0
      };
    }

    const totalReflections = summaries.length;
    const totalRating = summaries.reduce((sum, s) => sum + s.overall_rating, 0);
    const averageRating = Number((totalRating / totalReflections).toFixed(1));
    
    const totalPeriods = summaries.reduce((sum, s) => sum + s.total_periods, 0);
    const periodsWithReflections = summaries.reduce((sum, s) => sum + s.periods_with_reflections, 0);
    const completionRate = totalPeriods > 0 ? Number(((periodsWithReflections / totalPeriods) * 100).toFixed(1)) : 0;

    return {
      total_reflections: totalReflections,
      average_rating: averageRating,
      completion_rate: completionRate,
      total_periods: totalPeriods,
      periods_with_reflections: periodsWithReflections
    };
  };

  // Check if lesson plan has any reflections
  const hasReflection = (lessonPlanId: string): boolean => {
    const summary = globalReflectionSummaries.value.find(s => s.lesson_plan_id === lessonPlanId);
    return summary ? summary.periods_with_reflections > 0 : false;
  };

  // Get reflection summary for a specific lesson plan
  const getReflectionSummary = (lessonPlanId: string): LessonPlanReflectionSummary | undefined => {
    return globalReflectionSummaries.value.find(s => s.lesson_plan_id === lessonPlanId);
  };

  // Get overall rating for a lesson plan
  const getLessonPlanRating = (lessonPlanId: string): number => {
    const summary = getReflectionSummary(lessonPlanId);
    return summary ? summary.overall_rating : 5; // Default to 5 if no summary found
  };

  // Refresh a specific lesson plan's summary (useful after updating detailed reflections)
  const refreshLessonPlanSummary = async (lessonPlanId: string) => {
    const updatedSummary = await calculateLessonPlanSummary(lessonPlanId);
    if (updatedSummary) {
      const index = globalReflectionSummaries.value.findIndex(s => s.lesson_plan_id === lessonPlanId);
      if (index !== -1) {
        globalReflectionSummaries.value[index] = updatedSummary;
      } else {
        globalReflectionSummaries.value.unshift(updatedSummary);
      }
    }
  };

  return {
    // State
    reflectionSummaries: computed(() => globalReflectionSummaries.value),
    loading: computed(() => globalLoading.value),
    error: computed(() => globalError.value),

    // Methods
    fetchReflectionSummaries,
    getReflectionStats,
    hasReflection,
    getReflectionSummary,
    getLessonPlanRating,
    refreshLessonPlanSummary,
    calculateLessonPlanSummary
  };
};
