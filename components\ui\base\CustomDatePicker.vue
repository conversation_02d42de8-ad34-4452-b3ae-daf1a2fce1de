<template>
  <div class="relative">
    <!-- Floating Label -->
    <template v-if="props.variant === 'floating'">
      <FloatingLabel :for-input="componentId" :label="props.placeholder" :is-floated="isFloated"
        :is-focused="isFocused" />
    </template>
    <template v-else>
      <label v-if="props.label" :for="componentId"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {{ props.label }}
      </label>
    </template>

    <!-- Input Field -->
    <div class="relative">
      <div @click="openPicker" :class="[
        'form-input pb-2.5 pt-5 peer w-full cursor-pointer flex items-center justify-between',
        { 'cursor-not-allowed': props.disabled }
      ]" :data-has-value="!!props.modelValue">
        <!-- Date display area -->
        <div class="flex items-center flex-1 min-w-0">
          <span v-if="displayValue" class="text-gray-900 dark:text-gray-100">
            {{ displayValue }}
          </span>
          <span v-else-if="props.variant === 'normal'" class="text-gray-500 dark:text-gray-400">
            {{ props.placeholder }}
          </span>

          <!-- Clear Button (right next to date text) -->
          <button v-if="props.modelValue" type="button" @click.stop="clearDate" :disabled="props.disabled"
            class="ml-2 p-1 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 flex-shrink-0"
            tabindex="-1" title="Kosongkan tarikh">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Calendar Icon -->
        <button type="button" @click.stop="openPicker" :disabled="props.disabled"
          class="p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 flex-shrink-0"
          tabindex="-1">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </button>
      </div>

      <!-- Hidden input for form compatibility -->
      <input ref="inputEl" :id="componentId" type="text" :value="props.modelValue || ''" :disabled="props.disabled"
        :required="props.required" :aria-label="props.placeholder" class="sr-only" tabindex="-1" />
    </div>

    <!-- Modal Backdrop -->
    <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="closePicker" @keydown.esc="closePicker">
      <!-- Modal Content -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-80 max-w-sm mx-4" @click.stop
        @keydown="handleKeydown" tabindex="0" ref="modalEl">
        <!-- Calendar View -->
        <div v-if="currentView === 'calendar'">
          <!-- Header -->
          <div class="flex items-center justify-between mb-4">
            <button type="button" @click="previousMonth" class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <div class="flex space-x-2">
              <button type="button" @click="showMonthSelector"
                class="px-3 py-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded font-medium">
                {{ monthNames[currentMonth] }}
              </button>
              <button type="button" @click="showYearSelector"
                class="px-3 py-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded font-medium">
                {{ currentYear }}
              </button>
            </div>

            <button type="button" @click="nextMonth" class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <!-- Day Headers -->
          <div class="grid grid-cols-7 gap-1 mb-2">
            <div v-for="day in dayNames" :key="day"
              class="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-2">
              {{ day }}
            </div>
          </div>

          <!-- Calendar Days -->
          <div class="grid grid-cols-7 gap-1 mb-4">
            <button type="button" v-for="day in calendarDays" :key="`${day.date}-${day.isCurrentMonth}`"
              @click="selectDay(day)" :disabled="!day.isCurrentMonth" :class="[
                'h-8 text-sm rounded hover:bg-blue-100 dark:hover:bg-blue-900',
                day.isCurrentMonth ? 'text-gray-900 dark:text-gray-100' : 'text-gray-300 dark:text-gray-600 cursor-not-allowed',
                day.isToday ? 'bg-blue-100 dark:bg-blue-900 font-bold' : '',
                day.isSelected ? 'bg-blue-500 text-white hover:bg-blue-600' : ''
              ]">
              {{ day.date }}
            </button>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-between">
            <button type="button" @click="selectToday"
              class="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
              Hari Ini
            </button>
            <div class="space-x-2">
              <button type="button" @click="closePicker"
                class="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
                Batal
              </button>
              <button type="button" @click="confirmSelection" :disabled="!selectedDate"
                class="px-4 py-2 text-sm bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed rounded">
                Pilih
              </button>
            </div>
          </div>
        </div>

        <!-- Month Selector View -->
        <div v-else-if="currentView === 'months'" class="space-y-4">
          <div class="text-center font-medium text-lg">{{ currentYear }}</div>
          <div class="grid grid-cols-3 gap-2">
            <button type="button" v-for="(month, index) in monthNames" :key="month" @click="selectMonth(index)"
              class="px-3 py-2 text-sm hover:bg-blue-100 dark:hover:bg-blue-900 rounded"
              :class="index === currentMonth ? 'bg-blue-500 text-white' : ''">
              {{ month }}
            </button>
          </div>
        </div>

        <!-- Year Selector View -->
        <div v-else-if="currentView === 'years'" class="space-y-4">
          <div class="text-center font-medium text-lg">Pilih Tahun</div>
          <div class="h-48 overflow-y-auto year-selector-container">
            <div class="grid grid-cols-3 gap-2 p-2">
              <button type="button" v-for="year in yearRange" :key="year" :data-year="year" @click="selectYear(year)"
                class="px-3 py-2 text-sm hover:bg-blue-100 dark:hover:bg-blue-900 rounded"
                :class="year === currentYear ? 'bg-blue-500 text-white' : ''">
                {{ year }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, useId, onMounted, nextTick } from 'vue';
import FloatingLabel from './FloatingLabel.vue';

const props = withDefaults(defineProps<{
  modelValue?: string | null;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  variant?: "floating" | "normal";
  label?: string;
}>(), {
  placeholder: '',
  disabled: false,
  required: false,
  variant: 'floating',
});

const emit = defineEmits<{
  'update:modelValue': [value: string | null];
  'focus': [event: FocusEvent];
  'blur': [event: FocusEvent];
}>();

// Refs
const inputEl = ref<HTMLInputElement | null>(null);
const modalEl = ref<HTMLDivElement | null>(null);
const componentId = useId();
const isFocused = ref(false);
const isOpen = ref(false);

// Current view state
const currentView = ref<'calendar' | 'months' | 'years'>('calendar');
const currentDate = ref(new Date());
const selectedDate = ref<Date | null>(null);

// Constants
const monthNames = ['Jan', 'Feb', 'Mac', 'Apr', 'Mei', 'Jun', 'Jul', 'Ogs', 'Sep', 'Okt', 'Nov', 'Dis'];
const dayNames = ['Is', 'Se', 'Ra', 'Kh', 'Ju', 'Sa', 'Ah'];

// Computed properties
const currentMonth = computed(() => currentDate.value.getMonth());
const currentYear = computed(() => currentDate.value.getFullYear());

const displayValue = computed(() => {
  if (!props.modelValue) return '';
  try {
    const date = new Date(props.modelValue);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  } catch {
    return '';
  }
});

const isFloated = computed(() => {
  return isFocused.value || !!props.modelValue;
});

// Generate year range from 1900 to 2100
const yearRange = computed(() => {
  const years = [];

  for (let year = 1900; year <= 2100; year++) {
    years.push(year);
  }
  return years;
});

// Generate calendar days
const calendarDays = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  const firstDay = new Date(year, month, 1);
  const startDate = new Date(firstDay);

  // Adjust to start from Monday (getDay() returns 0 for Sunday)
  const dayOfWeek = (firstDay.getDay() + 6) % 7; // Convert to Monday = 0
  startDate.setDate(startDate.getDate() - dayOfWeek);

  const days = [];
  const today = new Date();

  for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    const isCurrentMonth = date.getMonth() === month;
    const isToday = date.toDateString() === today.toDateString();
    const isSelected = selectedDate.value && date.toDateString() === selectedDate.value.toDateString();

    days.push({
      date: date.getDate(),
      fullDate: new Date(date),
      isCurrentMonth,
      isToday,
      isSelected
    });
  }

  return days;
});

// Methods
const openPicker = () => {
  if (props.disabled) return;
  isOpen.value = true;
  currentView.value = 'calendar';

  // Initialize current date from modelValue or today
  if (props.modelValue) {
    try {
      currentDate.value = new Date(props.modelValue);
      selectedDate.value = new Date(props.modelValue);
    } catch {
      currentDate.value = new Date();
      selectedDate.value = null;
    }
  } else {
    currentDate.value = new Date();
    selectedDate.value = null;
  }

  nextTick(() => {
    modalEl.value?.focus();
  });
};

const closePicker = () => {
  isOpen.value = false;
  currentView.value = 'calendar';
};

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const previousMonth = () => {
  const newDate = new Date(currentDate.value);
  newDate.setMonth(newDate.getMonth() - 1);
  currentDate.value = newDate;
};

const nextMonth = () => {
  const newDate = new Date(currentDate.value);
  newDate.setMonth(newDate.getMonth() + 1);
  currentDate.value = newDate;
};

const showMonthSelector = () => {
  currentView.value = 'months';
};

const showYearSelector = () => {
  currentView.value = 'years';

  // Jump directly to current year when year selector opens
  nextTick(() => {
    const yearContainer = document.querySelector('.year-selector-container');
    const currentYearButton = document.querySelector(`[data-year="${currentYear.value}"]`);

    if (yearContainer && currentYearButton) {
      currentYearButton.scrollIntoView({
        behavior: 'instant',
        block: 'center'
      });
    }
  });
};

const selectMonth = (monthIndex: number) => {
  const newDate = new Date(currentDate.value);
  newDate.setMonth(monthIndex);
  currentDate.value = newDate;
  currentView.value = 'calendar';
};

const selectYear = (year: number) => {
  const newDate = new Date(currentDate.value);
  newDate.setFullYear(year);
  currentDate.value = newDate;
  currentView.value = 'calendar';
};

const selectDay = (day: any) => {
  if (!day.isCurrentMonth) return;
  selectedDate.value = new Date(day.fullDate);
};

const selectToday = () => {
  const today = new Date();
  currentDate.value = new Date(today);
  selectedDate.value = new Date(today);
};

const confirmSelection = () => {
  if (!selectedDate.value) return;

  const isoDate = selectedDate.value.toISOString().split('T')[0];
  emit('update:modelValue', isoDate);
  closePicker();
};

const clearDate = () => {
  emit('update:modelValue', null);
  selectedDate.value = null;
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closePicker();
  } else if (event.key === 'Enter') {
    if (currentView.value === 'calendar' && selectedDate.value) {
      confirmSelection();
    }
  } else if (currentView.value === 'calendar') {
    // Arrow key navigation for calendar days
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      event.preventDefault();
      navigateCalendar(event.key);
    }
  }
};

const navigateCalendar = (key: string) => {
  if (!selectedDate.value) {
    // If no date selected, start with today or first day of current month
    selectedDate.value = new Date();
  }

  const newDate = new Date(selectedDate.value);

  switch (key) {
    case 'ArrowLeft':
      newDate.setDate(newDate.getDate() - 1);
      break;
    case 'ArrowRight':
      newDate.setDate(newDate.getDate() + 1);
      break;
    case 'ArrowUp':
      newDate.setDate(newDate.getDate() - 7);
      break;
    case 'ArrowDown':
      newDate.setDate(newDate.getDate() + 7);
      break;
  }

  // Update current month/year if navigated to different month
  if (newDate.getMonth() !== currentMonth.value || newDate.getFullYear() !== currentYear.value) {
    currentDate.value = new Date(newDate);
  }

  selectedDate.value = newDate;
};

// Initialize component
onMounted(() => {
  if (props.modelValue) {
    try {
      selectedDate.value = new Date(props.modelValue);
    } catch {
      selectedDate.value = null;
    }
  }
});
</script>
