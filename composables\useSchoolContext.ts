/**
 * School Context Composable
 * Provides automatic data filtering and school-specific functionality
 */

export interface SchoolContextData {
  id: string
  name: string
  code: string
  description?: string
  location?: string
  established?: number
  subscription_plan?: string
  subscription_status?: string
  created_at?: string
  updated_at?: string
}

export interface SchoolMembership {
  id: string
  user_id: string
  school_id: string
  role: 'admin' | 'teacher' | 'staff'
  status: 'active' | 'inactive' | 'pending'
  joined_at: string
  invited_by?: string
  invitation_token?: string
  invitation_expires_at?: string
  notes?: string
  permissions: Record<string, any>
  created_at: string
  updated_at: string
}

export interface SchoolContext {
  school: SchoolContextData | null
  membership: SchoolMembership | null
  isLoading: boolean
  error: string | null
}

export const useSchoolContext = () => {
  const supabase = useSupabaseClient()
  const route = useRoute()
  
  // Get current subdomain
  const currentSubdomain = useState('currentSubdomain')
  
  // School context state
  const schoolContext = useState<SchoolContext>('schoolContext', () => ({
    school: null,
    membership: null,
    isLoading: false,
    error: null
  }))

  // Get current school code from subdomain or route
  const currentSchoolCode = computed(() => {
    return currentSubdomain.value || route.params.school as string || null
  })

  // Get current school ID
  const currentSchoolId = computed(() => {
    return schoolContext.value.school?.id || null
  })

  // Check if we're in a school context
  const isSchoolContext = computed(() => {
    return !!currentSchoolCode.value && !!schoolContext.value.school
  })

  // Load school data
  const loadSchoolData = async (schoolCode?: string) => {
    const code = schoolCode || currentSchoolCode.value
    if (!code) {
      schoolContext.value.error = 'No school code provided'
      return false
    }

    try {
      schoolContext.value.isLoading = true
      schoolContext.value.error = null

      // Fetch school information
      const { data: school, error: schoolError } = await supabase
        .from('schools')
        .select('*')
        .eq('code', code)
        .single() as { data: any, error: any }

      if (schoolError) {
        console.error('Error fetching school:', schoolError)
        schoolContext.value.error = `Failed to load school: ${schoolError.message}`
        return false
      }

      if (!school) {
        schoolContext.value.error = 'School not found'
        return false
      }

      // Update school context
      schoolContext.value.school = {
        id: school.id || '',
        name: school.name || '',
        code: school.code || '',
        description: school.description || undefined,
        location: school.location || undefined,
        established: school.established || undefined,
        subscription_plan: school.subscription_plan || undefined,
        subscription_status: school.subscription_status || undefined,
        created_at: school.created_at || undefined,
        updated_at: school.updated_at || undefined
      }

      console.log(`✅ School context loaded: ${school.name} (${school.code})`)
      return true

    } catch (error) {
      console.error('Error in loadSchoolData:', error)
      schoolContext.value.error = 'Failed to load school data'
      return false
    } finally {
      schoolContext.value.isLoading = false
    }
  }

  // Create school-filtered query builder
  const createSchoolQuery = (table: string) => {
    const schoolId = currentSchoolId.value
    if (!schoolId) {
      throw new Error('No school context available. Cannot create school-filtered query.')
    }

    return supabase
      .from(table)
      .select('*')
      .eq('school_id', schoolId)
  }

  // Helper functions for common school-filtered queries
  const getSchoolLessonPlans = async () => {
    if (!currentSchoolId.value) {
      console.warn('❌ No school ID available for lesson plans query')
      return { data: [], error: 'No school context' }
    }

    console.log(`🔍 Querying lesson plans for school ID: ${currentSchoolId.value}`)

    const result = await supabase
      .from('lesson_plans')
      .select('*')
      .eq('school_id', currentSchoolId.value)

    console.log(`📚 Lesson plans query result:`, result)

    return result
  }

  const getSchoolSubjects = async () => {
    if (!currentSchoolId.value) {
      console.warn('❌ No school ID available for subjects query')
      return { data: [], error: 'No school context' }
    }

    console.log(`🔍 Querying subjects for school ID: ${currentSchoolId.value}`)

    const result = await supabase
      .from('subjects')
      .select('*')
      .eq('school_id', currentSchoolId.value)

    console.log(`📊 Subjects query result:`, result)

    return result
  }

  const getSchoolTeachers = async () => {
    if (!currentSchoolId.value) return { data: [], error: 'No school context' }
    
    return await supabase
      .from('school_memberships')
      .select(`
        *,
        profiles:user_id (
          id,
          full_name,
          email,
          avatar_url
        )
      `)
      .eq('school_id', currentSchoolId.value)
      .eq('role', 'teacher')
      .eq('status', 'active')
  }

  const getSchoolClasses = async () => {
    if (!currentSchoolId.value) return { data: [], error: 'No school context' }
    
    return await supabase
      .from('classes')
      .select('*')
      .eq('school_id', currentSchoolId.value)
  }

  // Initialize school context if we have a school code
  const initializeSchoolContext = async () => {
    if (currentSchoolCode.value && !schoolContext.value.school) {
      await loadSchoolData()
    }
  }

  // Reset school context
  const resetSchoolContext = () => {
    schoolContext.value = {
      school: null,
      membership: null,
      isLoading: false,
      error: null
    }
  }

  return {
    // State
    schoolContext: readonly(schoolContext),
    currentSchoolCode,
    currentSchoolId,
    isSchoolContext,
    
    // Methods
    loadSchoolData,
    initializeSchoolContext,
    resetSchoolContext,
    createSchoolQuery,
    
    // Helper queries
    getSchoolLessonPlans,
    getSchoolSubjects,
    getSchoolTeachers,
    getSchoolClasses
  }
}
