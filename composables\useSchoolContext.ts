/**
 * School Context Composable
 * Provides automatic data filtering and school-specific functionality
 */

export interface SchoolContextData {
  id: string
  name: string
  code: string
  description?: string
  location?: string
  established?: number
  subscription_plan?: string
  subscription_status?: string
  created_at?: string
  updated_at?: string
}

export interface SchoolMembership {
  id: string
  user_id: string
  school_id: string
  role: 'admin' | 'teacher' | 'staff'
  status: 'active' | 'inactive' | 'pending'
  joined_at: string
  invited_by?: string
  invitation_token?: string
  invitation_expires_at?: string
  notes?: string
  permissions: Record<string, any>
  created_at: string
  updated_at: string
}

export interface SchoolContext {
  school: SchoolContextData | null
  membership: SchoolMembership | null
  isLoading: boolean
  error: string | null
}

// Smart caching for school data - matches middleware pattern
const schoolDataCache = new Map<string, { data: any, timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes cache for school data

export const useSchoolContext = () => {
  const supabase = useSupabaseClient()
  const route = useRoute()
  
  // Get current subdomain
  const currentSubdomain = useState('currentSubdomain')
  
  // School context state
  const schoolContext = useState<SchoolContext>('schoolContext', () => ({
    school: null,
    membership: null,
    isLoading: true,
    error: null
  }))

  // Get current school code from subdomain or route
  const currentSchoolCode = computed(() => {
    return currentSubdomain.value || route.params.school as string || null
  })

  // Get current school ID
  const currentSchoolId = computed(() => {
    return schoolContext.value.school?.id || null
  })

  // Check if we're in a school context
  const isSchoolContext = computed(() => {
    return !!currentSchoolCode.value && !!schoolContext.value.school
  })

  // Load school data with smart caching
  const loadSchoolData = async (schoolCode?: string) => {
    const code = schoolCode || currentSchoolCode.value
    if (!code || typeof code !== 'string') {
      schoolContext.value.error = 'No school code provided'
      schoolContext.value.isLoading = false
      return false
    }

    // Check cache first
    const cached = schoolDataCache.get(code)
    const now = Date.now()

    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      // Use cached data - much faster!
      schoolContext.value.school = {
        id: cached.data.id || '',
        name: cached.data.name || '',
        code: cached.data.code || '',
        description: cached.data.description || undefined,
        location: cached.data.location || undefined,
        established: cached.data.established || undefined,
        subscription_plan: cached.data.subscription_plan || undefined,
        subscription_status: cached.data.subscription_status || undefined,
        created_at: cached.data.created_at || undefined,
        updated_at: cached.data.updated_at || undefined
      }
      schoolContext.value.isLoading = false
      console.log(`✅ School context loaded from cache: ${cached.data.name} (${cached.data.code})`)
      return true
    }

    try {
      schoolContext.value.isLoading = true
      schoolContext.value.error = null

      // Fetch school information (only needed fields for better performance)
      const { data: school, error: schoolError } = await supabase
        .from('schools')
        .select('id, name, code, description, location, established, subscription_plan, subscription_status, created_at, updated_at')
        .eq('code', code)
        .single() as { data: any, error: any }

      if (schoolError) {
        console.error('Error fetching school:', schoolError)
        schoolContext.value.error = `Failed to load school: ${schoolError.message}`
        schoolContext.value.isLoading = false
        return false
      }

      if (!school) {
        schoolContext.value.error = 'School not found'
        schoolContext.value.isLoading = false
        return false
      }

      // Cache the school data for future use
      schoolDataCache.set(code, {
        data: school,
        timestamp: now
      })

      // Update school context
      schoolContext.value.school = {
        id: school.id || '',
        name: school.name || '',
        code: school.code || '',
        description: school.description || undefined,
        location: school.location || undefined,
        established: school.established || undefined,
        subscription_plan: school.subscription_plan || undefined,
        subscription_status: school.subscription_status || undefined,
        created_at: school.created_at || undefined,
        updated_at: school.updated_at || undefined
      }
      
      // Set loading to false only after school data is set
      schoolContext.value.isLoading = false

      console.log(`✅ School context loaded: ${school.name} (${school.code})`)
      return true

    } catch (error) {
      console.error('Error in loadSchoolData:', error)
      schoolContext.value.error = 'Failed to load school data'
      schoolContext.value.isLoading = false
      return false
    }
  }

  // Create school-filtered query builder
  const createSchoolQuery = (table: string) => {
    const schoolId = currentSchoolId.value
    if (!schoolId) {
      throw new Error('No school context available. Cannot create school-filtered query.')
    }

    return supabase
      .from(table)
      .select('*')
      .eq('school_id', schoolId)
  }

  // Helper functions for common school-filtered queries
  const getSchoolLessonPlans = async () => {
    if (!currentSchoolId.value) {
      console.warn('❌ No school ID available for lesson plans query')
      return { data: [], error: 'No school context' }
    }

    console.log(`🔍 Querying lesson plans for school ID: ${currentSchoolId.value}`)

    const result = await supabase
      .from('lesson_plans')
      .select('*')
      .eq('school_id', currentSchoolId.value)

    console.log(`📚 Lesson plans query result:`, result)

    return result
  }

  const getSchoolSubjects = async () => {
    if (!currentSchoolId.value) {
      console.warn('❌ No school ID available for subjects query')
      return { data: [], error: 'No school context' }
    }

    console.log(`🔍 Querying subjects for school ID: ${currentSchoolId.value}`)

    const result = await supabase
      .from('subjects')
      .select('*')
      .eq('school_id', currentSchoolId.value)

    console.log(`📊 Subjects query result:`, result)

    return result
  }

  const getSchoolTeachers = async () => {
    if (!currentSchoolId.value) return { data: [], error: 'No school context' }
    
    return await supabase
      .from('school_memberships')
      .select(`
        *,
        profiles:user_id (
          id,
          full_name,
          email,
          avatar_url
        )
      `)
      .eq('school_id', currentSchoolId.value)
      .eq('role', 'teacher')
      .eq('status', 'active')
  }

  const getSchoolClasses = async () => {
    if (!currentSchoolId.value) return { data: [], error: 'No school context' }
    
    return await supabase
      .from('classes')
      .select('*')
      .eq('school_id', currentSchoolId.value)
  }

  // Initialize school context if we have a school code
  const initializeSchoolContext = async () => {
    const code = currentSchoolCode.value
    
    // If we already have school data, don't reload unless school code changed
    if (schoolContext.value.school && schoolContext.value.school.code === code) {
      console.log(`✅ School context already loaded: ${schoolContext.value.school.name}`)
      schoolContext.value.isLoading = false
      return
    }
    
    if (code && typeof code === 'string') {
      // Check cache first for immediate loading
      const cached = schoolDataCache.get(code)
      if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
        // Load from cache immediately to prevent hydration mismatch
        schoolContext.value.school = {
          id: cached.data.id || '',
          name: cached.data.name || '',
          code: cached.data.code || '',
          description: cached.data.description || undefined,
          location: cached.data.location || undefined,
          established: cached.data.established || undefined,
          subscription_plan: cached.data.subscription_plan || undefined,
          subscription_status: cached.data.subscription_status || undefined,
          created_at: cached.data.created_at || undefined,
          updated_at: cached.data.updated_at || undefined
        }
        schoolContext.value.isLoading = false
        console.log(`✅ School context loaded from cache immediately: ${cached.data.name}`)
      } else {
        // Load from API
        await loadSchoolData()
      }
    } else {
      schoolContext.value.isLoading = false
    }
  }

  // Preload school data for faster navigation
  const preloadSchoolData = async (schoolCode: string) => {
    if (typeof schoolCode === 'string' && !schoolDataCache.get(schoolCode)) {
      // Preload in background without affecting UI
      try {
        const { data: school } = await supabase
          .from('schools')
          .select('id, name, code, description, location, established, subscription_plan, subscription_status, created_at, updated_at')
          .eq('code', schoolCode)
          .single() as { data: any, error: any }
        
        if (school) {
          schoolDataCache.set(schoolCode, {
            data: school,
            timestamp: Date.now()
          })
          console.log(`🚀 Preloaded school data: ${school.name}`)
        }
      } catch (error) {
        // Silently fail preloading
        console.log(`Failed to preload school ${schoolCode}:`, error)
      }
    }
  }

  // Reset school context
  const resetSchoolContext = () => {
    schoolContext.value = {
      school: null,
      membership: null,
      isLoading: false,
      error: null
    }
  }

  return {
    // State
    schoolContext: readonly(schoolContext),
    currentSchoolCode,
    currentSchoolId,
    isSchoolContext,
    
    // Methods
    loadSchoolData,
    initializeSchoolContext,
    preloadSchoolData,
    resetSchoolContext,
    createSchoolQuery,
    
    // Helper queries
    getSchoolLessonPlans,
    getSchoolSubjects,
    getSchoolTeachers,
    getSchoolClasses
  }
}
