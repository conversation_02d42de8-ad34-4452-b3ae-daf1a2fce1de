// User profile management composable
// Created: 2025-07-13

export interface UserProfile {
  id: string
  email: string
  full_name: string
  avatar_url: string | null
  phone: string | null
  bio: string | null
  location: string | null
  website: string | null
  preferences: Record<string, any>
  created_at: string
  updated_at: string
  email_verified: boolean
  last_sign_in: string | null
}

export interface ProfileUpdateData {
  full_name?: string
  avatar_url?: string | null
  phone?: string | null
  bio?: string | null
  location?: string | null
  website?: string | null
  preferences?: Record<string, any>
}

export const useProfile = () => {
  const supabase = useSupabaseClient()
  const user = useSupabaseUser()
  
  // State
  const profile = ref<UserProfile | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isUpdating = ref(false)

  // Fetch user profile
  const fetchProfile = async () => {
    if (!user.value) {
      profile.value = null
      return
    }

    try {
      isLoading.value = true
      error.value = null

      // Get auth token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.access_token) {
        throw new Error('Authentication required')
      }

      // Fetch profile using API
      const response = await $fetch('/api/profile', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      }) as any

      if (response.success) {
        profile.value = response.profile
      } else {
        throw new Error(response.error || 'Failed to fetch profile')
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch profile'
      console.error('Error fetching profile:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Update user profile
  const updateProfile = async (updateData: ProfileUpdateData) => {
    if (!user.value) {
      throw new Error('User not authenticated')
    }

    try {
      isUpdating.value = true
      error.value = null

      // Get auth token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.access_token) {
        throw new Error('Authentication required')
      }

      // Update profile using API
      const response = await $fetch('/api/profile', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: updateData
      }) as any

      if (response.success) {
        profile.value = response.profile
        return response.profile
      } else {
        throw new Error(response.error || 'Failed to update profile')
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to update profile'
      console.error('Error updating profile:', err)
      throw err
    } finally {
      isUpdating.value = false
    }
  }

  // Update specific preference
  const updatePreference = async (key: string, value: any) => {
    if (!profile.value) {
      throw new Error('Profile not loaded')
    }

    const newPreferences = {
      ...profile.value.preferences,
      [key]: value
    }

    return updateProfile({ preferences: newPreferences })
  }

  // Get specific preference
  const getPreference = (key: string, defaultValue: any = null) => {
    return profile.value?.preferences?.[key] ?? defaultValue
  }

  // Upload avatar
  const uploadAvatar = async (file: File) => {
    if (!user.value) {
      throw new Error('User not authenticated')
    }

    try {
      isUpdating.value = true
      error.value = null

      // Generate unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.value.id}-${Date.now()}.${fileExt}`
      const filePath = `avatars/${fileName}`

      // Upload file to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (uploadError) {
        throw uploadError
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath)

      const avatarUrl = urlData.publicUrl

      // Update profile with new avatar URL
      await updateProfile({ avatar_url: avatarUrl })

      return avatarUrl
    } catch (err: any) {
      error.value = err.message || 'Failed to upload avatar'
      console.error('Error uploading avatar:', err)
      throw err
    } finally {
      isUpdating.value = false
    }
  }

  // Remove avatar
  const removeAvatar = async () => {
    return updateProfile({ avatar_url: null })
  }

  // Initialize profile when user changes
  watch(user, (newUser) => {
    if (newUser) {
      fetchProfile()
    } else {
      profile.value = null
    }
  }, { immediate: true })

  // Computed properties
  const displayName = computed(() => {
    return profile.value?.full_name || profile.value?.email || 'User'
  })

  const initials = computed(() => {
    const name = displayName.value
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('')
  })

  const hasAvatar = computed(() => {
    return !!profile.value?.avatar_url
  })

  const isProfileComplete = computed(() => {
    return !!(profile.value?.full_name && profile.value?.full_name.trim())
  })

  return {
    // State
    profile: readonly(profile),
    isLoading: readonly(isLoading),
    error: readonly(error),
    isUpdating: readonly(isUpdating),

    // Computed
    displayName: readonly(displayName),
    initials: readonly(initials),
    hasAvatar: readonly(hasAvatar),
    isProfileComplete: readonly(isProfileComplete),

    // Methods
    fetchProfile,
    updateProfile,
    updatePreference,
    getPreference,
    uploadAvatar,
    removeAvatar
  }
}
