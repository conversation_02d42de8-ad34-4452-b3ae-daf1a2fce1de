<template>
  <button
    type="button"
    :class="[
      'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
      modelValue ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-600',
      disabled ? 'opacity-50 cursor-not-allowed' : ''
    ]"
    :disabled="disabled"
    @click="toggle"
    :aria-pressed="modelValue"
    :aria-labelledby="labelId"
  >
    <span class="sr-only">{{ label || 'Toggle' }}</span>
    <span
      :class="[
        'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
        modelValue ? 'translate-x-5' : 'translate-x-0'
      ]"
    />
  </button>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean;
  disabled?: boolean;
  label?: string;
  labelId?: string;
}

interface Emits {
  (e: 'update:model-value', value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  label: undefined,
  labelId: undefined
});

const emit = defineEmits<Emits>();

const toggle = () => {
  if (!props.disabled) {
    emit('update:model-value', !props.modelValue);
  }
};
</script>
