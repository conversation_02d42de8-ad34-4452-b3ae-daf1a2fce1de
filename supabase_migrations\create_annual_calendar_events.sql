-- Create annual_calendar_events table for school calendar management
CREATE TABLE IF NOT EXISTS annual_calendar_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- User/Teacher who created the event
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Event basic information
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL CHECK (category IN (
        'ACADEMIC',      -- Academic activities (exams, assessments)
        'HOLIDAY',       -- Public holidays and school breaks
        'COCURRICULAR',  -- Co-curricular activities and sports
        'MEETING',       -- Staff meetings and parent meetings
        'CEREMONY',      -- School ceremonies and assemblies
        'TRAINING',      -- Professional development and training
        'OTHER'          -- Other miscellaneous events
    )),
    
    -- Date and time information
    start_date DATE NOT NULL,
    end_date DATE,                    -- For multi-day events
    start_time TIME,                  -- For timed events
    end_time TIME,                    -- For timed events
    is_all_day BOOLEAN NOT NULL DEFAULT true,
    
    -- Location
    location TEXT,
    
    -- Recurrence information
    is_recurring BOOLEAN NOT NULL DEFAULT false,
    recurrence_pattern JSONB,        -- Stores recurrence details
    
    -- Custom styling
    color TEXT,                      -- Custom color override
    
    -- Indexes for performance
    CONSTRAINT annual_calendar_events_user_id_idx 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_user_id 
    ON annual_calendar_events(user_id);

CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_start_date 
    ON annual_calendar_events(start_date);

CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_category 
    ON annual_calendar_events(category);

CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_date_range 
    ON annual_calendar_events(start_date, end_date);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_user_date_category 
    ON annual_calendar_events(user_id, start_date, category);

-- Enable Row Level Security (RLS)
ALTER TABLE annual_calendar_events ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own events
CREATE POLICY "Users can view their own calendar events" 
    ON annual_calendar_events FOR SELECT 
    USING (auth.uid() = user_id);

-- Users can insert their own events
CREATE POLICY "Users can insert their own calendar events" 
    ON annual_calendar_events FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- Users can update their own events
CREATE POLICY "Users can update their own calendar events" 
    ON annual_calendar_events FOR UPDATE 
    USING (auth.uid() = user_id);

-- Users can delete their own events
CREATE POLICY "Users can delete their own calendar events" 
    ON annual_calendar_events FOR DELETE 
    USING (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_annual_calendar_events_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER trigger_update_annual_calendar_events_updated_at
    BEFORE UPDATE ON annual_calendar_events
    FOR EACH ROW
    EXECUTE FUNCTION update_annual_calendar_events_updated_at();

-- Insert some sample Malaysian school calendar events for testing
-- (These would typically be inserted by school administrators)
INSERT INTO annual_calendar_events (
    user_id, title, description, category, start_date, end_date, is_all_day
) VALUES 
-- Note: Replace with actual user_id in production
-- These are sample events for Malaysian school calendar
(
    (SELECT id FROM auth.users LIMIT 1), -- Get first user for demo
    'Cuti Tahun Baru',
    'Cuti umum Tahun Baru Masihi',
    'HOLIDAY',
    '2024-01-01',
    '2024-01-01',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Thaipusam',
    'Cuti umum Hari Thaipusam',
    'HOLIDAY',
    '2024-01-25',
    '2024-01-25',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Tahun Baru Cina',
    'Cuti umum Tahun Baru Cina',
    'HOLIDAY',
    '2024-02-10',
    '2024-02-11',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Peperiksaan Pertengahan Tahun',
    'Peperiksaan pertengahan tahun untuk semua tingkatan',
    'ACADEMIC',
    '2024-05-15',
    '2024-05-25',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Wesak',
    'Cuti umum Hari Wesak',
    'HOLIDAY',
    '2024-05-22',
    '2024-05-22',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Gawai',
    'Cuti umum Hari Gawai (Sarawak)',
    'HOLIDAY',
    '2024-06-01',
    '2024-06-02',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Kebangsaan',
    'Sambutan Hari Kemerdekaan Malaysia',
    'CEREMONY',
    '2024-08-31',
    '2024-08-31',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Malaysia',
    'Sambutan Hari Malaysia',
    'CEREMONY',
    '2024-09-16',
    '2024-09-16',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Peperiksaan Akhir Tahun',
    'Peperiksaan akhir tahun untuk semua tingkatan',
    'ACADEMIC',
    '2024-10-15',
    '2024-10-30',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Deepavali',
    'Cuti umum Hari Deepavali',
    'HOLIDAY',
    '2024-10-31',
    '2024-10-31',
    true
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Mesyuarat Ibu Bapa',
    'Mesyuarat ibu bapa dan guru semester pertama',
    'MEETING',
    '2024-07-20',
    '2024-07-20',
    false
),
(
    (SELECT id FROM auth.users LIMIT 1),
    'Hari Sukan Sekolah',
    'Hari sukan tahunan sekolah',
    'COCURRICULAR',
    '2024-08-15',
    '2024-08-15',
    true
);

-- Add comment to table
COMMENT ON TABLE annual_calendar_events IS 'Stores annual school calendar events including holidays, academic activities, meetings, and other school events';

-- Add comments to important columns
COMMENT ON COLUMN annual_calendar_events.recurrence_pattern IS 'JSONB field storing recurrence details like type (daily/weekly/monthly/yearly), interval, and end_date';
COMMENT ON COLUMN annual_calendar_events.category IS 'Event category: ACADEMIC, HOLIDAY, COCURRICULAR, MEETING, CEREMONY, TRAINING, OTHER';
COMMENT ON COLUMN annual_calendar_events.is_all_day IS 'Whether the event is an all-day event or has specific start/end times';
