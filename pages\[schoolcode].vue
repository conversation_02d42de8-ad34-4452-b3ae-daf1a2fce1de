<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo and School Info -->
          <div class="flex items-center space-x-4">
            <NuxtLink to="/" class="flex items-center space-x-2">
              <UiBaseIcon name="heroicons:academic-cap" class="h-8 w-8 text-blue-600" />
              <span class="text-xl font-bold text-gray-900 dark:text-white">RPHMate</span>
            </NuxtLink>
            <div class="hidden md:block">
              <span class="text-gray-400">|</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">
                {{ schoolInfo.name }} Admin Dashboard
              </span>
            </div>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              Welcome, {{ adminInfo.name }}
            </span>
            <button @click="logout" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
              <UiBaseIcon name="heroicons:arrow-right-on-rectangle" class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          School Administration
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-300">
          Manage your school's subscription, billing, and settings
        </p>
        <div v-if="isLoading" class="mt-4 flex items-center text-blue-600">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
          <span>Loading school information...</span>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Access School Portal -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <UiBaseIcon name="heroicons:building-office-2" class="h-8 w-8 text-blue-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">School Portal</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">Access your school's main application</p>
            </div>
          </div>
          <div class="mt-4">
            <a 
              :href="schoolPortalUrl" 
              target="_blank"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Open Portal
              <UiBaseIcon name="heroicons:arrow-top-right-on-square" class="ml-2 h-4 w-4" />
            </a>
          </div>
        </div>

        <!-- Subscription Status -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <UiBaseIcon name="heroicons:credit-card" class="h-8 w-8 text-green-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">Subscription</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ subscriptionInfo.plan }} Plan</p>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-300">Status:</span>
              <span :class="subscriptionStatusClass">{{ subscriptionInfo.status }}</span>
            </div>
            <div class="flex items-center justify-between mt-1">
              <span class="text-sm text-gray-600 dark:text-gray-300">Next billing:</span>
              <span class="text-sm text-gray-900 dark:text-white">{{ subscriptionInfo.nextBilling }}</span>
            </div>
          </div>
        </div>

        <!-- Teacher Count -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <UiBaseIcon name="heroicons:users" class="h-8 w-8 text-purple-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">Teachers</h3>
              <p class="text-sm text-gray-600 dark:text-gray-300">{{ teacherInfo.active }} of {{ teacherInfo.limit }} active</p>
            </div>
          </div>
          <div class="mt-4">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                class="bg-purple-600 h-2 rounded-full" 
                :style="{ width: `${(teacherInfo.active / teacherInfo.limit) * 100}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Sections -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Billing & Subscription -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Billing & Subscription</h2>
          </div>
          <div class="p-6 space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-300">Current Plan:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ subscriptionInfo.plan }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-300">Monthly Cost:</span>
              <span class="font-medium text-gray-900 dark:text-white">RM{{ subscriptionInfo.cost }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-300">Payment Method:</span>
              <span class="font-medium text-gray-900 dark:text-white">•••• •••• •••• {{ subscriptionInfo.lastFour }}</span>
            </div>
            
            <div class="pt-4 space-y-2">
              <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium">
                Change Plan
              </button>
              <button class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-2 px-4 rounded-md text-sm font-medium">
                Update Payment Method
              </button>
              <button class="w-full text-red-600 hover:text-red-700 py-2 px-4 rounded-md text-sm font-medium">
                Cancel Subscription
              </button>
            </div>
          </div>
        </div>

        <!-- School Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">School Information</h2>
          </div>
          <div class="p-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">School Name</label>
              <input 
                v-model="schoolInfo.name" 
                type="text" 
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">School Code</label>
              <input 
                :value="schoolInfo.code" 
                type="text" 
                disabled
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
              />
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                School URL: {{ schoolPortalUrl }}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Address</label>
              <textarea 
                v-model="schoolInfo.address" 
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              ></textarea>
            </div>
            
            <div class="pt-4">
              <button class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-sm font-medium">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Recent Activity</h2>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="activity in recentActivity" :key="activity.id" class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <UiBaseIcon :name="activity.icon" class="h-5 w-5 text-gray-400" />
              </div>
              <div class="flex-1">
                <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Get school code from route
const route = useRoute()
const schoolCode = route.params.schoolcode as string

// Set page meta
definePageMeta({
  layout: 'default',
  middleware: 'school-admin-auth'
})

// Reactive data
const schoolInfo = ref({
  name: 'Loading...',
  code: schoolCode,
  address: ''
})

const adminInfo = ref({
  name: 'Loading...',
  email: ''
})

const subscriptionInfo = ref({
  plan: 'Loading...',
  status: 'Loading...',
  cost: '0',
  nextBilling: 'Loading...',
  lastFour: '0000'
})

const teacherInfo = ref({
  active: 0,
  limit: 0
})

const isLoading = ref(true)

const recentActivity = ref([
  {
    id: 1,
    icon: 'heroicons:user-plus',
    description: 'New teacher Sarah Johnson joined',
    time: '2 hours ago'
  },
  {
    id: 2,
    icon: 'heroicons:credit-card',
    description: 'Monthly payment processed successfully',
    time: '1 day ago'
  },
  {
    id: 3,
    icon: 'heroicons:cog-6-tooth',
    description: 'School settings updated',
    time: '3 days ago'
  }
])

// Computed properties
const schoolPortalUrl = computed(() => {
  if (process.env.NODE_ENV === 'development') {
    return `http://${schoolCode}.localhost:3000`
  }
  return `https://${schoolCode}.rphmate.com`
})

const subscriptionStatusClass = computed(() => {
  switch (subscriptionInfo.value.status.toLowerCase()) {
    case 'active':
      return 'text-green-600 dark:text-green-400 font-medium'
    case 'trial':
      return 'text-blue-600 dark:text-blue-400 font-medium'
    case 'expired':
      return 'text-red-600 dark:text-red-400 font-medium'
    default:
      return 'text-gray-600 dark:text-gray-400'
  }
})

// Methods
const logout = async () => {
  try {
    const supabase = useSupabaseClient()
    await supabase.auth.signOut()
    await navigateTo('/login')
  } catch (error) {
    console.error('Error logging out:', error)
    await navigateTo('/login')
  }
}

// Fetch school data
const fetchSchoolData = async () => {
  isLoading.value = true

  try {
    const supabase = useSupabaseClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      throw new Error('No active session')
    }

    // Get user info
    const { data: { user } } = await supabase.auth.getUser()

    if (user) {
      adminInfo.value.name = user.user_metadata?.full_name || user.email || 'School Admin'
      adminInfo.value.email = user.email || ''
    }

    // Get school info
    const { data: schoolData, error: schoolError } = await supabase
      .from('schools')
      .select('*')
      .eq('code', schoolCode)
      .single()

    if (schoolError) {
      console.error('Error fetching school:', schoolError)
      // Use fallback data for development
      schoolInfo.value = {
        name: `${schoolCode.toUpperCase()} School`,
        code: schoolCode,
        address: 'School Address'
      }
    } else if (schoolData) {
      const typedSchoolData = schoolData as any

      schoolInfo.value = {
        name: typedSchoolData.name || `${schoolCode.toUpperCase()} School`,
        code: typedSchoolData.code || schoolCode,
        address: typedSchoolData.address || ''
      }

      // Set subscription info
      subscriptionInfo.value = {
        plan: typedSchoolData.subscription_plan || 'Professional',
        status: typedSchoolData.subscription_status || 'Active',
        cost: '199', // Default cost
        nextBilling: typedSchoolData.subscription_expires_at ?
          new Date(typedSchoolData.subscription_expires_at).toLocaleDateString() :
          'Not set',
        lastFour: '4242' // Default last four
      }
    }

  } catch (error) {
    console.error('Error fetching school data:', error)

    // Fallback data for development
    schoolInfo.value = {
      name: `${schoolCode.toUpperCase()} School`,
      code: schoolCode,
      address: 'School Address'
    }

    subscriptionInfo.value = {
      plan: 'Professional',
      status: 'Active',
      cost: '199',
      nextBilling: 'Jan 15, 2025',
      lastFour: '4242'
    }

    teacherInfo.value = {
      active: 12,
      limit: 50
    }
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchSchoolData()
})

// Set page head
useHead({
  title: `${schoolInfo.value.name} - Admin Dashboard - RPHMate`,
  meta: [
    {
      name: 'description',
      content: `Manage ${schoolInfo.value.name} subscription, billing, and settings on RPHMate.`
    }
  ]
})
</script>

<style scoped>
/* Custom styles for admin dashboard */
</style>
