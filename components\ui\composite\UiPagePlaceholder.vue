<template>
  <div class="flex flex-col items-center justify-center min-h-[calc(100vh-12rem)] text-center p-8">
    <div class="w-24 h-24 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mb-6">
      <Icon :name="icon" class="w-12 h-12 text-white" />
    </div>
    <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-3">{{ title }}</h1>
    <p class="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-md">
      This page is currently under construction. Check back soon for updates!
    </p>
    <NuxtLink to="/">
      <Button variant="primary" size="lg">
        <Icon name="heroicons:arrow-left-solid" class="w-5 h-5 mr-2" />
        Go Back Home
      </Button>
    </NuxtLink>
  </div>
</template>

<script setup lang="ts">
import Button from '~/components/ui/base/Button.vue';
import Icon from '~/components/ui/base/Icon.vue';

defineProps<{
  title: string;
  icon: string;
}>();
</script>
