// Global middleware to handle Google OAuth validation on login page
export default defineNuxtRouteMiddleware(async (to) => {
  // Only run on login page with validate query parameter
  if (to.path !== '/login' || to.query.validate !== 'true') {
    return
  }

  const supabase = useSupabaseClient()
  
  try {
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      // No session - show error modal
      // We'll handle this in the page component since we can't show modals from middleware
      return
    }

    // Check if user has schools (is admin)
    const response = await $fetch('/api/schools/user-schools', {
      headers: {
        'Authorization': `Bearer ${session.access_token}`
      }
    }).catch(() => ({ success: false, schools: [] }))

    if (!response.success || response.schools.length === 0) {
      // NOT a school admin - sign out completely and redirect to login with error
      await supabase.auth.signOut({ scope: 'global' })

      // Clear any cached session data
      if (process.client) {
        localStorage.removeItem('supabase.auth.token')
        sessionStorage.clear()
      }

      return navigateTo('/login?error=not-school-admin')
    }

    // IS a school admin - redirect to dashboard
    const primarySchool = response.schools[0]
    return navigateTo(`/${primarySchool.code}`)

  } catch (error) {
    console.error('Login validation error:', error)
    await supabase.auth.signOut({ scope: 'global' })

    // Clear any cached session data
    if (process.client) {
      localStorage.removeItem('supabase.auth.token')
      sessionStorage.clear()
    }

    return navigateTo('/pricing?error=validation-failed')
  }
})
