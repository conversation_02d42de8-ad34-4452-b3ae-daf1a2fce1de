// Simple debug script to test Supabase connection
console.log('=== SUPABASE CONNECTION TEST ===');

async function testSupabaseConnection() {
  try {
    // Get the Supabase client from Nuxt
    const { $supabase } = useNuxtApp();
    
    console.log('1. Testing basic connection...');
    
    // Test 1: Simple count query
    const { data: countData, error: countError } = await $supabase
      .from('lesson_plan_reflections')
      .select('*', { count: 'exact', head: true });
      
    if (countError) {
      console.error('Count query failed:', countError);
    } else {
      console.log('Count query succeeded, count:', countData);
    }
    
    // Test 2: Simple select query
    console.log('2. Testing select query...');
    const { data: selectData, error: selectError } = await $supabase
      .from('lesson_plan_reflections')
      .select('*')
      .limit(1);
      
    if (selectError) {
      console.error('Select query failed:', selectError);
    } else {
      console.log('Select query succeeded, data:', selectData);
    }
    
    // Test 3: Check user state
    console.log('3. Checking user state...');
    const user = await $supabase.auth.getUser();
    console.log('User state:', user);
    
  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

// Run the test when DOM is loaded
if (typeof window !== 'undefined') {
  window.testSupabaseConnection = testSupabaseConnection;
  console.log('Run window.testSupabaseConnection() in browser console to test');
}
