import { useSupabaseUser, useS<PERSON><PERSON>se<PERSON><PERSON>, navigateTo } from "#imports";

interface Profile {
  is_profile_complete: boolean;
}

// Cache for profile completion status to improve performance
const profileCompletionCache = new Map<string, { isComplete: boolean, timestamp: number }>()
const PROFILE_CACHE_DURATION = 2 * 60 * 1000 // 2 minutes

export default defineNuxtRouteMiddleware(async (to) => {
  // OPTIMIZED: Only run for authenticated users on school subdomains

  // Skip on main domain
  const isMainDomain = process.client ?
    (window.location.hostname === 'localhost' || !window.location.hostname.includes('.')) :
    !useState('currentSubdomain').value;

  if (isMainDomain) {
    return;
  }

  // Skip for auth routes - let them load first
  const allowedPaths = [
    "/auth/login",
    "/auth/daftar",
    "/auth/confirm",
    "/auth/butir-asas",
  ];

  if (allowedPaths.includes(to.path)) {
    return;
  }

  const user = useSupabaseUser();

  // Skip if user not authenticated - let auth middleware handle it
  if (!user.value) {
    return;
  }

  const client = useSupabaseClient();
  if (allowedPaths.includes(to.path)) {
    return;
  }

  // If user is not logged in, auth.global.ts should handle redirection to login.
  // This middleware only cares about logged-in users with incomplete profiles.
  if (!user.value) {
    return; // auth.global.ts will redirect to /auth/login
  }

  try {
    // Check cache first
    const cached = profileCompletionCache.get(user.value.id)
    const now = Date.now()

    let isProfileComplete = false

    if (cached && (now - cached.timestamp) < PROFILE_CACHE_DURATION) {
      // Use cached result
      isProfileComplete = cached.isComplete
    } else {
      // Fetch from database and cache result
      const { data, error } = await client
        .from("profiles")
        .select("is_profile_complete")
        .eq("id", user.value.id)
        .single<Profile>(); // Specify the type here

      if (error) {
        console.error("Error fetching profile status:", error);
        return;
      }

      isProfileComplete = data?.is_profile_complete || false
      profileCompletionCache.set(user.value.id, {
        isComplete: isProfileComplete,
        timestamp: now
      })
    }

    if (!isProfileComplete) {
      return navigateTo("/auth/butir-asas");
    }
  } catch (e) {
    console.error("Exception while checking profile status:", e);
    // Allow access by default if there's an unexpected error, or redirect to login/error page
  }
});
