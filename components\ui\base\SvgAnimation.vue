<template>
    <svg :width="size" :height="size" viewBox="0 0 200 200" class="animate-float">
        <!-- Background Circle -->
        <circle cx="100" cy="100" r="85" fill="white" opacity="0.1" stroke="white" stroke-width="1">
            <animate attributeName="r" values="85;90;85" dur="4s" repeatCount="indefinite" />
            <animate attributeName="opacity" values="0.1;0.2;0.1" dur="3s" repeatCount="indefinite" />
        </circle>

        <!-- Central Dashboard/Monitor -->
        <rect x="70" y="75" width="60" height="40" rx="4" fill="white" opacity="0.95">
            <animate attributeName="opacity" values="0.95;1;0.95" dur="2.5s" repeatCount="indefinite" />
        </rect>
        <rect x="72" y="77" width="56" height="36" rx="2" fill="#f8fafc" stroke="#e2e8f0" stroke-width="0.5" />

        <!-- Screen Content - Charts/Data -->
        <rect x="75" y="82" width="15" height="8" fill="#3b82f6" opacity="0.8">
            <animate attributeName="height" values="8;12;8" dur="2s" repeatCount="indefinite" />
        </rect>
        <rect x="93" y="85" width="15" height="5" fill="#10b981" opacity="0.8">
            <animate attributeName="height" values="5;10;5" dur="2.3s" repeatCount="indefinite" />
        </rect>
        <rect x="111" y="80" width="15" height="10" fill="#f59e0b" opacity="0.8">
            <animate attributeName="height" values="10;15;10" dur="1.8s" repeatCount="indefinite" />
        </rect>

        <!-- Screen Lines (Data visualization) -->
        <line x1="75" y1="95" x2="125" y2="95" stroke="#e2e8f0" stroke-width="0.5" />
        <line x1="75" y1="100" x2="115" y2="100" stroke="#e2e8f0" stroke-width="0.5" />
        <line x1="75" y1="105" x2="120" y2="105" stroke="#e2e8f0" stroke-width="0.5" />

        <!-- Floating Data Points -->
        <circle cx="140" cy="70" r="3" fill="white" opacity="0.9">
            <animateTransform attributeName="transform" type="translate" values="0,0;-5,-5;0,0;5,5;0,0" dur="3s"
                repeatCount="indefinite" />
            <animate attributeName="opacity" values="0.9;0.6;0.9" dur="2s" repeatCount="indefinite" />
        </circle>
        <circle cx="60" cy="65" r="2.5" fill="white" opacity="0.8">
            <animateTransform attributeName="transform" type="translate" values="0,0;3,-3;0,0;-3,3;0,0" dur="2.5s"
                repeatCount="indefinite" />
        </circle>
        <circle cx="150" cy="130" r="2" fill="white" opacity="0.7">
            <animateTransform attributeName="transform" type="translate" values="0,0;-3,3;0,0;3,-3;0,0" dur="3.5s"
                repeatCount="indefinite" />
        </circle>

        <!-- Network Connections -->
        <line x1="100" y1="115" x2="130" y2="140" stroke="white" stroke-width="1" opacity="0.6">
            <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2s" repeatCount="indefinite" />
        </line>
        <line x1="100" y1="115" x2="70" y2="140" stroke="white" stroke-width="1" opacity="0.6">
            <animate attributeName="opacity" values="0.6;0.9;0.6" dur="2.2s" repeatCount="indefinite" />
        </line>
        <line x1="100" y1="75" x2="140" y2="60" stroke="white" stroke-width="1" opacity="0.6">
            <animate attributeName="opacity" values="0.6;0.9;0.6" dur="1.8s" repeatCount="indefinite" />
        </line>

        <!-- Corner Elements - Modern geometric shapes -->
        <rect x="45" y="140" width="20" height="20" rx="3" fill="white" opacity="0.7" transform="rotate(15 55 150)">
            <animateTransform attributeName="transform" type="rotate" values="15 55 150;25 55 150;15 55 150" dur="4s"
                repeatCount="indefinite" />
        </rect>

        <polygon points="155,45 165,55 155,65 145,55" fill="white" opacity="0.6">
            <animateTransform attributeName="transform" type="rotate"
                values="0 155 55;10 155 55;0 155 55;-10 155 55;0 155 55" dur="5s" repeatCount="indefinite" />
        </polygon>

        <!-- Progress Ring -->
        <circle cx="100" cy="100" r="75" fill="none" stroke="white" stroke-width="1" opacity="0.3"
            stroke-dasharray="10,5">
            <animateTransform attributeName="transform" type="rotate" values="0 100 100;360 100 100" dur="20s"
                repeatCount="indefinite" />
        </circle>
    </svg>
</template>

<script setup lang="ts">
defineProps<{
    size?: number | string;
}>();
</script>

<style scoped>
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-8px);
    }
}

.animate-float {
    animation: float 4s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(255, 255, 255, 0.1));
}
</style>
