# Time Slot Modal Z-Index Fix

## Problem
When editing a time slot in the timetable and then clicking the "Padam" (delete) button, the delete confirmation modal would appear behind the time slot edit modal, making it inaccessible to users.

## Root Cause
Both modals were using the same default z-index value of 60:
- **Time Slot Edit Modal**: Uses `Modal` component with default z-index 60
- **Time Slot Delete Confirmation Modal**: Uses `UiCompositeDeleteConfirmationModal` (which wraps `Modal`) with default z-index 60

## Solution
Added an explicit `:z-index="70"` prop to the Time Slot Delete Confirmation Modal in `TimetableView.vue`:

```vue
<!-- Time Slot Delete Confirmation Modal -->
<UiCompositeDeleteConfirmationModal :is-open="isTimeSlotDeleteModalOpen" item-type="slot waktu"
    :item-name="editingTimeSlot ? `Waktu ${editingTimeSlot.period_number + 1}` : ''"
    :item-subtitle="editingTimeSlot ? `${formatTime(editingTimeSlot.start_time)} - ${formatTime(editingTimeSlot.end_time)}` : ''"
    warning-message="Slot waktu ini akan dipadam secara kekal daripada jadual anda. Tindakan ini tidak boleh dibatalkan."
    :loading="isDeletingTimeSlot" loading-text="Memadam slot waktu..."
    :z-index="70" @confirm="confirmDeleteTimeSlot" @cancel="isTimeSlotDeleteModalOpen = false" />
```

## Z-Index Hierarchy
- **Time Slot Edit Modal**: z-index 60 (default)
- **Time Slot Delete Confirmation Modal**: z-index 70 (explicit)
- **Other confirmation modals**: z-index 60 (default)

## Testing
To verify the fix works:

1. Navigate to the timetable page (`/schedule`)
2. Click on any time slot header (e.g., "08:00 - 08:40") when in edit mode
3. Click the "Padam" button in the time slot edit modal
4. The delete confirmation modal should now appear **above** the edit modal
5. You should be able to interact with the delete confirmation modal properly

## Technical Implementation
- The `Modal` component supports custom z-index via props
- Tailwind CSS config includes custom z-index values: `z-60`, `z-70`, `z-80`, `z-90`, `z-100`
- The modal's `zIndexClass` computed property maps numeric values to Tailwind classes
- Uses Teleport to body to ensure proper stacking context

## Files Modified
- `components/schedule/TimetableView.vue`: Added `:z-index="70"` to time slot delete confirmation modal
