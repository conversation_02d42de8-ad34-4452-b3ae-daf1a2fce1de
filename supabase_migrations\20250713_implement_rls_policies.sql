-- Migration: Implement Row Level Security policies for multi-tenant data isolation
-- Created: 2025-07-13
-- Description: Create RLS policies to ensure users can only access data from their associated schools

BEGIN;

-- =====================================================
-- ENABLE RLS ON ALL TABLES WITH SCHOOL_ID
-- =====================================================

-- Enable RLS on all user-data tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_plan_detailed_reflections ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE timetable_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE observation_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_observer_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rph_weeks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_week_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_calendar_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE annual_calendar_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE dskp_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE rpt_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE jadual_pencerapan ENABLE ROW LEVEL SECURITY;
ALTER TABLE tidak_terlaksana ENABLE ROW LEVEL SECURITY;
ALTER TABLE tindakan_susulan ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_reflection_template_preferences ENABLE ROW LEVEL SECURITY;

-- Enable RLS on global/school-specific tables
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE reflection_templates ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- CREATE HELPER FUNCTION FOR USER SCHOOL ACCESS
-- =====================================================

-- Function to get schools that a user has access to
CREATE OR REPLACE FUNCTION get_user_school_ids(user_uuid UUID DEFAULT auth.uid())
RETURNS UUID[] AS $$
BEGIN
    RETURN ARRAY(
        SELECT school_id 
        FROM school_memberships 
        WHERE user_id = user_uuid 
        AND status = 'active'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific role in any school
CREATE OR REPLACE FUNCTION user_has_role_in_schools(required_role TEXT, user_uuid UUID DEFAULT auth.uid())
RETURNS UUID[] AS $$
BEGIN
    RETURN ARRAY(
        SELECT school_id 
        FROM school_memberships 
        WHERE user_id = user_uuid 
        AND status = 'active'
        AND role = required_role
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- CREATE RLS POLICIES FOR USER-DATA TABLES
-- =====================================================

-- 1. PROFILES - Users can access profiles from their schools
DROP POLICY IF EXISTS "Users can access school profiles" ON profiles;
CREATE POLICY "Users can access school profiles" ON profiles
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 2. LESSON PLANS - Users can access lesson plans from their schools
DROP POLICY IF EXISTS "Users can access school lesson plans" ON lesson_plans;
CREATE POLICY "Users can access school lesson plans" ON lesson_plans
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 3. LESSON PLAN DETAILED REFLECTIONS - Users can access reflections from their schools
DROP POLICY IF EXISTS "Users can access school reflections" ON lesson_plan_detailed_reflections;
CREATE POLICY "Users can access school reflections" ON lesson_plan_detailed_reflections
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 4. TEACHER SCHEDULES - Users can access schedules from their schools
DROP POLICY IF EXISTS "Users can access school schedules" ON teacher_schedules;
CREATE POLICY "Users can access school schedules" ON teacher_schedules
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 5. TIMETABLE ENTRIES - Users can access timetables from their schools
DROP POLICY IF EXISTS "Users can access school timetables" ON timetable_entries;
CREATE POLICY "Users can access school timetables" ON timetable_entries
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 6. OBSERVATION SCHEDULES - Users can access observations from their schools
DROP POLICY IF EXISTS "Users can access school observations" ON observation_schedules;
CREATE POLICY "Users can access school observations" ON observation_schedules
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 7. TEACHER ACTIVITIES - Users can access activities from their schools
DROP POLICY IF EXISTS "Users can access school activities" ON teacher_activities;
CREATE POLICY "Users can access school activities" ON teacher_activities
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 8. TEACHER TASKS - Users can access tasks from their schools
DROP POLICY IF EXISTS "Users can access school tasks" ON teacher_tasks;
CREATE POLICY "Users can access school tasks" ON teacher_tasks
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 9. TEACHER OBSERVER ASSIGNMENTS - Users can access assignments from their schools
DROP POLICY IF EXISTS "Users can access school observer assignments" ON teacher_observer_assignments;
CREATE POLICY "Users can access school observer assignments" ON teacher_observer_assignments
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 10. RPH WEEKS - Users can access RPH weeks from their schools
DROP POLICY IF EXISTS "Users can access school rph weeks" ON rph_weeks;
CREATE POLICY "Users can access school rph weeks" ON rph_weeks
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 11. USER WEEK SUBMISSIONS - Users can access submissions from their schools
DROP POLICY IF EXISTS "Users can access school week submissions" ON user_week_submissions;
CREATE POLICY "Users can access school week submissions" ON user_week_submissions
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 12. ACADEMIC CALENDAR DOCUMENTS - Users can access documents from their schools
DROP POLICY IF EXISTS "Users can access school academic documents" ON academic_calendar_documents;
CREATE POLICY "Users can access school academic documents" ON academic_calendar_documents
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 13. ANNUAL CALENDAR EVENTS - Users can access events from their schools
DROP POLICY IF EXISTS "Users can access school calendar events" ON annual_calendar_events;
CREATE POLICY "Users can access school calendar events" ON annual_calendar_events
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 14. DSKP DOCUMENTS - Users can access DSKP documents from their schools
DROP POLICY IF EXISTS "Users can access school dskp documents" ON dskp_documents;
CREATE POLICY "Users can access school dskp documents" ON dskp_documents
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 15. RPT DOCUMENTS - Users can access RPT documents from their schools
DROP POLICY IF EXISTS "Users can access school rpt documents" ON rpt_documents;
CREATE POLICY "Users can access school rpt documents" ON rpt_documents
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 16. ITEMS - Users can access items from their schools
DROP POLICY IF EXISTS "Users can access school items" ON items;
CREATE POLICY "Users can access school items" ON items
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 17. JADUAL PENCERAPAN - Users can access observation schedules from their schools
DROP POLICY IF EXISTS "Users can access school jadual pencerapan" ON jadual_pencerapan;
CREATE POLICY "Users can access school jadual pencerapan" ON jadual_pencerapan
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 18. TIDAK TERLAKSANA - Users can access records from their schools
DROP POLICY IF EXISTS "Users can access school tidak terlaksana" ON tidak_terlaksana;
CREATE POLICY "Users can access school tidak terlaksana" ON tidak_terlaksana
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 19. TINDAKAN SUSULAN - Users can access follow-up actions from their schools
DROP POLICY IF EXISTS "Users can access school tindakan susulan" ON tindakan_susulan;
CREATE POLICY "Users can access school tindakan susulan" ON tindakan_susulan
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 20. USER PREFERENCES - Users can access preferences from their schools
DROP POLICY IF EXISTS "Users can access school preferences" ON user_preferences;
CREATE POLICY "Users can access school preferences" ON user_preferences
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- 21. USER REFLECTION TEMPLATE PREFERENCES - Users can access template preferences from their schools
DROP POLICY IF EXISTS "Users can access school template preferences" ON user_reflection_template_preferences;
CREATE POLICY "Users can access school template preferences" ON user_reflection_template_preferences
    FOR ALL USING (school_id = ANY(get_user_school_ids()));

-- =====================================================
-- CREATE RLS POLICIES FOR GLOBAL/SCHOOL-SPECIFIC TABLES
-- =====================================================

-- SUBJECTS - Global subjects (school_id IS NULL) + school-specific subjects
DROP POLICY IF EXISTS "Users can access global and school subjects" ON subjects;
CREATE POLICY "Users can access global and school subjects" ON subjects
    FOR ALL USING (
        school_id IS NULL -- Global subjects available to all
        OR school_id = ANY(get_user_school_ids()) -- School-specific subjects
    );

-- REFLECTION TEMPLATES - Global templates (school_id IS NULL) + school-specific templates
DROP POLICY IF EXISTS "Users can access global and school templates" ON reflection_templates;
CREATE POLICY "Users can access global and school templates" ON reflection_templates
    FOR ALL USING (
        school_id IS NULL -- Global templates available to all
        OR school_id = ANY(get_user_school_ids()) -- School-specific templates
    );

-- =====================================================
-- CREATE ADMIN-SPECIFIC POLICIES
-- =====================================================

-- School admins can view all data in their schools (additional access)
-- This is already covered by the main policies above, but we can add specific admin policies if needed

-- =====================================================
-- UPDATE EXISTING POLICIES ON SCHOOLS TABLE
-- =====================================================

-- Update the school members policy now that school_memberships exists
DROP POLICY IF EXISTS "School members can view school info" ON schools;
CREATE POLICY "School members can view school info" ON schools
    FOR SELECT USING (
        id = ANY(get_user_school_ids())
    );

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION get_user_school_ids(UUID) IS 'Get array of school IDs that a user has access to';
COMMENT ON FUNCTION user_has_role_in_schools(TEXT, UUID) IS 'Get array of school IDs where user has specific role';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify RLS is enabled on all tables
SELECT
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN (
    'profiles', 'lesson_plans', 'teacher_schedules', 'timetable_entries',
    'subjects', 'reflection_templates', 'schools', 'school_memberships'
)
ORDER BY tablename;

-- Show all policies created
SELECT
    schemaname,
    tablename,
    policyname,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

COMMIT;
