-- Migration: Create teacher tasks and activities tables for tugas-guru page
-- Created: 2025-01-09
-- Description: Create tables for managing teacher administrative tasks, co-curricular duties, student affairs, and external activities

BEGIN;

-- =====================================================
-- CREATE TEACHER_TASKS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS teacher_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- User reference
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Task categorization
    category TEXT NOT NULL CHECK (category IN ('pentadbiran', 'kokurikulum', 'hal_ehwal_murid')),
    
    -- Task content
    task_description TEXT NOT NULL CHECK (char_length(task_description) > 0),
    
    -- Optional fields for future enhancements
    is_completed BOOLEAN DEFAULT FALSE,
    priority TEXT CHECK (priority IN ('rendah', 'sederhana', 'tinggi')) DEFAULT 'sederhana',
    due_date DATE,
    notes TEXT,
    
    -- Constraints
    CONSTRAINT teacher_tasks_description_length CHECK (char_length(task_description) <= 500)
);

-- =====================================================
-- CREATE TEACHER_ACTIVITIES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS teacher_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- User reference
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Activity categorization
    category TEXT NOT NULL CHECK (category IN ('sukan', 'pertubuhan', 'sumbangan')),
    
    -- Activity content
    activity_description TEXT NOT NULL CHECK (char_length(activity_description) > 0),
    
    -- Optional fields for future enhancements
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE,
    end_date DATE,
    location TEXT,
    notes TEXT,
    
    -- Constraints
    CONSTRAINT teacher_activities_description_length CHECK (char_length(activity_description) <= 500),
    CONSTRAINT teacher_activities_date_order CHECK (end_date IS NULL OR start_date IS NULL OR end_date >= start_date)
);

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for teacher_tasks
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_user_id ON teacher_tasks (user_id);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_category ON teacher_tasks (category);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_user_category ON teacher_tasks (user_id, category);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_completed ON teacher_tasks (is_completed);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_due_date ON teacher_tasks (due_date) WHERE due_date IS NOT NULL;

-- Indexes for teacher_activities
CREATE INDEX IF NOT EXISTS idx_teacher_activities_user_id ON teacher_activities (user_id);
CREATE INDEX IF NOT EXISTS idx_teacher_activities_category ON teacher_activities (category);
CREATE INDEX IF NOT EXISTS idx_teacher_activities_user_category ON teacher_activities (user_id, category);
CREATE INDEX IF NOT EXISTS idx_teacher_activities_active ON teacher_activities (is_active);
CREATE INDEX IF NOT EXISTS idx_teacher_activities_dates ON teacher_activities (start_date, end_date) WHERE start_date IS NOT NULL;

-- =====================================================
-- CREATE UPDATED_AT TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_teacher_tasks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_teacher_activities_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_teacher_tasks_updated_at ON teacher_tasks;
CREATE TRIGGER trigger_teacher_tasks_updated_at
    BEFORE UPDATE ON teacher_tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_teacher_tasks_updated_at();

DROP TRIGGER IF EXISTS trigger_teacher_activities_updated_at ON teacher_activities;
CREATE TRIGGER trigger_teacher_activities_updated_at
    BEFORE UPDATE ON teacher_activities
    FOR EACH ROW
    EXECUTE FUNCTION update_teacher_activities_updated_at();

-- =====================================================
-- ADD ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS
ALTER TABLE teacher_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies for teacher_tasks
CREATE POLICY "Users can view their own tasks" ON teacher_tasks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tasks" ON teacher_tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tasks" ON teacher_tasks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own tasks" ON teacher_tasks
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for teacher_activities
CREATE POLICY "Users can view their own activities" ON teacher_activities
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activities" ON teacher_activities
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own activities" ON teacher_activities
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own activities" ON teacher_activities
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- ADD TABLE COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE teacher_tasks IS 'Teacher administrative tasks, co-curricular duties, and student affairs responsibilities';
COMMENT ON COLUMN teacher_tasks.category IS 'Task category: pentadbiran (administrative), kokurikulum (co-curricular), hal_ehwal_murid (student affairs)';
COMMENT ON COLUMN teacher_tasks.task_description IS 'Description of the task (max 500 characters)';
COMMENT ON COLUMN teacher_tasks.is_completed IS 'Whether the task has been completed';
COMMENT ON COLUMN teacher_tasks.priority IS 'Task priority level: rendah (low), sederhana (medium), tinggi (high)';

COMMENT ON TABLE teacher_activities IS 'Teacher external activities including sports, organizations, and contributions';
COMMENT ON COLUMN teacher_activities.category IS 'Activity category: sukan (sports), pertubuhan (organizations), sumbangan (contributions)';
COMMENT ON COLUMN teacher_activities.activity_description IS 'Description of the activity (max 500 characters)';
COMMENT ON COLUMN teacher_activities.is_active IS 'Whether the activity is currently active';

COMMIT;

-- =====================================================
-- EXAMPLE USAGE
-- =====================================================

/*
-- Insert sample teacher tasks
INSERT INTO teacher_tasks (user_id, category, task_description) VALUES
('user-uuid-here', 'pentadbiran', 'Menyediakan laporan bulanan'),
('user-uuid-here', 'kokurikulum', 'Melatih pasukan bola sepak sekolah'),
('user-uuid-here', 'hal_ehwal_murid', 'Mengendalikan kes disiplin murid');

-- Insert sample teacher activities
INSERT INTO teacher_activities (user_id, category, activity_description) VALUES
('user-uuid-here', 'sukan', 'Jurulatih bola sepak daerah'),
('user-uuid-here', 'pertubuhan', 'Ahli jawatankuasa PIBG'),
('user-uuid-here', 'sumbangan', 'Program bantuan masyarakat');

-- Query tasks by category
SELECT * FROM teacher_tasks WHERE user_id = 'user-uuid-here' AND category = 'pentadbiran';

-- Query all activities for a user
SELECT * FROM teacher_activities WHERE user_id = 'user-uuid-here' ORDER BY category, created_at;
*/
