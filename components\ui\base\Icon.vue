<template>
    <Icon :name="name" :class="$attrs.class" />
</template>

<script setup lang="ts">
// This component now directly uses nuxt-icon's <Icon> component.
// It passes through the name and any other attributes (like class for styling).

defineProps<{
    name: string;
}>();

// No need to explicitly define $attrs here as they are automatically passed
// to the root <Icon> element if not bound elsewhere in this component's template.
</script>

<style scoped>
/* You can add specific styles for your Icon wrapper if needed,
   but generally, styling will be applied directly to the nuxt-icon component
   via the passed class attribute. */
</style>
