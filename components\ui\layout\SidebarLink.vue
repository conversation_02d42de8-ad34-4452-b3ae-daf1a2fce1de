<template>
    <NuxtLink :to="to" @click="$emit('click')" :class="[
        'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
        active
            ? 'bg-primary text-white shadow-sm'
            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-primary dark:hover:text-primary'
    ]">
        <Icon :name="icon" :class="[
            'w-5 h-5 mr-3 transition-colors',
            active
                ? 'text-white'
                : 'text-gray-400 group-hover:text-primary'
        ]" />
        <span class="flex-1">
            <slot />
        </span>
        <span v-if="badge" :class="[
            'ml-2 px-2 py-0.5 text-xs rounded-full font-medium',
            active
                ? 'bg-white/20 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
        ]">
            {{ badge }}
        </span>
    </NuxtLink>
</template>

<script setup lang="ts">
interface Props {
    to: string;
    icon: string;
    active?: boolean;
    badge?: string | number | null;
}

withDefaults(defineProps<Props>(), {
    active: false,
    badge: null
});

defineEmits<{
    click: []
}>();
</script>
