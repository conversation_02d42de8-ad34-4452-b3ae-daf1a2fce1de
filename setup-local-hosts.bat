@echo off
echo Setting up local hosts for RPHMate SaaS testing...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator - Good!
    echo.
) else (
    echo ERROR: This script must be run as Administrator
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

REM Backup hosts file
echo Creating backup of hosts file...
copy C:\Windows\System32\drivers\etc\hosts C:\Windows\System32\drivers\etc\hosts.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%

REM Add subdomain entries
echo Adding subdomain entries to hosts file...
echo.
echo # RPHMate SaaS Local Development >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1 demo.localhost >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1 test.localhost >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1 school1.localhost >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1 school2.localhost >> C:\Windows\System32\drivers\etc\hosts
echo 127.0.0.1 example.localhost >> C:\Windows\System32\drivers\etc\hosts

echo Successfully added the following subdomains:
echo - demo.localhost
echo - test.localhost
echo - school1.localhost
echo - school2.localhost
echo - example.localhost
echo.
echo You can now test the multi-tenant SaaS platform!
echo.
echo Next steps:
echo 1. Run: npm run dev
echo 2. Visit: http://localhost:3000 (Admin Dashboard)
echo 3. Visit: http://demo.localhost:3000 (Demo School)
echo 4. Visit: http://test.localhost:3000 (Test Academy)
echo.
pause
