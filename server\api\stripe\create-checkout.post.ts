// Create Stripe checkout session
// Phase 3: Payment Integration

import Stripe from 'stripe'

// Initialize Stripe - will be done inside the handler to access runtime config

export default defineEventHandler(async (event) => {
  try {
    // Get Stripe secret key from environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      console.error('STRIPE_SECRET_KEY environment variable is missing')
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe secret key not configured'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    const body = await readBody(event)
    
    // Validate required fields
    const {
      schoolName,
      schoolCode,
      schoolAddress,
      adminFirstName,
      adminLastName,
      adminEmail,
      selectedPlan,
      couponCode
    } = body

    if (!schoolName || !schoolCode || !adminEmail || !selectedPlan) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields'
      })
    }

    // Validate school code format
    if (!/^[a-zA-Z0-9]+$/.test(schoolCode)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid school code format'
      })
    }

    // School code validation is already done in the daftar/maklumat phase
    // We'll get the data from pre_billing table instead of validating again

    // Define pricing plans
    const pricingPlans = {
      basic: {
        priceId: process.env.STRIPE_BASIC_PRICE_ID || 'price_basic_monthly',
        amount: 9900, // RM99.00 in cents
        name: 'Basic Plan'
      },
      professional: {
        priceId: process.env.STRIPE_PROFESSIONAL_PRICE_ID || 'price_professional_monthly',
        amount: 19900, // RM199.00 in cents
        name: 'Professional Plan'
      },
      enterprise: {
        priceId: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise_monthly',
        amount: 39900, // RM399.00 in cents
        name: 'Enterprise Plan'
      }
    }

    const selectedPlanData = pricingPlans[selectedPlan as keyof typeof pricingPlans]
    if (!selectedPlanData) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid plan selected'
      })
    }

    // Create or retrieve customer
    let customer: Stripe.Customer
    try {
      // Check if customer already exists
      const existingCustomers = await stripe.customers.list({
        email: adminEmail,
        limit: 1
      })

      if (existingCustomers.data.length > 0) {
        customer = existingCustomers.data[0]
      } else {
        // Create new customer
        customer = await stripe.customers.create({
          email: adminEmail,
          name: `${adminFirstName} ${adminLastName}`.trim(),
          metadata: {
            schoolCode,
            schoolName,
            role: 'school_admin'
          }
        })
      }
    } catch (error) {
      console.error('Error creating/retrieving customer:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to create customer'
      })
    }

    // Prepare line items
    const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = [
      {
        price_data: {
          currency: 'myr',
          product_data: {
            name: selectedPlanData.name,
            description: `RPHMate ${selectedPlanData.name} - Educational Platform Subscription`,
            metadata: {
              plan: selectedPlan,
              schoolCode
            }
          },
          unit_amount: selectedPlanData.amount,
          recurring: {
            interval: 'month'
          }
        },
        quantity: 1
      }
    ]

    // Prepare checkout session parameters
    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'subscription',
      success_url: `${getBaseUrl(event)}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${getBaseUrl(event)}/billing?cancelled=true`,
      metadata: {
        schoolCode,
        schoolName,
        schoolAddress: schoolAddress || '',
        adminEmail,
        adminFirstName: adminFirstName || '',
        adminLastName: adminLastName || '',
        selectedPlan
      },
      subscription_data: {
        trial_period_days: 30, // 1-month free trial
        metadata: {
          schoolCode,
          plan: selectedPlan
        }
      },
      allow_promotion_codes: true, // Allow coupon codes
      billing_address_collection: 'required',
      customer_update: {
        address: 'auto',
        name: 'auto'
      }
    }

    // Apply coupon if provided
    if (couponCode) {
      try {
        // Validate coupon
        const coupon = await stripe.coupons.retrieve(couponCode)
        if (coupon.valid) {
          sessionParams.discounts = [{
            coupon: couponCode
          }]
        }
      } catch (error) {
        console.warn('Invalid coupon code:', couponCode)
        // Continue without coupon rather than failing
      }
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create(sessionParams)

    return {
      success: true,
      sessionId: session.id,
      url: session.url,
      customerId: customer.id
    }

  } catch (error: any) {
    console.error('Error creating checkout session:', error)
    
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to create checkout session'
    })
  }
})

/**
 * Get base URL for redirects
 */
function getBaseUrl(event: any): string {
  const host = getHeader(event, 'host') || 'localhost:3000'
  const protocol = host.includes('localhost') ? 'http' : 'https'
  return `${protocol}://${host}`
}
