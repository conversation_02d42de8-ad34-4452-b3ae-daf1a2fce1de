<template>
    <!-- Loading State -->
    <SkeletonCalendar v-if="loading" />

    <div v-if="!loading" class="space-y-8">
        <!-- Page Header -->
        <UiCompositePageHeader title="<PERSON>kwi<PERSON>" subtitle="Kalendar akademik dan jadual aktiviti sekolah"
            icon="heroicons:calendar-days-solid">
            <template #actions>
                <UiBaseButton variant="outline" size="sm" sm:size="md" prepend-icon="heroicons:funnel-solid"
                    @click="handleTapisClick" class="flex-1 sm:flex-none">
                    <span class="hidden sm:inline">Cari</span>
                    <span class="sm:hidden">Cari</span>
                </UiBaseButton>
                <UiBaseButton variant="primary" size="sm" sm:size="md" prepend-icon="heroicons:plus-solid"
                    @click="handleTambahAcaraClick" class="flex-1 sm:flex-none">
                    <span class="hidden sm:inline">Tambah Acara</span>
                    <span class="sm:hidden">Tambah</span>
                </UiBaseButton>
            </template>
        </UiCompositePageHeader>

        <!-- Filters Panel -->
        <CalendarFilters v-if="showFilters" v-model:filters="filters" @apply="applyFilters" @reset="resetFilters"
            @close="showFilters = false" @close-and-reset="handleCloseAndReset" />

        <!-- Calendar Navigation -->
        <div
            class="flex flex-col sm:flex-row items-center justify-between gap-4 bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm border border-gray-200 dark:border-dark-border">
            <div class="flex items-center space-x-4 w-full sm:w-auto justify-center sm:justify-start">
                <div v-if="isSearchMode" class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
                    Keputusan Carian
                </div>
                <div v-else class="flex items-center space-x-3">
                    <!-- Previous button -->
                    <UiBaseButton variant="outline" size="sm" prepend-icon="heroicons:chevron-left-solid"
                        @click="previousMonth" class="flex-shrink-0">&nbsp;
                        <span class="hidden sm:inline">Sebelum</span>
                        <span class="sm:hidden sr-only">Bulan Sebelum</span>
                    </UiBaseButton>
                    <!-- Month Dropdown -->
                    <div class="min-w-[140px]">
                        <UiBaseSingleSelect v-model="selectedMonthOption" :options="monthOptions"
                            placeholder="Pilih bulan" variant="compact" :allow-clear="false"
                            @update:model-value="handleMonthChange" />
                    </div>
                    <!-- Next button -->
                    <UiBaseButton variant="outline" size="sm" append-icon="heroicons:chevron-right-solid"
                        @click="nextMonth" class="flex-shrink-0">&nbsp;
                        <span class="hidden sm:inline">Seterusnya</span>
                        <span class="sm:hidden sr-only">Bulan Seterusnya</span>
                    </UiBaseButton>
                </div>
            </div>
            <div class="flex items-center w-full sm:w-auto">
                <div class="w-full sm:w-auto">
                    <UiBaseViewToggle v-model="viewMode" :options="viewOptions" />
                </div>
            </div>
        </div>

        <!-- Independent Hari Ini Button -->
        <div class="flex justify-start">
            <UiBaseButton variant="outline" size="sm" prepend-icon="heroicons:clock-solid" @click="goToToday"
                class="flex-shrink-0">
                <span>Hari Ini</span>
            </UiBaseButton>
        </div>

        <!-- Calendar View -->
        <div v-if="!loading">
            <!-- Search Results View -->
            <CalendarSearchView v-if="isSearchMode" :events="searchResults" :total-count="searchTotalCount"
                :loading="isSearching" :has-more="searchHasMore" @event-click="openEventDetails"
                @load-more="loadMoreSearchResults" />

            <!-- Regular Calendar View -->
            <CalendarView v-else-if="viewMode === 'calendar'" :month="currentMonth" :year="currentYear"
                :events="filteredEvents" @event-click="openEventDetails" @date-click="openAddEventModal" />

            <!-- List View -->
            <CalendarListView v-else :events="filteredEvents" :month="currentMonth" :year="currentYear"
                :is-all-year-mode="isAllYearSelected" @event-click="openEventDetails" />
        </div>

        <!-- Search Loading State -->
        <div v-if="isSearching" class="flex items-center justify-center py-12">
            <UiBaseIcon name="mdi:loading" class="w-8 h-8 animate-spin text-primary mr-3" />
            <span class="text-gray-600 dark:text-gray-400">Mencari acara...</span>
        </div>

        <!-- Event Modal -->
        <CalendarEventModal :is-open="eventModalOpen" :event="selectedEvent" :selected-date="selectedDate"
            @close="closeEventModal" @saved="handleEventSaved" @deleted="handleEventDeleted" />

        <!-- Event Details Modal -->
        <CalendarEventDetailsModal :is-open="eventDetailsOpen" :event="selectedEvent" @close="closeEventDetails"
            @edit="editEvent" @delete="deleteEventHandler" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import type { CalendarEvent, CalendarFilters as CalendarFiltersType } from '~/types/calendar'
import { getMalaysianMonthName, MALAYSIAN_MONTHS } from '~/types/calendar'
import { useToast } from '~/composables/useToast'

// Import components
import CalendarView from '~/components/calendar/CalendarView.vue'
import CalendarListView from '~/components/calendar/CalendarListView.vue'
import CalendarFilters from '~/components/calendar/CalendarFilters.vue'
import CalendarEventModal from '~/components/calendar/CalendarEventModal.vue'
import CalendarEventDetailsModal from '~/components/calendar/CalendarEventDetailsModal.vue'
import SkeletonCalendar from '~/components/ui/skeleton/SkeletonCalendar.vue'

// Define page meta
definePageMeta({
    layout: 'default'
})

// Set page head
useHead({
    title: 'Takwim Tahunan - RPHMate',
    meta: [
        {
            name: 'description',
            content: 'Kalendar akademik dan jadual aktiviti sekolah tahunan untuk perancangan yang lebih baik.'
        }
    ]
})

// Use calendar events composable
const {
    events,
    loading,
    fetchEventsByMonth,
    fetchEventsByYear,
    createEvent,
    updateEvent,
    deleteEvent,
    searchEvents
} = useCalendarEvents()

// Toast notifications
const { success: showSuccessToast, error: showErrorToast } = useToast()

// Reactive state
const showFilters = ref(false)
const eventModalOpen = ref(false)
const eventDetailsOpen = ref(false)
const selectedEvent = ref<CalendarEvent | null>(null)
const selectedDate = ref<string | null>(null)

// Calendar navigation
const currentDate = ref(new Date())
const currentMonth = computed(() => currentDate.value.getMonth())
const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonthName = computed(() => getMalaysianMonthName(currentMonth.value))

// Dropdown selections
const selectedMonthOption = ref<string>('')
const isAllYearSelected = computed(() => selectedMonthOption.value === 'all')

// Month options with "Sepanjang Tahun" for list view
const monthOptions = computed(() => {
    const options = [
        { value: 'all', label: 'Sepanjang Tahun' },
        ...MALAYSIAN_MONTHS.map((month, index) => ({
            value: index.toString(),
            label: month
        }))
    ]

    // Only show "Sepanjang Tahun" in list view
    if (viewMode.value === 'list') {
        return options
    } else {
        return options.slice(1) // Remove "Sepanjang Tahun" for calendar view
    }
})



// View mode
const viewMode = ref<'calendar' | 'list'>('calendar')
const viewOptions = [
    { value: 'calendar', label: 'Kalendar', icon: 'heroicons:calendar-days-solid' },
    { value: 'list', label: 'Senarai', icon: 'heroicons:list-bullet-solid' }
]

// Search state
const isSearchMode = ref(false)
const searchResults = ref<CalendarEvent[]>([])
const isSearching = ref(false)
const searchTotalCount = ref(0)
const searchCurrentPage = ref(0)
const searchHasMore = ref(false)
const currentSearchFilters = ref<CalendarFiltersType>({ categories: [], searchQuery: '' })

// Filters
const filters = ref<CalendarFiltersType>({
    categories: [],
    searchQuery: ''
})

// Computed
const filteredEvents = computed(() => {
    let filtered = events.value

    // Only apply category filters in calendar/list view (not in search mode)
    if (!isSearchMode.value && filters.value.categories.length > 0) {
        filtered = filtered.filter(event => filters.value.categories.includes(event.category))
    }

    // Don't apply search query here - search triggers search mode
    // Search query filtering is handled by the search API

    return filtered
})

// Methods
const previousMonth = () => {
    const newDate = new Date(currentDate.value)
    newDate.setMonth(newDate.getMonth() - 1)
    currentDate.value = newDate
    // Update dropdown selection
    selectedMonthOption.value = newDate.getMonth().toString()
}

const nextMonth = () => {
    const newDate = new Date(currentDate.value)
    newDate.setMonth(newDate.getMonth() + 1)
    currentDate.value = newDate
    // Update dropdown selection
    selectedMonthOption.value = newDate.getMonth().toString()
}

// Handle month dropdown change
const handleMonthChange = async (value: string) => {
    if (value === 'all') {
        // "Sepanjang Tahun" selected - don't change currentDate
        selectedMonthOption.value = value
    } else {
        const monthIndex = parseInt(value)
        const newDate = new Date(currentDate.value)
        newDate.setMonth(monthIndex)
        currentDate.value = newDate
        selectedMonthOption.value = value
    }

    // Reload events when switching between modes
    await loadEvents()
}



const goToToday = () => {
    const today = new Date()
    currentDate.value = today
    // Update the month dropdown to reflect today's month
    selectedMonthOption.value = today.getMonth().toString()
}

// Watch for view mode changes
watch(viewMode, () => {
    // If in search mode, reset first before changing view
    if (isSearchMode.value) {
        resetFilters()
    }
})

const openAddEventModal = (date?: string) => {
    selectedEvent.value = null
    selectedDate.value = date || null
    eventModalOpen.value = true
}

const openEventDetails = (event: CalendarEvent) => {
    selectedEvent.value = event
    eventDetailsOpen.value = true
}

const closeEventModal = () => {
    eventModalOpen.value = false
    selectedEvent.value = null
    selectedDate.value = null
}

const closeEventDetails = () => {
    eventDetailsOpen.value = false
    selectedEvent.value = null
}

const editEvent = (event: any) => {
    selectedEvent.value = event
    eventDetailsOpen.value = false
    eventModalOpen.value = true
}

const deleteEventHandler = async (event: CalendarEvent) => {
    try {
        await deleteEvent(event.id)
        showSuccessToast(`Acara "${event.title}" telah berjaya dipadam.`)
        closeEventDetails()
        // Reload events for current month
        await loadEvents()
    } catch (error) {
        console.error('Error deleting event:', error)
        showErrorToast('Gagal memadam acara. Sila cuba lagi.')
    }
}

const handleEventSaved = async (event: CalendarEvent) => {
    try {
        if (selectedEvent.value) {
            // Update existing event - extract only the fields that can be updated
            const updateData = {
                title: event.title,
                description: event.description,
                category: event.category,
                start_date: event.start_date,
                end_date: event.end_date,
                location: event.location
            }
            await updateEvent(selectedEvent.value.id, updateData)
        } else {
            // Create new event
            await createEvent(event)
        }
        showSuccessToast(selectedEvent.value ? 'Acara telah berjaya dikemas kini.' : 'Acara telah berjaya dicipta.')
        closeEventModal()
        // Reload events for current month
        await loadEvents()
    } catch (error) {
        console.error('Error saving event:', error)
        showErrorToast('Gagal menyimpan acara. Sila cuba lagi.')
    }
}

const handleEventDeleted = async (eventId: string) => {
    try {
        await deleteEvent(eventId)
        showSuccessToast('Acara telah berjaya dipadam.')
        closeEventModal()
        // Reload events for current month
        await loadEvents()
    } catch (error) {
        console.error('Error deleting event:', error)
        showErrorToast('Gagal memadam acara. Sila cuba lagi.')
    }
}

const applyFilters = async (newFilters: CalendarFiltersType) => {
    filters.value = { ...newFilters }

    // Only trigger search mode if there's a search query (3+ characters)
    if (filters.value.searchQuery.trim().length >= 3) {
        isSearching.value = true
        try {
            currentSearchFilters.value = { ...filters.value }
            searchCurrentPage.value = 0
            const result = await searchEvents(filters.value, 0, 20)
            searchResults.value = result.events
            searchTotalCount.value = result.totalCount
            searchHasMore.value = result.hasMore
            isSearchMode.value = true
        } catch (error) {
            console.error('Error searching events:', error)
            showErrorToast('Gagal mencari acara. Sila cuba lagi.')
        } finally {
            isSearching.value = false
        }
    } else {
        // No valid search query, stay in calendar/list mode
        // Category filtering will be handled by filteredEvents computed property
        isSearchMode.value = false
        searchResults.value = []
        searchTotalCount.value = 0
        searchHasMore.value = false
    }
}

const resetFilters = () => {
    filters.value = {
        categories: [],
        searchQuery: ''
    }
    isSearchMode.value = false
    searchResults.value = []
    searchTotalCount.value = 0
    searchHasMore.value = false
}

const loadMoreSearchResults = async () => {
    if (!searchHasMore.value || isSearching.value) return

    isSearching.value = true
    try {
        searchCurrentPage.value += 1
        const result = await searchEvents(currentSearchFilters.value, searchCurrentPage.value, 20)
        searchResults.value = [...searchResults.value, ...result.events]
        searchHasMore.value = result.hasMore
    } catch (error) {
        console.error('Error loading more search results:', error)
        showErrorToast('Gagal memuatkan lebih banyak hasil carian.')
    } finally {
        isSearching.value = false
    }
}

const handleCloseAndReset = () => {
    showFilters.value = false
    resetFilters()
}

const handleTapisClick = () => {
    // If in search mode, reset first
    if (isSearchMode.value) {
        resetFilters()
    }
    // Then toggle filters
    showFilters.value = !showFilters.value
}

const handleTambahAcaraClick = () => {
    // If in search mode, reset first
    if (isSearchMode.value) {
        resetFilters()
    }
    // Then open add event modal
    openAddEventModal()
}

// Load events
const loadEvents = async () => {
    try {
        if (isAllYearSelected.value) {
            await fetchEventsByYear(currentYear.value)
        } else {
            await fetchEventsByMonth(currentYear.value, currentMonth.value)
        }
    } catch (error) {
        console.error('Error loading events:', error)
        showErrorToast('Gagal memuatkan acara. Sila cuba lagi.')
    }
}

// Initialize dropdown values
const initializeDropdowns = () => {
    const now = new Date()

    // For list view, default to "Sepanjang Tahun", for calendar view default to current month
    if (viewMode.value === 'list') {
        selectedMonthOption.value = 'all'
    } else {
        selectedMonthOption.value = now.getMonth().toString()
    }
}

// Watch for view mode changes to update month dropdown
watch(viewMode, async (newMode, oldMode) => {
    if (newMode !== oldMode) {
        if (newMode === 'list' && selectedMonthOption.value !== 'all') {
            selectedMonthOption.value = 'all'
            // Load events for the entire year when switching to list view
            await nextTick()
            await loadEvents()
        } else if (newMode === 'calendar' && selectedMonthOption.value === 'all') {
            selectedMonthOption.value = currentMonth.value.toString()
            // Load events for current month when switching to calendar view
            await nextTick()
            await loadEvents()
        }
    }
})

// Lifecycle
onMounted(async () => {
    initializeDropdowns()
    // Wait for next tick to ensure reactive values are updated
    await nextTick()
    await loadEvents()
})

// Watch for month changes to reload events
watch([currentMonth, currentYear], () => {
    loadEvents()
})
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
