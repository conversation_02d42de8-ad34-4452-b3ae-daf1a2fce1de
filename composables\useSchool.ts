// School context management composable
// Created: 2025-07-13

import { ref, computed, watch } from 'vue'
import type { 
  School, 
  SchoolMembership, 
  SchoolContext, 
  UserSchoolAccess 
} from '~/types/multiTenant'

// Global state for school context
const currentSchool = ref<School | null>(null)
const currentMembership = ref<SchoolMembership | null>(null)
const userSchools = ref<School[]>([])
const userMemberships = ref<SchoolMembership[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)

export const useSchool = () => {
  const supabase = useSupabaseClient()
  const user = useSupabaseUser()
  const route = useRoute()

  // Computed properties
  const schoolContext = computed<SchoolContext>(() => ({
    school: currentSchool.value,
    membership: currentMembership.value,
    isLoading: isLoading.value,
    error: error.value
  }))

  const userSchoolAccess = computed<UserSchoolAccess>(() => ({
    schools: userSchools.value,
    memberships: userMemberships.value,
    currentSchool: currentSchool.value,
    currentMembership: currentMembership.value
  }))

  const hasSchoolAccess = computed(() => {
    return currentSchool.value !== null && currentMembership.value !== null
  })

  const isSchoolAdmin = computed(() => {
    return currentMembership.value?.role === 'admin'
  })

  const isSchoolSupervisor = computed(() => {
    return currentMembership.value?.role === 'supervisor' || isSchoolAdmin.value
  })

  const canManageTeachers = computed(() => {
    return isSchoolAdmin.value || isSchoolSupervisor.value
  })

  // Methods
  const fetchUserSchools = async () => {
    if (!user.value) {
      userSchools.value = []
      userMemberships.value = []
      return
    }

    try {
      isLoading.value = true
      error.value = null

      // Get auth token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.access_token) {
        throw new Error('Authentication required')
      }

      // Fetch user's schools using API
      const response = await $fetch('/api/schools/user-schools', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      }) as any

      if (response.success) {
        userSchools.value = response.schools.map((s: any) => ({
          id: s.id,
          name: s.name,
          code: s.code,
          admin_user_id: s.admin_user_id,
          subscription_status: s.subscription_status,
          subscription_expires_at: s.subscription_expires_at,
          description: s.description,
          contact_email: s.contact_email,
          contact_phone: s.contact_phone,
          address: s.address,
          settings: s.settings,
          created_at: s.created_at,
          updated_at: s.updated_at
        }))

        userMemberships.value = response.schools.map((s: any) => s.membership).filter(Boolean)
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch user schools'
      console.error('Error fetching user schools:', err)
    } finally {
      isLoading.value = false
    }
  }

  const setCurrentSchool = async (schoolCode: string) => {
    if (!user.value) {
      error.value = 'User not authenticated'
      return false
    }

    try {
      isLoading.value = true
      error.value = null

      // Get auth token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.access_token) {
        error.value = 'Authentication required'
        return false
      }

      // Validate school access using API
      const response = await $fetch('/api/schools/validate-access', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: { schoolCode }
      }) as any

      if (response.hasAccess) {
        currentSchool.value = response.school
        currentMembership.value = response.membership
        return true
      } else {
        error.value = response.error || 'No access to this school'
        return false
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to set current school'
      console.error('Error setting current school:', err)
      return false
    } finally {
      isLoading.value = false
    }
  }

  const validateSchoolAccess = async (schoolCode: string): Promise<boolean> => {
    if (!user.value) return false

    try {
      // Get auth token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session?.access_token) return false

      // Validate school access using API
      const response = await $fetch('/api/schools/validate-access', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: { schoolCode }
      }) as any

      return response.hasAccess
    } catch (err) {
      console.error('Error validating school access:', err)
      return false
    }
  }

  const switchSchool = (schoolCode: string) => {
    // Navigate to the new school's subdomain
    const currentHost = window.location.host
    const baseDomain = currentHost.split('.').slice(1).join('.') // Remove subdomain
    const newUrl = `${window.location.protocol}//${schoolCode}.${baseDomain}${route.path}`
    
    window.location.href = newUrl
  }

  const clearSchoolContext = () => {
    currentSchool.value = null
    currentMembership.value = null
    error.value = null
  }

  const refreshSchoolContext = async () => {
    const schoolCode = route.params.school as string
    if (schoolCode) {
      await setCurrentSchool(schoolCode)
    }
  }

  // Watch for user changes
  watch(user, async (newUser) => {
    if (newUser) {
      await fetchUserSchools()
      
      // If we're on a school route, set the current school
      const schoolCode = route.params.school as string
      if (schoolCode) {
        await setCurrentSchool(schoolCode)
      }
    } else {
      // Clear all school data when user logs out
      userSchools.value = []
      userMemberships.value = []
      clearSchoolContext()
    }
  }, { immediate: true })

  // Watch for route changes to update current school
  watch(() => route.params.school, async (newSchoolCode) => {
    if (newSchoolCode && typeof newSchoolCode === 'string') {
      await setCurrentSchool(newSchoolCode)
    } else {
      clearSchoolContext()
    }
  })

  return {
    // State
    schoolContext: readonly(schoolContext),
    userSchoolAccess: readonly(userSchoolAccess),
    currentSchool: readonly(currentSchool),
    currentMembership: readonly(currentMembership),
    userSchools: readonly(userSchools),
    userMemberships: readonly(userMemberships),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Computed
    hasSchoolAccess,
    isSchoolAdmin,
    isSchoolSupervisor,
    canManageTeachers,

    // Methods
    fetchUserSchools,
    setCurrentSchool,
    validateSchoolAccess,
    switchSchool,
    clearSchoolContext,
    refreshSchoolContext
  }
}
