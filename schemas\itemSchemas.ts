import { z } from "zod";

export const ItemSchema = z.object({
  id: z.number().int().positive({ message: "ID must be a positive integer." }),
  title: z
    .string()
    .min(3, { message: "Title must be at least 3 characters long." })
    .max(100, { message: "Title must be 100 characters or less." }),
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters long." })
    .max(500, { message: "Description must be 500 characters or less." }),
  // You can add other item-specific fields here, for example:
  // price: z.number().positive({ message: 'Price must be a positive number.' }).optional(),
  // category: z.string().optional(),
});

export type Item = z.infer<typeof ItemSchema>;
