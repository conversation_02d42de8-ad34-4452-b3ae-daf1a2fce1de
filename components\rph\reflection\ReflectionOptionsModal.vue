<template>
    <Modal :is-open="show" :title="`Urus ${title}`" @close="$emit('close')" size="md">

        <div class="space-y-6">
            <!-- Add New Option -->
            <div class="space-y-2">
                <div class="pt-2">
                    <Input v-model="newOptionText" placeholder="Tambah Pilihan Baru" class="flex-1"
                        @keyup.enter="handleAddOption" />
                </div>
                <div class="mt-2 flex justify-end">
                    <UiBaseButton @click="handleAddOption" :loading="loading" :disabled="!newOptionText.trim()"
                        size="sm" prepend-icon="heroicons:plus-solid">
                        Tambah
                    </UiBaseButton>
                </div>
            </div>

            <!-- Options List -->
            <div class="space-y-3">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                    <PERSON><PERSON><PERSON>
                </h4>

                <div class="max-h-96 overflow-y-auto space-y-2">
                    <div v-for="option in options" :key="option.id"
                        class="flex items-center justify-between bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex-1">
                            <div v-if="editingOption?.id === option.id" class="flex gap-2">
                                <Input v-model="editOptionText" class="flex-1" @keyup.enter="handleUpdateOption"
                                    @keyup.escape="cancelEdit" />
                                <Button @click="handleUpdateOption" :loading="loading" size="sm" variant="outline">
                                    <Icon name="heroicons:check" class="h-4 w-4" />
                                </Button>
                                <Button @click="cancelEdit" size="sm" variant="flat">
                                    <Icon name="heroicons:x-mark" class="h-4 w-4" />
                                </Button>
                            </div>
                            <div v-else class="flex items-center justify-between">
                                <div>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ option.option_text }}
                                    </span>
                                    <span v-if="option.is_default"
                                        class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                                        Lalai
                                    </span>
                                </div>
                                <div v-if="!option.is_default" class="flex gap-1">
                                    <Button @click="startEdit(option)" size="sm" variant="flat" class="h-8 w-8 p-0">
                                        <Icon name="heroicons:pencil" class="h-4 w-4" />
                                    </Button>
                                    <Button @click="confirmDelete(option)" size="sm" variant="flat"
                                        class="h-8 w-8 p-0 text-red-500 hover:text-red-700">
                                        <Icon name="heroicons:trash" class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="options.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <Icon name="heroicons:document-text" class="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Tiada pilihan tersedia</p>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end">
                <Button @click="$emit('close')" variant="outline">
                    Tutup
                </Button>
            </div>
        </template>
    </Modal>

    <!-- Delete Confirmation Modal -->
    <DeleteConfirmationModal :is-open="showDeleteModal" :item-name="optionToDelete?.option_text || ''"
        confirmation-message="Adakah anda pasti ingin memadam pilihan ini?"
        warning-message="Tindakan ini tidak boleh dibatalkan." :z-index="60" @update:is-open="showDeleteModal = $event"
        @confirm="handleConfirmDelete" />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import Modal from '~/components/ui/composite/Modal.vue'
import Button from '~/components/ui/base/Button.vue'
import Input from '~/components/ui/base/Input.vue'
import Icon from '~/components/ui/base/Icon.vue'
import DeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue'
import type { ReflectionOption } from '~/composables/useReflectionOptions'

interface Props {
    show: boolean
    title: string
    options: ReflectionOption[]
    loading: boolean
}

interface Emits {
    (e: 'close'): void
    (e: 'add-option', text: string): void
    (e: 'update-option', id: string, text: string): void
    (e: 'delete-option', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// State
const newOptionText = ref('')
const editingOption = ref<ReflectionOption | null>(null)
const editOptionText = ref('')
const showDeleteModal = ref(false)
const optionToDelete = ref<any>(null)

// Methods
const handleAddOption = () => {
    if (!newOptionText.value.trim()) return

    emit('add-option', newOptionText.value.trim())
    newOptionText.value = ''
}

const startEdit = (option: ReflectionOption) => {
    editingOption.value = option
    editOptionText.value = option.option_text
}

const handleUpdateOption = () => {
    if (!editingOption.value || !editOptionText.value.trim()) return

    emit('update-option', editingOption.value.id, editOptionText.value.trim())
    cancelEdit()
}

const cancelEdit = () => {
    editingOption.value = null
    editOptionText.value = ''
}

const confirmDelete = (option: any) => {
    optionToDelete.value = option
    showDeleteModal.value = true
}

const handleConfirmDelete = () => {
    if (optionToDelete.value) {
        emit('delete-option', optionToDelete.value.id)
        showDeleteModal.value = false
        optionToDelete.value = null
    }
}

// Watch for modal close to reset state
watch(() => props.show, (newShow) => {
    if (!newShow) {
        newOptionText.value = ''
        cancelEdit()
    }
})
</script>
