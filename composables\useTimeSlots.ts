import { ref, computed } from 'vue'
import type { TimeSlot } from '~/types/timetable'
import { useSupabase } from '~/composables/useSupabase'
import { useSupabaseUser } from '#imports'
import { formatTimeRange } from '~/utils/timeHelpers'

// Default time slots that are used if none exist in database
const DEFAULT_TIME_SLOTS: TimeSlot[] = [
  { id: '1', start_time: '07:20', end_time: '07:50', label: '7:20 AM - 7:50 AM', period_number: 0 },
  { id: '2', start_time: '07:50', end_time: '08:20', label: '7:50 AM - 8:20 AM', period_number: 1 },
  { id: '3', start_time: '08:20', end_time: '08:50', label: '8:20 AM - 8:50 AM', period_number: 2 },
  { id: '4', start_time: '08:50', end_time: '09:20', label: '8:50 AM - 9:20 AM', period_number: 3 },
  { id: '5', start_time: '09:20', end_time: '09:50', label: '9:20 AM - 9:50 AM', period_number: 4 },
  { id: '6', start_time: '09:50', end_time: '10:20', label: '9:50 AM - 10:20 AM', period_number: 5 },
  { id: '7', start_time: '10:20', end_time: '10:50', label: '10:20 AM - 10:50 AM', period_number: 6 },
  { id: '8', start_time: '10:50', end_time: '11:20', label: '10:50 AM - 11:20 AM', period_number: 7 },
  { id: '9', start_time: '11:20', end_time: '11:50', label: '11:20 AM - 11:50 AM', period_number: 8 },
  { id: '10', start_time: '11:50', end_time: '12:20', label: '11:50 AM - 12:20 PM', period_number: 9 },
  { id: '11', start_time: '12:20', end_time: '12:50', label: '12:20 PM - 12:50 PM', period_number: 10 },
  { id: '12', start_time: '12:50', end_time: '13:20', label: '12:50 PM - 1:20 PM', period_number: 11 },
]

export const useTimeSlots = () => {
  const { client } = useSupabase()
  const user = useSupabaseUser()
  
  const timeSlots = ref<TimeSlot[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Load time slots from database or use defaults
  const fetchTimeSlots = async () => {
    if (!user.value) return

    loading.value = true
    error.value = null

    try {
      const { data: profile, error: profileError } = await client
        .from('profiles')
        .select('time_slots')
        .eq('id', user.value.id)
        .single()

      if (profileError) {
        console.error('Error fetching time slots:', profileError)
        // Use default time slots if there's an error
        timeSlots.value = [...DEFAULT_TIME_SLOTS]
        return
      }

      if (profile?.time_slots && Array.isArray(profile.time_slots) && profile.time_slots.length > 0) {
        // Use saved time slots from database, but filter out 'Rehat 1' and 'Rehat Tengah Hari'
        timeSlots.value = profile.time_slots
          .filter((slot: any) => slot.id !== 'break1' && slot.label !== 'Rehat 1' && slot.id !== 'break2' && slot.label !== 'Rehat Tengah Hari')
          .map((slot: any) => ({
            ...slot,
            label: slot.period_number < 0 ? slot.label : formatTimeRange(slot.start_time, slot.end_time)
          }))
      } else {
        // No saved time slots, use defaults and save them
        timeSlots.value = [...DEFAULT_TIME_SLOTS]
        await saveTimeSlots(timeSlots.value)
      }
    } catch (err) {
      console.error('Error in fetchTimeSlots:', err)
      error.value = 'Failed to load time slots'
      // Fallback to default time slots
      timeSlots.value = [...DEFAULT_TIME_SLOTS]
    } finally {
      loading.value = false
    }
  }
  // Save time slots to database
  const saveTimeSlots = async (slots: TimeSlot[]) => {
    if (!user.value) return

    try {
      const { error: updateError } = await client
        .from('profiles')
        .update({
          time_slots: slots as any // Cast to Json type
        })
        .eq('id', user.value.id)

      if (updateError) {
        console.error('Error saving time slots:', updateError)
        error.value = 'Failed to save time slots'
        return false
      }

      return true
    } catch (err) {
      console.error('Error in saveTimeSlots:', err)
      error.value = 'Failed to save time slots'
      return false
    }
  }

  // Update a specific time slot
  const updateTimeSlot = async (slotId: string, updates: Partial<TimeSlot>) => {
    const index = timeSlots.value.findIndex(slot => slot.id === slotId)
    if (index === -1) return false

    // Update local state
    const updatedSlot = {
      ...timeSlots.value[index],
      ...updates
    }

    // Update label if time changed
    if (updates.start_time || updates.end_time) {
      if (updatedSlot.period_number >= 0) {
        updatedSlot.label = formatTimeRange(updatedSlot.start_time, updatedSlot.end_time)
      }
    }

    timeSlots.value[index] = updatedSlot

    // Save to database
    const success = await saveTimeSlots(timeSlots.value)
    return success
  }

  // Reset to default time slots
  const resetToDefaults = async () => {
    timeSlots.value = [...DEFAULT_TIME_SLOTS]
    const success = await saveTimeSlots(timeSlots.value)
    return success
  }

  // Get time slot by ID
  const getTimeSlot = (slotId: string) => {
    return timeSlots.value.find(slot => slot.id === slotId)
  }

  // Get teaching periods (exclude breaks)
  const teachingPeriods = computed(() => {
    return timeSlots.value.filter(slot => slot.period_number >= 0)
  })

  // Get break periods
  const breakPeriods = computed(() => {
    return timeSlots.value.filter(slot => slot.period_number < 0)
  })

  // Add a new time slot
  const addTimeSlot = async (newSlot?: Partial<TimeSlot>) => {
    // Generate a new time slot based on the last existing one
    const lastSlot = timeSlots.value[timeSlots.value.length - 1]
    const nextPeriodNumber = Math.max(...timeSlots.value.map(slot => slot.period_number)) + 1
    
    // Default to 30 minutes after the last slot if no custom data provided
    const defaultStartTime = lastSlot ? 
      addMinutesToTime(lastSlot.end_time, 0) : '13:20'
    const defaultEndTime = lastSlot ?
      addMinutesToTime(lastSlot.end_time, 30) : '13:50'

    const newTimeSlot: TimeSlot = {
      id: (timeSlots.value.length + 1).toString(),
      start_time: newSlot?.start_time || defaultStartTime,
      end_time: newSlot?.end_time || defaultEndTime,
      label: newSlot?.label || formatTimeRange(defaultStartTime, defaultEndTime),
      period_number: newSlot?.period_number ?? nextPeriodNumber,
      ...newSlot
    }

    // Add to local state
    timeSlots.value.push(newTimeSlot)

    // Save to database
    const success = await saveTimeSlots(timeSlots.value)
    return success ? newTimeSlot : null
  }

  // Delete a time slot
  const deleteTimeSlot = async (slotId: string) => {
    const index = timeSlots.value.findIndex(slot => slot.id === slotId)
    if (index === -1) return false

    // Remove from local state
    const deletedSlot = timeSlots.value[index]
    timeSlots.value.splice(index, 1)

    // Save to database
    const success = await saveTimeSlots(timeSlots.value)
    
    if (!success) {
      // Restore if save failed
      timeSlots.value.splice(index, 0, deletedSlot)
      return false
    }

    return true
  }

  // Helper function to add minutes to time string
  const addMinutesToTime = (time: string, minutes: number): string => {
    const [hours, mins] = time.split(':').map(Number)
    const totalMinutes = hours * 60 + mins + minutes
    const newHours = Math.floor(totalMinutes / 60)
    const newMins = totalMinutes % 60
    
    return `${String(newHours).padStart(2, '0')}:${String(newMins).padStart(2, '0')}`
  }

  return {
    // State
    timeSlots,
    loading,
    error,

    // Computed
    teachingPeriods,
    breakPeriods,

    // Actions
    fetchTimeSlots,
    saveTimeSlots,
    updateTimeSlot,
    resetToDefaults,
    getTimeSlot,
    addTimeSlot,
    deleteTimeSlot
  }
}
