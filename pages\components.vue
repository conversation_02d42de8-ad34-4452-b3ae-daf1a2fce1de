<template>
    <div class="p-4">
        <h1 class="text-2xl font-semibold mb-6">Composite Components Showcase</h1>

        <Card class="mb-8">
            <template #header>
                <h2 class="text-xl font-medium">ClassSubject Component</h2>
            </template>
            <template #default>
                <p class="mb-4 text-sm text-gray-600">
                    This component allows users to link a class to a subject they are teaching.
                    They first select a class, then provide a name for that class, and finally select the subject.
                </p>
                <ClassSubject />
            </template>
        </Card>

        <Card class="mb-8">
            <template #header>
                <h2 class="text-xl font-medium">SingleSelect Component Variants</h2>
            </template>
            <template #default>
                <p class="mb-4 text-sm text-gray-600">
                    The SingleSelect component supports two variants: floating labels and standard labels with
                    placeholders.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Floating Variant -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200">Floating Label Variant</h3>
                        <SingleSelect v-model="selectedFloating" :options="sampleOptions" variant="floating"
                            placeholder="Choose a subject" option-label="name" option-value="id" :allow-clear="true" />
                        <p class="text-xs text-gray-500">Selected: {{ selectedFloating || 'None' }}</p>
                    </div> <!-- Standard Variant -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200">Standard Label Variant</h3>
                        <SingleSelect v-model="selectedStandard" :options="sampleOptions" variant="standard"
                            label="Subject" placeholder="Select a subject" option-label="name" option-value="id"
                            :required="true" :allow-clear="true" />
                        <p class="text-xs text-gray-500">Selected: {{ selectedStandard || 'None' }}</p>
                        <p class="text-xs text-gray-400">Note: This field is marked as required with an asterisk (*)</p>
                    </div>
                </div>

                <!-- With Search -->
                <div class="mt-8 space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200">With Search (Standard Variant)</h3>
                    <SingleSelect v-model="selectedSearch" :options="longOptionsList" variant="standard"
                        label="Long List of Options" placeholder="Type to search..." :show-search="true"
                        search-placeholder="Search options..." option-label="name" option-value="id"
                        :allow-clear="true" />
                    <p class="text-xs text-gray-500">Selected: {{ selectedSearch || 'None' }}</p>
                </div>
            </template>
        </Card>

        <Card class="mb-8">
            <template #header>
                <h2 class="text-xl font-medium">ProfileUpload Component</h2>
            </template>
            <template #default>
                <p class="mb-4 text-sm text-gray-600">
                    This component allows users to upload a profile picture with preview and validation.
                </p>
                <ProfileUpload :show-name="true" name="John Doe" />
            </template>
        </Card>

        <!-- Add other composite components here as they are developed -->

    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ClassSubject from '@/components/ui/composite/ClassSubject.vue';
import ProfileUpload from '@/components/ui/composite/ProfileUpload.vue';
import Card from '@/components/ui/composite/Card.vue';
import SingleSelect from '@/components/ui/base/SingleSelect.vue';

// Sample data for SingleSelect demo
const selectedFloating = ref(null)
const selectedStandard = ref(null)
const selectedSearch = ref(null)

const sampleOptions = ref([
    { id: '1', name: 'Mathematics' },
    { id: '2', name: 'Physics' },
    { id: '3', name: 'Chemistry' },
    { id: '4', name: 'Biology' },
    { id: '5', name: 'English' },
    { id: '6', name: 'Bahasa Melayu' },
])

const longOptionsList = ref([
    { id: '1', name: 'Mathematics' },
    { id: '2', name: 'Physics' },
    { id: '3', name: 'Chemistry' },
    { id: '4', name: 'Biology' },
    { id: '5', name: 'English' },
    { id: '6', name: 'Bahasa Melayu' },
    { id: '7', name: 'History' },
    { id: '8', name: 'Geography' },
    { id: '9', name: 'Economics' },
    { id: '10', name: 'Moral Education' },
    { id: '11', name: 'Islamic Studies' },
    { id: '12', name: 'Art' },
    { id: '13', name: 'Physical Education' },
    { id: '14', name: 'Computer Science' },
    { id: '15', name: 'Additional Mathematics' },
])

// Set a title for the page, if Nuxt SEO or head management is set up
// useHead({
//   title: 'Composite Components - Showcase'
// });
</script>

<style scoped>
/* Add any page-specific styles if needed */
</style>
