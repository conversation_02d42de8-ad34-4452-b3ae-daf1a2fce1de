#!/usr/bin/env node

/**
 * RPHMate SaaS Test Data Seeding Script
 * Phase 4: Development Environment Setup
 * 
 * This script creates test data for development and testing purposes.
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

console.log('🌱 RPHMate SaaS Test Data Seeding')
console.log('=================================\n')

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

// Test data configuration - REMOVED DEMO SCHOOLS FOR PRODUCTION READINESS
// Only real schools created through payment flow should exist
const testData = {
  schools: [
    // No hardcoded demo schools - schools will be created dynamically through billing flow
  ],
  
  users: [
    // No demo users - users will be created through real registration flow
  ],
  
  subjects: [
    // No demo subjects - subjects will be created by schools
  ],

  classes: [
    // No demo classes - classes will be created by schools
  ]
}

/**
 * Check if tables exist (basic check)
 */
async function checkDatabase() {
  console.log('🔍 Checking database connection...')
  
  try {
    // Try a simple query to check connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
    
    if (error && error.code === '42P01') {
      console.log('⚠️  Database tables not found. This is expected for development.')
      console.log('   The seeding script will create mock data for testing.\n')
      return false
    } else if (error) {
      throw error
    }
    
    console.log('✅ Database connection successful\n')
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    console.log('   Continuing with mock data creation for development.\n')
    return false
  }
}

/**
 * Create mock data files for development
 */
async function createMockDataFiles() {
  console.log('📁 Creating mock data files for development...')
  
  // fs and path already imported at top
  
  // Create mock data directory
  const mockDataDir = path.join(process.cwd(), 'mock-data')
  if (!fs.existsSync(mockDataDir)) {
    fs.mkdirSync(mockDataDir)
  }
  
  // Create schools mock data
  fs.writeFileSync(
    path.join(mockDataDir, 'schools.json'),
    JSON.stringify(testData.schools, null, 2)
  )
  
  // Create users mock data
  fs.writeFileSync(
    path.join(mockDataDir, 'users.json'),
    JSON.stringify(testData.users, null, 2)
  )
  
  // Create subjects mock data
  fs.writeFileSync(
    path.join(mockDataDir, 'subjects.json'),
    JSON.stringify(testData.subjects, null, 2)
  )
  
  // Create classes mock data
  fs.writeFileSync(
    path.join(mockDataDir, 'classes.json'),
    JSON.stringify(testData.classes, null, 2)
  )
  
  // Create a summary file
  const summary = {
    created_at: new Date().toISOString(),
    schools_count: testData.schools.length,
    users_count: testData.users.length,
    subjects_count: testData.subjects.length,
    classes_count: testData.classes.length,
    test_credentials: {
      'xba1224.localhost:3000': {
        admin: '<EMAIL> / password123',
        teacher: '<EMAIL> / password123'
      },
      'demo.localhost:3000': {
        admin: '<EMAIL> / password123'
      },
      'test.localhost:3000': {
        admin: '<EMAIL> / password123'
      }
    }
  }
  
  fs.writeFileSync(
    path.join(mockDataDir, 'summary.json'),
    JSON.stringify(summary, null, 2)
  )
  
  console.log('✅ Mock data files created in ./mock-data/')
  console.log(`   - ${testData.schools.length} schools`)
  console.log(`   - ${testData.users.length} users`)
  console.log(`   - ${testData.subjects.length} subjects`)
  console.log(`   - ${testData.classes.length} classes\n`)
}

/**
 * Seed actual database (when available)
 */
async function seedDatabase() {
  console.log('🌱 Seeding database with test data...')
  
  try {
    // Note: This is a placeholder for when database tables are ready
    // For now, we'll just create the mock data files
    
    console.log('⚠️  Database seeding not implemented yet.')
    console.log('   This will be implemented when database schema is finalized.\n')
    
    return true
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message)
    return false
  }
}

/**
 * Display test credentials
 */
function displayTestCredentials() {
  console.log('🔑 Test Credentials')
  console.log('==================\n')
  
  console.log('🏫 School Admin Accounts:')
  testData.users.filter(user => user.role === 'school_admin').forEach(user => {
    console.log(`   ${user.school_code}.localhost:3000`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Password: ${user.password}`)
    console.log('')
  })
  
  console.log('👨‍🏫 Teacher Accounts:')
  testData.users.filter(user => user.role === 'teacher').forEach(user => {
    console.log(`   ${user.school_code}.localhost:3000`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Password: ${user.password}`)
    console.log('')
  })
  
  console.log('💡 Usage:')
  console.log('   1. Visit http://xba1224.localhost:3000')
  console.log('   2. Click "Login" or go to /auth/login')
  console.log('   3. Use the credentials above')
  console.log('   4. Test the school features\n')
}

/**
 * Main seeding function
 */
async function main() {
  try {
    const dbExists = await checkDatabase()
    
    // Always create mock data files for development
    await createMockDataFiles()
    
    // Seed database if available
    if (dbExists) {
      await seedDatabase()
    }
    
    displayTestCredentials()
    
    console.log('🎉 Test data seeding complete!')
    console.log('You can now test the application with the provided credentials.\n')
    
  } catch (error) {
    console.error('❌ Seeding failed:', error.message)
    process.exit(1)
  }
}

// Run the seeding
main()
