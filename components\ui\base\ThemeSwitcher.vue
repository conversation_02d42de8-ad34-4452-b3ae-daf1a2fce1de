<template>
    <button @click="toggleTheme" class="p-2 rounded-md hover:bg-light-card dark:hover:bg-dark-card transition-colors"
        aria-label="Toggle theme">
        <IconSun v-if="isDarkMode" class="h-6 w-6 text-light-foreground dark:text-dark-foreground" />
        <IconMoon v-else class="h-6 w-6 text-light-foreground dark:text-dark-foreground" />
    </button>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { IconSun, IconMoon } from '@tabler/icons-vue';

// @ts-ignore - useColorMode is auto-imported by Nuxt
const colorMode = useColorMode();
const isDarkMode = ref(colorMode.preference === 'dark');

const toggleTheme = () => {
    if (colorMode.preference === 'dark') {
        colorMode.preference = 'light';
        isDarkMode.value = false;
    } else {
        colorMode.preference = 'dark';
        isDarkMode.value = true;
    }
};

onMounted(() => {
    // Ensure the initial state is correct based on system preference or stored value
    isDarkMode.value = colorMode.value === 'dark';
    // Watch for changes from other sources (e.g., system preference change)
    watch(() => colorMode.value, (newValue) => {
        isDarkMode.value = newValue === 'dark';
    });
});
</script>
