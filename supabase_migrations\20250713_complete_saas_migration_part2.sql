-- Complete SaaS Multi-tenant Migration Script - Part 2
-- Created: 2025-07-13
-- Description: Add school_id columns, RLS policies, and helper functions

-- Log migration start for part 2
INSERT INTO migration_log (migration_name, status, notes) 
VALUES ('20250713_complete_saas_migration_part2', 'started', 'Adding school_id columns and RLS policies');

BEGIN;

-- =====================================================
-- STEP 5: ADD SCHOOL_ID TO EXISTING TABLES
-- =====================================================

-- Add school_id to user-specific tables
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE lesson_plans ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE lesson_plan_detailed_reflections ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE teacher_schedules ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE timetable_entries ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE observation_schedules ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE teacher_activities ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE teacher_tasks ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE teacher_observer_assignments ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE rph_weeks ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE user_week_submissions ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE academic_calendar_documents ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE annual_calendar_events ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE dskp_documents ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE rpt_documents ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE items ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE jadual_pencerapan ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE tidak_terlaksana ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE tindakan_susulan ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE user_reflection_template_preferences ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- Add nullable school_id to global/school-specific tables
ALTER TABLE subjects ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
ALTER TABLE reflection_templates ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- =====================================================
-- STEP 6: CREATE INDEXES FOR SCHOOL_ID COLUMNS
-- =====================================================

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_profiles_school_id ON profiles (school_id);
CREATE INDEX IF NOT EXISTS idx_lesson_plans_school_id ON lesson_plans (school_id);
CREATE INDEX IF NOT EXISTS idx_lesson_plan_detailed_reflections_school_id ON lesson_plan_detailed_reflections (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_schedules_school_id ON teacher_schedules (school_id);
CREATE INDEX IF NOT EXISTS idx_timetable_entries_school_id ON timetable_entries (school_id);
CREATE INDEX IF NOT EXISTS idx_observation_schedules_school_id ON observation_schedules (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_activities_school_id ON teacher_activities (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_tasks_school_id ON teacher_tasks (school_id);
CREATE INDEX IF NOT EXISTS idx_teacher_observer_assignments_school_id ON teacher_observer_assignments (school_id);
CREATE INDEX IF NOT EXISTS idx_rph_weeks_school_id ON rph_weeks (school_id);
CREATE INDEX IF NOT EXISTS idx_user_week_submissions_school_id ON user_week_submissions (school_id);
CREATE INDEX IF NOT EXISTS idx_academic_calendar_documents_school_id ON academic_calendar_documents (school_id);
CREATE INDEX IF NOT EXISTS idx_annual_calendar_events_school_id ON annual_calendar_events (school_id);
CREATE INDEX IF NOT EXISTS idx_dskp_documents_school_id ON dskp_documents (school_id);
CREATE INDEX IF NOT EXISTS idx_rpt_documents_school_id ON rpt_documents (school_id);
CREATE INDEX IF NOT EXISTS idx_items_school_id ON items (school_id);
CREATE INDEX IF NOT EXISTS idx_jadual_pencerapan_school_id ON jadual_pencerapan (school_id);
CREATE INDEX IF NOT EXISTS idx_tidak_terlaksana_school_id ON tidak_terlaksana (school_id);
CREATE INDEX IF NOT EXISTS idx_tindakan_susulan_school_id ON tindakan_susulan (school_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_school_id ON user_preferences (school_id);
CREATE INDEX IF NOT EXISTS idx_user_reflection_template_preferences_school_id ON user_reflection_template_preferences (school_id);
CREATE INDEX IF NOT EXISTS idx_subjects_school_id ON subjects (school_id);
CREATE INDEX IF NOT EXISTS idx_reflection_templates_school_id ON reflection_templates (school_id);

-- =====================================================
-- STEP 7: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to get schools that a user has access to
CREATE OR REPLACE FUNCTION get_user_school_ids(user_uuid UUID DEFAULT auth.uid())
RETURNS UUID[] AS $$
BEGIN
    RETURN ARRAY(
        SELECT school_id 
        FROM school_memberships 
        WHERE user_id = user_uuid 
        AND status = 'active'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific role in any school
CREATE OR REPLACE FUNCTION user_has_role_in_schools(required_role TEXT, user_uuid UUID DEFAULT auth.uid())
RETURNS UUID[] AS $$
BEGIN
    RETURN ARRAY(
        SELECT school_id 
        FROM school_memberships 
        WHERE user_id = user_uuid 
        AND status = 'active'
        AND role = required_role
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if a coupon is valid
CREATE OR REPLACE FUNCTION is_coupon_valid(coupon_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    coupon_record RECORD;
    current_time TIMESTAMP WITH TIME ZONE := timezone('utc'::text, now());
BEGIN
    SELECT * INTO coupon_record 
    FROM coupons 
    WHERE UPPER(code) = UPPER(coupon_code);
    
    IF NOT FOUND THEN RETURN FALSE; END IF;
    IF NOT coupon_record.is_active THEN RETURN FALSE; END IF;
    IF coupon_record.starts_at > current_time THEN RETURN FALSE; END IF;
    IF coupon_record.expires_at IS NOT NULL AND coupon_record.expires_at < current_time THEN RETURN FALSE; END IF;
    IF coupon_record.usage_limit IS NOT NULL AND coupon_record.used_count >= coupon_record.usage_limit THEN RETURN FALSE; END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to record coupon usage
CREATE OR REPLACE FUNCTION record_coupon_usage(
    p_coupon_code TEXT,
    p_school_id UUID,
    p_used_by UUID,
    p_usage_type TEXT DEFAULT 'school_registration',
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_coupon_id UUID;
    v_coupon_record RECORD;
    v_usage_id UUID;
BEGIN
    SELECT id, discount_type, discount_value INTO v_coupon_record
    FROM coupons 
    WHERE UPPER(code) = UPPER(p_coupon_code) AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Coupon not found or inactive: %', p_coupon_code;
    END IF;
    
    v_coupon_id := v_coupon_record.id;
    
    IF NOT is_coupon_valid(p_coupon_code) THEN
        RAISE EXCEPTION 'Coupon is not valid: %', p_coupon_code;
    END IF;
    
    INSERT INTO coupon_usage (
        coupon_id, school_id, used_by, usage_type,
        original_discount_type, original_discount_value,
        applied_discount_amount, user_agent, ip_address
    ) VALUES (
        v_coupon_id, p_school_id, p_used_by, p_usage_type,
        v_coupon_record.discount_type, v_coupon_record.discount_value,
        CASE WHEN v_coupon_record.discount_type = 'free_registration' THEN 100.00 ELSE v_coupon_record.discount_value END,
        p_user_agent, p_ip_address
    ) RETURNING id INTO v_usage_id;
    
    RETURN v_usage_id;
END;
$$ LANGUAGE plpgsql;

COMMIT;

-- Log successful completion of part 2
UPDATE migration_log 
SET status = 'completed', notes = 'School_id columns and helper functions added successfully'
WHERE migration_name = '20250713_complete_saas_migration_part2' AND status = 'started';
