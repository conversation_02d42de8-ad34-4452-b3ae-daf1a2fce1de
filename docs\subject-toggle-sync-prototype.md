# Subject Toggle Sync - Quick Prototype

## Problem
When switching between "Singkatan" (abbreviation) and "Penuh" (full name) modes in ActivityEntryModal, the dropdown options update correctly, but the currently selected option's display text doesn't change to match the new format.

## Solution: Reactive Key Approach

### Key Changes Required

#### 1. Add Reactive Key to SingleSelect Component

**Current Code (ActivityEntryModal.vue:52-53):**
```vue
<SingleSelect v-model="formData.class_subject_combination" :options="classSubjectOptions"
    placeholder="Pilih kelas dan subjek" :error="errors.class_subject" />
```

**Updated Code:**
```vue
<SingleSelect 
    :key="singleSelectKey" 
    v-model="formData.class_subject_combination" 
    :options="classSubjectOptions"
    placeholder="Pilih kelas dan subjek" 
    :error="errors.class_subject" />
```

#### 2. Add Reactive Key Variable

**Add to script setup section (around line 166):**
```typescript
// Reactive key to force SingleSelect re-render when toggle changes
const singleSelectKey = ref(0)
```

#### 3. Update the showFullSubjectNames Watcher

**Current Code (ActivityEntryModal.vue:538-553):**
```typescript
watch(showFullSubjectNames, (newValue) => {
    if (formData.value.class_subject_combination) {
        const [classId, subjectId] = formData.value.class_subject_combination.split('|');
        const classSubject = props.userClassSubjects.find(cs =>
            cs.class_id === classId && cs.subject_id === subjectId
        );

        if (classSubject) {
            const subjectName = newValue
                ? getSubjectName(subjectId)
                : (classSubject.subject_abbreviation || getSubjectName(subjectId));

            formData.value.class_subject_combination = `${classId}|${subjectId}`;
        }
    }
});
```

**Updated Code:**
```typescript
watch(showFullSubjectNames, (newValue) => {
    if (formData.value.class_subject_combination) {
        const [classId, subjectId] = formData.value.class_subject_combination.split('|');
        const classSubject = props.userClassSubjects.find(cs =>
            cs.class_id === classId && cs.subject_id === subjectId
        );

        if (classSubject) {
            // Store current selection
            const currentSelection = formData.value.class_subject_combination;
            
            // Force SingleSelect re-render to update display text
            singleSelectKey.value++;
            
            // Restore selection after re-render
            nextTick(() => {
                formData.value.class_subject_combination = currentSelection;
            });
        }
    }
});
```

## How It Works

1. **Reactive Key**: The `:key="singleSelectKey"` forces Vue to completely re-render the SingleSelect component when the key changes
2. **Key Increment**: When `showFullSubjectNames` changes, we increment `singleSelectKey.value++`
3. **Selection Preservation**: We store the current selection and restore it after the re-render using `nextTick()`
4. **Display Update**: The re-rendered SingleSelect uses the updated `classSubjectOptions` computed property, which now shows the correct format

## Expected Behavior

**Before Toggle:**
- Selected: "1A - BM" (abbreviation mode)
- Dropdown shows: "1A - BM", "1A - BI", etc.

**After Toggle to "Penuh":**
- Selected: "1A - Bahasa Melayu" (full name mode)
- Dropdown shows: "1A - Bahasa Melayu", "1A - Bahasa Inggeris", etc.
- **Same selection value internally**: `"t1|subject-uuid-123"`

## Benefits

✅ **Minimal Code Changes**: Only 3 small modifications
✅ **Preserves Selection**: The actual selected value remains unchanged
✅ **Immediate Visual Feedback**: User sees the change instantly
✅ **Vue-Native Solution**: Uses Vue's reactivity system
✅ **No Breaking Changes**: Existing functionality remains intact

## Alternative Approaches Considered

1. **Manual Display Update**: More complex, requires custom logic
2. **SingleSelect Enhancement**: Would require modifying the base component
3. **Computed Display Property**: Adds complexity to the template

The reactive key approach is the cleanest and most maintainable solution.