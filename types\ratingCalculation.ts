import type { <PERSON><PERSON><PERSON>Week, SchedulePeriod } from './teacherSchedule'
import type { LessonPlan } from './lessonPlans'
import type { LessonPlanDetailedReflection } from './reflections'

// Core calculation utilities for the rating system

// Helper function type for calculating overall rating
export interface RatingCalculationInput {
  lesson_plan: LessonPlan
  teacher_schedules: Array<{
    class_id: string
    subject_id: string
    days_scheduled: DayOfWeek[]
  }>
  detailed_reflections: LessonPlanDetailedReflection[]
  user_class_subjects?: any[] // From user profile
}

// Result of rating calculation
export interface RatingCalculationResult {
  total_periods: number
  total_stars: number
  calculated_rating: number // Rounded to nearest 0.1
  periods_with_reflections: number
  periods_using_default: number
  periods_tidak_terlaksana: number
  period_details: Array<{
    class_id: string
    subject_id: string
    day: DayOfWeek
    class_name: string
    subject_name: string
    class_subject_id: string
    rating: number
    has_reflection: boolean
  }>
}

// Configuration for the rating system
export interface RatingSystemConfig {
  default_rating: number // Default: 5
  rounding_precision: number // Default: 0.1 (round to nearest 0.1)
}

// Helper for matching lesson plan class-subjects with teacher schedules
export interface ScheduleMatchResult {
  matched_periods: SchedulePeriod[]
  unscheduled_class_subjects: string[] // class-subject combinations in lesson plan but not in schedule
  extra_schedules: string[] // schedules that don't match any lesson plan class-subjects
}

// For debugging and validation
export interface RatingCalculationDebugInfo {
  lesson_plan_class_subjects: string[]
  scheduled_class_subjects: string[]
  total_lesson_plan_days: number
  calculation_steps: Array<{
    step: string
    value: any
    description: string
  }>
}
