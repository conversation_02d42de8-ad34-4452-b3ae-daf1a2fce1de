import { ref, computed } from "vue";
import { useSupabaseClient, useSupabaseUser } from "#imports";
import type { RphWeek, RphWeekUserInput, RphWeekUpdate } from "~/types/rph";
import type { Database } from "~/types/supabase";
import { useLessonPlans } from "./useLessonPlans";
import type { LessonPlan } from "~/types/lessonPlans"; // Import LessonPlan type

export function useRphWeeks() {
  const client = useSupabaseClient<Database>();
  const user = useSupabaseUser();
  // Use the new internal function to avoid side effects on useLessonPlans state
  const { fetchLessonPlansByWeekId_internal } = useLessonPlans();

  const weeks = ref<RphWeek[]>([]);
  const loading = ref(false);
  const error = ref<any>(null);

  // Computed property to get the next suggested week number
  const nextWeekNumber = computed(() => {
    if (weeks.value.length === 0) {
      return 1;
    }
    // Assuming weeks are sorted by week_number
    const lastWeek = weeks.value[weeks.value.length - 1];
    return (lastWeek.week_number || 0) + 1;
  });

  // Computed property for the suggested name for the next week
  const suggestedNextWeekName = computed(() => {
    return `Minggu ${nextWeekNumber.value}`;
  });

  async function fetchWeeks() {
    // Removed year parameter
    if (!user.value) {
      error.value = "User not authenticated";
      weeks.value = [];
      return;
    }
    loading.value = true;
    error.value = null;
    try {
      let query = client
        .from("rph_weeks")
        .select("*")
        .eq("user_id", user.value.id)
        // .order('year', { ascending: true }) // REMOVED year ordering
        .order("week_number", { ascending: true });

      // REMOVED: if (year) { query = query.eq('year', year); }

      const { data, error: fetchError } = await query;

      if (fetchError) throw fetchError;
      weeks.value = data || [];
    } catch (e) {
      error.value = e;
      weeks.value = [];
      console.error("Error fetching weeks:", e);
    } finally {
      loading.value = false;
    }
  }

  async function addWeek(
    weekDetails: RphWeekUserInput
  ): Promise<RphWeek | null> {
    if (!user.value) {
      error.value = "User not authenticated";
      return null;
    }
    loading.value = true;
    error.value = null;
    try {
      const newWeekData: Omit<
        Database["public"]["Tables"]["rph_weeks"]["Insert"],
        "year" | "id" | "created_at" | "updated_at" | "user_id"
      > & { user_id: string } = {
        ...weekDetails,
        user_id: user.value.id,
      };

      const { data, error: insertError } = await client
        .from("rph_weeks")
        .insert(newWeekData)
        .select()
        .single();

      if (insertError) {
        if (insertError.code === "23505") {
          // Unique constraint violation
          console.error(
            "Error adding week: Unique constraint violation (e.g., duplicate name or week_number for user)",
            insertError
          );
          error.value = {
            message: `A week with this name or number already exists.`,
            details: insertError,
          };
        } else {
          throw insertError;
        }
        return null;
      }

      if (data) {
        // Manually add to the local state and re-sort, or refetch
        // For simplicity, refetching, but for better UX, update locally
        await fetchWeeks(); // Re-fetch to get the sorted list with the new week
        return data as RphWeek;
      }
      return null;
    } catch (e) {
      error.value = e;
      console.error("Error adding week:", e);
      return null;
    } finally {
      loading.value = false;
    }
  }

  async function updateWeek(
    weekId: string, // Changed from number to string
    weekDetails: RphWeekUpdate
  ): Promise<RphWeek | null> {
    if (!user.value) {
      error.value = "User not authenticated";
      return null;
    }
    loading.value = true;
    error.value = null;
    try {
      const updateData: Omit<
        Database["public"]["Tables"]["rph_weeks"]["Update"],
        "user_id" | "id" | "created_at" | "updated_at"
      > = {
        ...weekDetails,
      };

      const { data, error: updateError } = await client
        .from("rph_weeks")
        .update(updateData)
        .eq("id", weekId) // weekId is now a string
        .eq("user_id", user.value.id)
        .select()
        .single();

      if (updateError) {
        if (updateError.code === "23505") {
          console.error(
            "Error updating week: Unique constraint violation",
            updateError
          );
          error.value = {
            message: `A week with this name or number already exists.`,
            details: updateError,
          };
        } else {
          throw updateError;
        }
        return null;
      }

      if (data) {
        const index = weeks.value.findIndex((w) => w.id === weekId); // Comparison is now string vs string
        if (index !== -1) {
          weeks.value[index] = { ...weeks.value[index], ...data } as RphWeek;
        }
        weeks.value.sort((a, b) => (a.week_number || 0) - (b.week_number || 0));
        return data as RphWeek;
      }
      return null;
    } catch (e) {
      error.value = e;
      console.error("Error updating week:", e);
      return null;
    } finally {
      loading.value = false;
    }
  }

  async function deleteWeek(weekId: string): Promise<boolean> {
    // weekId is now string
    if (!user.value) {
      error.value = "User not authenticated";
      return false;
    }
    loading.value = true;
    error.value = null;

    try {
      // Step 1: Fetch all lesson plans for this week using the internal function
      const lessonPlansToDelete: LessonPlan[] | null =
        await fetchLessonPlansByWeekId_internal(weekId);

      if (lessonPlansToDelete && lessonPlansToDelete.length > 0) {
        const filePathsToDelete: string[] = lessonPlansToDelete
          .map((lp: LessonPlan) => lp.storage_file_path) // Added type for lp, changed to storage_file_path
          .filter((path): path is string => !!path && path.trim() !== "");

        if (filePathsToDelete.length > 0) {
          const { data: fileDeleteData, error: fileDeleteError } =
            await client.storage.from("rph-files").remove(filePathsToDelete);

          if (fileDeleteError) {
            console.error(
              `Error deleting files from storage for week ${weekId}:`,
              fileDeleteError
            );
            // Optionally, set error.value or throw if file deletion failure should halt the process
          } else {
            // console.log(
            //   `Successfully deleted files from storage for week ${weekId}:`,
            //   fileDeleteData
            // );
          }
        }
      }

      // Step 2: Delete the week record from the database
      const { error: deleteError } = await client
        .from("rph_weeks")
        .delete()
        .eq("id", weekId) // weekId is now string
        .eq("user_id", user.value.id);

      if (deleteError) throw deleteError;

      weeks.value = weeks.value.filter((w) => w.id !== weekId); // Comparison is now string vs string
      return true;
    } catch (e) {
      error.value = e;
      console.error(`Error deleting week ${weekId}:`, e);
      return false;
    } finally {
      loading.value = false;
    }
  }

  // Initial fetch of weeks when the composable is used
  // fetchWeeks(); // Consider if auto-fetch is desired or should be manually triggered

  return {
    weeks,
    loading,
    error,
    fetchWeeks,
    addWeek,
    updateWeek,
    deleteWeek,
    nextWeekNumber,
    suggestedNextWeekName,
  };
}
