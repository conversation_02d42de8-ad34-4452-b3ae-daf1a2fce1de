// Global middleware for subdomain detection and routing
// Created: 2025-07-13

import { navigateTo, defineNuxtRouteMiddleware } from "#app"
import type { SubdomainInfo, RouteContext } from "~/types/multiTenant"

export default defineNuxtRouteMiddleware(async (to, from) => {
  // Enable subdomain middleware for Phase 2 implementation
  // Updated for new RPHMate SaaS architecture

  // Skip middleware on server-side rendering for static generation
  if (process.server && !process.env.NUXT_SSR_ENABLED) {
    return
  }

  // Get subdomain information
  const subdomainInfo = getSubdomainInfo()

  // Determine route context
  const routeContext = determineRouteContext(to.path, subdomainInfo)

  // Handle routing based on context
  return await handleRouting(to, routeContext, subdomainInfo)
})

/**
 * Extract subdomain information from the current host
 */
function getSubdomainInfo(): SubdomainInfo {
  let host: string

  // Get host from server-side or client-side
  if (typeof window === 'undefined') {
    // Server-side: get host from Nuxt context
    const nuxtApp = useNuxtApp()
    const event = nuxtApp.ssrContext?.event
    host = event ? getHeader(event, 'host') || 'localhost:3000' : 'localhost:3000'
  } else {
    // Client-side: get host from window
    host = window.location.host
  }

  const parts = host.split('.')
  
  // For development (localhost:3000 or schoolcode.localhost:3000)
  if (host.includes('localhost')) {
    if (parts.length > 1 && parts[0] !== 'localhost') {
      const schoolCode = parts[0].toLowerCase()
      return {
        isSchoolSubdomain: true,
        schoolCode,
        isMainDomain: false,
        subdomain: schoolCode
      }
    }
    return {
      isSchoolSubdomain: false,
      schoolCode: null,
      isMainDomain: true,
      subdomain: null
    }
  }

  // For production (schoolcode.yourdomain.com)
  if (parts.length >= 3) {
    const schoolCode = parts[0].toLowerCase()
    // Exclude common subdomains that are not school codes
    const excludedSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp']
    
    if (!excludedSubdomains.includes(schoolCode)) {
      return {
        isSchoolSubdomain: true,
        schoolCode,
        isMainDomain: false,
        subdomain: schoolCode
      }
    }
  }

  return {
    isSchoolSubdomain: false,
    schoolCode: null,
    isMainDomain: true,
    subdomain: null
  }
}

/**
 * Determine the route context based on path and subdomain
 */
function determineRouteContext(path: string, subdomainInfo: SubdomainInfo): RouteContext {
  // Landing page routes (main domain)
  if (subdomainInfo.isMainDomain) {
    if (path === '/' || path.startsWith('/pricing') || path.startsWith('/billing') || path.startsWith('/features') || path.startsWith('/contact')) {
      return {
        isLandingPage: true,
        isAdminDashboard: false,
        isSchoolApp: false,
        schoolCode: null,
        requiredAuth: false
      }
    }

    // School admin login (main domain)
    if (path === '/login') {
      return {
        isLandingPage: false,
        isAdminDashboard: false,
        isSchoolApp: false,
        schoolCode: null,
        requiredAuth: false
      }
    }

    // School admin dashboard routes (main domain - /schoolcode)
    if (path.match(/^\/[a-zA-Z0-9]+$/)) {
      return {
        isLandingPage: false,
        isAdminDashboard: true,
        isSchoolApp: false,
        schoolCode: path.substring(1), // Extract school code from path
        requiredAuth: true,
        requiredRole: 'school_admin'
      }
    }
  }

  // School app routes (school subdomain)
  if (subdomainInfo.isSchoolSubdomain) {
    // School admin page (schoolcode.domain.com/admin)
    if (path === '/admin') {
      return {
        isLandingPage: false,
        isAdminDashboard: false,
        isSchoolApp: true,
        isSchoolAdmin: true,
        schoolCode: subdomainInfo.schoolCode,
        requiredAuth: true,
        requiredRole: 'school_admin'
      }
    }

    // Regular school app routes
    return {
      isLandingPage: false,
      isAdminDashboard: false,
      isSchoolApp: true,
      isSchoolAdmin: false,
      schoolCode: subdomainInfo.schoolCode,
      requiredAuth: !path.includes('/auth/'),
      requiredRole: 'teacher' // Default role, can be admin, supervisor, or teacher
    }
  }

  // Default context
  return {
    isLandingPage: true,
    isAdminDashboard: false,
    isSchoolApp: false,
    schoolCode: null,
    requiredAuth: false
  }
}

/**
 * Handle routing based on context
 */
async function handleRouting(to: any, routeContext: RouteContext, subdomainInfo: SubdomainInfo) {
  // Skip middleware for main domain public pages
  const mainDomainPublicPaths = [
    '/',
    '/login',
    '/pricing',
    '/features',
    '/contact',
    '/billing',
    '/pembayaran',
    '/daftar',
    '/daftar/maklumat',
    '/daftar/verify-email',
    '/daftar/confirm',
    '/success'
  ]
  if (subdomainInfo.isMainDomain && mainDomainPublicPaths.includes(to.path)) {
    return
  }

  // Skip auth routes for school subdomains - let the layout handle validation
  // This prevents hydration mismatches while still securing the routes
  if (subdomainInfo.isSchoolSubdomain && to.path.includes('/auth/')) {
    return
  }

  // Handle school subdomain routing - validate school existence for non-auth routes
  if (subdomainInfo.isSchoolSubdomain) {
    return await handleSchoolRouting(to, routeContext, subdomainInfo)
  }

  // Handle main domain routing
  if (subdomainInfo.isMainDomain) {
    return await handleMainDomainRouting(to, routeContext)
  }
}

/**
 * Handle routing for school subdomains
 */
async function handleSchoolRouting(to: any, routeContext: RouteContext, subdomainInfo: SubdomainInfo) {
  const supabase = useSupabaseClient()

  // First, check if the school actually exists in the database
  try {
    const schoolExistsResponse = await $fetch('/api/schools/exists', {
      method: 'POST',
      body: { code: subdomainInfo.schoolCode }
    }) as any

    if (!schoolExistsResponse.exists) {
      // School doesn't exist - return 404
      throw createError({
        statusCode: 404,
        statusMessage: 'School Not Found',
        data: {
          message: `School with code "${subdomainInfo.schoolCode}" does not exist.`,
          suggestion: 'Please check the school code or contact your school administrator.'
        }
      })
    }
  } catch (error: any) {
    // If it's already a 404 error, re-throw it
    if (error.statusCode === 404) {
      throw error
    }

    // For other errors, log and return 404 to be safe
    console.error('Error checking school existence:', error)
    throw createError({
      statusCode: 404,
      statusMessage: 'School Not Found',
      data: {
        message: 'Unable to verify school existence.',
        suggestion: 'Please try again later or contact support.'
      }
    })
  }

  // Check if user is authenticated
  if (routeContext.requiredAuth) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        // Redirect to school login
        return navigateTo(`/${subdomainInfo.schoolCode}/auth/login`)
      }

      // Check if user has access to this school
      try {
        const response = await $fetch('/api/schools/validate-access', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session.access_token}`
          },
          body: { schoolCode: subdomainInfo.schoolCode }
        }) as any

        if (!response.hasAccess) {
          // User doesn't have access to this school
          return navigateTo('/admin/dashboard')
        }
      } catch (accessError) {
        console.error('Error checking school access:', accessError)
        return navigateTo(`/${subdomainInfo.schoolCode}/auth/login`)
      }

    } catch (error) {
      console.error('Error checking authentication:', error)
      return navigateTo(`/${subdomainInfo.schoolCode}/auth/login`)
    }
  }

  // Ensure the route includes the school code parameter
  if (!to.params.school && subdomainInfo.schoolCode) {
    const newPath = `/${subdomainInfo.schoolCode}${to.path}`
    return navigateTo(newPath)
  }
}

/**
 * Handle routing for main domain
 */
async function handleMainDomainRouting(to: any, routeContext: RouteContext) {
  const supabase = useSupabaseClient()

  // Handle school admin dashboard authentication (/schoolcode routes)
  if (routeContext.isAdminDashboard && routeContext.requiredAuth) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        return navigateTo('/login')
      }

      // Check if user has access to this school
      if (routeContext.schoolCode) {
        try {
          const response = await $fetch('/api/schools/validate-admin-access', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`
            },
            body: { schoolCode: routeContext.schoolCode }
          }) as any

          if (!response.hasAccess) {
            // User doesn't have admin access to this school
            return navigateTo('/login')
          }
        } catch (accessError) {
          console.error('Error validating school admin access:', accessError)
          return navigateTo('/login')
        }
      }

    } catch (error) {
      console.error('Error checking admin authentication:', error)
      return navigateTo('/login')
    }
  }

  // Redirect authenticated users away from login page
  if (to.path === '/login' && routeContext.requiredAuth === false) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        // Get user's schools and redirect to first one
        try {
          const response = await $fetch('/api/schools/user-schools', {
            headers: {
              'Authorization': `Bearer ${session.access_token}`
            }
          }) as any

          if (response.success && response.schools && response.schools.length > 0) {
            // Redirect to the first school's admin dashboard
            const firstSchool = response.schools[0]
            return navigateTo(`/${firstSchool.code}`)
          } else {
            // No schools found, redirect to pricing page to create one
            return navigateTo('/pricing')
          }
        } catch (error) {
          console.error('Error fetching user schools:', error)
          // Continue to login page if there's an error
        }
      }
    } catch (error) {
      // Continue to login page if there's an error
      console.error('Error checking session:', error)
    }
  }
}

/**
 * Utility function to check if a school code is valid
 * This will be used later when we implement school validation
 */
async function validateSchoolCode(schoolCode: string): Promise<boolean> {
  // TODO: Implement school code validation against database
  // For now, return true to allow development
  return true
}

/**
 * Utility function to check user's access to a school
 * This will be used later when we implement school membership validation
 */
async function checkSchoolAccess(userId: string, schoolCode: string): Promise<boolean> {
  // TODO: Implement school access validation
  // Check if user has active membership in the school
  return true
}
