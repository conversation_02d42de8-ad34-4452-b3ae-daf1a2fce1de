// Global middleware for subdomain detection and routing
// Created: 2025-07-13

import { navigateTo, defineNuxtRouteMiddleware } from "#app"
import type { SubdomainInfo, RouteContext } from "~/types/multiTenant"

// Cache for school validation and user access to improve performance
const schoolValidationCache = new Map<string, { exists: boolean, timestamp: number }>()
const userAccessCache = new Map<string, { hasAccess: boolean, timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export default defineNuxtRouteMiddleware(async (to, from) => {
  // SMART SUBDOMAIN ROUTING - Optimized for performance

  // Skip on server-side for static generation
  if (process.server && !process.env.NUXT_SSR_ENABLED) {
    return
  }

  // Get subdomain information
  const subdomainInfo = getSubdomainInfo()

  // CRITICAL FIX: Skip ALL auth routes immediately - no validation needed
  if (to.path.includes('/auth/')) {
    console.log('🔓 Skipping middleware for auth route:', to.path)
    return
  }

  // EARLY RETURN: Skip middleware for main domain public pages
  if (subdomainInfo.isMainDomain) {
    const publicPaths = ['/', '/login', '/pricing', '/features', '/contact', '/billing', '/pembayaran', '/daftar', '/success']
    if (publicPaths.some(path => to.path === path || to.path.startsWith(path))) {
      return
    }
  }

  // Handle school subdomain routing with smart authentication
  if (subdomainInfo.isSchoolSubdomain) {
    console.log('🏫 Running school validation for:', to.path)
    return await handleSchoolRoutingSmart(to, subdomainInfo)
  }

  // Handle main domain routing
  if (subdomainInfo.isMainDomain) {
    const routeContext = {
      isLandingPage: false,
      isAdminDashboard: true,
      isSchoolApp: false,
      schoolCode: to.path.substring(1),
      requiredAuth: true
    }
    return await handleMainDomainRouting(to, routeContext)
  }
})

/**
 * Extract subdomain information from the current host
 */
function getSubdomainInfo(): SubdomainInfo {
  let host: string

  // Get host from server-side or client-side
  if (typeof window === 'undefined') {
    // Server-side: get host from Nuxt context
    const nuxtApp = useNuxtApp()
    const event = nuxtApp.ssrContext?.event
    host = event ? getHeader(event, 'host') || 'localhost:3000' : 'localhost:3000'
  } else {
    // Client-side: get host from window
    host = window.location.host
  }

  const parts = host.split('.')
  
  // For development (localhost:3000 or schoolcode.localhost:3000)
  if (host.includes('localhost')) {
    if (parts.length > 1 && parts[0] !== 'localhost') {
      const schoolCode = parts[0].toLowerCase()
      return {
        isSchoolSubdomain: true,
        schoolCode,
        isMainDomain: false,
        subdomain: schoolCode
      }
    }
    return {
      isSchoolSubdomain: false,
      schoolCode: null,
      isMainDomain: true,
      subdomain: null
    }
  }

  // For production (schoolcode.yourdomain.com)
  if (parts.length >= 3) {
    const schoolCode = parts[0].toLowerCase()
    // Exclude common subdomains that are not school codes
    const excludedSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp']
    
    if (!excludedSubdomains.includes(schoolCode)) {
      return {
        isSchoolSubdomain: true,
        schoolCode,
        isMainDomain: false,
        subdomain: schoolCode
      }
    }
  }

  return {
    isSchoolSubdomain: false,
    schoolCode: null,
    isMainDomain: true,
    subdomain: null
  }
}

/**
 * Determine the route context based on path and subdomain
 */
function determineRouteContext(path: string, subdomainInfo: SubdomainInfo): RouteContext {
  // Landing page routes (main domain)
  if (subdomainInfo.isMainDomain) {
    if (path === '/' || path.startsWith('/pricing') || path.startsWith('/billing') || path.startsWith('/features') || path.startsWith('/contact')) {
      return {
        isLandingPage: true,
        isAdminDashboard: false,
        isSchoolApp: false,
        schoolCode: null,
        requiredAuth: false
      }
    }

    // School admin login (main domain)
    if (path === '/login') {
      return {
        isLandingPage: false,
        isAdminDashboard: false,
        isSchoolApp: false,
        schoolCode: null,
        requiredAuth: false
      }
    }

    // School admin dashboard routes (main domain - /schoolcode)
    if (path.match(/^\/[a-zA-Z0-9]+$/)) {
      return {
        isLandingPage: false,
        isAdminDashboard: true,
        isSchoolApp: false,
        schoolCode: path.substring(1), // Extract school code from path
        requiredAuth: true,
        requiredRole: 'school_admin'
      }
    }
  }

  // School app routes (school subdomain)
  if (subdomainInfo.isSchoolSubdomain) {
    // School admin page (schoolcode.domain.com/admin)
    if (path === '/admin') {
      return {
        isLandingPage: false,
        isAdminDashboard: false,
        isSchoolApp: true,
        isSchoolAdmin: true,
        schoolCode: subdomainInfo.schoolCode,
        requiredAuth: true,
        requiredRole: 'school_admin'
      }
    }

    // Regular school app routes
    return {
      isLandingPage: false,
      isAdminDashboard: false,
      isSchoolApp: true,
      isSchoolAdmin: false,
      schoolCode: subdomainInfo.schoolCode,
      requiredAuth: !path.includes('/auth/'),
      requiredRole: 'teacher' // Default role, can be admin, supervisor, or teacher
    }
  }

  // Default context
  return {
    isLandingPage: true,
    isAdminDashboard: false,
    isSchoolApp: false,
    schoolCode: null,
    requiredAuth: false
  }
}

// OLD ROUTING LOGIC REMOVED - Now handled directly in main middleware

/**
 * SMART School Routing - Optimized for performance and user experience
 */
async function handleSchoolRoutingSmart(to: any, subdomainInfo: SubdomainInfo) {
  const supabase = useSupabaseClient()

  // STEP 1: Quick authentication check first (no API calls)
  const { data: { session } } = await supabase.auth.getSession()

  if (!session) {
    // User not authenticated - redirect to login immediately
    // No need to validate school existence for unauthenticated users
    return navigateTo('/auth/login')
  }

  // STEP 2: Validate school existence and user access in parallel (with caching)
  try {
    const schoolCode = subdomainInfo.schoolCode!
    const userId = session.user.id
    const now = Date.now()

    // Check caches first
    const schoolCached = schoolValidationCache.get(schoolCode)
    const accessCached = userAccessCache.get(`${userId}-${schoolCode}`)

    let schoolExists = false
    let hasAccess = false

    // Determine what we need to fetch
    const needSchoolCheck = !schoolCached || (now - schoolCached.timestamp) >= CACHE_DURATION
    const needAccessCheck = !accessCached || (now - accessCached.timestamp) >= CACHE_DURATION

    if (needSchoolCheck || needAccessCheck) {
      // Run parallel API calls only for what we need
      const promises: Promise<any>[] = []

      if (needSchoolCheck) {
        promises.push(
          $fetch('/api/schools/exists', {
            method: 'POST',
            body: { code: schoolCode }
          }).then(response => ({ type: 'school', data: response }))
        )
      }

      if (needAccessCheck) {
        promises.push(
          $fetch('/api/schools/validate-access', {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${session.access_token}` },
            body: { schoolCode }
          }).then(response => ({ type: 'access', data: response }))
        )
      }

      // Wait for all API calls
      const results = await Promise.all(promises)

      // Process results and update cache
      for (const result of results) {
        if (result.type === 'school') {
          schoolExists = result.data.exists
          schoolValidationCache.set(schoolCode, { exists: schoolExists, timestamp: now })
        } else if (result.type === 'access') {
          hasAccess = result.data.hasAccess
          userAccessCache.set(`${userId}-${schoolCode}`, { hasAccess, timestamp: now })
        }
      }
    }

    // Use cached values if we didn't fetch
    if (!needSchoolCheck) schoolExists = schoolCached!.exists
    if (!needAccessCheck) hasAccess = accessCached!.hasAccess

    // STEP 3: Handle results with proper error messages
    if (!schoolExists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'School Not Found',
        data: {
          message: `School "${schoolCode}" does not exist.`,
          suggestion: 'Please check the school code or contact support.'
        }
      })
    }

    if (!hasAccess) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access Denied',
        data: {
          message: `You don't have access to ${schoolCode}.`,
          suggestion: 'Please contact your school administrator to get access.'
        }
      })
    }

    // SUCCESS: User is authenticated and has access
    return

  } catch (error: any) {
    // Handle specific errors
    if (error.statusCode === 404 || error.statusCode === 403) {
      throw error
    }

    // For other errors, redirect to login with error message
    console.error('Error in school routing:', error)
    return navigateTo('/auth/login?error=system')
  }
}

/**
 * Handle routing for main domain
 */
async function handleMainDomainRouting(to: any, routeContext: RouteContext) {
  const supabase = useSupabaseClient()

  // Handle school admin dashboard authentication (/schoolcode routes)
  if (routeContext.isAdminDashboard && routeContext.requiredAuth) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        return navigateTo('/login')
      }

      // Check if user has access to this school
      if (routeContext.schoolCode) {
        try {
          const response = await $fetch('/api/schools/validate-admin-access', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`
            },
            body: { schoolCode: routeContext.schoolCode }
          }) as any

          if (!response.hasAccess) {
            // User doesn't have admin access to this school
            return navigateTo('/login')
          }
        } catch (accessError) {
          console.error('Error validating school admin access:', accessError)
          return navigateTo('/login')
        }
      }

    } catch (error) {
      console.error('Error checking admin authentication:', error)
      return navigateTo('/login')
    }
  }

  // Redirect authenticated users away from login page
  if (to.path === '/login' && routeContext.requiredAuth === false) {
    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        // Get user's schools and redirect to first one
        try {
          const response = await $fetch('/api/schools/user-schools', {
            headers: {
              'Authorization': `Bearer ${session.access_token}`
            }
          }) as any

          if (response.success && response.schools && response.schools.length > 0) {
            // Redirect to the first school's admin dashboard
            const firstSchool = response.schools[0]
            return navigateTo(`/${firstSchool.code}`)
          } else {
            // No schools found, redirect to pricing page to create one
            return navigateTo('/pricing')
          }
        } catch (error) {
          console.error('Error fetching user schools:', error)
          // Continue to login page if there's an error
        }
      }
    } catch (error) {
      // Continue to login page if there's an error
      console.error('Error checking session:', error)
    }
  }
}

/**
 * Utility function to check if a school code is valid
 * This will be used later when we implement school validation
 */
async function validateSchoolCode(schoolCode: string): Promise<boolean> {
  // TODO: Implement school code validation against database
  // For now, return true to allow development
  return true
}

/**
 * Utility function to check user's access to a school
 * This will be used later when we implement school membership validation
 */
async function checkSchoolAccess(userId: string, schoolCode: string): Promise<boolean> {
  // TODO: Implement school access validation
  // Check if user has active membership in the school
  return true
}
