<template>
  <div class="space-y-8">
    <!-- <PERSON> Header Skeleton -->
    <SkeletonPageHeader title-width="12rem" subtitle-width="24rem" :show-actions="false" />

    <!-- 1. <PERSON><PERSON> Kurikulum Section -->
    <div class="bg-light-card dark:bg-dark-card rounded-lg p-4 shadow-md">
      <!-- Section Header -->
      <div class="mb-4 border-b border-light-border dark:border-dark-border pb-2">
        <div class="flex items-center space-x-2">
          <SkeletonBox width="1.25rem" height="1.25rem" class="rounded" />
          <SkeletonBox width="10rem" height="1.5rem" />
        </div>
      </div>

      <!-- Table Skeleton -->
      <SkeletonTable :rows="3" :columns="3" show-header />

      <!-- Info Box -->
      <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md">
        <div class="flex items-center">
          <SkeletonBox width="1.25rem" height="1.25rem" class="rounded mr-2" />
          <SkeletonBox width="20rem" height="1rem" />
        </div>
      </div>
    </div>

    <!-- 2. Tugas Khas/Pentadbiran Section -->
    <div class="bg-light-card dark:bg-dark-card rounded-lg p-4 shadow-md">
      <!-- Section Header with Button -->
      <div class="mb-4 border-b border-light-border dark:border-dark-border pb-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <SkeletonBox width="1.25rem" height="1.25rem" class="rounded" />
            <SkeletonBox width="12rem" height="1.5rem" />
          </div>
          <SkeletonBox width="8rem" height="2rem" class="rounded-md" />
        </div>
      </div>

      <!-- Task List -->
      <div class="space-y-3">
        <div v-for="n in 2" :key="`admin-task-${n}`"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center space-x-3">
            <SkeletonBox width="1.5rem" height="1rem" />
            <SkeletonBox width="16rem" height="1rem" />
          </div>
          <div class="flex items-center space-x-2">
            <SkeletonBox width="3rem" height="1rem" />
            <SkeletonBox width="0.25rem" height="1rem" />
            <SkeletonBox width="3.5rem" height="1rem" />
          </div>
        </div>
      </div>
    </div>

    <!-- 3. Tugas Kokurikulum Section -->
    <div class="bg-light-card dark:bg-dark-card rounded-lg p-4 shadow-md">
      <!-- Section Header with Button -->
      <div class="mb-4 border-b border-light-border dark:border-dark-border pb-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <SkeletonBox width="1.25rem" height="1.25rem" class="rounded" />
            <SkeletonBox width="10rem" height="1.5rem" />
          </div>
          <SkeletonBox width="8rem" height="2rem" class="rounded-md" />
        </div>
      </div>

      <!-- Task List -->
      <div class="space-y-3">
        <div v-for="n in 3" :key="`cocur-task-${n}`"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center space-x-3">
            <SkeletonBox width="1.5rem" height="1rem" />
            <SkeletonBox :width="n === 1 ? '18rem' : n === 2 ? '14rem' : '20rem'" height="1rem" />
          </div>
          <div class="flex items-center space-x-2">
            <SkeletonBox width="3rem" height="1rem" />
            <SkeletonBox width="0.25rem" height="1rem" />
            <SkeletonBox width="3.5rem" height="1rem" />
          </div>
        </div>
      </div>
    </div>

    <!-- 4. Tugas Hal Ehwal Murid Section -->
    <div class="bg-light-card dark:bg-dark-card rounded-lg p-4 shadow-md">
      <!-- Section Header with Button -->
      <div class="mb-4 border-b border-light-border dark:border-dark-border pb-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <SkeletonBox width="1.25rem" height="1.25rem" class="rounded" />
            <SkeletonBox width="11rem" height="1.5rem" />
          </div>
          <SkeletonBox width="8rem" height="2rem" class="rounded-md" />
        </div>
      </div>

      <!-- Task List -->
      <div class="space-y-3">
        <div v-for="n in 1" :key="`student-task-${n}`"
          class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center space-x-3">
            <SkeletonBox width="1.5rem" height="1rem" />
            <SkeletonBox width="22rem" height="1rem" />
          </div>
          <div class="flex items-center space-x-2">
            <SkeletonBox width="3rem" height="1rem" />
            <SkeletonBox width="0.25rem" height="1rem" />
            <SkeletonBox width="3.5rem" height="1rem" />
          </div>
        </div>
      </div>
    </div>

    <!-- 5. Kegiatan dan Sumbangan Section -->
    <div class="bg-light-card dark:bg-dark-card rounded-lg p-4 shadow-md">
      <!-- Section Header -->
      <div class="mb-4 border-b border-light-border dark:border-dark-border pb-2">
        <div class="flex items-center space-x-2">
          <SkeletonBox width="1.25rem" height="1.25rem" class="rounded" />
          <SkeletonBox width="18rem" height="1.5rem" />
        </div>
      </div>

      <!-- Activities Table Structure -->
      <div class="space-y-6">
        <!-- Sukan Section -->
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <SkeletonBox width="4rem" height="1.5rem" />
          </div>
          <div class="space-y-2">
            <div class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded">
              <SkeletonBox width="8rem" height="1rem" />
            </div>
            <div class="space-y-1">
              <div v-for="n in 2" :key="`sukan-${n}`" class="flex items-center space-x-2 p-2">
                <SkeletonBox width="1rem" height="1rem" />
                <SkeletonBox :width="n === 1 ? '12rem' : '16rem'" height="1rem" />
              </div>
            </div>
          </div>
        </div>

        <!-- Pertubuhan Section -->
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <SkeletonBox width="5rem" height="1.5rem" />
          </div>
          <div class="space-y-2">
            <div class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded">
              <SkeletonBox width="8rem" height="1rem" />
            </div>
            <div class="space-y-1">
              <div v-for="n in 1" :key="`pertubuhan-${n}`" class="flex items-center space-x-2 p-2">
                <SkeletonBox width="1rem" height="1rem" />
                <SkeletonBox width="14rem" height="1rem" />
              </div>
            </div>
          </div>
        </div>

        <!-- Sumbangan Section -->
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <SkeletonBox width="5.5rem" height="1.5rem" />
          </div>
          <div class="space-y-2">
            <div class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded">
              <SkeletonBox width="8rem" height="1rem" />
            </div>
            <div class="space-y-1">
              <div v-for="n in 3" :key="`sumbangan-${n}`" class="flex items-center space-x-2 p-2">
                <SkeletonBox width="1rem" height="1rem" />
                <SkeletonBox :width="n === 1 ? '10rem' : n === 2 ? '14rem' : '12rem'" height="1rem" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
import SkeletonTable from './SkeletonTable.vue'
</script>

<style scoped>
/* Ensure consistent skeleton styling */
.bg-light-card {
  @apply bg-white;
}

.dark .bg-light-card {
  @apply bg-gray-800;
}

.bg-dark-card {
  @apply bg-gray-800;
}

.border-light-border {
  @apply border-gray-200;
}

.dark .border-light-border {
  @apply border-gray-700;
}

.border-dark-border {
  @apply border-gray-700;
}
</style>
