<template>
  <div class="space-y-8">
    <!-- <PERSON> Header -->
    <SkeletonPageHeader :title-width="'25rem'" :subtitle-width="'35rem'" :show-actions="false" />

    <!-- Week Selector Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <!-- Card Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
            <SkeletonBox height="1.5rem" width="12rem" />
          </div>
          <div class="flex items-center space-x-2">
            <SkeletonBox height="2rem" width="8rem" class="rounded-md" />
            <SkeletonBox height="2rem" width="10rem" class="rounded-md" />
          </div>
        </div>
      </div>

      <!-- Week Selection Content -->
      <div class="p-6 space-y-4">
        <!-- Week Navigation -->
        <div class="flex items-center justify-between">
          <SkeletonBox height="2rem" width="2rem" class="rounded-md" />
          <SkeletonBox height="1.5rem" width="15rem" />
          <SkeletonBox height="2rem" width="2rem" class="rounded-md" />
        </div>

        <!-- Week Info -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div class="space-y-2">
            <SkeletonBox height="1rem" width="10rem" variant="light" />
            <SkeletonBox height="1rem" width="15rem" variant="light" />
          </div>
        </div>
      </div>
    </div>

    <!-- RPH Form Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <!-- Form Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2">
          <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
          <SkeletonBox height="1.5rem" width="8rem" />
        </div>
      </div>

      <!-- Form Content -->
      <div class="p-6 space-y-6">
        <!-- Section 1: Class and Subject Selection -->
        <section>
          <SkeletonBox height="1.125rem" width="15rem" class="mb-2" />
          <SkeletonBox height="2.5rem" width="100%" class="rounded-md" />
          <SkeletonBox height="0.875rem" width="20rem" class="mt-2" variant="light" />
        </section>

        <!-- Section 2: Day Selection -->
        <section>
          <SkeletonBox height="1.125rem" width="8rem" class="mb-2" />
          <!-- Select All Days -->
          <div class="mb-2 p-2 border rounded-md">
            <div class="flex items-center space-x-2">
              <SkeletonBox height="1rem" width="1rem" class="rounded" />
              <SkeletonBox height="1rem" width="10rem" />
            </div>
          </div>
          <!-- Day Grid -->
          <div class="grid grid-cols-2 sm:grid-cols-3 gap-2">
            <div v-for="day in 7" :key="`day-${day}`" class="p-2 border rounded-md">
              <div class="flex items-center space-x-2">
                <SkeletonBox height="1rem" width="1rem" class="rounded" />
                <SkeletonBox height="1rem" width="4rem" />
              </div>
            </div>
          </div>
        </section>

        <!-- Section 3: File Upload -->
        <section>
          <SkeletonBox height="1.125rem" width="12rem" class="mb-2" />
          <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
            <div class="text-center space-y-2">
              <SkeletonBox height="3rem" width="3rem" class="mx-auto rounded" />
              <SkeletonBox height="1rem" width="20rem" class="mx-auto" variant="light" />
              <SkeletonBox height="2rem" width="8rem" class="mx-auto rounded-md" />
            </div>
          </div>
        </section>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <SkeletonBox height="2.5rem" width="6rem" class="rounded-md" />
          <SkeletonBox height="2.5rem" width="8rem" class="rounded-md" />
        </div>
      </div>
    </div>

    <!-- Lesson Plans List Section -->
    <section class="mt-8">
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- List Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <SkeletonBox height="1.5rem" width="15rem" />
            <SkeletonBox height="2rem" width="10rem" class="rounded-md" />
          </div>
        </div>

        <!-- List Items -->
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <div v-for="item in 4" :key="`lesson-${item}`" class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <SkeletonBox height="2.5rem" width="2.5rem" class="rounded-lg" />
                <div class="space-y-2">
                  <SkeletonBox height="1.125rem" width="20rem" />
                  <SkeletonBox height="0.875rem" width="15rem" variant="light" />
                  <SkeletonBox height="0.875rem" width="12rem" variant="light" />
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <SkeletonBox height="1.5rem" width="5rem" class="rounded-full" />
                <SkeletonBox height="2rem" width="2rem" class="rounded-md" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
</script>
