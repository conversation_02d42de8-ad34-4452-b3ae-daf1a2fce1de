// API endpoint to fetch school data from Stripe checkout session
// Used by success page to display real school information

import <PERSON><PERSON> from 'stripe'

export default defineEventHandler(async (event) => {
  try {
    // Get Stripe secret key from environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY

    if (!stripeSecretKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe secret key not configured'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    const body = await readBody(event)
    const { sessionId } = body

    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      })
    }

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['customer', 'subscription']
    })

    if (!session) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found'
      })
    }

    // Extract school data from session metadata
    const metadata = session.metadata
    if (!metadata) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session metadata not found'
      })
    }

    // Prepare school data response
    const schoolData = {
      schoolCode: metadata.schoolCode,
      schoolName: metadata.schoolName,
      schoolAddress: metadata.schoolAddress || '',
      adminEmail: metadata.adminEmail,
      adminFirstName: metadata.adminFirstName || '',
      adminLastName: metadata.adminLastName || '',
      selectedPlan: metadata.selectedPlan,
      sessionId: session.id,
      customerId: session.customer,
      subscriptionId: session.subscription,
      paymentStatus: session.payment_status,
      subscriptionStatus: session.mode === 'subscription' ? 'trialing' : 'active'
    }

    console.log('📋 Retrieved school data for session:', sessionId)

    return {
      success: true,
      schoolData,
      session: {
        id: session.id,
        status: session.status,
        payment_status: session.payment_status,
        amount_total: session.amount_total,
        currency: session.currency
      }
    }

  } catch (error: any) {
    console.error('Error retrieving session data:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to retrieve session data'
    })
  }
})
