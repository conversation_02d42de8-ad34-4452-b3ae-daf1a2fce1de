#!/usr/bin/env node

/**
 * eRPH+ Database Backup Script
 * Creates a complete backup of the Supabase database
 */

import fs from 'fs';
import path from 'path';

// Configuration
const SUPABASE_URL = 'https://nhgyywlfopodxomxbegx.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZ3l5d2xmb3BvZHhvbXhiZWd4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxNTU5OCwiZXhwIjoyMDYyNzkxNTk4fQ.koYblnJg3Je5LxL4QOpIBMjs4N_EMODDAsnwIJnEbJg';

if (!SUPABASE_SERVICE_KEY) {
    console.error('Error: SUPABASE_SERVICE_KEY environment variable is required');
    console.error('Please set it with your Supabase service role key');
    process.exit(1);
}

// Tables to backup
const TABLES = [
    'academic_calendar_documents',
    'annual_calendar_events',
    'dskp_documents',
    'global_settings',
    'items',
    'jadual_pencerapan',
    'lesson_plan_detailed_reflections',
    'lesson_plans',
    'observation_schedules',
    'profiles',
    'reflection_templates',
    'rph_weeks',
    'rpt_documents',
    'subjects',
    'teacher_activities',
    'teacher_observer_assignments',
    'teacher_schedules',
    'teacher_tasks',
    'tidak_terlaksana',
    'timetable_entries',
    'tindakan_susulan',
    'user_preferences',
    'user_reflection_template_preferences',
    'user_week_submissions'
];

async function fetchTableData(tableName) {
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/${tableName}?select=*`, {
            headers: {
                'apikey': SUPABASE_SERVICE_KEY,
                'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch ${tableName}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error(`Error fetching ${tableName}:`, error.message);
        return null;
    }
}

async function createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = `backup-erph-plus-${timestamp}`;
    
    // Create backup directory
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir);
    }

    console.log(`Creating backup in directory: ${backupDir}`);
    console.log('Starting backup process...\n');

    const backup = {
        metadata: {
            project: 'eRPH+',
            project_id: 'nhgyywlfopodxomxbegx',
            backup_date: new Date().toISOString(),
            tables_count: TABLES.length
        },
        data: {}
    };

    let successCount = 0;
    let errorCount = 0;

    for (const tableName of TABLES) {
        process.stdout.write(`Backing up ${tableName}... `);
        
        const data = await fetchTableData(tableName);
        
        if (data !== null) {
            backup.data[tableName] = data;
            
            // Save individual table backup
            const tableFile = path.join(backupDir, `${tableName}.json`);
            fs.writeFileSync(tableFile, JSON.stringify(data, null, 2));
            
            console.log(`✓ (${data.length} records)`);
            successCount++;
        } else {
            console.log('✗ Failed');
            errorCount++;
        }
    }

    // Save complete backup
    const backupFile = path.join(backupDir, 'complete-backup.json');
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));

    // Create backup summary
    const summary = {
        backup_completed: new Date().toISOString(),
        total_tables: TABLES.length,
        successful_backups: successCount,
        failed_backups: errorCount,
        backup_directory: backupDir,
        files_created: [
            'complete-backup.json',
            ...TABLES.map(table => `${table}.json`)
        ]
    };

    const summaryFile = path.join(backupDir, 'backup-summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));

    console.log('\n' + '='.repeat(50));
    console.log('BACKUP COMPLETED');
    console.log('='.repeat(50));
    console.log(`Backup directory: ${backupDir}`);
    console.log(`Tables backed up: ${successCount}/${TABLES.length}`);
    if (errorCount > 0) {
        console.log(`Failed backups: ${errorCount}`);
    }
    console.log(`Total files created: ${successCount + 2}`); // +2 for complete backup and summary
    console.log('='.repeat(50));
}

// Run backup
createBackup().catch(console.error);
