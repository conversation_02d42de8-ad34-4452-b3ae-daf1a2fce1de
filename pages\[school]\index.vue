<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p class="text-gray-600 dark:text-gray-400">Redirecting...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// Use minimal layout for redirect page
definePageMeta({
  layout: 'blank' as any
})

// Handle routing based on authentication status
const user = useSupabaseUser()

if (!user.value) {
  // Not logged in - redirect to login
  await navigateTo('/auth/login')
} else {
  // Logged in - redirect to dashboard
  await navigateTo('/dashboard')
}
</script>