// School code validation API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get request body
    const body = await readBody(event)
    const { code } = body

    // Validate input
    if (!code || typeof code !== 'string') {
      throw createError({
        statusCode: 400,
        statusMessage: 'School code is required'
      })
    }

    // Basic format validation
    const trimmedCode = code.trim().toLowerCase()
    
    if (trimmedCode.length < 3) {
      return {
        isValid: false,
        error: 'School code must be at least 3 characters long'
      }
    }

    if (trimmedCode.length > 20) {
      return {
        isValid: false,
        error: 'School code must be at most 20 characters long'
      }
    }

    if (!/^[a-z0-9]+$/.test(trimmedCode)) {
      return {
        isValid: false,
        error: 'School code can only contain letters and numbers'
      }
    }

    // Check against reserved words
    const reservedWords = [
      'www', 'api', 'admin', 'app', 'mail', 'ftp', 'blog', 'shop', 'store',
      'support', 'help', 'docs', 'dev', 'test', 'staging', 'prod', 'production',
      'dashboard', 'panel', 'control', 'manage', 'system', 'root', 'server'
    ]

    if (reservedWords.includes(trimmedCode)) {
      return {
        isValid: false,
        error: 'This school code is reserved and cannot be used'
      }
    }

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    )

    // Check if code is already taken
    const { data: existingSchool, error: dbError } = await supabase
      .from('schools')
      .select('id')
      .eq('code', trimmedCode)
      .single()

    if (dbError && dbError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw createError({
        statusCode: 500,
        statusMessage: 'Error checking school code availability'
      })
    }

    if (existingSchool) {
      return {
        isValid: false,
        error: 'This school code is already taken'
      }
    }

    // Code is available
    return {
      isValid: true,
      code: trimmedCode,
      message: 'School code is available'
    }

  } catch (error: any) {
    console.error('School code validation error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during validation'
    })
  }
})
