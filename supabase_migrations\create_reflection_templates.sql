-- Create reflection templates system
-- This migration creates tables for system and user reflection templates

BEGIN;

-- =====================================================
-- CREATE REFLECTION TEMPLATES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS reflection_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Template metadata
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN ('lesson_type', 'assessment', 'behavior', 'technology', 'general')),
    
    -- Template content
    prompts JSONB NOT NULL DEFAULT '{}',
    default_values JSONB DEFAULT '{}',
    
    -- System vs user template
    is_system_template BOOLEAN DEFAULT false,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT valid_system_template CHECK (
        (is_system_template = true AND created_by IS NULL) OR 
        (is_system_template = false AND created_by IS NOT NULL)
    )
);

-- =====================================================
-- CREATE USER REFLECTION TEMPLATE PREFERENCES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS user_reflection_template_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- User and template references
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    template_id UUID REFERENCES reflection_templates(id) ON DELETE CASCADE NOT NULL,
    
    -- User customizations
    customizations JSONB DEFAULT '{}',
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP WITH TIME ZONE,
    is_favorite BOOLEAN DEFAULT false,
    
    -- Ensure one preference per user per template
    UNIQUE(user_id, template_id)
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================

-- Reflection templates indexes
CREATE INDEX IF NOT EXISTS idx_reflection_templates_category ON reflection_templates(category);
CREATE INDEX IF NOT EXISTS idx_reflection_templates_system ON reflection_templates(is_system_template);
CREATE INDEX IF NOT EXISTS idx_reflection_templates_created_by ON reflection_templates(created_by) WHERE created_by IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_reflection_templates_usage ON reflection_templates(usage_count DESC);

-- User template preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_template_prefs_user_id ON user_reflection_template_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_template_prefs_template_id ON user_reflection_template_preferences(template_id);
CREATE INDEX IF NOT EXISTS idx_user_template_prefs_favorites ON user_reflection_template_preferences(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX IF NOT EXISTS idx_user_template_prefs_usage ON user_reflection_template_preferences(user_id, usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_user_template_prefs_last_used ON user_reflection_template_preferences(user_id, last_used DESC) WHERE last_used IS NOT NULL;

-- =====================================================
-- CREATE RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE reflection_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_reflection_template_preferences ENABLE ROW LEVEL SECURITY;

-- Reflection templates policies
CREATE POLICY "Users can view all system templates" ON reflection_templates
    FOR SELECT USING (is_system_template = true);

CREATE POLICY "Users can view their own custom templates" ON reflection_templates
    FOR SELECT USING (is_system_template = false AND created_by = auth.uid());

CREATE POLICY "Users can create their own templates" ON reflection_templates
    FOR INSERT WITH CHECK (is_system_template = false AND created_by = auth.uid());

CREATE POLICY "Users can update their own templates" ON reflection_templates
    FOR UPDATE USING (is_system_template = false AND created_by = auth.uid());

CREATE POLICY "Users can delete their own templates" ON reflection_templates
    FOR DELETE USING (is_system_template = false AND created_by = auth.uid());

-- User template preferences policies
CREATE POLICY "Users can manage their own template preferences" ON user_reflection_template_preferences
    FOR ALL USING (user_id = auth.uid());

-- =====================================================
-- CREATE TRIGGERS
-- =====================================================

-- Update timestamp trigger for reflection_templates
CREATE OR REPLACE FUNCTION update_reflection_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_reflection_templates_updated_at
    BEFORE UPDATE ON reflection_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_reflection_templates_updated_at();

-- Update timestamp trigger for user_reflection_template_preferences
CREATE OR REPLACE FUNCTION update_user_template_prefs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_template_prefs_updated_at
    BEFORE UPDATE ON user_reflection_template_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_template_prefs_updated_at();

-- =====================================================
-- INSERT COMPREHENSIVE SYSTEM TEMPLATES
-- =====================================================

-- LESSON TYPE TEMPLATES
INSERT INTO reflection_templates (name, description, category, prompts, default_values, is_system_template) VALUES
('Topik Baharu', 'Template untuk refleksi pengenalan topik baharu kepada pelajar', 'lesson_type',
'{"challenges_faced": "Adakah pelajar menghadapi kesukaran memahami konsep baharu? Nyatakan kesukaran utama.", "successful_strategies": "Kaedah pengenalan yang berkesan digunakan (contoh: analogi, demonstrasi, aktiviti hands-on):", "improvements_needed": "Cara untuk meningkatkan pemahaman pelajar pada topik ini:", "additional_notes": "Reaksi pelajar terhadap topik baharu dan tahap minat yang ditunjukkan:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 4, "overall_rating": 4, "objectives_achieved": true}',
true),

('Ulangkaji & Pengukuhan', 'Template untuk refleksi sesi ulangkaji dan pengukuhan topik terdahulu', 'lesson_type',
'{"challenges_faced": "Topik mana yang pelajar masih lemah atau keliru? Adakah miskonsepsi yang dikesan?", "successful_strategies": "Teknik ulangkaji yang berkesan (contoh: mind map, quiz, peer teaching):", "improvements_needed": "Kawasan yang perlu lebih tumpuan dalam sesi ulangkaji akan datang:", "additional_notes": "Tahap kefahaman pelajar selepas ulangkaji dan kemajuan yang diperhatikan:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 3, "overall_rating": 4, "objectives_achieved": true}',
true),

('Aktiviti Kumpulan', 'Template untuk refleksi aktiviti pembelajaran berasaskan kumpulan', 'lesson_type',
'{"challenges_faced": "Cabaran dalam pengurusan kumpulan dan kerjasama pelajar:", "successful_strategies": "Strategi pembentukan kumpulan dan fasilitasi yang berkesan:", "improvements_needed": "Penambahbaikan untuk aktiviti kumpulan pada masa hadapan:", "additional_notes": "Dinamik kumpulan dan tahap penyertaan setiap ahli:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 5, "overall_rating": 4, "objectives_achieved": true}',
true),

('Pembelajaran Kendiri', 'Template untuk refleksi sesi pembelajaran kendiri dan eksplorasi', 'lesson_type',
'{"challenges_faced": "Kesukaran pelajar dalam pembelajaran kendiri dan pengurusan masa:", "successful_strategies": "Panduan dan scaffolding yang membantu pembelajaran kendiri:", "improvements_needed": "Sokongan tambahan yang diperlukan untuk pembelajaran kendiri:", "additional_notes": "Tahap kemandirian dan motivasi pelajar yang diperhatikan:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 3, "student_engagement": 3, "overall_rating": 3, "objectives_achieved": true}',
true),

-- ASSESSMENT TEMPLATES
('Pentaksiran Formatif', 'Template untuk refleksi pentaksiran formatif dan maklum balas', 'assessment',
'{"challenges_faced": "Kesukaran dalam melaksanakan pentaksiran dan mengumpul maklum balas:", "successful_strategies": "Kaedah pentaksiran formatif yang berkesan digunakan:", "improvements_needed": "Penambahbaikan untuk pentaksiran dan maklum balas akan datang:", "additional_notes": "Analisis prestasi pelajar dan kawasan yang perlu diperbaiki:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 3, "overall_rating": 4, "objectives_achieved": true}',
true),

('Pentaksiran Sumatif', 'Template untuk refleksi pentaksiran sumatif dan analisis prestasi', 'assessment',
'{"challenges_faced": "Masalah yang dihadapi semasa pentaksiran dan pengurusan masa:", "successful_strategies": "Kaedah pentaksiran yang berkesan dan adil:", "improvements_needed": "Penambahbaikan untuk pentaksiran sumatif akan datang:", "additional_notes": "Analisis keputusan dan trend prestasi pelajar:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 3, "overall_rating": 4, "objectives_achieved": true}',
true),

('Pentaksiran Autentik', 'Template untuk refleksi pentaksiran autentik dan portfolio', 'assessment',
'{"challenges_faced": "Cabaran dalam mereka bentuk dan melaksanakan pentaksiran autentik:", "successful_strategies": "Pendekatan pentaksiran autentik yang berkesan:", "improvements_needed": "Penambahbaikan untuk pentaksiran autentik pada masa hadapan:", "additional_notes": "Kualiti kerja pelajar dan kemahiran yang ditunjukkan:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 4, "overall_rating": 4, "objectives_achieved": true}',
true),

-- TECHNOLOGY TEMPLATES
('Teknologi dalam P&P', 'Template untuk refleksi penggunaan teknologi dalam pengajaran dan pembelajaran', 'technology',
'{"challenges_faced": "Cabaran teknikal dan pedagogi dalam penggunaan teknologi:", "successful_strategies": "Teknologi dan aplikasi yang berjaya meningkatkan pembelajaran:", "improvements_needed": "Penambahbaikan infrastruktur, kemahiran atau pendekatan teknologi:", "additional_notes": "Respons pelajar terhadap teknologi dan tahap penglibatan digital:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 4, "overall_rating": 4, "objectives_achieved": true}',
true),

('Pembelajaran Dalam Talian', 'Template untuk refleksi sesi pembelajaran dalam talian atau hibrid', 'technology',
'{"challenges_faced": "Cabaran dalam pembelajaran dalam talian (teknikal, penglibatan, komunikasi):", "successful_strategies": "Platform dan strategi dalam talian yang berkesan:", "improvements_needed": "Penambahbaikan untuk pembelajaran dalam talian akan datang:", "additional_notes": "Tahap penyertaan pelajar dan kualiti interaksi dalam talian:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 3, "student_engagement": 3, "overall_rating": 3, "objectives_achieved": true}',
true),

-- BEHAVIOR MANAGEMENT TEMPLATES
('Pengurusan Tingkah Laku', 'Template untuk refleksi pengurusan tingkah laku dan disiplin kelas', 'behavior',
'{"challenges_faced": "Isu tingkah laku yang dihadapi dan impaknya terhadap pembelajaran:", "successful_strategies": "Strategi pengurusan tingkah laku yang berkesan:", "improvements_needed": "Pendekatan yang perlu diperbaiki untuk pengurusan tingkah laku:", "additional_notes": "Perubahan tingkah laku yang diperhatikan dan kemajuan pelajar:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 3, "student_engagement": 3, "overall_rating": 3, "objectives_achieved": true}',
true),

('Motivasi & Penglibatan', 'Template untuk refleksi strategi motivasi dan peningkatan penglibatan pelajar', 'behavior',
'{"challenges_faced": "Cabaran dalam memotivasikan pelajar dan meningkatkan penglibatan:", "successful_strategies": "Teknik motivasi dan penglibatan yang berkesan:", "improvements_needed": "Strategi tambahan untuk meningkatkan motivasi pelajar:", "additional_notes": "Tahap motivasi dan perubahan sikap pelajar yang diperhatikan:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 4, "student_engagement": 4, "overall_rating": 4, "objectives_achieved": true}',
true),

-- GENERAL TEMPLATE
('Refleksi Umum', 'Template umum untuk refleksi harian tanpa fokus khusus', 'general',
'{"challenges_faced": "Cabaran utama yang dihadapi dalam sesi ini:", "successful_strategies": "Strategi dan pendekatan yang berjaya:", "improvements_needed": "Kawasan yang perlu diperbaiki:", "additional_notes": "Pemerhatian dan refleksi tambahan:"}',
'{"time_management": "on_time", "resource_adequacy": "adequate", "activity_effectiveness": 3, "student_engagement": 3, "overall_rating": 3, "objectives_achieved": true}',
true);

-- =====================================================
-- ADD COMMENTS
-- =====================================================

COMMENT ON TABLE reflection_templates IS 'System and user-created reflection templates with prompts and default values';
COMMENT ON COLUMN reflection_templates.prompts IS 'JSONB containing field-specific prompts for reflection forms';
COMMENT ON COLUMN reflection_templates.default_values IS 'JSONB containing default values for reflection form fields';
COMMENT ON COLUMN reflection_templates.is_system_template IS 'True for system-provided templates, false for user-created templates';

COMMENT ON TABLE user_reflection_template_preferences IS 'User preferences and customizations for reflection templates';
COMMENT ON COLUMN user_reflection_template_preferences.customizations IS 'JSONB containing user-specific modifications to template prompts/defaults';

COMMIT;
