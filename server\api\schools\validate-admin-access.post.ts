// API endpoint to validate school admin access
// Created for Phase 2: Subdomain Infrastructure

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Authorization header required'
      })
    }

    // Get request body
    const body = await readBody(event)
    const { schoolCode } = body

    if (!schoolCode) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School code is required'
      })
    }

    // Extract token
    const token = authHeader.substring(7)
    
    // Get Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )
    
    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid token'
      })
    }

    // Check if user has admin access to this school
    let hasAccess = false

    try {
      // First, try to check against the schools table
      const { data: school, error: schoolError } = await supabase
        .from('schools')
        .select('*')
        .eq('code', schoolCode.toLowerCase())
        .eq('admin_user_id', user.id)
        .single()

      if (!schoolError && school) {
        hasAccess = true
      }
    } catch (error) {
      console.log('Schools table not available, using development fallback')
    }

    // No development fallback - only real schools from database

    return {
      success: true,
      hasAccess,
      schoolCode,
      userId: user.id,
      message: hasAccess ? 'Access granted' : 'Access denied'
    }

  } catch (error: any) {
    console.error('Error validating school admin access:', error)
    
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Internal server error'
    })
  }
})
