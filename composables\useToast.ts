import { ref, reactive } from 'vue'

export interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  persistent?: boolean
}

interface ToastState {
  toasts: Toast[]
}

// Global state for toasts
const state = reactive<ToastState>({
  toasts: []
})

// Default durations for each toast type (in milliseconds)
const DEFAULT_DURATIONS = {
  success: 4000,
  info: 4000,
  warning: 6000,
  error: 8000
}

let toastIdCounter = 0

export const useToast = () => {
  const generateId = (): string => {
    return `toast-${++toastIdCounter}-${Date.now()}`
  }

  const addToast = (toast: Omit<Toast, 'id'>): string => {
    const id = generateId()
    const duration = toast.duration ?? DEFAULT_DURATIONS[toast.type]
    
    const newToast: Toast = {
      ...toast,
      id,
      duration
    }

    // Add to the beginning of the array so newest toasts appear at top
    state.toasts.unshift(newToast)

    // Auto-remove toast after duration (unless persistent)
    if (!toast.persistent && duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, duration)
    }

    return id
  }

  const removeToast = (id: string): void => {
    const index = state.toasts.findIndex(toast => toast.id === id)
    if (index > -1) {
      state.toasts.splice(index, 1)
    }
  }

  const clearAllToasts = (): void => {
    state.toasts.splice(0)
  }

  // Convenience methods for different toast types
  const success = (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>): string => {
    return addToast({
      type: 'success',
      message,
      ...options
    })
  }

  const error = (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>): string => {
    return addToast({
      type: 'error',
      message,
      ...options
    })
  }

  const warning = (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>): string => {
    return addToast({
      type: 'warning',
      message,
      ...options
    })
  }

  const info = (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>): string => {
    return addToast({
      type: 'info',
      message,
      ...options
    })
  }

  return {
    // State
    toasts: readonly(ref(state.toasts)),
    
    // Methods
    addToast,
    removeToast,
    clearAllToasts,
    
    // Convenience methods
    success,
    error,
    warning,
    info
  }
}
