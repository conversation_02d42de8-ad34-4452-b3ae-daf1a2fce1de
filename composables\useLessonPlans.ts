import { ref } from "vue";
import { useSupabaseClient, useSupabaseUser } from "#imports";
import type { LessonPlan } from "~/types/lessonPlans";
import type { Database } from "~/types/supabase";
import type { SupabaseClient as GenericSupabaseClient } from "@supabase/supabase-js";
import { useTeacherSchedules } from '~/composables/useTeacherSchedules';
import { useTeacherScheduleHelpers } from '~/composables/useTeacherScheduleHelpers';
import type { DayOfWeek } from '~/types/teacherSchedule';
import { useDetailedReflections } from './useDetailedReflections';

// Define the input type for adding/updating a lesson plan
export interface LessonPlanUserInput {
  week_id: string;
  class_subject_ids: string[]; // These are composite IDs like "classId_subjectId"
  days_selected: DayOfWeek[]; // Ensure this is DayOfWeek[]
}

export type LessonPlanUpdateInput = Partial<LessonPlanUserInput> & {
  file_name?: string;
  storage_file_path?: string | null; // Allow null for explicit removal
  file_mime_type?: string;
  file_size_bytes?: number;
};

const RPH_FILES_BUCKET = "rph-files";

// --- Storage and Utility Helpers ---

function sanitizeFilenameForStorage(originalName: string): string {
  return originalName.replace(/[^a-zA-Z0-9_.-]/g, "_");
}

function constructStoragePath(
  userId: string,
  weekId: string,
  sanitizedFileName: string
): string {
  return `${userId}/${weekId}/${Date.now()}_${sanitizedFileName}`;
}

async function uploadFileToStorage(
  supabaseClient: GenericSupabaseClient,
  bucket: string,
  storagePath: string,
  file: File
): Promise<{ path: string }> {
  const { data, error } = await supabaseClient.storage
    .from(bucket)
    .upload(storagePath, file, { cacheControl: "3600", upsert: false });
  if (error) {
    console.error(`[StorageHelper] Supabase Storage upload error:`, error);
    throw error;
  }
  if (!data?.path) {
    console.error(
      "[StorageHelper] File upload failed, Supabase Storage returned no path."
    );
    throw new Error("File upload failed, no path returned.");
  }
  return { path: data.path };
}

async function deleteFileFromStorage(
  supabaseClient: GenericSupabaseClient,
  bucket: string,
  storagePath: string
): Promise<void> {
  const { error } = await supabaseClient.storage
    .from(bucket)
    .remove([storagePath]);
  if (error) {
    console.error(
      `[StorageHelper] Failed to delete file '${storagePath}':`,
      error.message
    );
    // Not throwing here, as DB operation might have succeeded. Log and continue.
  }
}

function determinePreviewType(
  mimeType: string | null | undefined
): LessonPlan["preview_type"] {
  if (!mimeType) return "other";
  if (mimeType.startsWith("image/")) return "image";
  if (
    mimeType.includes("officedocument") ||
    mimeType.includes("msword") ||
    mimeType.includes("ms-excel") ||
    mimeType.includes("ms-powerpoint") ||
    mimeType.includes("opendocument.text") ||
    mimeType.includes("opendocument.spreadsheet") ||
    mimeType.includes("opendocument.presentation") ||
    mimeType === "application/pdf"
  ) {
    return "office";
  }
  if (mimeType.startsWith("video/")) return "video";
  if (mimeType.startsWith("audio/")) return "audio";
  if (mimeType.startsWith("text/")) return "text";
  return "other";
}

export function useLessonPlans() {
  const client = useSupabaseClient<Database>();
  const user = useSupabaseUser();
    const { createSchedule, updateSchedule, deleteSchedule } = useTeacherSchedules();

  const lessonPlans = ref<LessonPlan[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Note: Removed automatic reflection generation since we're now calculating everything
  // from lesson_plan_detailed_reflections table in real-time

  // Helper function to update linked teacher schedule
  const updateLinkedTeacherSchedule = async (
    lessonPlanId: string,
    lessonPlan: LessonPlan
  ): Promise<void> => {
    const { useTimetable } = await import('~/composables/useTimetable');
    const { useTeacherScheduleHelpers } = await import('~/composables/useTeacherScheduleHelpers');

    const { timetableEntries } = useTimetable();
    const { createTeacherScheduleFromTimetable } = useTeacherScheduleHelpers();

    // Ensure timetable entries are available (they should already be loaded)

    // Create new schedule details from current lesson plan data
    const scheduleDetails = createTeacherScheduleFromTimetable(
      timetableEntries.value,
      lessonPlan.class_subject_ids,
      lessonPlan.days_selected
    );

    if (scheduleDetails.class_subjects.length > 0) {
      // Find existing teacher schedule for this lesson plan
      const { data: existingSchedules } = await client
        .from('teacher_schedules')
        .select('id')
        .eq('lesson_plan_id', lessonPlanId)
        .eq('user_id', user.value!.id);

      if (existingSchedules && existingSchedules.length > 0) {
        // Update existing schedule
        await updateSchedule(existingSchedules[0].id, { class_subjects: scheduleDetails.class_subjects });
      } else {
        // Create new schedule if none exists
        await createSchedule({ class_subjects: scheduleDetails.class_subjects }, lessonPlanId);
      }
    }
  };

  // Helper function to delete linked teacher schedule
  const deleteLinkedTeacherSchedule = async (lessonPlanId: string): Promise<void> => {
    const { data: existingSchedules } = await client
      .from('teacher_schedules')
      .select('id')
      .eq('lesson_plan_id', lessonPlanId)
      .eq('user_id', user.value!.id);

    if (existingSchedules && existingSchedules.length > 0) {
      for (const schedule of existingSchedules) {
        await deleteSchedule(schedule.id);
      }
    }
  };

  function enrichPlanWithPreviewType(plan: LessonPlan): LessonPlan {
    const enrichedPlan = { ...plan };
    enrichedPlan.preview_type = determinePreviewType(
      enrichedPlan.file_mime_type
    );
    return enrichedPlan;
  }

  async function getWeekLabel(weekId: string, userId: string): Promise<string> {
    const { data: weekData, error: weekError } = await client
      .from("rph_weeks")
      .select("name")
      .eq("id", weekId)
      .eq("user_id", userId)
      .single();
    if (weekError || !weekData) {
      throw new Error(
        weekError?.message || "Failed to fetch week details to get label."
      );
    }
    return weekData.name;
  }

  async function fetchLessonPlans(weekId?: string) {
    if (!user.value) {
      error.value = "User not authenticated";
      lessonPlans.value = [];
      return;
    }
    loading.value = true;
    error.value = null;
    try {
      let query = client
        .from("lesson_plans")
        .select("*")
        .eq("user_id", user.value.id);

      // Only filter by week_id if a valid weekId is provided
      if (weekId && weekId.trim() !== '') {
        query = query.eq("week_id", weekId);
      }

      const { data: rawPlans, error: fetchError } = await query
        .order("created_at", { ascending: false });

      if (fetchError) throw fetchError;

      lessonPlans.value = (rawPlans || []).map((rawPlan) =>
        enrichPlanWithPreviewType(rawPlan as LessonPlan)
      );
    } catch (e) {
      // Catch as unknown
      if (e instanceof Error) {
        error.value = e.message;
      } else {
        error.value = "An unknown error occurred while fetching lesson plans.";
      }
      lessonPlans.value = [];
      console.error("Error fetching lesson plans:", e);
    } finally {
      loading.value = false;
    }
  }

  async function fetchLessonPlansByWeekId_internal(
    weekId: string
  ): Promise<LessonPlan[] | null> {
    if (!user.value) {
      console.error(
        "fetchLessonPlansByWeekId_internal: User not authenticated"
      );
      return null;
    }
    try {
      const { data: rawPlans, error: fetchError } = await client
        .from("lesson_plans")
        .select("id, user_id, week_id, storage_file_path") // Only select necessary fields
        .eq("user_id", user.value.id)
        .eq("week_id", weekId);

      if (fetchError) {
        console.error(
          `Error fetching lesson plans for week ${weekId}:`,
          fetchError
        );
        throw fetchError;
      }
      return (rawPlans as LessonPlan[]) || [];
    } catch (e) {
      console.error(
        `Exception in fetchLessonPlansByWeekId_internal for week ${weekId}:`,
        e
      );
      return null;
    }
  }

  const addLessonPlan = async (
    planDetails: LessonPlanUserInput,
    file: File,
    timetableEntries?: any[]
  ): Promise<LessonPlan | null> => {
    if (!user.value?.id) {
      error.value = "User not authenticated";
      return null;
    }
    loading.value = true;
    error.value = null;
    let uploadedStoragePath: string | null = null;

    try {
      const userId = user.value.id;
      const weekLabel = await getWeekLabel(planDetails.week_id, userId);

      const originalFileName = file.name;
      const sanitizedFileName = sanitizeFilenameForStorage(originalFileName);
      const storagePath = constructStoragePath(
        userId,
        planDetails.week_id,
        sanitizedFileName
      );

      const uploadResult = await uploadFileToStorage(
        client,
        RPH_FILES_BUCKET,
        storagePath,
        file
      );
      uploadedStoragePath = uploadResult.path; // Use path from upload result

      const newPlanData: Database["public"]["Tables"]["lesson_plans"]["Insert"] =
        {
          user_id: userId,
          week_id: planDetails.week_id,
          week_label: weekLabel,
          class_subject_ids: planDetails.class_subject_ids,
          days_selected: planDetails.days_selected,
          file_name: originalFileName,
          storage_file_path: uploadedStoragePath,
          file_mime_type: file.type,
          file_size_bytes: file.size,
        };

      const { data: dbData, error: dbError } = await client
        .from("lesson_plans")
        .insert(newPlanData)
        .select()
        .single();

      if (dbError) {
        console.error(`[AddLessonPlan] Database insert error:`, dbError);
        if (uploadedStoragePath) {
          await deleteFileFromStorage(
            client,
            RPH_FILES_BUCKET,
            uploadedStoragePath
          );
        }
        throw dbError;
      }

      if (dbData) {
        const newPlan = enrichPlanWithPreviewType(dbData as LessonPlan);
        lessonPlans.value.unshift(newPlan);

        // --- Create linked teacher schedule using new structure ---
        if (timetableEntries && timetableEntries.length > 0) {
          try {
            const { createTeacherScheduleFromTimetable } = useTeacherScheduleHelpers();

            const scheduleDetails = createTeacherScheduleFromTimetable(
              timetableEntries,
              planDetails.class_subject_ids,
              planDetails.days_selected
            );

            if (scheduleDetails.class_subjects.length > 0) {
              await createSchedule({ class_subjects: scheduleDetails.class_subjects }, newPlan.id);
              console.log(`[AddLessonPlan] Created teacher schedule for lesson plan ${newPlan.id}`);
            } else {
              console.warn(`[AddLessonPlan] No matching timetable entries found for lesson plan ${newPlan.id}`);
            }
          } catch (scheduleError) {
            console.error(`[AddLessonPlan] Failed to create teacher schedule for lesson plan ${newPlan.id}:`, scheduleError);
            // Continue without failing the lesson plan creation
          }
        } else {
          console.warn(`[AddLessonPlan] No timetable entries provided for lesson plan ${newPlan.id}`);
        }
        // --- End linked teacher schedule creation ---

        // --- Auto-generate initial single-row reflections ---
        try {
          const { createDetailedReflection } = useDetailedReflections();

          const reflection = await createDetailedReflection(
            newPlan.id,
            planDetails
          );

          if (reflection) {
            console.log(`[AddLessonPlan] Created initial single-row reflections for lesson plan ${newPlan.id}`);
          } else {
            console.warn(`[AddLessonPlan] Failed to create initial reflections for lesson plan ${newPlan.id}`);
          }
        } catch (reflectionError) {
          console.error(`[AddLessonPlan] Failed to create initial reflections for lesson plan ${newPlan.id}:`, reflectionError);
          // Continue without failing the lesson plan creation
        }
        // --- End auto-generate initial single-row reflections ---

        return newPlan;
      }
      return null;
    } catch (e) {
      // Catch as unknown
      console.error("Error adding lesson plan:", e);
      if (e instanceof Error) {
        error.value = e.message;
      } else {
        error.value =
          "An unexpected error occurred while adding the lesson plan.";
      }
      // Attempt to clean up uploaded file if an error occurs after upload but before DB insert
      if (uploadedStoragePath) {
        // await deleteFileFromStorage(client, RPH_FILES_BUCKET, uploadedStoragePath);
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  const updateLessonPlan = async (
    planId: string,
    planDetails: LessonPlanUpdateInput, // Corrected order: planDetails is second
    newFile?: File | null, // Corrected order: newFile is third
    originalStoragePath?: string | null // Corrected order: originalStoragePath is fourth and optional
  ): Promise<LessonPlan | null> => {
    if (!user.value?.id) {
      error.value = "User not authenticated";
      return null;
    }
    loading.value = true;
    error.value = null;

    const userId = user.value.id;
    let newUploadedStoragePath: string | null = null;
    let finalStoragePathForDB: string | null | undefined =
      planDetails.storage_file_path === undefined
        ? originalStoragePath // Use the passed originalStoragePath
        : planDetails.storage_file_path;
    let originalFileNameForDB: string | undefined = planDetails.file_name;

    try {
      const updatePayload: Database["public"]["Tables"]["lesson_plans"]["Update"] =
        {
          // Initialize with properties from planDetails that are directly assignable
          ...(planDetails.week_id && { week_id: planDetails.week_id }),
          ...(planDetails.class_subject_ids && {
            class_subject_ids: planDetails.class_subject_ids,
          }),
          ...(planDetails.days_selected && {
            days_selected: planDetails.days_selected,
          }),
          // file_name, storage_file_path, etc., will be set conditionally below
        };

      // If week_id is being changed, fetch new week_label
      if (planDetails.week_id) {
        updatePayload.week_id = planDetails.week_id; // Ensure it's in payload
        updatePayload.week_label = await getWeekLabel(
          planDetails.week_id,
          userId
        );
      } else {
        // If week_id is not part of planDetails, it's not changing.
        // Remove week_label from payload if it was spread from planDetails but week_id wasn't.
        delete updatePayload.week_label;
      }

      // Handle file replacement or removal
      if (newFile) {
        const weekIdForPath =
          planDetails.week_id ||
          lessonPlans.value.find((p) => p.id === planId)?.week_id;
        if (!weekIdForPath) {
          throw new Error(
            "Week ID is missing for new file upload during update."
          );
        }
        originalFileNameForDB = newFile.name;
        const sanitizedNewFileName = sanitizeFilenameForStorage(
          originalFileNameForDB
        );
        const tempNewStoragePath = constructStoragePath(
          userId,
          weekIdForPath,
          sanitizedNewFileName
        );

        const uploadResult = await uploadFileToStorage(
          client,
          RPH_FILES_BUCKET,
          tempNewStoragePath,
          newFile
        );
        newUploadedStoragePath = uploadResult.path;
        finalStoragePathForDB = newUploadedStoragePath;

        updatePayload.file_name = originalFileNameForDB || undefined; // Ensure undefined if null/empty
        updatePayload.storage_file_path = finalStoragePathForDB || undefined;
        updatePayload.file_mime_type = newFile.type || undefined;
        updatePayload.file_size_bytes = newFile.size;
      } else if (
        planDetails.storage_file_path === null && // User explicitly wants to remove the file
        originalStoragePath
      ) {
        // Explicitly removing file, no new file
        finalStoragePathForDB = undefined; // Use undefined for consistency if path is removed
        updatePayload.storage_file_path = undefined;
        updatePayload.file_name = undefined;
        updatePayload.file_mime_type = undefined;
        updatePayload.file_size_bytes = undefined;
      } else if (planDetails.file_name && !newFile && originalStoragePath) {
        // Only metadata (file_name) changed, no new file, file not removed
        updatePayload.file_name = planDetails.file_name || undefined;
        updatePayload.storage_file_path = originalStoragePath || undefined;
        if (planDetails.file_mime_type)
          updatePayload.file_mime_type = planDetails.file_mime_type;
        if (planDetails.file_size_bytes)
          updatePayload.file_size_bytes = planDetails.file_size_bytes;
        // If planDetails.file_mime_type or .file_size_bytes could be null, ensure they become undefined:
        // updatePayload.file_mime_type = planDetails.file_mime_type || undefined;
        // updatePayload.file_size_bytes = planDetails.file_size_bytes === null ? undefined : planDetails.file_size_bytes;
      } else if (!newFile && planDetails.storage_file_path === undefined) {
        // No new file, and storage_file_path not explicitly set to null or a new value.
        // It means we keep the original file if it exists.
        // Remove file-related fields from payload if they were not meant to be updated.
        delete updatePayload.file_name;
        delete updatePayload.storage_file_path;
        delete updatePayload.file_mime_type;
        delete updatePayload.file_size_bytes;
      }

      // Prevent empty update if only file metadata changed without actual file change
      // and those metadata fields are already part of planDetails
      const payloadKeys = Object.keys(updatePayload);
      if (
        payloadKeys.length === 0 &&
        !newFile &&
        planDetails.storage_file_path !== null
      ) {
        // This case should be rare if UI correctly sends only changed fields or new file.
        // We might still need to update local state if enrichment changes (e.g. public URL if somehow storage path changed without DB record change - unlikely)
        const currentPlan = lessonPlans.value.find((p) => p.id === planId);
        if (currentPlan) {
          const potentiallyUpdatedPlan = enrichPlanWithPreviewType({
            ...currentPlan,
            ...planDetails,
          } as LessonPlan);
          const index = lessonPlans.value.findIndex((p) => p.id === planId);
          if (index !== -1)
            lessonPlans.value.splice(index, 1, potentiallyUpdatedPlan);
          return potentiallyUpdatedPlan;
        }
        return null;
      }

      // Filter out undefined values from payload to avoid accidental nulling in DB
      const cleanedUpdatePayload = Object.entries(updatePayload).reduce(
        (acc, [key, value]) => {
          if (value !== undefined) {
            (acc as any)[key] = value;
          }
          return acc;
        },
        {} as Database["public"]["Tables"]["lesson_plans"]["Update"]
      );

      if (Object.keys(cleanedUpdatePayload).length === 0) {
        // console.log("[UpdateLessonPlan] No effective changes for DB after cleaning payload. Local update might still be needed for enrichment.");
        // This can happen if planDetails only contained undefined values for DB fields
        // or if the only change was a new file that failed to upload before this point.
        // If newUploadedStoragePath exists, it means a file was uploaded but something else went wrong.
        // This state should ideally be caught by earlier error handling.
        // For now, if no DB update, enrich and return.
        const existingPlan = lessonPlans.value.find((p) => p.id === planId);
        if (existingPlan) {
          // If a new file was uploaded, its details are in newUploadedStoragePath, originalFileNameForDB etc.
          // We need to reflect this in the plan before enrichment if DB call is skipped.
          const planForEnrichment = { ...existingPlan } as LessonPlan;
          if (newUploadedStoragePath) {
            planForEnrichment.storage_file_path = newUploadedStoragePath;
            planForEnrichment.file_name = originalFileNameForDB!;
            planForEnrichment.file_mime_type = newFile!.type;
            planForEnrichment.file_size_bytes = newFile!.size;
          } else if (finalStoragePathForDB === null) {
            // File was removed
            planForEnrichment.storage_file_path = null;
            planForEnrichment.public_url = undefined;
            planForEnrichment.preview_type = undefined;
            planForEnrichment.file_name = null;
            planForEnrichment.file_mime_type = null;
            planForEnrichment.file_size_bytes = null;
          }
          for (const key in planDetails) {
            // Apply other textual changes from planDetails
            if (
              Object.prototype.hasOwnProperty.call(planDetails, key) &&
              key in planForEnrichment
            ) {
              (planForEnrichment as any)[key] = (planDetails as any)[key];
            }
          }

          const updatedPlanLocally =
            enrichPlanWithPreviewType(planForEnrichment);
          const index = lessonPlans.value.findIndex((p) => p.id === planId);
          if (index !== -1)
            lessonPlans.value.splice(index, 1, updatedPlanLocally);
          return updatedPlanLocally;
        }
        return null;
      }

      const { data: dbData, error: dbError } = await client
        .from("lesson_plans")
        .update(cleanedUpdatePayload)
        .eq("id", planId)
        .eq("user_id", userId)
        .select()
        .single();

      if (dbError) {
        console.error(
          `[UpdateLessonPlan] Database update error for planId ${planId}:`,
          dbError
        );
        if (newUploadedStoragePath) {
          // If new file was uploaded, try to roll it back
          await deleteFileFromStorage(
            client,
            RPH_FILES_BUCKET,
            newUploadedStoragePath
          );
        }
        throw dbError;
      }

      // If update successful, handle old file deletion if necessary
      if (
        originalStoragePath &&
        newUploadedStoragePath &&
        newUploadedStoragePath !== originalStoragePath
      ) {
        // New file replaced old one
        await deleteFileFromStorage(
          client,
          RPH_FILES_BUCKET,
          originalStoragePath
        );
      } else if (originalStoragePath && finalStoragePathForDB === null) {
        // File was explicitly removed
        await deleteFileFromStorage(
          client,
          RPH_FILES_BUCKET,
          originalStoragePath
        );
      }

      if (dbData) {
        const updatedPlanWithUrl = enrichPlanWithPreviewType(
          dbData as LessonPlan
        );

        // --- Update linked teacher schedule if class_subject_ids or days_selected changed ---
        if (planDetails.class_subject_ids || planDetails.days_selected) {
          try {
            await updateLinkedTeacherSchedule(planId, updatedPlanWithUrl);
            console.log(`[UpdateLessonPlan] Updated teacher schedule for lesson plan ${planId}`);
          } catch (scheduleError) {
            console.error(`[UpdateLessonPlan] Failed to update teacher schedule for lesson plan ${planId}:`, scheduleError);
            // Continue without failing the lesson plan update
          }
        }
        // --- End teacher schedule update ---

        // --- Sync reflections if class_subject_ids or days_selected changed ---
        if (planDetails.class_subject_ids || planDetails.days_selected) {
          try {
            const { useDetailedReflections } = await import('~/composables/useDetailedReflections');
            const { syncReflectionsWithLessonPlan } = useDetailedReflections();

            await syncReflectionsWithLessonPlan(
              planId,
              true // Force sync after lesson plan update
            );
            console.log(`[UpdateLessonPlan] Synced reflections for lesson plan ${planId}`);
          } catch (reflectionError) {
            console.error(`[UpdateLessonPlan] Failed to sync reflections for lesson plan ${planId}:`, reflectionError);
            // Continue without failing the lesson plan update
          }
        }
        // --- End reflection sync ---

        const index = lessonPlans.value.findIndex((p) => p.id === planId);
        if (index !== -1) {
          lessonPlans.value.splice(index, 1, updatedPlanWithUrl);
        }
        return updatedPlanWithUrl;
      }
      return null;
    } catch (e) {
      // Catch as unknown
      console.error("Error updating lesson plan:", e);
      if (e instanceof Error) {
        error.value = e.message;
      } else {
        error.value =
          "An unexpected error occurred while updating the lesson plan.";
      }
      // Clean up newly uploaded file if DB update fails
      if (
        newUploadedStoragePath &&
        newUploadedStoragePath !== originalStoragePath
      ) {
        // await deleteFileFromStorage(client, RPH_FILES_BUCKET, newUploadedStoragePath);
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  const deleteLessonPlan = async (
    planId: string,
    storageFilePath: string | null
  ): Promise<boolean> => {
    if (!user.value?.id) {
      error.value = "User not authenticated";
      return false;
    }
    loading.value = true;
    error.value = null;
    try {
      const { error: dbError } = await client
        .from("lesson_plans")
        .delete()
        .eq("id", planId)
        .eq("user_id", user.value.id); // Ensure user can only delete their own

      if (dbError) {
        console.error("[DeleteLessonPlan] Database delete error:", dbError);
        throw dbError;
      }

      // If DB deletion was successful, delete linked teacher schedule
      try {
        await deleteLinkedTeacherSchedule(planId);
        console.log(`[DeleteLessonPlan] Deleted teacher schedule for lesson plan ${planId}`);
      } catch (scheduleError) {
        console.error(`[DeleteLessonPlan] Failed to delete teacher schedule for lesson plan ${planId}:`, scheduleError);
        // Continue without failing the lesson plan deletion
      }

      // Attempt to delete from storage
      if (storageFilePath) {
        await deleteFileFromStorage(client, RPH_FILES_BUCKET, storageFilePath);
      }

      lessonPlans.value = lessonPlans.value.filter((p) => p.id !== planId);
      return true;
    } catch (e) {
      // Catch as unknown
      console.error("Error deleting lesson plan:", e);
      if (e instanceof Error) {
        error.value = e.message;
      } else {
        error.value =
          "An unexpected error occurred while deleting the lesson plan.";
      }
      return false;
    } finally {
      loading.value = false;
    }
  };

  async function getTemporaryPublicUrl(
    filePath: string,
    expiresInSeconds: number = 300
  ): Promise<string | null> {
    if (!user.value) {
      error.value = "User not authenticated";
      return null;
    }
    if (!filePath) {
      error.value = "No file path provided to get temporary URL.";
      return null;
    }
    try {
      const { data, error: signedUrlError } = await client.storage
        .from(RPH_FILES_BUCKET)
        .createSignedUrl(filePath, expiresInSeconds);

      if (signedUrlError) {
        console.error(
          "[getTemporaryPublicUrl] Supabase error creating signed URL:",
          signedUrlError
        );
        throw signedUrlError;
      }

      return data?.signedUrl || null;
    } catch (e) {
      console.error(
        "[getTemporaryPublicUrl] CATCH BLOCK: Error getting temporary public URL:",
        e
      );
      if (e instanceof Error) {
        error.value = e.message;
      } else {
        error.value = "Failed to get temporary public URL";
      }
      return null;
    }
  }

  return {
    lessonPlans,
    loading,
    error,
    fetchLessonPlans,
    addLessonPlan,
    updateLessonPlan,
    deleteLessonPlan,
    getTemporaryPublicUrl,
    fetchLessonPlansByWeekId_internal,
  };
}
