<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
      <!-- Header -->
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Invite Teacher
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Email -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email Address *
          </label>
          <input
            v-model="form.email"
            type="email"
            required
            placeholder="<EMAIL>"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            :class="{ 'border-red-500': errors.email }"
          >
          <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
        </div>

        <!-- Role -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Role *
          </label>
          <select
            v-model="form.role"
            required
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="teacher">Teacher</option>
            <option value="supervisor">Supervisor</option>
            <option value="admin">Admin</option>
          </select>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {{ getRoleDescription(form.role) }}
          </p>
        </div>

        <!-- Notes -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notes (Optional)
          </label>
          <textarea
            v-model="form.notes"
            rows="3"
            placeholder="Add any notes about this invitation..."
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          ></textarea>
        </div>

        <!-- Custom Message -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Custom Message (Optional)
          </label>
          <textarea
            v-model="form.customMessage"
            rows="2"
            placeholder="Add a personal message to the invitation email..."
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          ></textarea>
        </div>

        <!-- Send Email Option -->
        <div class="flex items-center">
          <input
            v-model="form.sendEmail"
            type="checkbox"
            id="sendEmail"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          >
          <label for="sendEmail" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Send invitation email immediately
          </label>
        </div>

        <!-- Error Message -->
        <div v-if="submitError" class="bg-red-50 border border-red-200 rounded-md p-3">
          <p class="text-sm text-red-600">{{ submitError }}</p>
        </div>

        <!-- Success Message -->
        <div v-if="successMessage" class="bg-green-50 border border-green-200 rounded-md p-3">
          <p class="text-sm text-green-600">{{ successMessage }}</p>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
          >
            <span v-if="isSubmitting" class="flex items-center">
              <Icon name="heroicons:arrow-path" class="w-4 h-4 mr-2 animate-spin" />
              Sending...
            </span>
            <span v-else>Send Invitation</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  schoolId: string
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'invited'])

// Composables
const supabase = useSupabaseClient()

// State
const isSubmitting = ref(false)
const submitError = ref('')
const successMessage = ref('')
const errors = ref<Record<string, string>>({})

const form = ref({
  email: '',
  role: 'teacher',
  notes: '',
  customMessage: '',
  sendEmail: true
})

// Methods
const handleSubmit = async () => {
  try {
    isSubmitting.value = true
    submitError.value = ''
    successMessage.value = ''
    errors.value = {}

    // Validate form
    if (!validateForm()) {
      return
    }

    // Get auth token
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('Authentication required')
    }

    // Send invitation
    const response = await $fetch(`/api/schools/${props.schoolId}/invite-teacher`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`
      },
      body: {
        email: form.value.email.trim(),
        role: form.value.role,
        notes: form.value.notes.trim() || null,
        customMessage: form.value.customMessage.trim() || null,
        sendEmail: form.value.sendEmail
      }
    }) as any

    if (response.success) {
      successMessage.value = 'Invitation sent successfully!'
      
      // Reset form
      form.value = {
        email: '',
        role: 'teacher',
        notes: '',
        customMessage: '',
        sendEmail: true
      }

      // Emit success event
      setTimeout(() => {
        emit('invited', response.membership)
      }, 1500)
    } else {
      throw new Error(response.error || 'Failed to send invitation')
    }

  } catch (error: any) {
    console.error('Error sending invitation:', error)
    submitError.value = error.data?.message || error.message || 'Failed to send invitation'
  } finally {
    isSubmitting.value = false
  }
}

const validateForm = () => {
  const newErrors: any = {}

  // Email validation
  if (!form.value.email.trim()) {
    newErrors.email = 'Email is required'
  } else if (!isValidEmail(form.value.email)) {
    newErrors.email = 'Please enter a valid email address'
  }

  errors.value = newErrors
  return Object.keys(newErrors).length === 0
}

const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const getRoleDescription = (role: string) => {
  switch (role) {
    case 'admin':
      return 'Full access to school management and settings'
    case 'supervisor':
      return 'Can manage teachers and view all content'
    case 'teacher':
      return 'Can create and manage their own content'
    default:
      return ''
  }
}

// Auto-focus email input when modal opens
onMounted(() => {
  nextTick(() => {
    const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement
    if (emailInput) {
      emailInput.focus()
    }
  })
})
</script>
