-- Migration: Create tables for reflection options (tindakan_susulan and tidak_terlaksana)
-- Created: 2024-12-28

BEGIN;

-- =====================================================
-- CREATE TINDAKAN_SUSULAN TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tindakan_susulan (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    option_text TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tindakan_susulan_option_text_check CHECK (char_length(option_text) > 0),
    CONSTRAINT tindakan_susulan_user_option_unique UNIQUE (user_id, option_text)
);

-- Create indexes for performance
CREATE INDEX idx_tindakan_susulan_user_id ON tindakan_susulan (user_id);
CREATE INDEX idx_tindakan_susulan_is_default ON tindakan_susulan (is_default);
CREATE INDEX idx_tindakan_susulan_user_default ON tindakan_susulan (user_id, is_default);

-- Insert default options (global, no user_id)
INSERT INTO tindakan_susulan (option_text, is_default) VALUES
    ('Pembelajaran perlu diulang', TRUE),
    ('Pembelajaran akan diperbaiki', TRUE),
    ('Pengurusan masa yang lebih baik', TRUE)
ON CONFLICT DO NOTHING;

-- =====================================================
-- CREATE TIDAK_TERLAKSANA TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tidak_terlaksana (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    option_text TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tidak_terlaksana_option_text_check CHECK (char_length(option_text) > 0),
    CONSTRAINT tidak_terlaksana_user_option_unique UNIQUE (user_id, option_text)
);

-- Create indexes for performance
CREATE INDEX idx_tidak_terlaksana_user_id ON tidak_terlaksana (user_id);
CREATE INDEX idx_tidak_terlaksana_is_default ON tidak_terlaksana (is_default);
CREATE INDEX idx_tidak_terlaksana_user_default ON tidak_terlaksana (user_id, is_default);

-- Insert default options (global, no user_id)
INSERT INTO tidak_terlaksana (option_text, is_default) VALUES
    ('Mesyuarat / Bengkel', TRUE),
    ('Aktiviti sekolah / Sukan', TRUE),
    ('Tugas rasmi', TRUE),
    ('CRK / Cuti Sakit', TRUE)
ON CONFLICT DO NOTHING;

-- =====================================================
-- CREATE UPDATED_AT TRIGGERS
-- =====================================================

-- Create or replace the trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for automatic updated_at
CREATE TRIGGER update_tindakan_susulan_updated_at
    BEFORE UPDATE ON tindakan_susulan
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tidak_terlaksana_updated_at
    BEFORE UPDATE ON tidak_terlaksana
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SET UP ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS
ALTER TABLE tindakan_susulan ENABLE ROW LEVEL SECURITY;
ALTER TABLE tidak_terlaksana ENABLE ROW LEVEL SECURITY;

-- RLS Policies for tindakan_susulan
CREATE POLICY "Users can view default and their own tindakan_susulan options" 
ON tindakan_susulan FOR SELECT 
USING (is_default = TRUE OR auth.uid() = user_id);

CREATE POLICY "Users can insert their own tindakan_susulan options" 
ON tindakan_susulan FOR INSERT 
WITH CHECK (auth.uid() = user_id AND is_default = FALSE);

CREATE POLICY "Users can update their own tindakan_susulan options" 
ON tindakan_susulan FOR UPDATE 
USING (auth.uid() = user_id AND is_default = FALSE)
WITH CHECK (auth.uid() = user_id AND is_default = FALSE);

CREATE POLICY "Users can delete their own tindakan_susulan options" 
ON tindakan_susulan FOR DELETE 
USING (auth.uid() = user_id AND is_default = FALSE);

-- RLS Policies for tidak_terlaksana
CREATE POLICY "Users can view default and their own tidak_terlaksana options" 
ON tidak_terlaksana FOR SELECT 
USING (is_default = TRUE OR auth.uid() = user_id);

CREATE POLICY "Users can insert their own tidak_terlaksana options" 
ON tidak_terlaksana FOR INSERT 
WITH CHECK (auth.uid() = user_id AND is_default = FALSE);

CREATE POLICY "Users can update their own tidak_terlaksana options" 
ON tidak_terlaksana FOR UPDATE 
USING (auth.uid() = user_id AND is_default = FALSE)
WITH CHECK (auth.uid() = user_id AND is_default = FALSE);

CREATE POLICY "Users can delete their own tidak_terlaksana options" 
ON tidak_terlaksana FOR DELETE 
USING (auth.uid() = user_id AND is_default = FALSE);

-- =====================================================
-- ADD TABLE COMMENTS
-- =====================================================

COMMENT ON TABLE tindakan_susulan IS 'Options for follow-up actions in lesson plan reflections';
COMMENT ON COLUMN tindakan_susulan.id IS 'Primary key UUID';
COMMENT ON COLUMN tindakan_susulan.user_id IS 'User who created this option (NULL for default options)';
COMMENT ON COLUMN tindakan_susulan.option_text IS 'The text of the follow-up action option';
COMMENT ON COLUMN tindakan_susulan.is_default IS 'Whether this is a default system option';
COMMENT ON COLUMN tindakan_susulan.created_at IS 'When the option was created';
COMMENT ON COLUMN tindakan_susulan.updated_at IS 'When the option was last updated';

COMMENT ON TABLE tidak_terlaksana IS 'Options for reasons why lesson was not implemented';
COMMENT ON COLUMN tidak_terlaksana.id IS 'Primary key UUID';
COMMENT ON COLUMN tidak_terlaksana.user_id IS 'User who created this option (NULL for default options)';
COMMENT ON COLUMN tidak_terlaksana.option_text IS 'The text of the not implemented reason';
COMMENT ON COLUMN tidak_terlaksana.is_default IS 'Whether this is a default system option';
COMMENT ON COLUMN tidak_terlaksana.created_at IS 'When the option was created';
COMMENT ON COLUMN tidak_terlaksana.updated_at IS 'When the option was last updated';

COMMIT;

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

/*
-- Query all available tindakan_susulan options for a user
SELECT * FROM tindakan_susulan 
WHERE is_default = TRUE OR user_id = 'user-uuid-here'
ORDER BY is_default DESC, option_text ASC;

-- Add a new user-specific tindakan_susulan option
INSERT INTO tindakan_susulan (user_id, option_text, is_default)
VALUES ('user-uuid-here', 'Custom follow-up action', FALSE);

-- Update a user-specific option
UPDATE tindakan_susulan 
SET option_text = 'Updated custom action'
WHERE id = 'option-uuid-here' AND user_id = 'user-uuid-here' AND is_default = FALSE;

-- Delete a user-specific option
DELETE FROM tindakan_susulan 
WHERE id = 'option-uuid-here' AND user_id = 'user-uuid-here' AND is_default = FALSE;
*/
