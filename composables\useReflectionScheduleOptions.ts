import { ref, computed } from 'vue';
import { useSupabaseUser } from '#imports';
import type { ClassSubjectOption, DayOption } from '~/types/reflections';
import type { LessonPlan } from '~/types/lessonPlans';
import { useTeacherSchedules } from '~/composables/useTeacherSchedules';
import type { TeacherSchedule, SchedulePeriod, DayOfWeek } from '~/types/teacherSchedule';

// State
const loading = ref(false);
const error = ref<string | null>(null);

export const useReflectionScheduleOptions = () => {
  const user = useSupabaseUser();
  const { fetchScheduleByLessonPlanId } = useTeacherSchedules();

  // Day mapping for Malay days
  const dayMapping: Record<DayOfWeek, string> = {
    'AHAD': 'Ahad',
    'ISNIN': 'Isnin',
    'SELASA': 'Selasa',
    'RABU': 'Rabu',
    'KHAMIS': 'Khamis',
    'JUMAAT': 'Jumaat'
  };

  // Get class subject options for a lesson plan (only those that exist in timetable for specific day)
  const getClassSubjectOptions = async (lessonPlanId: string, filterByDay?: string): Promise<ClassSubjectOption[]> => {
    if (!user.value) return [];

    try {
      loading.value = true;
      error.value = null;

      // Get the lesson plan's schedule
      const schedule = await fetchScheduleByLessonPlanId(lessonPlanId);
      if (!schedule) {
        console.warn('No schedule found for lesson plan:', lessonPlanId);
        return [];
      }

      const options: ClassSubjectOption[] = [];
      const addedCombinations = new Set<string>();

      // Handle new structure (class_subjects)
      if (schedule.schedule_details?.class_subjects) {
        schedule.schedule_details.class_subjects.forEach(classSubject => {
          // If filtering by day, check if this class-subject is scheduled on that day
          if (filterByDay && !classSubject.days_scheduled.includes(filterByDay.toUpperCase() as DayOfWeek)) {
            return;
          }

          const classSubjectId = `${classSubject.class_id}_${classSubject.subject_id}`;

          // Process each scheduled day for this class-subject
          classSubject.days_scheduled.forEach(day => {
            if (filterByDay && day !== filterByDay.toUpperCase()) {
              return;
            }

            const combinationKey = `${classSubjectId}_${day}`;

            // Avoid duplicates for the same day
            if (!addedCombinations.has(combinationKey)) {
              addedCombinations.add(combinationKey);

              options.push({
                id: classSubjectId,
                label: `${classSubject.class_name} - ${classSubject.subject_name}`,
                class_id: classSubject.class_id,
                className: classSubject.class_name,
                subject_id: classSubject.subject_id,
                subjectName: classSubject.subject_name
              });
            }
          });
        });
      }
      // Handle legacy structure (periods)
      else if (schedule.schedule_details?.periods) {
        (schedule.schedule_details.periods as any[]).forEach((period: any) => {
          // If filtering by day, skip other days
          if (filterByDay && period.day !== filterByDay.toUpperCase()) {
            return;
          }

          if (period.class_id && period.subject_id) {
            const classSubjectId = `${period.class_id}_${period.subject_id}`;
            const combinationKey = `${classSubjectId}_${period.day}`;

            // Avoid duplicates for the same day
            if (!addedCombinations.has(combinationKey)) {
              addedCombinations.add(combinationKey);

              options.push({
                id: classSubjectId,
                label: `${period.class_name || period.class_id} - ${period.subject_name || period.subject_id}`,
                class_id: period.class_id,
                className: period.class_name || period.class_id,
                subject_id: period.subject_id,
                subjectName: period.subject_name || period.subject_id
              });
            }
          }
        });
      }

      // Remove duplicates based on class_subject_id (across all days if not filtering)
      const uniqueOptions = options.filter((option, index, self) => 
        index === self.findIndex(o => o.id === option.id)
      );

      return uniqueOptions.sort((a, b) => a.label.localeCompare(b.label));
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get class subject options';
      console.error('Error getting class subject options:', err);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // Get day options for a lesson plan (only days that have valid timetable entries)
  const getDayOptions = async (lessonPlanId: string): Promise<DayOption[]> => {
    if (!user.value) return [];

    try {
      loading.value = true;
      error.value = null;

      // Get the lesson plan's schedule
      const schedule = await fetchScheduleByLessonPlanId(lessonPlanId);
      if (!schedule) {
        console.warn('No schedule found for lesson plan:', lessonPlanId);
        return [];
      }

      const dayOptions: DayOption[] = [];
      const validDays = new Set<string>();

      // Handle new structure (class_subjects)
      if (schedule.schedule_details?.class_subjects) {
        schedule.schedule_details.class_subjects.forEach(classSubject => {
          if (classSubject.class_id && classSubject.subject_id) {
            classSubject.days_scheduled.forEach(day => {
              validDays.add(day);
            });
          }
        });
      }
      // Handle legacy structure (periods)
      else if (schedule.schedule_details?.periods) {
        (schedule.schedule_details.periods as any[]).forEach((period: any) => {
          if (period.class_id && period.subject_id && period.day) {
            validDays.add(period.day);
          }
        });
      }

      // Convert to day options
      validDays.forEach(day => {
        const dayLabel = dayMapping[day as DayOfWeek] || day;
        dayOptions.push({
          value: day,
          label: dayLabel
        });
      });

      // Sort by day order (Sunday to Saturday)
      const dayOrder: DayOfWeek[] = ['AHAD', 'ISNIN', 'SELASA', 'RABU', 'KHAMIS', 'JUMAAT'];
      return dayOptions.sort((a, b) => {
        const aIndex = dayOrder.indexOf(a.value as DayOfWeek);
        const bIndex = dayOrder.indexOf(b.value as DayOfWeek);
        return aIndex - bIndex;
      });
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get day options';
      console.error('Error getting day options:', err);
      return [];
    } finally {
      loading.value = false;
    }
  };

  // Get class subject options for a specific day
  const getClassSubjectOptionsForDay = async (lessonPlanId: string, day: string): Promise<ClassSubjectOption[]> => {
    return getClassSubjectOptions(lessonPlanId, day);
  };

  // Validate if a class-subject exists in the timetable for a specific day
  const validateClassSubjectForDay = async (
    lessonPlanId: string, 
    classSubjectId: string, 
    day: string
  ): Promise<boolean> => {
    const options = await getClassSubjectOptionsForDay(lessonPlanId, day);
    return options.some(option => option.id === classSubjectId);
  };

  // Get all valid class-subject and day combinations
  const getValidCombinations = async (lessonPlanId: string): Promise<Array<{
    classSubjectId: string;
    day: string;
    className: string;
    subjectName: string;
  }>> => {
    if (!user.value) return [];

    try {
      const schedule = await fetchScheduleByLessonPlanId(lessonPlanId);
      if (!schedule) return [];

      const combinations: Array<{
        classSubjectId: string;
        day: string;
        className: string;
        subjectName: string;
      }> = [];

      // Handle new structure (class_subjects)
      if (schedule.schedule_details?.class_subjects) {
        schedule.schedule_details.class_subjects.forEach(classSubject => {
          if (classSubject.class_id && classSubject.subject_id) {
            const classSubjectId = `${classSubject.class_id}_${classSubject.subject_id}`;
            classSubject.days_scheduled.forEach(day => {
              combinations.push({
                classSubjectId,
                day,
                className: classSubject.class_name,
                subjectName: classSubject.subject_name
              });
            });
          }
        });
      }
      // Handle legacy structure (periods)
      else if (schedule.schedule_details?.periods) {
        (schedule.schedule_details.periods as any[]).forEach((period: any) => {
          if (period.class_id && period.subject_id && period.day) {
            const classSubjectId = `${period.class_id}_${period.subject_id}`;
            combinations.push({
              classSubjectId,
              day: period.day,
              className: period.class_name || period.class_id,
              subjectName: period.subject_name || period.subject_id
            });
          }
        });
      }

      return combinations;
    } catch (err) {
      console.error('Error getting valid combinations:', err);
      return [];
    }
  };

  // Get student count for a class-subject combination
  const getStudentCountForClassSubject = async (classSubjectId: string): Promise<number> => {
    // This would typically fetch from the class_subjects table
    // For now, return a default value
    // TODO: Implement actual student count fetching
    return 30; // Default student count
  };

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Methods
    getClassSubjectOptions,
    getDayOptions,
    getClassSubjectOptionsForDay,
    validateClassSubjectForDay,
    getValidCombinations,
    getStudentCountForClassSubject,

    // Constants
    dayMapping
  };
};
