<template>
    <div ref="triggerRef" class="inline-block" @mouseenter="showTooltip" @mouseleave="hideTooltip"
        @focusin="showTooltip" @focusout="hideTooltip" tabindex="0">
        <slot></slot>
        <Teleport to="body">
            <transition enter-active-class="transition ease-out duration-200 transform"
                enter-from-class="opacity-0 scale-95" enter-to-class="opacity-100 scale-100"
                leave-active-class="transition ease-in duration-200 transform" leave-from-class="opacity-100 scale-100"
                leave-to-class="opacity-0 scale-95">
                <div v-if="isVisible" ref="tooltipRef" :style="tooltipStyle"
                    class="fixed z-[9999] px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm dark:bg-gray-700 whitespace-nowrap"
                    role="tooltip">
                    {{ text }}
                </div>
            </transition>
        </Teleport>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';

const props = withDefaults(defineProps<{
    text: string;
    position?: 'top' | 'bottom' | 'left' | 'right';
    delay?: number;
}>(), {
    position: 'top',
    delay: 150,
});

const isVisible = ref(false);
const showTimeoutId = ref<number | null>(null);
const triggerRef = ref<HTMLElement | null>(null);
const tooltipRef = ref<HTMLElement | null>(null);
const tooltipStyle = ref({});

const updatePosition = () => {
    if (!triggerRef.value || !tooltipRef.value || !isVisible.value) {
        return;
    }

    const triggerRect = triggerRef.value.getBoundingClientRect();
    const tt = tooltipRef.value;
    const tooltipHeight = tt.offsetHeight;
    const tooltipWidth = tt.offsetWidth;
    const gap = 8; // Gap from trigger

    let top = 0;
    let left = 0;

    switch (props.position) {
        case 'bottom':
            top = triggerRect.bottom + gap;
            left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
            break;
        case 'left':
            top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
            left = triggerRect.left - tooltipWidth - gap;
            break;
        case 'right':
            top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
            left = triggerRect.right + gap;
            break;
        case 'top':
        default:
            top = triggerRect.top - tooltipHeight - gap;
            left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
            break;
    }

    // Viewport boundary checks
    if (left < gap) left = gap;
    if (left + tooltipWidth > window.innerWidth - gap) {
        left = window.innerWidth - tooltipWidth - gap;
    }
    if (top < gap) top = gap;
    if (top + tooltipHeight > window.innerHeight - gap) {
        top = window.innerHeight - tooltipHeight - gap;
    }

    tooltipStyle.value = {
        top: `${top}px`,
        left: `${left}px`,
    };
};

const showTooltip = () => {
    if (showTimeoutId.value) clearTimeout(showTimeoutId.value);
    showTimeoutId.value = window.setTimeout(async () => {
        isVisible.value = true;
        await nextTick(); // Wait for DOM update & tooltipRef to be available
        updatePosition();
    }, props.delay);
};

const hideTooltip = () => {
    if (showTimeoutId.value) {
        clearTimeout(showTimeoutId.value);
        showTimeoutId.value = null;
    }
    isVisible.value = false;
};

const handleResize = () => {
    if (isVisible.value) {
        updatePosition();
    }
};

onMounted(() => {
    window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    if (showTimeoutId.value) clearTimeout(showTimeoutId.value);
});

</script>

<style scoped>
/* Tooltip arrows can be added here later if desired */
/* Ensure the triggerRef itself doesn't cause overflow if it's a block element taking full width */
/* The .inline-block class on triggerRef should handle this for most cases. */
</style>
