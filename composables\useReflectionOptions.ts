import { ref, computed } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'

export interface ReflectionOption {
  id: string
  option_text: string
  is_default: boolean
  user_id?: string
  created_at: string
  updated_at: string
}

export const useReflectionOptions = () => {
  const supabase = useSupabaseClient()
  const user = useSupabaseUser()
  
  // State
  const tindakanSusulanOptions = ref<ReflectionOption[]>([])
  const tidakTerlaksanaOptions = ref<ReflectionOption[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const sortedTindakanSusulanOptions = computed(() => {
    return [...tindakanSusulanOptions.value].sort((a, b) => {
      // Default options first, then alphabetical
      if (a.is_default && !b.is_default) return -1
      if (!a.is_default && b.is_default) return 1
      return a.option_text.localeCompare(b.option_text)
    })
  })

  const sortedTidakTerlaksanaOptions = computed(() => {
    return [...tidakTerlaksanaOptions.value].sort((a, b) => {
      // Default options first, then alphabetical
      if (a.is_default && !b.is_default) return -1
      if (!a.is_default && b.is_default) return 1
      return a.option_text.localeCompare(b.option_text)
    })
  })

  // Fetch tindakan susulan options
  const fetchTindakanSusulanOptions = async () => {
    try {
      loading.value = true
      error.value = null

      const { data, error: fetchError } = await supabase
        .from('tindakan_susulan')
        .select('*')
        .order('is_default', { ascending: false })
        .order('option_text', { ascending: true })

      if (fetchError) throw fetchError

      tindakanSusulanOptions.value = (data as ReflectionOption[]) || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch tindakan susulan options'
      console.error('Error fetching tindakan susulan options:', err)
    } finally {
      loading.value = false
    }
  }

  // Fetch tidak terlaksana options
  const fetchTidakTerlaksanaOptions = async () => {
    try {
      loading.value = true
      error.value = null

      const { data, error: fetchError } = await supabase
        .from('tidak_terlaksana')
        .select('*')
        .order('is_default', { ascending: false })
        .order('option_text', { ascending: true })

      if (fetchError) throw fetchError

      tidakTerlaksanaOptions.value = (data as ReflectionOption[]) || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch tidak terlaksana options'
      console.error('Error fetching tidak terlaksana options:', err)
    } finally {
      loading.value = false
    }
  }

  // Create tindakan susulan option
  const createTindakanSusulanOption = async (optionText: string) => {
    if (!user.value) throw new Error('User not authenticated')

    try {
      loading.value = true
      error.value = null

      const { data, error: createError } = await supabase
        .from('tindakan_susulan')
        .insert({
          user_id: user.value.id,
          option_text: optionText.trim(),
          is_default: false
        } as any)
        .select()
        .single()

      if (createError) throw createError

      // Add to local state
      tindakanSusulanOptions.value.push(data as ReflectionOption)

      return data as ReflectionOption
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create tindakan susulan option'
      console.error('Error creating tindakan susulan option:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Create tidak terlaksana option
  const createTidakTerlaksanaOption = async (optionText: string) => {
    if (!user.value) throw new Error('User not authenticated')

    try {
      loading.value = true
      error.value = null

      const { data, error: createError } = await supabase
        .from('tidak_terlaksana')
        .insert({
          user_id: user.value.id,
          option_text: optionText.trim(),
          is_default: false
        } as any)
        .select()
        .single()

      if (createError) throw createError

      // Add to local state
      tidakTerlaksanaOptions.value.push(data as ReflectionOption)

      return data as ReflectionOption
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create tidak terlaksana option'
      console.error('Error creating tidak terlaksana option:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Update tindakan susulan option
  const updateTindakanSusulanOption = async (id: string, optionText: string) => {
    if (!user.value) throw new Error('User not authenticated')

    try {
      loading.value = true
      error.value = null

      const { data, error: updateError } = await (supabase
        .from('tindakan_susulan') as any)
        .update({ option_text: optionText.trim() })
        .eq('id', id)
        .eq('user_id', user.value.id)
        .eq('is_default', false)
        .select()
        .single()

      if (updateError) throw updateError

      // Update local state
      const index = tindakanSusulanOptions.value.findIndex(opt => opt.id === id)
      if (index !== -1) {
        tindakanSusulanOptions.value[index] = data as ReflectionOption
      }

      return data as ReflectionOption
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update tindakan susulan option'
      console.error('Error updating tindakan susulan option:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Update tidak terlaksana option
  const updateTidakTerlaksanaOption = async (id: string, optionText: string) => {
    if (!user.value) throw new Error('User not authenticated')

    try {
      loading.value = true
      error.value = null

      const { data, error: updateError } = await (supabase
        .from('tidak_terlaksana') as any)
        .update({ option_text: optionText.trim() })
        .eq('id', id)
        .eq('user_id', user.value.id)
        .eq('is_default', false)
        .select()
        .single()

      if (updateError) throw updateError

      // Update local state
      const index = tidakTerlaksanaOptions.value.findIndex(opt => opt.id === id)
      if (index !== -1) {
        tidakTerlaksanaOptions.value[index] = data as ReflectionOption
      }

      return data as ReflectionOption
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update tidak terlaksana option'
      console.error('Error updating tidak terlaksana option:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Delete tindakan susulan option
  const deleteTindakanSusulanOption = async (id: string) => {
    if (!user.value) throw new Error('User not authenticated')

    try {
      loading.value = true
      error.value = null

      const { error: deleteError } = await supabase
        .from('tindakan_susulan')
        .delete()
        .eq('id', id)
        .eq('user_id', user.value.id)
        .eq('is_default', false)

      if (deleteError) throw deleteError

      // Remove from local state
      tindakanSusulanOptions.value = tindakanSusulanOptions.value.filter(opt => opt.id !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete tindakan susulan option'
      console.error('Error deleting tindakan susulan option:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // Delete tidak terlaksana option
  const deleteTidakTerlaksanaOption = async (id: string) => {
    if (!user.value) throw new Error('User not authenticated')

    try {
      loading.value = true
      error.value = null

      const { error: deleteError } = await supabase
        .from('tidak_terlaksana')
        .delete()
        .eq('id', id)
        .eq('user_id', user.value.id)
        .eq('is_default', false)

      if (deleteError) throw deleteError

      // Remove from local state
      tidakTerlaksanaOptions.value = tidakTerlaksanaOptions.value.filter(opt => opt.id !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete tidak terlaksana option'
      console.error('Error deleting tidak terlaksana option:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    tindakanSusulanOptions: sortedTindakanSusulanOptions,
    tidakTerlaksanaOptions: sortedTidakTerlaksanaOptions,
    loading,
    error,

    // Methods
    fetchTindakanSusulanOptions,
    fetchTidakTerlaksanaOptions,
    createTindakanSusulanOption,
    createTidakTerlaksanaOption,
    updateTindakanSusulanOption,
    updateTidakTerlaksanaOption,
    deleteTindakanSusulanOption,
    deleteTidakTerlaksanaOption
  }
}
