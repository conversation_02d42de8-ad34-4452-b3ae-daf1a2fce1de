<template>
    <Modal :is-open="isOpen" title="Urus Kelas & Subjek" @update:is-open="$emit('update:isOpen', $event)" size="5xl">
        <ClassSubject :initial-data="userClassSubjects" @update:data="handleDataUpdate" @data-valid="() => { }"
            @data-invalid="() => { }" />
        <template #footer>
            <div class="flex justify-end space-x-3">
                <Button variant="outline" @click="$emit('update:isOpen', false)">
                    Batal
                </Button>
                <Button variant="primary" prepend-icon="mdi:check" @click="handleSave">
                    Simpan
                </Button>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import Modal from '~/components/ui/composite/Modal.vue'
import Button from '~/components/ui/base/Button.vue'
import ClassSubject from '~/components/ui/composite/ClassSubject.vue'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'

const props = defineProps<{
    isOpen: boolean
    userClassSubjects: UserClassSubjectEntry[]
}>()

const emit = defineEmits<{
    'update:isOpen': [value: boolean]
    'update:userClassSubjects': [data: UserClassSubjectEntry[]]
    'save': [data: UserClassSubjectEntry[]]
}>()

// Store the updated data locally
const currentClassSubjects = ref<UserClassSubjectEntry[]>([])

// Initialize currentClassSubjects with the initial data
watch(() => props.userClassSubjects, (newData) => {
    currentClassSubjects.value = [...newData]
}, { immediate: true })

const handleDataUpdate = (data: UserClassSubjectEntry[]) => {
    currentClassSubjects.value = data
    emit('update:userClassSubjects', data)
}

const handleSave = () => {
    // Use the current updated data instead of the original props
    emit('save', currentClassSubjects.value);
}
</script>