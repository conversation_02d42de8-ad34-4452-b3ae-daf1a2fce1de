<template>
  <div class="reflection-template-selector">
    <!-- Template Selection Header -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-2">
        <Icon name="heroicons:document-text" class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Pilih Template Refleksi
        </h3>
      </div>
      <UiBaseButton
        v-if="!showTemplateSelector"
        @click="showTemplateSelector = true"
        variant="outline"
        size="sm"
        prepend-icon="heroicons:plus"
      >
        Guna Template
      </UiBaseButton>
      <UiBaseButton
        v-else
        @click="closeTemplateSelector"
        variant="outline"
        size="sm"
        prepend-icon="heroicons:x-mark"
      >
        Tutup
      </UiBaseButton>
    </div>

    <!-- Template Selector Panel -->
    <div v-if="showTemplateSelector" class="template-selector-panel bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-8">
        <div class="flex items-center space-x-2">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
          <span class="text-sm text-gray-600 dark:text-gray-400">Memuatkan template...</span>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
          <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5" />
          <div class="text-sm text-red-800 dark:text-red-200">
            {{ error }}
          </div>
        </div>
      </div>

      <!-- Template Categories -->
      <div v-else class="space-y-4">
        <!-- Quick Access: Favorites and Recent -->
        <div v-if="favoriteTemplates.length > 0 || recentlyUsedTemplates.length > 0" class="space-y-3">
          <!-- Favorites -->
          <div v-if="favoriteTemplates.length > 0">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <Icon name="heroicons:heart-solid" class="w-4 h-4 text-red-500 mr-1" />
              Kegemaran
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <TemplateCard
                v-for="template in favoriteTemplates"
                :key="template.id"
                :template="template"
                @select="selectTemplate"
                @toggle-favorite="toggleFavorite"
                @preview="previewTemplate"
              />
            </div>
          </div>

          <!-- Recently Used -->
          <div v-if="recentlyUsedTemplates.length > 0">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
              <Icon name="heroicons:clock" class="w-4 h-4 text-blue-500 mr-1" />
              Baru-baru Ini
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <TemplateCard
                v-for="template in recentlyUsedTemplates"
                :key="template.id"
                :template="template"
                @select="selectTemplate"
                @toggle-favorite="toggleFavorite"
                @preview="previewTemplate"
              />
            </div>
          </div>

          <!-- Divider -->
          <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
        </div>

        <!-- Category Tabs -->
        <div class="flex flex-wrap gap-2 mb-4">
          <button
            v-for="(label, category) in categoryLabels"
            :key="category"
            @click="selectedCategory = category"
            :class="[
              'px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
              selectedCategory === category
                ? 'bg-primary text-white'
                : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
            ]"
          >
            {{ label }}
          </button>
        </div>

        <!-- Templates by Category -->
        <div class="space-y-2">
          <div v-if="templatesByCategory[selectedCategory].length === 0" class="text-center py-8">
            <Icon name="heroicons:document-text" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Tiada template dalam kategori ini
            </p>
          </div>
          <div v-else class="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <TemplateCard
              v-for="template in templatesByCategory[selectedCategory]"
              :key="template.id"
              :template="template"
              @select="selectTemplate"
              @toggle-favorite="toggleFavorite"
              @preview="previewTemplate"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Template Preview Modal -->
    <Modal
      :is-open="showPreviewModal"
      :title="`Preview: ${previewedTemplate?.name || ''}`"
      @update:is-open="showPreviewModal = $event"
      size="lg"
    >
      <div v-if="previewedTemplate" class="space-y-4">
        <!-- Template Info -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div class="flex items-start justify-between">
            <div>
              <h4 class="font-medium text-gray-900 dark:text-gray-100">
                {{ previewedTemplate.name }}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ previewedTemplate.description }}
              </p>
              <div class="flex items-center space-x-2 mt-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {{ categoryLabels[previewedTemplate.category] }}
                </span>
                <span v-if="previewedTemplate.is_system_template" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Sistem
                </span>
              </div>
            </div>
            <button
              @click="toggleFavorite(previewedTemplate.id)"
              :class="[
                'p-2 rounded-full transition-colors',
                previewedTemplate.is_favorite
                  ? 'text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20'
                  : 'text-gray-400 hover:text-red-500 hover:bg-gray-50 dark:hover:bg-gray-800'
              ]"
            >
              <Icon :name="previewedTemplate.is_favorite ? 'heroicons:heart-solid' : 'heroicons:heart'" class="w-5 h-5" />
            </button>
          </div>
        </div>

        <!-- Prompts Preview -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Soalan Panduan:</h5>
          <div class="space-y-2">
            <div v-for="(prompt, field) in previewedTemplate.prompts" :key="field" class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
              <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {{ getFieldLabel(field) }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ prompt }}
              </div>
            </div>
          </div>
        </div>

        <!-- Default Values Preview -->
        <div v-if="Object.keys(previewedTemplate.default_values).length > 0">
          <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Nilai Lalai:</h5>
          <div class="bg-white dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div v-for="(value, field) in previewedTemplate.default_values" :key="field">
                <span class="text-gray-600 dark:text-gray-400">{{ getFieldLabel(field) }}:</span>
                <span class="ml-2 font-medium text-gray-900 dark:text-gray-100">{{ formatDefaultValue(field, value) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <UiBaseButton
            @click="showPreviewModal = false"
            variant="outline"
          >
            Tutup
          </UiBaseButton>
          <UiBaseButton
            v-if="previewedTemplate"
            @click="selectTemplate(previewedTemplate.id)"
            variant="primary"
          >
            Guna Template Ini
          </UiBaseButton>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useReflectionTemplates } from '~/composables/useReflectionTemplates';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import type { ReflectionTemplateWithPreference, ReflectionTemplateCategory } from '~/types/reflections';
import Modal from '~/components/ui/composite/Modal.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import TemplateCard from './TemplateCard.vue';

interface Props {
  modelValue?: string | null; // Selected template ID
}

interface Emits {
  (e: 'update:modelValue', value: string | null): void;
  (e: 'template-selected', templateId: string): void;
  (e: 'template-applied', result: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
});

const emit = defineEmits<Emits>();

// Composables
const {
  templates,
  favoriteTemplates,
  recentlyUsedTemplates,
  templatesByCategory,
  loading,
  error,
  fetchAllTemplates,
  toggleTemplateFavorite,
  applyTemplate
} = useReflectionTemplates();

// State
const showTemplateSelector = ref(false);
const showPreviewModal = ref(false);
const selectedCategory = ref<ReflectionTemplateCategory>('lesson_type');
const previewedTemplate = ref<ReflectionTemplateWithPreference | null>(null);

// Category labels
const categoryLabels = TEMPLATE_CATEGORY_LABELS;

// Field labels for display
const fieldLabels: Record<string, string> = {
  challenges_faced: 'Cabaran Dihadapi',
  successful_strategies: 'Strategi Berjaya',
  improvements_needed: 'Penambahbaikan Diperlukan',
  additional_notes: 'Catatan Tambahan',
  overall_rating: 'Penilaian Keseluruhan',
  objectives_achieved: 'Objektif Tercapai',
  activity_effectiveness: 'Keberkesanan Aktiviti',
  time_management: 'Pengurusan Masa',
  student_engagement: 'Penglibatan Pelajar',
  resource_adequacy: 'Kecukupan Sumber'
};

// Methods
const getFieldLabel = (field: string): string => {
  return fieldLabels[field] || field;
};

const formatDefaultValue = (field: string, value: any): string => {
  if (typeof value === 'boolean') {
    return value ? 'Ya' : 'Tidak';
  }
  if (typeof value === 'number') {
    return value.toString();
  }
  if (typeof value === 'string') {
    // Format specific field values
    if (field === 'time_management') {
      const timeLabels: Record<string, string> = {
        'on_time': 'Tepat Masa',
        'early': 'Awal',
        'late': 'Lewat',
        'not_applicable': 'Tidak Berkenaan'
      };
      return timeLabels[value] || value;
    }
    if (field === 'resource_adequacy') {
      const resourceLabels: Record<string, string> = {
        'inadequate': 'Tidak Mencukupi',
        'adequate': 'Mencukupi',
        'excellent': 'Sangat Baik',
        'not_applicable': 'Tidak Berkenaan'
      };
      return resourceLabels[value] || value;
    }
    return value;
  }
  return String(value);
};

const selectTemplate = async (templateId: string) => {
  try {
    const result = await applyTemplate(templateId);
    emit('update:modelValue', templateId);
    emit('template-selected', templateId);
    emit('template-applied', result);
    
    // Close modals
    showPreviewModal.value = false;
    showTemplateSelector.value = false;
  } catch (err) {
    console.error('Error applying template:', err);
  }
};

const toggleFavorite = async (templateId: string) => {
  try {
    await toggleTemplateFavorite(templateId);
  } catch (err) {
    console.error('Error toggling favorite:', err);
    // You could emit an error event here for toast notifications
    // emit('error', 'Failed to update template favorite status');
  }
};

const previewTemplate = (templateId: string) => {
  const template = templates.value.find(t => t.id === templateId);
  if (template) {
    previewedTemplate.value = template;
    showPreviewModal.value = true;
  }
};

const closeTemplateSelector = () => {
  showTemplateSelector.value = false;
  showPreviewModal.value = false;
};

// Lifecycle
onMounted(async () => {
  try {
    await fetchAllTemplates();
  } catch (err) {
    console.error('Error loading templates:', err);
  }
});
</script>

<style scoped>
.template-selector-panel {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
