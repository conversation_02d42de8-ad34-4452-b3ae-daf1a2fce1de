<template>
    <Modal :is-open="isOpen" title="Edit Waktu Slot" @update:is-open="$emit('update:isOpen', $event)" size="md">
        <div v-if="timeSlot" class="space-y-6">
            <!-- Current Time Display -->
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <Icon name="mdi:clock-edit" class="h-5 w-5 text-blue-500" />
                        <div>
                            <div class="font-medium text-blue-900 dark:text-blue-100">
                                Waktu {{ timeSlot?.period_number + 1 }}
                            </div>
                            <div class="text-sm text-blue-600 dark:text-blue-300">
                                {{ formatTime(timeSlot?.start_time) }} - {{ formatTime(timeSlot?.end_time) }}
                            </div>
                        </div>
                    </div>
                    <!-- Padam But<PERSON> -->
                    <Button variant="delete" size="sm" prepend-icon="mdi:delete" @click="$emit('delete')">
                        Padam
                    </Button>
                </div>
            </div>

            <!-- Time Inputs -->
            <div class="grid grid-cols-2 gap-4">
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Masa Mula
                    </label>
                    <Input v-if="localTimeSlot" v-model="localTimeSlot.start_time" type="time" class="text-center" />
                </div>

                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Masa Tamat
                    </label>
                    <Input v-if="localTimeSlot" v-model="localTimeSlot.end_time" type="time" class="text-center" />
                </div>
            </div>

            <!-- Duration Display -->
            <div
                class="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <Icon name="mdi:timer" class="h-4 w-4" />
                <span>Tempoh: {{ calculateTimeSlotDuration() }} minit</span>
            </div> <!-- Quick Presets -->
            <div class="space-y-3">
                <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Pratetap Tempoh Masa:
                </div>
                <div class="grid grid-cols-2 gap-2">
                    <Button v-for="preset in DURATION_PRESETS" :key="preset.minutes" variant="outline" size="md"
                        class="text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20 w-full py-2"
                        @click="applyDurationPreset(preset.minutes)">
                        {{ preset.label }}
                    </Button>
                </div>
            </div> <!-- Information about affected classes -->
            <div v-if="affectedClasses.length > 0"
                class="flex items-start space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <Icon name="mdi:information" class="h-4 w-4 text-blue-500 mt-0.5" />
                <div class="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Maklumat:</strong> {{ affectedClasses.length }} kelas akan dikemas kini ke waktu baharu
                    secara automatik.
                    <div class="mt-1 text-xs text-blue-600 dark:text-blue-300">
                        Tiada kelas akan hilang - semuanya akan dipindahkan ke waktu baharu.
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex justify-end space-x-3">
                <Button variant="outline" @click="$emit('update:isOpen', false)" :disabled="isUpdating">
                    Batal
                </Button>
                <Button variant="primary" prepend-icon="mdi:check" @click="handleUpdate" :disabled="isUpdating">
                    <Icon v-if="isUpdating" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                    {{ isUpdating ? 'Mengemas kini...' : 'Simpan Perubahan' }}
                </Button>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Modal from '~/components/ui/composite/Modal.vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import Input from '~/components/ui/base/Input.vue'
import type { TimeSlot } from '~/types/timetable'
import { DURATION_PRESETS } from '~/utils/timeHelpers'
import { formatTime, calculateDurationMinutes, addDurationToTime } from '~/utils/timeHelpers'

const props = defineProps<{
    isOpen: boolean
    timeSlot: TimeSlot | null
    affectedClasses: any[]
}>()

const emit = defineEmits<{
    'update:isOpen': [value: boolean]
    'update': [timeSlot: TimeSlot]
    'delete': []
}>()

const localTimeSlot = ref<TimeSlot | null>(null)
const isUpdating = ref(false)

watch(() => props.timeSlot, (newSlot) => {
    if (newSlot) {
        localTimeSlot.value = { ...newSlot }
    }
}, { immediate: true })

const calculateTimeSlotDuration = (): number => {
    if (!localTimeSlot.value) return 0
    return calculateDurationMinutes(localTimeSlot.value.start_time, localTimeSlot.value.end_time)
}

const applyDurationPreset = (durationMinutes: number) => {
    if (!localTimeSlot.value) return
    localTimeSlot.value.end_time = addDurationToTime(localTimeSlot.value.start_time, durationMinutes)
}

const handleUpdate = () => {
    if (localTimeSlot.value) {
        isUpdating.value = true
        emit('update', localTimeSlot.value)
        // Actual update logic will be handled by parent
        isUpdating.value = false
        emit('update:isOpen', false)
    }
}
</script>