import type { ReflectionTemplate, ReflectionTemplateCategory } from '~/types/reflections';

// System reflection templates with comprehensive prompts and defaults
export const SYSTEM_REFLECTION_TEMPLATES: Omit<ReflectionTemplate, 'id' | 'created_at' | 'updated_at' | 'usage_count' | 'created_by'>[] = [
  // =====================================================
  // LESSON TYPE TEMPLATES
  // =====================================================
  {
    name: 'Topik Baharu',
    description: 'Template untuk refleksi pengenalan topik baharu kepada pelajar',
    category: 'lesson_type' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Adakah pelajar menghadapi kesukaran memahami konsep baharu? Nyatakan kesukaran utama.',
      successful_strategies: 'Ka<PERSON>h pengenalan yang berkesan digunakan (contoh: analogi, demonstrasi, aktiviti hands-on):',
      improvements_needed: 'Cara untuk meningkatkan pemahaman pelajar pada topik ini:',
      additional_notes: 'Reaksi pelajar terhadap topik baharu dan tahap minat yang ditunjukkan:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 4,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Ulangkaji & Pengukuhan',
    description: 'Template untuk refleksi sesi ulangkaji dan pengukuhan topik terdahulu',
    category: 'lesson_type' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Topik mana yang pelajar masih lemah atau keliru? Adakah miskonsepsi yang dikesan?',
      successful_strategies: 'Teknik ulangkaji yang berkesan (contoh: mind map, quiz, peer teaching):',
      improvements_needed: 'Kawasan yang perlu lebih tumpuan dalam sesi ulangkaji akan datang:',
      additional_notes: 'Tahap kefahaman pelajar selepas ulangkaji dan kemajuan yang diperhatikan:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 3,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Aktiviti Kumpulan',
    description: 'Template untuk refleksi aktiviti pembelajaran berasaskan kumpulan',
    category: 'lesson_type' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Cabaran dalam pengurusan kumpulan dan kerjasama pelajar:',
      successful_strategies: 'Strategi pembentukan kumpulan dan fasilitasi yang berkesan:',
      improvements_needed: 'Penambahbaikan untuk aktiviti kumpulan pada masa hadapan:',
      additional_notes: 'Dinamik kumpulan dan tahap penyertaan setiap ahli:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 5,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Pembelajaran Kendiri',
    description: 'Template untuk refleksi sesi pembelajaran kendiri dan eksplorasi',
    category: 'lesson_type' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Kesukaran pelajar dalam pembelajaran kendiri dan pengurusan masa:',
      successful_strategies: 'Panduan dan scaffolding yang membantu pembelajaran kendiri:',
      improvements_needed: 'Sokongan tambahan yang diperlukan untuk pembelajaran kendiri:',
      additional_notes: 'Tahap kemandirian dan motivasi pelajar yang diperhatikan:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 3,
      student_engagement: 3,
      overall_rating: 3,
      objectives_achieved: true
    },
    is_system_template: true
  },

  // =====================================================
  // ASSESSMENT TEMPLATES
  // =====================================================
  {
    name: 'Pentaksiran Formatif',
    description: 'Template untuk refleksi pentaksiran formatif dan maklum balas',
    category: 'assessment' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Kesukaran dalam melaksanakan pentaksiran dan mengumpul maklum balas:',
      successful_strategies: 'Kaedah pentaksiran formatif yang berkesan digunakan:',
      improvements_needed: 'Penambahbaikan untuk pentaksiran dan maklum balas akan datang:',
      additional_notes: 'Analisis prestasi pelajar dan kawasan yang perlu diperbaiki:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 3,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Pentaksiran Sumatif',
    description: 'Template untuk refleksi pentaksiran sumatif dan analisis prestasi',
    category: 'assessment' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Masalah yang dihadapi semasa pentaksiran dan pengurusan masa:',
      successful_strategies: 'Kaedah pentaksiran yang berkesan dan adil:',
      improvements_needed: 'Penambahbaikan untuk pentaksiran sumatif akan datang:',
      additional_notes: 'Analisis keputusan dan trend prestasi pelajar:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 3,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Pentaksiran Autentik',
    description: 'Template untuk refleksi pentaksiran autentik dan portfolio',
    category: 'assessment' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Cabaran dalam mereka bentuk dan melaksanakan pentaksiran autentik:',
      successful_strategies: 'Pendekatan pentaksiran autentik yang berkesan:',
      improvements_needed: 'Penambahbaikan untuk pentaksiran autentik pada masa hadapan:',
      additional_notes: 'Kualiti kerja pelajar dan kemahiran yang ditunjukkan:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 4,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  // =====================================================
  // TECHNOLOGY TEMPLATES
  // =====================================================
  {
    name: 'Teknologi dalam P&P',
    description: 'Template untuk refleksi penggunaan teknologi dalam pengajaran dan pembelajaran',
    category: 'technology' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Cabaran teknikal dan pedagogi dalam penggunaan teknologi:',
      successful_strategies: 'Teknologi dan aplikasi yang berjaya meningkatkan pembelajaran:',
      improvements_needed: 'Penambahbaikan infrastruktur, kemahiran atau pendekatan teknologi:',
      additional_notes: 'Respons pelajar terhadap teknologi dan tahap penglibatan digital:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 4,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Pembelajaran Dalam Talian',
    description: 'Template untuk refleksi sesi pembelajaran dalam talian atau hibrid',
    category: 'technology' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Cabaran dalam pembelajaran dalam talian (teknikal, penglibatan, komunikasi):',
      successful_strategies: 'Platform dan strategi dalam talian yang berkesan:',
      improvements_needed: 'Penambahbaikan untuk pembelajaran dalam talian akan datang:',
      additional_notes: 'Tahap penyertaan pelajar dan kualiti interaksi dalam talian:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 3,
      student_engagement: 3,
      overall_rating: 3,
      objectives_achieved: true
    },
    is_system_template: true
  },

  // =====================================================
  // BEHAVIOR MANAGEMENT TEMPLATES
  // =====================================================
  {
    name: 'Pengurusan Tingkah Laku',
    description: 'Template untuk refleksi pengurusan tingkah laku dan disiplin kelas',
    category: 'behavior' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Isu tingkah laku yang dihadapi dan impaknya terhadap pembelajaran:',
      successful_strategies: 'Strategi pengurusan tingkah laku yang berkesan:',
      improvements_needed: 'Pendekatan yang perlu diperbaiki untuk pengurusan tingkah laku:',
      additional_notes: 'Perubahan tingkah laku yang diperhatikan dan kemajuan pelajar:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 3,
      student_engagement: 3,
      overall_rating: 3,
      objectives_achieved: true
    },
    is_system_template: true
  },

  {
    name: 'Motivasi & Penglibatan',
    description: 'Template untuk refleksi strategi motivasi dan peningkatan penglibatan pelajar',
    category: 'behavior' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Cabaran dalam memotivasikan pelajar dan meningkatkan penglibatan:',
      successful_strategies: 'Teknik motivasi dan penglibatan yang berkesan:',
      improvements_needed: 'Strategi tambahan untuk meningkatkan motivasi pelajar:',
      additional_notes: 'Tahap motivasi dan perubahan sikap pelajar yang diperhatikan:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 4,
      student_engagement: 4,
      overall_rating: 4,
      objectives_achieved: true
    },
    is_system_template: true
  },

  // =====================================================
  // GENERAL TEMPLATES
  // =====================================================
  {
    name: 'Refleksi Umum',
    description: 'Template umum untuk refleksi harian tanpa fokus khusus',
    category: 'general' as ReflectionTemplateCategory,
    prompts: {
      challenges_faced: 'Cabaran utama yang dihadapi dalam sesi ini:',
      successful_strategies: 'Strategi dan pendekatan yang berjaya:',
      improvements_needed: 'Kawasan yang perlu diperbaiki:',
      additional_notes: 'Pemerhatian dan refleksi tambahan:'
    },
    default_values: {
      time_management: 'on_time',
      resource_adequacy: 'adequate',
      activity_effectiveness: 3,
      student_engagement: 3,
      overall_rating: 3,
      objectives_achieved: true
    },
    is_system_template: true
  }
];

// Helper function to get templates by category
export const getTemplatesByCategory = (category: ReflectionTemplateCategory) => {
  return SYSTEM_REFLECTION_TEMPLATES.filter(template => template.category === category);
};

// Helper function to get template by name
export const getTemplateByName = (name: string) => {
  return SYSTEM_REFLECTION_TEMPLATES.find(template => template.name === name);
};

// Category labels in Malay
export const TEMPLATE_CATEGORY_LABELS: Record<ReflectionTemplateCategory, string> = {
  lesson_type: 'Jenis Pembelajaran',
  assessment: 'Pentaksiran',
  behavior: 'Pengurusan Tingkah Laku',
  technology: 'Teknologi',
  general: 'Umum'
};

// Category descriptions
export const TEMPLATE_CATEGORY_DESCRIPTIONS: Record<ReflectionTemplateCategory, string> = {
  lesson_type: 'Template untuk pelbagai jenis aktiviti pembelajaran',
  assessment: 'Template untuk pelbagai bentuk pentaksiran',
  behavior: 'Template untuk pengurusan tingkah laku dan motivasi',
  technology: 'Template untuk penggunaan teknologi dalam P&P',
  general: 'Template umum untuk refleksi harian'
};
