-- Migration: Create schools table for multi-tenant SaaS architecture
-- Created: 2025-07-13
-- Description: Create the main schools table to support multi-tenancy with school codes and admin management

BEGIN;

-- =====================================================
-- CREATE SCHOOLS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS schools (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- School basic information
    name TEXT NOT NULL,
    code TEXT NOT NULL,
    
    -- School admin (the user who registered the school)
    admin_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Subscription management
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'suspended', 'cancelled', 'expired')),
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional school metadata
    description TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    address TEXT,
    
    -- Settings
    settings JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT schools_code_unique UNIQUE (code),
    CONSTRAINT schools_name_not_empty CHECK (char_length(name) > 0),
    CONSTRAINT schools_code_not_empty CHECK (char_length(code) > 0),
    CONSTRAINT schools_code_format CHECK (code ~ '^[a-zA-Z0-9]+$') -- Only alphanumeric characters
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================

-- Index for fast school code lookups (most common query)
CREATE INDEX IF NOT EXISTS idx_schools_code ON schools (LOWER(code));

-- Index for admin user lookups
CREATE INDEX IF NOT EXISTS idx_schools_admin_user_id ON schools (admin_user_id);

-- Index for subscription status queries
CREATE INDEX IF NOT EXISTS idx_schools_subscription_status ON schools (subscription_status);

-- Index for subscription expiry checks
CREATE INDEX IF NOT EXISTS idx_schools_subscription_expires_at ON schools (subscription_expires_at) 
WHERE subscription_expires_at IS NOT NULL;

-- GIN index for settings JSONB column
CREATE INDEX IF NOT EXISTS idx_schools_settings ON schools USING GIN (settings);

-- =====================================================
-- CREATE TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_schools_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row changes
DROP TRIGGER IF EXISTS trigger_schools_updated_at ON schools;
CREATE TRIGGER trigger_schools_updated_at
    BEFORE UPDATE ON schools
    FOR EACH ROW
    EXECUTE FUNCTION update_schools_updated_at();

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE schools IS 'Main schools table for multi-tenant SaaS architecture';
COMMENT ON COLUMN schools.id IS 'Unique identifier for the school';
COMMENT ON COLUMN schools.name IS 'Display name of the school';
COMMENT ON COLUMN schools.code IS 'Unique school code used in subdomains (case-insensitive)';
COMMENT ON COLUMN schools.admin_user_id IS 'User ID of the school administrator who registered the school';
COMMENT ON COLUMN schools.subscription_status IS 'Current subscription status of the school';
COMMENT ON COLUMN schools.subscription_expires_at IS 'When the school subscription expires (NULL for lifetime/free)';
COMMENT ON COLUMN schools.settings IS 'School-specific configuration settings in JSON format';

-- =====================================================
-- CREATE RLS POLICIES
-- =====================================================

-- Enable RLS on schools table
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;

-- Policy: School admins can view and manage their own schools
CREATE POLICY "School admins can manage their schools" ON schools
    FOR ALL USING (admin_user_id = auth.uid());

-- Policy: School members can view their school's basic info (will be refined when school_memberships is created)
CREATE POLICY "School members can view school info" ON schools
    FOR SELECT USING (
        id IN (
            SELECT school_id FROM school_memberships 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

-- Note: The school_memberships table doesn't exist yet, so this policy will be updated later

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 'Schools table created successfully' as status;

-- Show table structure
\d schools;
