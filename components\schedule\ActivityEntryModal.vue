<template>
    <Modal :is-open="isOpen" :title="modalTitle" @update:is-open="$emit('close')" size="2xl" :z-index="60">
        <div class="flex flex-col md:flex-row gap-6">
            <!-- First Column: Activity Type Selection -->
            <div class="flex-1 space-y-6">
                <form @submit.prevent="handleSave" class="space-y-6">
                    <!-- Activity Type Selection -->
                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Jenis Aktiviti
                        </label>
                        <div class="flex flex-col gap-4">
                            <button v-for="activityType in availableActivityTypes" :key="activityType.value"
                                type="button" @click="formData.activity_type = activityType.value" :class="[
                                    'p-4 rounded-lg border-2 transition-all duration-200 text-left w-full',
                                    formData.activity_type === activityType.value
                                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                                ]">
                                <div class="flex items-center space-x-3">
                                    <Icon :name="activityType.icon"
                                        :class="['h-6 w-6', formData.activity_type === activityType.value ? 'text-blue-600' : 'text-gray-400']" />
                                    <div>
                                        <div
                                            :class="['font-medium', formData.activity_type === activityType.value ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white']">
                                            {{ activityType.label }}
                                        </div>
                                        <div class="text-xs text-gray-600 dark:text-gray-400">
                                            {{ activityType.description }}
                                        </div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Second Column: Other Contents -->
            <div class="flex-1 space-y-6">
                <form @submit.prevent="handleSave" class="space-y-6">
                    <!-- Class Selection (for CLASS type) -->
                    <div v-if="formData.activity_type === 'CLASS'" class="space-y-6">
                        <!-- Subject Name Toggle -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Paparan Nama Subjek
                                </label>
                            </div>
                            <ViewToggle v-model="showFullSubjectNames" :options="subjectNameToggleOptions"
                                @change="saveTogglePreference" />
                        </div>

                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Kelas & Subjek
                            </label>
                            <SingleSelect :key="singleSelectKey" v-model="formData.class_subject_combination"
                                :options="classSubjectOptions" placeholder="Pilih kelas dan subjek"
                                :error="errors.class_subject" />
                        </div>
                    </div>

                    <!-- Activity Details (for non-CLASS types) -->
                    <div v-else class="space-y-4">
                        <div class="space-y-2">
                            <Input v-model="formData.activity_title" :error="errors.activity_title" variant="normal"
                                label="Tajuk Aktiviti" :placeholder="formData.activity_placeholder" />
                        </div>

                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Penerangan Aktiviti
                            </label> <textarea v-model="formData.activity_description" rows="3"
                                class="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" />
                        </div>
                    </div>

                    <!-- Common Fields -->

                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Masa
                        </label>
                        <div class="flex items-center space-x-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                            <Icon name="mdi:clock" class="h-4 w-4 text-gray-500" />
                            <span class="text-sm text-gray-700 dark:text-gray-300">
                                {{ timeSlotLabel }}
                            </span>
                        </div>
                    </div>


                    <!-- Preview Card -->
                    <div v-if="formData.activity_type" class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Pratonton:</div>
                        <TimetableEntryCard :entry="previewEntry" :subject-color="getPreviewColor()"
                            :is-preview="true" />
                    </div>
                </form>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end space-x-3">
                <Button variant="outline" @click="$emit('close')" :disabled="loading">
                    Batal
                </Button>
                <Button variant="primary" @click="handleSave" :disabled="!isFormValid || loading"
                    prepend-icon="mdi:check">
                    <Icon v-if="loading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                    {{ loading ? 'Menyimpan...' : (isEditing ? 'Kemaskini' : 'Simpan') }}
                </Button>
            </div>
        </template>
    </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import Modal from '~/components/ui/composite/Modal.vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import Input from '~/components/ui/base/Input.vue'
import SingleSelect from '~/components/ui/base/SingleSelect.vue'
import ViewToggle from '~/components/ui/base/ViewToggle.vue'
import TimetableEntryCard from '~/components/schedule/TimetableEntryCard.vue'
import type {
    TimetableEntry,
    TimeSlot,
    DayOfWeek,
    ActivityType,
    ActivityTypeConfig
} from '~/types/timetable'
import { ACTIVITY_TYPES, getActivityConfig, getActivityDisplayName } from '~/types/timetable'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'
import { useSubjects } from '~/composables/useSubjects'
import { formatTimeRange } from '~/utils/timeHelpers'
import { useSupabase } from '~/composables/useSupabase'

interface Props {
    isOpen: boolean
    entry?: TimetableEntry | null
    timeSlot?: TimeSlot | null
    day?: DayOfWeek | null
    userClassSubjects?: UserClassSubjectEntry[]
}

interface Emits {
    (e: 'close'): void
    (e: 'saved', entry: TimetableEntry): void
}

const props = withDefaults(defineProps<Props>(), {
    entry: null,
    timeSlot: null,
    day: null,
    userClassSubjects: () => []
})

const emit = defineEmits<Emits>()

// Supabase setup
const { client } = useSupabase()
const user = useSupabaseUser()

// Use subjects composable for fetching subject data
const { subjects, loading: subjectsLoading, error: subjectsError, fetchSubjects } = useSubjects()

// Form state
const loading = ref(false)
const errors = ref<Record<string, string>>({})

// Subject name display toggle state
const showFullSubjectNames = ref(false)

// Reactive key to force SingleSelect re-render when toggle changes
const singleSelectKey = ref(0)

// Computed property for subject name display text
const subjectNameDisplayText = computed(() =>
    showFullSubjectNames.value
        ? 'Menunjukkan nama penuh subjek'
        : 'Menunjukkan nama singkat subjek'
)

// Load toggle preference from localStorage on component mount
const loadTogglePreference = () => {
    const saved = localStorage.getItem('showFullSubjectNames')
    if (saved !== null) {
        showFullSubjectNames.value = JSON.parse(saved)
    }
}

// Save toggle preference to localStorage
const saveTogglePreference = () => {
    localStorage.setItem('showFullSubjectNames', JSON.stringify(showFullSubjectNames.value))
}

// Toggle subject name display
const toggleSubjectNameDisplay = () => {
    showFullSubjectNames.value = !showFullSubjectNames.value
    saveTogglePreference()
}

interface FormData {
    activity_type: ActivityType
    activity_title: string
    activity_description: string
    class_subject_combination: string
    activity_placeholder?: string // Added optional property for placeholder
}

const formData = ref<FormData>({
    activity_type: 'CLASS',
    activity_title: '',
    activity_description: '',
    class_subject_combination: ''
})

// Load subjects when userClassSubjects change
watch(() => props.userClassSubjects, async (newClassSubjects) => {
    if (newClassSubjects && newClassSubjects.length > 0) {
        // Extract unique subject IDs from user's class-subjects
        const subjectIds = [...new Set(
            newClassSubjects
                .map(cs => cs.subject_id)
                .filter((id): id is string => !!id && id !== null)
        )];

        if (subjectIds.length > 0) {
            await fetchSubjects(subjectIds);
        }
    }
}, { immediate: true, deep: true });

// Computed properties
const isEditing = computed(() => !!props.entry)

const modalTitle = computed(() => {
    if (isEditing.value) {
        return 'Edit Aktiviti'
    }
    return 'Tambah Aktiviti Baharu'
})

const timeSlotLabel = computed(() => {
    if (props.timeSlot) {
        return formatTimeRange(props.timeSlot.start_time, props.timeSlot.end_time)
    }
    if (props.entry) {
        return formatTimeRange(props.entry.time_slot_start, props.entry.time_slot_end)
    }
    return 'Masa tidak diketahui'
})

const availableActivityTypes = computed(() => {
    return Object.values(ACTIVITY_TYPES)
})

const subjectNameToggleOptions = computed(() => [
    {
        value: false,
        label: 'Singkatan',
        ariaLabel: 'Nama Singkat Subjek'
    },
    {
        value: true,
        label: 'Penuh',
        ariaLabel: 'Nama Penuh Subjek'
    }
])

const classSubjectOptions = computed(() => {
    return props.userClassSubjects
        .filter(cs => {
            // Filter out entries with null or invalid subject_ids
            if (!cs.subject_id || cs.subject_id === null || cs.subject_id === 'null') {
                console.warn('Filtering out class-subject entry with invalid subject_id:', cs);
                return false;
            }
            return true;
        })
        .map(cs => {
            const subjectName = showFullSubjectNames.value
                ? getSubjectName(cs.subject_id)
                : (cs.subject_abbreviation || getSubjectName(cs.subject_id))

            return {
                value: `${cs.class_id}|${cs.subject_id}`,
                label: `${cs.className} - ${subjectName}`,
                data: cs
            }
        })
})

const isFormValid = computed(() => {
    if (formData.value.activity_type === 'CLASS') {
        return !!formData.value.class_subject_combination
    }
    return !!formData.value.activity_title.trim()
})

const previewEntry = computed((): TimetableEntry => {
    const baseEntry = {
        id: 'preview',
        day: props.day || 'ISNIN' as DayOfWeek,
        time_slot_start: props.timeSlot?.start_time || props.entry?.time_slot_start || '08:00',
        time_slot_end: props.timeSlot?.end_time || props.entry?.time_slot_end || '09:00',
        activity_type: formData.value.activity_type,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    }

    if (formData.value.activity_type === 'CLASS') {
        // Show dummy class-subject if none selected
        if (!formData.value.class_subject_combination) {
            return {
                ...baseEntry,
                class_id: 'dummy',
                subject_id: 'dummy',
                class_name: '4 Bijak',
                subject_name: showFullSubjectNames.value ? 'Bahasa Inggeris' : 'BI',
                activity_title: null,
                activity_description: null,
                is_dummy: true
            }
        }

        // Show selected class-subject
        const [classId, subjectId] = formData.value.class_subject_combination.split('|')
        const classSubject = props.userClassSubjects.find(cs =>
            cs.class_id === classId && cs.subject_id === subjectId
        )

        // Use abbreviated or full subject name based on toggle preference
        const subjectName = showFullSubjectNames.value
            ? getSubjectName(subjectId)
            : (classSubject?.subject_abbreviation || getSubjectName(subjectId))

        return {
            ...baseEntry,
            class_id: classId,
            subject_id: subjectId,
            class_name: classSubject?.className || '',
            subject_name: subjectName,
            activity_title: null,
            activity_description: null
        }
    }

    return {
        ...baseEntry,
        class_id: null,
        subject_id: null,
        class_name: null,
        subject_name: null,
        activity_title: formData.value.activity_title || getActivityConfig(formData.value.activity_type).label,
        activity_description: formData.value.activity_description || null
    }
})

// Helper functions
const getSubjectName = (subjectId: string | null): string => {
    if (!subjectId || subjectId === null || subjectId === 'null') {
        console.warn('Invalid subject ID provided:', subjectId)
        return 'Subjek Tidak Sah'
    }

    const subject = subjects.value.find(s => String(s.id) === String(subjectId))
    if (!subject) {
        console.warn('Subject not found for ID:', subjectId, 'Available subjects:', subjects.value)
        return `Subjek Tidak Dijumpai`
    }

    return subject.name
}

const getPreviewColor = () => {
    if (formData.value.activity_type === 'CLASS') {
        // Return default class color
        return {
            subject_id: 'preview',
            subject_name: 'Preview',
            color: 'blue-600',
            bg_color: 'bg-blue-100 dark:bg-blue-900/20',
            text_color: 'text-blue-800 dark:text-blue-200'
        }
    }

    const config = getActivityConfig(formData.value.activity_type)
    return {
        subject_id: 'activity',
        subject_name: config.label,
        color: config.color,
        bg_color: config.bg_color,
        text_color: config.text_color
    }
}

const resetForm = () => {
    formData.value = {
        activity_type: 'CLASS',
        activity_title: '',
        activity_description: '',
        class_subject_combination: ''
    }
    errors.value = {}
}

const populateForm = () => {
    if (!props.entry) {
        resetForm()
        return
    }

    const entry = props.entry
    formData.value.activity_type = entry.activity_type

    if (entry.activity_type === 'CLASS') {
        formData.value.class_subject_combination = `${entry.class_id}|${entry.subject_id}`
        formData.value.activity_title = ''
        formData.value.activity_description = ''
    } else {
        formData.value.activity_title = entry.activity_title || ''
        formData.value.activity_description = entry.activity_description || ''
        formData.value.class_subject_combination = ''
    }
}

const validateForm = (): boolean => {
    errors.value = {}

    if (formData.value.activity_type === 'CLASS') {
        if (!formData.value.class_subject_combination) {
            errors.value.class_subject = 'Sila pilih kelas dan subjek'
        }
    } else {
        if (!formData.value.activity_title.trim()) {
            errors.value.activity_title = 'Sila masukkan tajuk aktiviti'
        }
    }

    return Object.keys(errors.value).length === 0
}

const handleSave = async () => {
    if (!validateForm()) return

    loading.value = true

    try {
        // Prepare entry data for Supabase insert using the Database Insert type
        const entryData: {
            day: string
            time_slot_start: string
            time_slot_end: string
            activity_type: ActivityType
            user_id: string
            class_id?: string | null
            subject_id?: string | null
            class_name?: string | null
            subject_name?: string | null
            activity_title?: string | null
            activity_description?: string | null
        } = {
            day: props.day!,
            time_slot_start: props.timeSlot?.start_time || props.entry?.time_slot_start!,
            time_slot_end: props.timeSlot?.end_time || props.entry?.time_slot_end!,
            activity_type: formData.value.activity_type,
            user_id: user.value?.id!
        }

        if (formData.value.activity_type === 'CLASS') {
            const [classId, subjectId] = formData.value.class_subject_combination.split('|')
            const classSubject = props.userClassSubjects.find(cs =>
                cs.class_id === classId && cs.subject_id === subjectId
            )

            // Use abbreviated or full subject name based on toggle preference
            const subjectName = showFullSubjectNames.value
                ? getSubjectName(subjectId)
                : (classSubject?.subject_abbreviation || getSubjectName(subjectId))

            entryData.class_id = classId
            entryData.subject_id = subjectId
            entryData.class_name = classSubject?.className || ''
            entryData.subject_name = subjectName
            entryData.activity_title = null
            entryData.activity_description = null
        } else {
            entryData.class_id = null
            entryData.subject_id = null
            entryData.class_name = null
            entryData.subject_name = null
            entryData.activity_title = formData.value.activity_title
            entryData.activity_description = formData.value.activity_description || null
        }

        console.log('Saving entry to Supabase:', entryData) // Debug log

        let data, error

        if (isEditing.value && props.entry?.id) {
            // Update existing entry
            console.log('Updating existing entry with ID:', props.entry.id)
            const result = await client
                .from('timetable_entries')
                .update(entryData)
                .eq('id', props.entry.id)
                .select()
                .single()

            data = result.data
            error = result.error
        } else {
            // Create new entry
            console.log('Creating new entry')
            const result = await client
                .from('timetable_entries')
                .insert([entryData])
                .select()
                .single()

            data = result.data
            error = result.error
        }

        if (error) {
            console.error('Supabase error:', error)
            throw error
        }

        if (!data) {
            throw new Error('No data returned from Supabase')
        }

        console.log('Successfully saved to Supabase:', data) // Debug log

        // Emit the saved entry with complete data from Supabase
        emit('saved', data as TimetableEntry)

        // Close the modal after successful save
        emit('close')

        // Reset form for next use
        resetForm()
    } catch (error) {
        console.error('Error saving activity:', error)
        // You might want to show a toast notification or error message here
        // For now, we'll just log the error
    } finally {
        loading.value = false
    }
}

// Watchers
watch(() => props.isOpen, (newValue) => {
    if (newValue) {
        populateForm()
    } else {
        // Reset form when modal is closed
        nextTick(() => {
            resetForm()
        })
    }
})

watch(() => formData.value.activity_type, () => {
    // Clear errors when activity type changes
    errors.value = {}
})

watch(() => formData.value.activity_type, (newActivityType) => {
    if (newActivityType === 'ASSEMBLY') {
        formData.value.activity_title = 'Perhimpunan';
    } else if (newActivityType === 'COCURRICULAR') {
        formData.value.activity_title = 'Kokurikulum';
    } else if (newActivityType === 'BREAK') {
        formData.value.activity_title = 'Rehat';
    } else if (newActivityType === 'OTHER') {
        formData.value.activity_title = '';
        formData.value.activity_placeholder = 'Nyatakan aktiviti'; // Updated placeholder text
        formData.value.activity_description = '';
    }
});

watch(showFullSubjectNames, (newValue) => {
    if (formData.value.class_subject_combination) {
        const [classId, subjectId] = formData.value.class_subject_combination.split('|');
        const classSubject = props.userClassSubjects.find(cs =>
            cs.class_id === classId && cs.subject_id === subjectId
        );

        if (classSubject) {
            // Store current selection
            const currentSelection = formData.value.class_subject_combination;

            // Force SingleSelect re-render to update display text
            singleSelectKey.value++;

            // Restore selection after re-render
            nextTick(() => {
                formData.value.class_subject_combination = currentSelection;
            });
        }
    }
});

onMounted(() => {
    // Load toggle preference from localStorage
    loadTogglePreference()

    if (props.isOpen) {
        populateForm()
    }
})
</script>
