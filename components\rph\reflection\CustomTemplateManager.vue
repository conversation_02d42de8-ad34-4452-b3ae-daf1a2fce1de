<template>
  <div class="custom-template-manager">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Template <PERSON><PERSON><PERSON><PERSON>
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Urus template refleksi peribadi anda
        </p>
      </div>
      <UiBaseButton @click="openTemplateBuilder()" variant="primary" prepend-icon="heroicons:plus">
        Cipta Template Baharu
      </UiBaseButton>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-2">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        <span class="text-gray-600 dark:text-gray-400">Memuatkan template...</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
      class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
      <div class="flex">
        <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5" />
        <div class="text-sm text-red-800 dark:text-red-200">
          {{ error }}
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="userTemplates.length === 0" class="text-center py-12">
      <Icon name="heroicons:document-text" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        Tiada Template Peribadi
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        Anda belum mencipta sebarang template refleksi peribadi. Cipta template pertama anda sekarang!
      </p>
      <UiBaseButton @click="openTemplateBuilder()" variant="primary" prepend-icon="heroicons:plus">
        Cipta Template Pertama
      </UiBaseButton>
    </div>

    <!-- Templates Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="template in userTemplates" :key="template.id"
        class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
        <!-- Template Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
              {{ template.name }}
            </h3>
            <p v-if="template.description" class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
              {{ template.description }}
            </p>
          </div>

          <!-- Actions Dropdown -->
          <div class="relative ml-2">
            <button @click="toggleDropdown(template.id)"
              class="p-1 rounded-full text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
              <Icon name="heroicons:ellipsis-vertical" class="w-5 h-5" />
            </button>

            <!-- Dropdown Menu -->
            <div v-if="activeDropdown === template.id"
              class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10">
              <div class="py-1">
                <button @click="editTemplate(template)"
                  class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Icon name="heroicons:pencil" class="w-4 h-4 mr-2" />
                  Edit Template
                </button>
                <button @click="duplicateTemplate(template)"
                  class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Icon name="heroicons:document-duplicate" class="w-4 h-4 mr-2" />
                  Salin Template
                </button>
                <button @click="previewTemplate(template)"
                  class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <Icon name="heroicons:eye" class="w-4 h-4 mr-2" />
                  Pratonton
                </button>
                <hr class="my-1 border-gray-200 dark:border-gray-600" />
                <button @click="deleteTemplate(template)"
                  class="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                  <Icon name="heroicons:trash" class="w-4 h-4 mr-2" />
                  Padam Template
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Template Metadata -->
        <div class="space-y-3">
          <!-- Category -->
          <div class="flex items-center space-x-2">
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {{ getCategoryLabel(template.category) }}
            </span>
          </div>

          <!-- Prompts Count -->
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <Icon name="heroicons:chat-bubble-left-right" class="w-4 h-4 inline mr-1" />
            {{ Object.keys(template.prompts).length }} soalan panduan
          </div>

          <!-- Usage Stats -->
          <div v-if="template.usage_count > 0" class="text-sm text-gray-600 dark:text-gray-400">
            <Icon name="heroicons:chart-bar" class="w-4 h-4 inline mr-1" />
            Digunakan {{ template.usage_count }} kali
          </div>

          <!-- Created Date -->
          <div class="text-xs text-gray-500 dark:text-gray-500">
            Dicipta: {{ formatDate(template.created_at) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Template Builder Modal -->
    <TemplateBuilderModal :is-open="showTemplateBuilder" :template="editingTemplate"
      @update:is-open="showTemplateBuilder = $event" @template-saved="handleTemplateSaved" />

    <!-- Template Preview Modal -->
    <TemplatePreviewModal :is-open="showPreviewModal" :template="previewingTemplate"
      @update:is-open="showPreviewModal = $event" />

    <!-- Delete Confirmation Modal -->
    <DeleteConfirmationModal :is-open="showDeleteModal" :title="`Padam Template: ${deletingTemplate?.name || ''}`"
      :confirmation-message="`Adakah anda pasti ingin memadam template '${deletingTemplate?.name || ''}'?`"
      warning-message="Tindakan ini tidak boleh dibatalkan." item-type="template"
      :item-name="deletingTemplate?.name || ''" @update:is-open="showDeleteModal = $event" @confirm="confirmDelete" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useReflectionTemplates } from '~/composables/useReflectionTemplates';
import { TEMPLATE_CATEGORY_LABELS } from '~/utils/systemReflectionTemplates';
import type { ReflectionTemplate } from '~/types/reflections';
import UiBaseButton from '~/components/ui/base/Button.vue';
import TemplateBuilderModal from './TemplateBuilderModal.vue';
import TemplatePreviewModal from './TemplatePreviewModal.vue';
import DeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue';

// Composables
const {
  userTemplates,
  loading,
  error,
  fetchUserTemplates,
  createUserTemplate,
  deleteUserTemplate
} = useReflectionTemplates();

// State
const showTemplateBuilder = ref(false);
const showPreviewModal = ref(false);
const showDeleteModal = ref(false);
const editingTemplate = ref<ReflectionTemplate | null>(null);
const previewingTemplate = ref<ReflectionTemplate | null>(null);
const deletingTemplate = ref<ReflectionTemplate | null>(null);
const activeDropdown = ref<string | null>(null);

// Methods
const getCategoryLabel = (category: string): string => {
  return TEMPLATE_CATEGORY_LABELS[category as keyof typeof TEMPLATE_CATEGORY_LABELS] || category;
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('ms-MY', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const openTemplateBuilder = (template?: ReflectionTemplate) => {
  editingTemplate.value = template || null;
  showTemplateBuilder.value = true;
  activeDropdown.value = null;
};

const editTemplate = (template: ReflectionTemplate) => {
  openTemplateBuilder(template);
};

const duplicateTemplate = (template: ReflectionTemplate) => {
  const duplicatedTemplate = {
    ...template,
    id: '', // Will be generated
    name: `${template.name} (Salinan)`,
    created_at: '',
    updated_at: '',
    usage_count: 0
  };
  openTemplateBuilder(duplicatedTemplate as ReflectionTemplate);
};

const previewTemplate = (template: ReflectionTemplate) => {
  previewingTemplate.value = template;
  showPreviewModal.value = true;
  activeDropdown.value = null;
};

const deleteTemplate = (template: ReflectionTemplate) => {
  deletingTemplate.value = template;
  showDeleteModal.value = true;
  activeDropdown.value = null;
};

const confirmDelete = async () => {
  if (!deletingTemplate.value) return;

  try {
    await deleteUserTemplate(deletingTemplate.value.id);
    showDeleteModal.value = false;
    deletingTemplate.value = null;
    // Refresh templates list
    await fetchUserTemplates();
  } catch (err) {
    console.error('Error deleting template:', err);
  }
};

const handleTemplateSaved = async () => {
  showTemplateBuilder.value = false;
  editingTemplate.value = null;
  // Refresh templates list
  await fetchUserTemplates();
};

const toggleDropdown = (templateId: string) => {
  activeDropdown.value = activeDropdown.value === templateId ? null : templateId;
};

const closeDropdowns = () => {
  activeDropdown.value = null;
};

// Lifecycle
onMounted(async () => {
  await fetchUserTemplates();
  document.addEventListener('click', closeDropdowns);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdowns);
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
