<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <!-- Card Header -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex items-center space-x-3">
        <SkeletonBox height="2.5rem" width="2.5rem" class="rounded-lg" />
        <div class="space-y-2">
          <SkeletonBox height="1.25rem" width="8rem" />
          <SkeletonBox height="0.875rem" width="6rem" variant="light" />
        </div>
      </div>
      <SkeletonBox height="1.5rem" width="1.5rem" class="rounded" />
    </div>
    
    <!-- Status Badge -->
    <div class="mb-4">
      <SkeletonBox height="1.5rem" width="5rem" class="rounded-full" />
    </div>
    
    <!-- Content Lines -->
    <div class="space-y-2 mb-4">
      <SkeletonBox height="0.875rem" width="100%" variant="light" />
      <SkeletonBox height="0.875rem" width="75%" variant="light" />
    </div>
    
    <!-- Actions -->
    <div v-if="showActions" class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex space-x-2">
        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
      </div>
      <SkeletonBox height="2rem" width="2rem" class="rounded-md" />
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  showActions?: boolean
}

withDefaults(defineProps<Props>(), {
  showActions: true
})
</script>
