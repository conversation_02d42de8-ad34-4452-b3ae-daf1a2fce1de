<template>
    <div :class="['inline-flex items-center', customClass]">
        <input :id="id" type="checkbox" :name="name" :checked="modelValue" :disabled="disabled" :class="[
            'h-4 w-4 rounded bg-transparent border-2',
            'focus:ring-offset-gray-800',
            textColorClass,
            focusRingClass,
            modelValue || indeterminate ? checkedBorderColorClass : borderColorClass,
            { 'cursor-not-allowed opacity-50': disabled }
        ]" @change="handleChange" ref="inputRef" />
        <label v-if="label" :for="id" :class="['ml-2 text-sm', { 'opacity-50': disabled }]">
            {{ label }}
        </label>
    </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';

const props = withDefaults(defineProps<{
    modelValue: boolean;
    id: string;
    name?: string;
    label?: string;
    disabled?: boolean;
    indeterminate?: boolean;
    customClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
    borderColorClass?: string;
    checkedBorderColorClass?: string;
    textColorClass?: string;
    focusRingClass?: string;
}>(), {
    name: undefined,
    label: undefined,
    disabled: false,
    indeterminate: false,
    customClass: '',
    borderColorClass: 'border-gray-300 dark:border-gray-600',
    checkedBorderColorClass: 'border-primary',
    textColorClass: 'text-primary dark:text-primary',
    focusRingClass: 'focus:ring-primary dark:focus:ring-primary',
});

const emit = defineEmits(['update:modelValue']);

const inputRef = ref<HTMLInputElement | null>(null);

const handleChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    emit('update:modelValue', target.checked);
};

watchEffect(() => {
    if (inputRef.value) {
        inputRef.value.indeterminate = props.indeterminate && !props.modelValue;
    }
});
</script>

<style scoped>
/* Add any component-specific styles here if needed */
/* For instance, if you want to hide the default checkbox appearance and style a custom one */
</style>
