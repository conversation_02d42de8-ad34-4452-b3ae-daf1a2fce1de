<template>
    <!-- Page Loading State -->
    <SkeletonRphPage v-if="isPageLoading" />

    <div v-if="!isPageLoading" class="space-y-8" id="main-content-area">
        <!-- Page Header -->
        <UiCompositePageHeader title="Rancangan <PERSON> (RPH)"
            subtitle="Urus dan pantau rancangan pengajaran harian anda" icon="heroicons:document-text-solid" />

        <!-- General System Alerts -->
        <UiBaseAlert :duration="5000" :model-value="!!lessonPlansError" type="error" :message="lessonPlansError || ''"
            class="my-4" @dismiss="lessonPlansError = null" />
        <UiBaseAlert :duration="5000" :model-value="!!weekSubmissionSystemError" type="error"
            :message="weekSubmissionSystemError || ''" class="my-4" @dismiss="weekSubmissionSystemError = null" />

        <!-- Form Submission Status Alert -->
        <UiBaseAlert :duration="5000" :model-value="!!submissionStatus" :type="submissionStatus?.type || 'error'"
            :message="submissionStatus?.message || ''" class="my-4" @dismiss="submissionStatus = null" />
        <!-- Week Selector -->

        <div class="week-selector-area">
            <WeekSelector v-if="!isTimetableEmpty" ref="weekSelectorRef" @week-selected="handleWeekSelected"
                @week-deleted="handleWeekDeleted" :is-loading="isLoadingRphWeeks" />
        </div> <!-- RPH Form Section -->


        <!-- Timetable Empty Warning using WeekSelector style -->
        <div v-if="isTimetableEmpty" class="week-selector-area">
            <UiCompositeCard>
                <template #header>
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">⚠️ Lengkapkan Jadual Waktu
                            </h2>
                        </div>
                    </div>
                </template>

                <div class="space-y-4">
                    <div
                        class="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                        <div class="text-center">
                            <Icon name="mdi:calendar-alert" class="w-12 h-12 text-gray-400 mx-auto mb-3" />
                            <p class="text-gray-500 dark:text-gray-400 mb-3">Sila lengkapkan jadual waktu anda
                                terlebih dahulu
                                sebelum menambah rancangan pengajaran</p>
                            <NuxtLink to="/jadual-mengajar">
                                <UiBaseButton variant="primary" size="sm">
                                    <Icon name="mdi:calendar-plus" class="w-4 h-4 mr-2" />
                                    Pergi ke Jadual Mengajar
                                </UiBaseButton>
                            </NuxtLink>
                        </div>
                    </div>
                </div>
            </UiCompositeCard>
        </div>

        <RphForm v-if="currentSelectedWeek && !isTimetableEmpty" ref="rphFormRef" :editing-plan-id="editingPlanId"
            :current-editing-plan-data="currentEditingPlanData" :user-class-subjects="userClassSubjects"
            :all-days="allDays" :current-selected-week="currentSelectedWeek" :is-loading-parent="isLoadingLessonPlans"
            :max-file-size-mb="MAX_FILE_SIZE_MB" @submit-rph="handleSubmitRphFromForm"
            @update-rph="handleUpdateRphFromForm" @cancel-edit="cancelEditMode" @file-error="handleFileErrorFromForm"
            @form-alert="handleFormAlertFromForm" class="rph-form-section" />
        <div v-else-if="!isLoadingRphWeeks && !currentSelectedWeek && !isTimetableEmpty"
            class="text-center text-gray-500 py-8">
            <p class="text-xl">Sila pilih minggu untuk memuat naik RPH.</p>
        </div>

        <!-- Lesson Plan List Section -->

        <LessonPlanList v-if="currentSelectedWeek && !isTimetableEmpty" :lesson-plans="lessonPlans"
            :current-selected-week="currentSelectedWeek" :user-class-subjects="userClassSubjects"
            :is-loading-lesson-plans="isLoadingLessonPlans" :is-loading-rph-weeks="isLoadingRphWeeks"
            @edit-plan="loadPlanForEditing" @delete-plan="openDeleteConfirmationModalWrapper"
            @preview-office-plan="openOfficeFilePreviewModalWrapper" @preview-image-plan="openImagePreviewModalWrapper"
            @download-plan="handleDownloadPlan" @add-reflection="handleAddReflection"
            @edit-reflection="handleEditReflection" />

        <!-- Week Submission Status Section -->
        <WeekSubmissionSection v-if="currentSelectedWeek && user && !isTimetableEmpty"
            :current-selected-week="currentSelectedWeek" :current-week-submission="currentWeekSubmission"
            :loading-submission-status="loadingSubmissionStatus" :is-loading-lesson-plans="isLoadingLessonPlans"
            :lesson-plans-count="lessonPlans.length" :is-loading-rph-weeks="isLoadingRphWeeks"
            @submit-week="handleWeekSubmissionAction" />
    </div>

    <!-- Modals -->
    <UiCompositeDeleteConfirmationModal :is-open="showDeleteConfirmationModal" item-type="Rancangan Pengajaran"
        :item-name="planToDelete?.file_name || 'fail'" :item-subtitle="currentSelectedWeek?.name || ''"
        warning-message="Rancangan pengajaran ini akan dipadam secara kekal. Tindakan ini tidak boleh dibatalkan."
        @confirm="confirmDeleteLessonPlan" @cancel="showDeleteConfirmationModal = false" />
    <FilePreviewModal :is-open="showPreviewModal" :title="previewModalTitle" :preview-url="previewModalUrl"
        :preview-type="previewModalType" :is-loading="isPreviewLoading" :raw-file-url="newTabPreviewUrl"
        @close="showPreviewModal = false" />
    <ReflectionModal :is-open="showReflectionModal" :lesson-plan="selectedLessonPlanForReflection"
        :existing-reflection="existingReflection" :user-class-subjects="userClassSubjects"
        @update:is-open="showReflectionModal = $event" @close="closeReflectionModal" @saved="handleReflectionSaved" />
</template>

// Use school layout - simplified for performance
definePageMeta({
layout: 'school' as any
})

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import type { Database } from '~/types/supabase';
import type { RphWeek } from '~/types/rph';
import type { LessonPlan, ClassSubject as GlobalClassSubject, DayOption as GlobalDayOption, SubmissionStatus } from '~/types/lessonPlans';
import type { LessonPlanReflection } from '~/types/reflections';
import { useLessonPlans, type LessonPlanUserInput, type LessonPlanUpdateInput } from '~/composables/useLessonPlans';
import { useWeekSubmissions, type UserWeekSubmission } from '~/composables/useWeekSubmissions';
import { useLessonPlanReflections } from '~/composables/useLessonPlanReflections';
import { useSubjects } from '~/composables/useSubjects'; // + Import useSubjects
import { useTimetable } from '~/composables/useTimetable'; // + Import useTimetable
import { useToast } from '~/composables/useToast'
import SkeletonRphPage from '~/components/ui/skeleton/SkeletonRphPage.vue';
import WeekSelector from '~/components/rph/WeekSelector.vue';
import RphForm from '~/components/rph/RphForm.vue';
import LessonPlanList from '~/components/rph/LessonPlanList.vue'; // Import the new LessonPlanList component
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue';
import FilePreviewModal from '~/components/rph/FilePreviewModal.vue';
import ReflectionModal from '~/components/rph/ReflectionModal.vue';
import UiBaseAlert from '~/components/ui/base/Alert.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import UiCompositeCard from '~/components/ui/composite/Card.vue';
import Icon from '~/components/ui/base/Icon.vue';
import WeekSubmissionSection from '~/components/rph/WeekSubmissionSection.vue';
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'; // Import the correct type

// Supabase and User
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();

// Composable for Lesson Plans
const {
    lessonPlans,
    loading: isLoadingLessonPlans,
    error: lessonPlansError,
    fetchLessonPlans,
    addLessonPlan,
    deleteLessonPlan,
    updateLessonPlan,
    getTemporaryPublicUrl
} = useLessonPlans();

// Week Submission Composable
const {
    loadingSubmissionStatus,
    submissionError: rawWeekSubmissionError, // Rename to avoid conflict, will be processed
    getWeekSubmission,
    upsertWeekSubmission
} = useWeekSubmissions();

// Toast notifications
const { success: showSuccessToast, error: showErrorToast } = useToast()

// Processed week submission error for display
const weekSubmissionSystemError = computed(() => rawWeekSubmissionError.value?.message || null);

// Subjects Composable
const subjectsStore = useSubjects(); // + Initialize useSubjects

// Timetable Composable
const { timetableEntries, fetchTimetableEntries } = useTimetable();

// General Page State
const isPageLoading = ref(true);
const submissionStatus = ref<SubmissionStatus | null>(null);
const weekActionStatus = ref<SubmissionStatus | null>(null);
const lessonPlanActionStatus = ref<SubmissionStatus | null>(null);
const weekSubmissionActionStatus = ref<SubmissionStatus | null>(null);
const MAX_FILE_SIZE_MB = 10;

// Editing State
const editingPlanId = ref<string | null>(null);
const currentEditingPlanData = ref<LessonPlan | null>(null);
const rphFormRef = ref<InstanceType<typeof RphForm> | null>(null);

// Status localization
const statusMap: Record<string, string> = {
    Draft: 'Draf',
    Submitted: 'Dihantar',
    Approved: 'Disemak',
    Rejected: 'Ditolak',
};

const localizeStatus = (statusKey?: string) => {
    if (!statusKey) return 'N/A';
    return statusMap[statusKey] || statusKey;
};



// Check if timetable is empty
const isTimetableEmpty = computed(() => {
    return timetableEntries.value.length === 0;
});

// Modal States
const showDeleteConfirmationModal = ref(false);
const planToDelete = ref<LessonPlan | null>(null);

const showPreviewModal = ref(false);
const previewModalTitle = ref('Pratonton Fail');
const previewModalUrl = ref<string | null>(null);
const previewModalType = ref<'image' | 'office' | null>(null);
const isPreviewLoading = ref(false);
const newTabPreviewUrl = ref<string | null>(null);

// Reflection Modal States
const showReflectionModal = ref(false);
const selectedLessonPlanForReflection = ref<LessonPlan | null>(null);
const existingReflection = ref<LessonPlanReflection | null>(null);

// Reflection Composable - using new system
const { getReflectionSummary, fetchReflectionSummaries, reflectionSummaries } = useLessonPlanReflections();


// --- Week Selection ---
const currentSelectedWeek = ref<RphWeek | null>(null);
const weekSelectorRef = ref<InstanceType<typeof WeekSelector> | null>(null);

const currentWeekSubmission = ref<UserWeekSubmission | null>(null);
const isLoadingRphWeeks = ref(false); // This should be managed by WeekSelector or a dedicated composable if complex

const handleWeekSelected = async (week: RphWeek | null) => {
    currentSelectedWeek.value = week;
    clearAlertStates(); // Use helper to clear all alert states
    clearDataStates(); // Use helper to clear data states

    if (week && user.value) {
        // Fetch lesson plans and reflection summaries for the selected week
        await Promise.all([
            fetchLessonPlans(week.id),
            fetchReflectionSummaries(week.id)
        ]);
        // Fetch submission status for the selected week
        currentWeekSubmission.value = await getWeekSubmission(week.id);
    }
};

const handleWeekDeleted = (deletedWeekId: string) => {
    showSuccessToast('Minggu berjaya dipadam.')

    // If the deleted week was the one currently selected, clear the view
    if (currentSelectedWeek.value?.id === deletedWeekId) {
        currentSelectedWeek.value = null;
        clearDataStates(); // Use helper to clear data states
    }
};

// --- Class and Subject Data (passed to RphForm and LessonPlanList) ---
const userClassSubjects = ref<UserClassSubjectEntry[]>([]);

// Define the expected structure from Supabase 'profiles' table
interface ProfileData {
    class_subjects: {
        id: string | number;
        class_id: string;
        className: string;
        subject_id: number | string;
        studentCount?: number;
    }[] | null;
}

// --- Day Data (passed to RphForm) ---
const allDays = ref<GlobalDayOption[]>([
    { id: 'ahad', name: 'Ahad' },
    { id: 'isnin', name: 'Isnin' },
    { id: 'selasa', name: 'Selasa' },
    { id: 'rabu', name: 'Rabu' },
    { id: 'khamis', name: 'Khamis' },
    { id: 'jumaat', name: 'Jumaat' },
]);

// --- RPH Form Event Handlers ---
const handleFileErrorFromForm = (errorMessage: string | null) => {
    if (errorMessage) {
        submissionStatus.value = { type: 'error', message: errorMessage };
    } else {
        if (submissionStatus.value?.message === errorMessage) {
            submissionStatus.value = null;
        }
    }
};

const handleFormAlertFromForm = (alert: SubmissionStatus) => {
    submissionStatus.value = alert;
    // Remove scrollToTop - no automatic scrolling for form alerts
};

const handleSubmitRphFromForm = async (payload: { planDetails: Omit<LessonPlanUserInput, 'week_id'>, file: File }) => {
    if (!user.value || !currentSelectedWeek.value?.id) {
        showErrorToast('Sila pilih minggu dan pastikan anda log masuk.')
        return;
    }

    const fullPlanDetails: LessonPlanUserInput = {
        ...payload.planDetails,
        week_id: currentSelectedWeek.value.id,
        // user_id is handled by the composable
    };

    submissionStatus.value = null; // Clear previous status
    const newPlan = await addLessonPlan(fullPlanDetails, payload.file, timetableEntries.value); if (newPlan) {
        showSuccessToast('Rancangan pengajaran berjaya ditambah.')
        rphFormRef.value?.resetFormInternal(); // Reset form fields
        rphFormRef.value?.clearFormSubmissionState(); // Clear form submission state
    } else {
        // Error is handled by the composable and set to lessonPlansError
        // We can also set a general submissionStatus here if needed
        if (!lessonPlansError.value) { // If composable didn't set a specific error
            submissionStatus.value = { type: 'error', message: 'Gagal menambah rancangan pengajaran. Sila cuba lagi.' };
        }
        rphFormRef.value?.clearFormSubmissionState(); // Clear form submission state on error too
    }
};

const handleUpdateRphFromForm = async (payload: { planId: string, originalFilePath: string | null, planUpdateDetails: Omit<LessonPlanUpdateInput, 'week_id'>, file: File | null }) => {
    if (!currentSelectedWeek.value?.id || !user.value) {
        showErrorToast('Operasi tidak dibenarkan. Minggu tidak dipilih atau pengguna tidak log masuk.')
        return;
    } if (!editingPlanId.value) {
        submissionStatus.value = { type: 'error', message: 'Tiada rancangan pengajaran dipilih untuk dikemaskini.' };
        // No automatic scrolling needed
        return;
    }

    submissionStatus.value = null; // Clear previous status

    const fullPlanUpdateDetails: LessonPlanUpdateInput = {
        ...payload.planUpdateDetails,
        week_id: currentSelectedWeek.value.id, // Ensure week_id is part of the update if it can change
        // user_id is handled by the composable
    };

    const originalPath = payload.originalFilePath || currentEditingPlanData.value?.storage_file_path || null;

    const updatedPlan = await updateLessonPlan(
        payload.planId,
        fullPlanUpdateDetails, // 2nd: details
        payload.file,          // 3rd: new file
        originalPath           // 4th: original path
    ); if (updatedPlan) {
        submissionStatus.value = { type: 'success', message: 'Rancangan pengajaran berjaya dikemaskini.' };
        // Cancel edit mode but preserve the success alert
        editingPlanId.value = null;
        currentEditingPlanData.value = null;
        lessonPlanActionStatus.value = null;
        lessonPlansError.value = null;
        rphFormRef.value?.clearFormSubmissionState(); // Clear form submission state
    } else {
        if (!lessonPlansError.value) {
            submissionStatus.value = { type: 'error', message: 'Gagal mengemaskini rancangan pengajaran. Sila cuba lagi.' };
        }
        rphFormRef.value?.clearFormSubmissionState(); // Clear form submission state on error too
    }
};

// --- Lesson Plan List Event Handlers (delegated from LessonPlanList component) ---

const loadPlanForEditing = (plan: LessonPlan) => {
    editingPlanId.value = plan.id;
    currentEditingPlanData.value = JSON.parse(JSON.stringify(plan)); if (weekSelectorRef.value && typeof weekSelectorRef.value.selectWeekById === 'function') {
        if (currentSelectedWeek.value?.id !== plan.week_id) {
            weekSelectorRef.value.selectWeekById(plan.week_id);
        }
    }
    submissionStatus.value = null;
    lessonPlanActionStatus.value = null; // Clear lesson plan action status when editing
    // Scroll to the form where user will edit
    nextTick(() => {
        const formElement = document.querySelector('.rph-form-section') as HTMLElement;
        if (formElement) {
            formElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
};

const cancelEditMode = () => {
    editingPlanId.value = null;
    currentEditingPlanData.value = null;
    submissionStatus.value = null;
    lessonPlanActionStatus.value = null;
    lessonPlansError.value = null;
};

// --- Deletion (triggered by event from LessonPlanList) ---
const openDeleteConfirmationModalWrapper = (plan: LessonPlan) => {
    planToDelete.value = plan;
    showDeleteConfirmationModal.value = true;
};

const confirmDeleteLessonPlan = async () => {
    if (!planToDelete.value || !user.value) {
        lessonPlanActionStatus.value = { type: 'error', message: 'Operasi tidak dibenarkan.' };
        showDeleteConfirmationModal.value = false;
        return;
    }

    lessonPlanActionStatus.value = null; // Clear previous status
    const success = await deleteLessonPlan(planToDelete.value.id, planToDelete.value.storage_file_path);

    if (success) {
        lessonPlanActionStatus.value = { type: 'success', message: 'Rancangan pengajaran berjaya dipadam.' };
    } else {
        if (!lessonPlansError.value) {
            lessonPlanActionStatus.value = { type: 'error', message: 'Gagal memadam rancangan pengajaran.' };
        }
    }
    showDeleteConfirmationModal.value = false;
    planToDelete.value = null;
};

// --- Preview (triggered by events from LessonPlanList) ---
const getPublicFileUrlWithFallback = async (plan: LessonPlan): Promise<string | null> => {
    if (plan.storage_file_path) {
        const signedUrl = await getTemporaryPublicUrl(plan.storage_file_path);
        return signedUrl;
    }
    return null;
};

// File preview utilities
const setupPreviewModal = (plan: LessonPlan, type: 'image' | 'office') => {
    previewModalTitle.value = `Pratonton: ${plan.file_name}`;
    previewModalType.value = type;
    previewModalUrl.value = null;
    newTabPreviewUrl.value = null;
    isPreviewLoading.value = true;
    showPreviewModal.value = true;
};

const handlePreviewError = (message: string) => {
    lessonPlanActionStatus.value = { type: 'error', message };
    showPreviewModal.value = false;
};

const openOfficeFilePreviewModalWrapper = async (plan: LessonPlan) => {
    // Set loading state and open modal immediately
    setupPreviewModal(plan, 'office');

    const fileUrlForPreview = await getPublicFileUrlWithFallback(plan);

    if (fileUrlForPreview) {
        if (plan.file_mime_type === 'application/pdf') {
            // Use Google Docs viewer for PDF files in iframe
            previewModalUrl.value = `https://docs.google.com/gview?url=${encodeURIComponent(fileUrlForPreview)}&embedded=true`;
            // For new tab, open the PDF directly
            newTabPreviewUrl.value = fileUrlForPreview;
        } else {
            // Use Microsoft Office Online Viewer for other office documents (docx, xlsx)
            previewModalUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrlForPreview)}`;
            // For new tab, use the non-embedded viewer which has more controls
            newTabPreviewUrl.value = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrlForPreview)}`;
        }
    } else {
        handlePreviewError('Tidak dapat menjana pautan pratonton untuk fail ini.');
    }

    isPreviewLoading.value = false;
};

const openImagePreviewModalWrapper = async (plan: LessonPlan) => {
    setupPreviewModal(plan, 'image');

    const fileUrlForPreview = await getPublicFileUrlWithFallback(plan);

    if (fileUrlForPreview) {
        previewModalUrl.value = fileUrlForPreview;
        newTabPreviewUrl.value = fileUrlForPreview; // For images, both URLs are the same
    } else {
        handlePreviewError('Tidak dapat menjana pautan pratonton untuk imej ini.');
        return;
    }

    isPreviewLoading.value = false;
};

const handleDownloadPlan = async (plan: LessonPlan) => {
    lessonPlanActionStatus.value = null;
    const url = await getPublicFileUrlWithFallback(plan);

    if (url) {
        try {
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', plan.file_name || 'download');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            showErrorToast('Gagal memuat turun fail.')
        }
    } else {
        showErrorToast('Tidak dapat menjana pautan muat turun untuk fail ini.')
    }
};

// --- Reflection Handlers ---
const handleAddReflection = async (plan: LessonPlan) => {
    selectedLessonPlanForReflection.value = plan;
    existingReflection.value = null;
    showReflectionModal.value = true;
};

const handleEditReflection = async (plan: LessonPlan) => {
    selectedLessonPlanForReflection.value = plan;

    // Since reflections are now auto-calculated, we don't need to fetch existing reflections
    // The reflection modal will handle detailed reflections directly
    existingReflection.value = null;

    showReflectionModal.value = true;
};

const closeReflectionModal = () => {
    showReflectionModal.value = false;
    selectedLessonPlanForReflection.value = null;
    existingReflection.value = null;
};

const handleReflectionSaved = async () => {
    closeReflectionModal();

    // Refetch reflection summaries for the current week to update the UI
    if (currentSelectedWeek.value) {
        await fetchReflectionSummaries(currentSelectedWeek.value.id);
    }

    showSuccessToast('Refleksi berjaya disimpan.')
};

// --- Lifecycle and Watchers ---
onMounted(() => {
    // Watch for user changes (login/logout)
    watch(user, async (newUser) => {
        if (newUser) {
            try {
                isPageLoading.value = true;

                // Fetch user profile to get their class/subject assignments
                const { data: profile, error } = await supabase
                    .from('profiles')
                    .select('class_subjects') // Corrected column name
                    .eq('id', newUser.id)
                    .single();

                if (error) {
                    console.error('Error fetching profile:', error);
                    userClassSubjects.value = [];
                    return;
                }

                const classSubjects = (profile?.class_subjects as UserClassSubjectEntry[]) || [];
                userClassSubjects.value = classSubjects;

                // Extract subject IDs from the profile and fetch them using the singleton store.
                // This will fetch the base subjects on the first run and any additional
                // subjects that aren't in the store yet.
                const subjectIdsFromProfile = classSubjects
                    .map(cs => cs.subject_id)
                    .filter((id): id is string => !!id);

                await subjectsStore.fetchSubjects(subjectIdsFromProfile);

                // Fetch timetable entries for the user
                await fetchTimetableEntries();

            } catch (error) {
                console.error('Error during page initialization:', error);
            } finally {
                // Add a small delay to ensure smooth loading experience
                setTimeout(() => {
                    isPageLoading.value = false;
                }, 500);
            }
        } else {
            // Clear data on logout
            userClassSubjects.value = [];
            lessonPlans.value = [];
            currentSelectedWeek.value = null;
            isPageLoading.value = false;
        }
    }, { immediate: true });
});

const handleWeekSubmissionAction = async (action: 'submit' | 'unsubmit' | 'resubmit') => { // <-- MODIFIED HERE
    if (!currentSelectedWeek.value || !user.value) {
        // Use weekSubmissionActionStatus for week submission feedback
        showErrorToast("Tidak dapat menghantar/membatalkan penghantaran: Minggu atau pengguna tidak sah.")
        return;
    }
    // Fetch the current submission status first to pass to upsertWeekSubmission
    const currentSub = await getWeekSubmission(currentSelectedWeek.value.id);

    const newStatus = action === 'submit' || action === 'resubmit' ? 'Dihantar' : 'Draf';

    const success = await upsertWeekSubmission(
        currentSelectedWeek.value.id,
        newStatus, // Use newStatus
        currentSub // Pass the current submission state
    );
    if (success) {
        // Re-fetch submission status to get the latest
        currentWeekSubmission.value = await getWeekSubmission(currentSelectedWeek.value.id);
        showSuccessToast(`Status RPH berjaya dikemaskini kepada ${localizeStatus(newStatus)}.`)
    } else {
        // Error is handled by the composable and reflected in rawWeekSubmissionError
        // Set a general weekSubmissionActionStatus for UI feedback if not already set by composable
        if (!rawWeekSubmissionError.value?.message) {
            showErrorToast(`Gagal untuk ${action === 'submit' || action === 'resubmit' ? 'menghantar' : 'membatalkan penghantaran'} RPH untuk minggu ini.`)
        } else {
            // Use the error from the composable if available
            showErrorToast(rawWeekSubmissionError.value.message)
        }
    }
};



// State management helpers
const clearAlertStates = () => {
    submissionStatus.value = null;
    lessonPlanActionStatus.value = null;
    weekSubmissionActionStatus.value = null;
    lessonPlansError.value = null;
};

const clearDataStates = () => {
    lessonPlans.value = [];
    currentWeekSubmission.value = null;
    cancelEditMode();
};

</script>

<style scoped>
.truncate-multiline {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* Adjust number of lines */
    line-clamp: 2;
    /* Standard property */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
