<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
    <!-- Header with optional avatar -->
    <div class="flex items-start space-x-4 mb-4">
      <SkeletonBox 
        v-if="showAvatar" 
        height="3rem" 
        width="3rem" 
        class="rounded-full flex-shrink-0" 
      />
      <div class="flex-1 space-y-2">
        <SkeletonBox height="1.25rem" :width="titleWidth" />
        <SkeletonBox height="1rem" width="60%" variant="light" />
      </div>
    </div>
    
    <!-- Content lines -->
    <div class="space-y-3 mb-4">
      <SkeletonBox height="1rem" width="100%" variant="light" />
      <SkeletonBox height="1rem" width="85%" variant="light" />
      <SkeletonBox height="1rem" width="70%" variant="light" />
    </div>
    
    <!-- Stats or metrics -->
    <div class="grid grid-cols-3 gap-4 mb-4">
      <div class="text-center">
        <SkeletonBox height="1.5rem" width="100%" class="mb-1" />
        <SkeletonBox height="0.75rem" width="80%" variant="light" class="mx-auto" />
      </div>
      <div class="text-center">
        <SkeletonBox height="1.5rem" width="100%" class="mb-1" />
        <SkeletonBox height="0.75rem" width="70%" variant="light" class="mx-auto" />
      </div>
      <div class="text-center">
        <SkeletonBox height="1.5rem" width="100%" class="mb-1" />
        <SkeletonBox height="0.75rem" width="90%" variant="light" class="mx-auto" />
      </div>
    </div>
    
    <!-- Actions -->
    <div v-if="showActions" class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="flex space-x-2">
        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
      </div>
      <SkeletonBox height="2rem" width="6rem" class="rounded-md" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  height?: string
  showAvatar?: boolean
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 'auto',
  showAvatar: false,
  showActions: false
})

const titleWidth = computed(() => {
  return props.showAvatar ? '70%' : '50%'
})
</script>
