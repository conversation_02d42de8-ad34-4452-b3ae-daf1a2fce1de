<template>
  <div v-if="hasErrors" class="space-y-3">
    <!-- Summary <PERSON><PERSON><PERSON> Banner -->
    <div v-if="showSummary" class="bg-red-50 border border-red-200 rounded-lg p-4 dark:bg-red-900/20 dark:border-red-800">
      <div class="flex items-start space-x-3">
        <Icon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
        <div class="flex-1">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            {{ summaryTitle }}
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <p>{{ summaryMessage }}</p>
            <ul v-if="errorList.length > 0" class="mt-2 list-disc list-inside space-y-1">
              <li v-for="error in errorList" :key="error.field">
                <span class="font-medium">{{ getFieldLabel(error.field) }}:</span>
                {{ error.message }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Individual Field Errors -->
    <div v-if="showFieldErrors" class="space-y-2">
      <div
        v-for="error in errorList"
        :key="error.field"
        class="flex items-start space-x-2 text-sm text-red-600 dark:text-red-400"
      >
        <Icon name="heroicons:exclamation-circle" class="h-4 w-4 flex-shrink-0 mt-0.5" />
        <div>
          <span class="font-medium">{{ getFieldLabel(error.field) }}:</span>
          {{ error.message }}
        </div>
      </div>
    </div>
    
    <!-- Suggestions -->
    <div v-if="showSuggestions && suggestions.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-900/20 dark:border-blue-800">
      <div class="flex items-start space-x-3">
        <Icon name="heroicons:light-bulb" class="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <div class="flex-1">
          <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            Cadangan untuk menyelesaikan masalah:
          </h4>
          <ul class="space-y-1 text-sm text-blue-700 dark:text-blue-300">
            <li v-for="(suggestion, index) in suggestions" :key="index" class="flex items-start space-x-2">
              <span class="text-blue-500 dark:text-blue-400">•</span>
              <span>{{ suggestion }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from '~/components/ui/base/Icon.vue'

interface FieldError {
  field: string
  message: string
  code?: string
}

interface Props {
  errors: Record<string, string[]> | FieldError[] | null
  fieldLabels?: Record<string, string>
  showSummary?: boolean
  showFieldErrors?: boolean
  showSuggestions?: boolean
  summaryTitle?: string
  summaryMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  showSummary: true,
  showFieldErrors: true,
  showSuggestions: true,
  summaryTitle: 'Terdapat ralat dalam borang',
  summaryMessage: 'Sila betulkan ralat berikut sebelum meneruskan:'
})

// Convert errors to consistent format
const errorList = computed((): FieldError[] => {
  if (!props.errors) return []
  
  if (Array.isArray(props.errors)) {
    return props.errors
  }
  
  // Convert Record<string, string[]> to FieldError[]
  const errors: FieldError[] = []
  for (const [field, messages] of Object.entries(props.errors)) {
    for (const message of messages) {
      errors.push({ field, message })
    }
  }
  return errors
})

const hasErrors = computed(() => errorList.value.length > 0)

// Get field label with fallback
const getFieldLabel = (field: string): string => {
  if (props.fieldLabels && props.fieldLabels[field]) {
    return props.fieldLabels[field]
  }
  
  // Default field labels in Malay
  const defaultLabels: Record<string, string> = {
    // Common fields
    'name': 'Nama',
    'email': 'E-mel',
    'password': 'Kata laluan',
    'confirm_password': 'Pengesahan kata laluan',
    'phone': 'Nombor telefon',
    'address': 'Alamat',
    'date': 'Tarikh',
    'time': 'Masa',
    'description': 'Penerangan',
    'title': 'Tajuk',
    'content': 'Kandungan',
    'file': 'Fail',
    'image': 'Imej',
    
    // Lesson plan fields
    'lesson_plan_id': 'Rancangan Pengajaran',
    'class_subject_id': 'Kelas & Subjek',
    'overall_rating': 'Penilaian Keseluruhan',
    'objectives_achieved': 'Objektif Tercapai',
    'challenges_faced': 'Cabaran Dihadapi',
    'improvements_needed': 'Penambahbaikan Diperlukan',
    'successful_strategies': 'Strategi Berjaya',
    'action_items': 'Tindakan Susulan',
    'additional_notes': 'Catatan Tambahan',
    'activity_effectiveness': 'Keberkesanan Aktiviti',
    'student_engagement': 'Penglibatan Murid',
    'time_management': 'Pengurusan Masa',
    'resource_adequacy': 'Kecukupan Sumber',
    'jumlah_murid_mencapai_objektif': 'Jumlah Murid Mencapai Objektif',
    'tidak_terlaksana': 'Sebab Tidak Terlaksana',
    'tindakan_susulan': 'Tindakan Susulan',
    
    // Schedule fields
    'observer_name': 'Nama Pemerhati',
    'observer_position': 'Jawatan Pemerhati',
    'observation_date': 'Tarikh Pencerapan',
    'notes': 'Catatan',
    
    // User fields
    'full_name': 'Nama Penuh',
    'username': 'Nama Pengguna',
    'role': 'Peranan',
    'class_subjects': 'Kelas & Subjek',
    'time_slots': 'Slot Masa'
  }
  
  return defaultLabels[field] || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// Generate suggestions based on error types
const suggestions = computed((): string[] => {
  const suggestions: string[] = []
  const errorFields = errorList.value.map(e => e.field)
  const errorMessages = errorList.value.map(e => e.message.toLowerCase())
  
  // Required field suggestions
  if (errorMessages.some(msg => msg.includes('required') || msg.includes('diperlukan'))) {
    suggestions.push('Pastikan semua medan yang bertanda (*) diisi')
    suggestions.push('Semak semula medan yang kosong')
  }
  
  // Email validation suggestions
  if (errorFields.includes('email') || errorMessages.some(msg => msg.includes('email'))) {
    suggestions.push('Pastikan format e-mel adalah betul (contoh: <EMAIL>)')
  }
  
  // Password suggestions
  if (errorFields.includes('password') || errorFields.includes('confirm_password')) {
    suggestions.push('Kata laluan mesti mengandungi sekurang-kurangnya 8 aksara')
    suggestions.push('Pastikan kata laluan dan pengesahan kata laluan adalah sama')
  }
  
  // File upload suggestions
  if (errorFields.includes('file') || errorMessages.some(msg => msg.includes('file') || msg.includes('fail'))) {
    suggestions.push('Pastikan fail tidak melebihi had saiz yang ditetapkan')
    suggestions.push('Gunakan format fail yang disokong')
  }
  
  // Date/time suggestions
  if (errorFields.some(f => f.includes('date') || f.includes('time')) || 
      errorMessages.some(msg => msg.includes('date') || msg.includes('tarikh'))) {
    suggestions.push('Pastikan format tarikh dan masa adalah betul')
    suggestions.push('Semak tarikh tidak melebihi had yang ditetapkan')
  }
  
  // Rating/number suggestions
  if (errorFields.some(f => f.includes('rating') || f.includes('jumlah')) ||
      errorMessages.some(msg => msg.includes('number') || msg.includes('nombor'))) {
    suggestions.push('Pastikan nilai nombor berada dalam julat yang betul')
    suggestions.push('Gunakan nombor sahaja untuk medan yang berkaitan')
  }
  
  // General suggestions
  if (suggestions.length === 0) {
    suggestions.push('Semak semula semua maklumat yang dimasukkan')
    suggestions.push('Pastikan semua medan diisi dengan betul')
  }
  
  return suggestions
})
</script>
