import { mount } from "@vue/test-utils";
import But<PERSON> from "../Button.vue";
import { describe, it, expect } from "vitest";

describe("Button", () => {
  it("renders primary variant", () => {
    const wrapper = mount(But<PERSON>, { props: { variant: "primary" } });
    expect(wrapper.classes()).toContain("bg-primary");
  });

  it("renders secondary variant", () => {
    const wrapper = mount(Button, { props: { variant: "secondary" } });
    expect(wrapper.classes()).toContain("bg-secondary");
  });

  it("renders outline variant", () => {
    const wrapper = mount(Button, { props: { variant: "outline" } });
    expect(wrapper.classes()).toContain("border-primary");
    expect(wrapper.classes()).toContain("text-primary");
  });

  it("renders sm size", () => {
    const wrapper = mount(Button, { props: { size: "sm" } });
    expect(wrapper.classes()).toContain("px-3");
    expect(wrapper.classes()).toContain("py-1.5");
    expect(wrapper.classes()).toContain("text-sm");
  });

  it("renders md size by default", () => {
    const wrapper = mount(Button, {}); // No props, should default to md
    expect(wrapper.classes()).toContain("px-4");
    expect(wrapper.classes()).toContain("py-2");
    expect(wrapper.classes()).toContain("text-base");
  });

  it("renders lg size", () => {
    const wrapper = mount(Button, { props: { size: "lg" } });
    expect(wrapper.classes()).toContain("px-6");
    expect(wrapper.classes()).toContain("py-3");
    expect(wrapper.classes()).toContain("text-lg");
  });

  it("emits click event when clicked", async () => {
    const wrapper = mount(Button);
    await wrapper.trigger("click");
    expect(wrapper.emitted().click).toBeTruthy();
  });

  it("renders slot content", () => {
    const wrapper = mount(Button, {
      slots: {
        default: "Click Me",
      },
    });
    expect(wrapper.text()).toContain("Click Me");
  });

  it("applies aria-label when provided", () => {
    const label = "Test Button Label";
    const wrapper = mount(Button, { props: { ariaLabel: label } });
    expect(wrapper.attributes("aria-label")).toBe(label);
  });

  it("does not apply aria-label when not provided", () => {
    const wrapper = mount(Button);
    expect(wrapper.attributes("aria-label")).toBeUndefined();
  });
});
