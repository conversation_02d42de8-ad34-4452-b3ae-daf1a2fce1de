<template>
  <div class="relative">
    <template v-if="props.variant === 'floating'">
      <FloatingLabel :for-input="componentId" :label="placeholder" :is-floated="isFloated" :is-focused="isFocused" />
    </template>
    <template v-else>
      <label :for="componentId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {{ props.label }}
      </label>
    </template>
    <input ref="inputEl" type="date" :id="componentId" :value="props.modelValue" @input="onInput" @focus="handleFocus"
      @blur="handleBlur" @click="handleClick" :placeholder="props.variant === 'normal' ? props.placeholder : ''"
      :disabled="props.disabled" :required="props.required" :aria-label="props.placeholder"
      :aria-describedby="props.ariaDescribedby" :autocomplete="props.autocomplete" :data-has-value="!!props.modelValue"
      class="form-input block pb-2.5 pt-5 peer" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, useId, watch, onMounted, onUnmounted, nextTick } from 'vue';
import FloatingLabel from './FloatingLabel.vue';

const props = withDefaults(defineProps<{
  modelValue?: string | null;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  ariaDescribedby?: string;
  autocomplete?: string;
  variant?: "floating" | "normal";
  label?: string;
  min?: string; // Minimum date
  max?: string; // Maximum date
}>(), {
  placeholder: '',
  disabled: false,
  required: false,
  autocomplete: 'off',
  variant: 'floating',
});

const emit = defineEmits<{
  'update:modelValue': [value: string | null];
  'focus': [event: FocusEvent];
  'blur': [event: FocusEvent];
}>();

const inputEl = ref<HTMLInputElement | null>(null);
const componentId = useId();
const isFocused = ref(false);

const isFloated = computed(() => {
  return isFocused.value || !!props.modelValue;
});

const onInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value || null;
  emit('update:modelValue', value);
};

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const handleClick = (event: MouseEvent) => {
  // Ensure the date picker opens when clicking anywhere on the input
  const input = event.target as HTMLInputElement;
  if (input && !props.disabled) {
    // Focus the input first
    input.focus();
    // Try to trigger the date picker programmatically
    try {
      input.showPicker?.();
    } catch (error) {
      // Fallback: some browsers don't support showPicker()
      // The focus should be enough to make the field interactive
    }
  }
};

// Focus method for external access
const focus = () => {
  inputEl.value?.focus();
};

// Expose focus method
defineExpose({
  focus
});
</script>

<style scoped>
/* Custom styles for date input to match the design system */
.form-input[type="date"] {
  width: 100%;
  padding: 0.8rem 0.8rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  color: #111827;
  transition: all 0.2s;
  cursor: pointer;
}

/* Dark mode styles */
.dark .form-input[type="date"] {
  border-color: #4b5563;
  background-color: #1f2937;
  color: white;
}

/* Focus styles */
.form-input[type="date"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgb(var(--color-primary) / 0.2);
  border-color: rgb(var(--color-primary));
}

/* Disabled styles */
.form-input[type="date"]:disabled {
  background-color: #f9fafb;
  color: #6b7280;
}

.dark .form-input[type="date"]:disabled {
  background-color: #374151;
  color: #9ca3af;
}

/* Hide the browser's default date placeholder (dd/mm/yyyy) */
.form-input[type="date"]::-webkit-input-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}

.form-input[type="date"]::-moz-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}

.form-input[type="date"]:-moz-placeholder {
  color: transparent !important;
  opacity: 0 !important;
}

.form-input[type="date"]::placeholder {
  color: transparent !important;
  opacity: 0 !important;
}

/* Chrome/Safari specific - hide the placeholder text */
.form-input[type="date"]::-webkit-datetime-edit-text {
  color: transparent;
}

.form-input[type="date"]::-webkit-datetime-edit-month-field {
  color: transparent;
}

.form-input[type="date"]::-webkit-datetime-edit-day-field {
  color: transparent;
}

.form-input[type="date"]::-webkit-datetime-edit-year-field {
  color: transparent;
}

/* Show the actual date when there's a value */
.form-input[type="date"][data-has-value="true"]::-webkit-datetime-edit-text,
.form-input[type="date"][data-has-value="true"]::-webkit-datetime-edit-month-field,
.form-input[type="date"][data-has-value="true"]::-webkit-datetime-edit-day-field,
.form-input[type="date"][data-has-value="true"]::-webkit-datetime-edit-year-field {
  color: inherit;
}

/* Show when focused */
.form-input[type="date"]:focus::-webkit-datetime-edit-text,
.form-input[type="date"]:focus::-webkit-datetime-edit-month-field,
.form-input[type="date"]:focus::-webkit-datetime-edit-day-field,
.form-input[type="date"]:focus::-webkit-datetime-edit-year-field {
  color: inherit;
}

/* Hide text when no value is set (for browsers that show dd/mm/yyyy as text) */
.form-input[type="date"]:not([data-has-value="true"]):not(:focus) {
  color: transparent;
}

/* Show text when focused or when there's a value */
.form-input[type="date"]:focus,
.form-input[type="date"][data-has-value="true"] {
  color: inherit;
}

/* Additional approach for stubborn browsers */
.form-input[type="date"]:not(:focus):invalid {
  color: transparent;
}

.form-input[type="date"]:focus:invalid {
  color: inherit;
}

/* Hide the default date picker icon in WebKit browsers */
.form-input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.form-input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Style for Firefox */
.form-input[type="date"]::-moz-focus-inner {
  border: 0;
}
</style>
