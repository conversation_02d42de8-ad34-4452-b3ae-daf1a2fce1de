<template>
  <div class="space-y-8">
    <!-- <PERSON>er -->
    <SkeletonPageHeader :title-width="'20rem'" :subtitle-width="'30rem'" :action-count="1" />

    <!-- Quick Stats -->
    <SkeletonStats :items="4" />

    <!-- Detailed Analysis Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Senarai RPH & Refleksi -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex justify-between items-center mb-6">
          <SkeletonBox height="1.25rem" width="15rem" />
          <SkeletonBox height="2rem" width="8rem" class="rounded-md" />
        </div>

        <!-- Reflections List -->
        <div class="space-y-4 max-h-96 overflow-y-auto">
          <div v-for="item in 4" :key="`reflection-${item}`"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="flex justify-between items-start gap-4 mb-3">
              <div class="flex-1 min-w-0 space-y-2">
                <div class="flex items-center gap-2">
                  <SkeletonBox height="1rem" width="8rem" />
                  <SkeletonBox height="1.25rem" width="4rem" class="rounded-full" />
                </div>
                <SkeletonBox height="0.875rem" width="12rem" variant="light" />
                <SkeletonBox height="0.875rem" width="10rem" variant="light" />
                <SkeletonBox height="0.875rem" width="15rem" variant="light" />
              </div>
              <div class="flex items-center gap-1">
                <SkeletonBox v-for="star in 5" :key="`star-${star}`" height="1rem" width="1rem" class="rounded" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analisis Prestasi -->
      <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <SkeletonBox height="1.25rem" width="12rem" class="mb-6" />

        <!-- Rating Distribution -->
        <div class="mb-6">
          <SkeletonBox height="1rem" width="10rem" class="mb-3" variant="light" />
          <div class="space-y-2">
            <div v-for="rating in 5" :key="`rating-${rating}`" class="flex items-center gap-3">
              <SkeletonBox height="0.75rem" width="4rem" />
              <SkeletonBox height="0.5rem" width="100%" class="rounded-full" />
              <SkeletonBox height="0.75rem" width="2rem" />
            </div>
          </div>
        </div>

        <!-- Quick Insights -->
        <div>
          <SkeletonBox height="1rem" width="8rem" class="mb-3" variant="light" />
          <div class="space-y-2">
            <div v-for="insight in 3" :key="`insight-${insight}`"
              class="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <SkeletonBox height="1.25rem" width="1.25rem" class="rounded mt-0.5" />
              <div class="flex-1 space-y-1">
                <SkeletonBox height="0.875rem" width="100%" variant="light" />
                <SkeletonBox height="0.875rem" width="80%" variant="light" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonStats from './SkeletonStats.vue'
import SkeletonTable from './SkeletonTable.vue'
import SkeletonCard from './SkeletonCard.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
</script>
