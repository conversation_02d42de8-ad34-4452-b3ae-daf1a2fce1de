// Multi-tenant SaaS types for schools, memberships, and coupons
// Created: 2025-07-13

import { z } from 'zod'

// =====================================================
// SCHOOL TYPES
// =====================================================

export interface School {
  id: string
  created_at: string
  updated_at: string
  name: string
  code: string
  admin_user_id: string | null
  subscription_status: 'active' | 'suspended' | 'cancelled' | 'expired'
  subscription_expires_at: string | null
  description: string | null
  contact_email: string | null
  contact_phone: string | null
  address: string | null
  settings: Record<string, any>
}

export interface SchoolInsert {
  name: string
  code: string
  admin_user_id?: string | null
  subscription_status?: 'active' | 'suspended' | 'cancelled' | 'expired'
  subscription_expires_at?: string | null
  description?: string | null
  contact_email?: string | null
  contact_phone?: string | null
  address?: string | null
  settings?: Record<string, any>
}

export interface SchoolUpdate {
  name?: string
  code?: string
  admin_user_id?: string | null
  subscription_status?: 'active' | 'suspended' | 'cancelled' | 'expired'
  subscription_expires_at?: string | null
  description?: string | null
  contact_email?: string | null
  contact_phone?: string | null
  address?: string | null
  settings?: Record<string, any>
}

// =====================================================
// SCHOOL MEMBERSHIP TYPES
// =====================================================

export interface SchoolMembership {
  id: string
  created_at: string
  updated_at: string
  user_id: string
  school_id: string
  role: 'admin' | 'supervisor' | 'teacher'
  status: 'active' | 'inactive' | 'suspended' | 'pending'
  joined_at: string
  invited_by: string | null
  invitation_token: string | null
  invitation_expires_at: string | null
  notes: string | null
  permissions: Record<string, any>
}

export interface SchoolMembershipInsert {
  user_id: string
  school_id: string
  role: 'admin' | 'supervisor' | 'teacher'
  status?: 'active' | 'inactive' | 'suspended' | 'pending'
  invited_by?: string | null
  invitation_token?: string | null
  invitation_expires_at?: string | null
  notes?: string | null
  permissions?: Record<string, any>
}

export interface SchoolMembershipUpdate {
  role?: 'admin' | 'supervisor' | 'teacher'
  status?: 'active' | 'inactive' | 'suspended' | 'pending'
  notes?: string | null
  permissions?: Record<string, any>
}

// =====================================================
// COUPON TYPES
// =====================================================

export interface Coupon {
  id: string
  created_at: string
  updated_at: string
  code: string
  name: string | null
  description: string | null
  is_active: boolean
  usage_limit: number | null
  used_count: number
  expires_at: string | null
  starts_at: string
  discount_type: 'free_registration' | 'percentage' | 'fixed_amount'
  discount_value: number
  created_by: string | null
  deactivated_by: string | null
  deactivated_at: string | null
  deactivation_reason: string | null
  notes: string | null
  metadata: Record<string, any>
}

export interface CouponInsert {
  code: string
  name?: string | null
  description?: string | null
  is_active?: boolean
  usage_limit?: number | null
  expires_at?: string | null
  starts_at?: string
  discount_type?: 'free_registration' | 'percentage' | 'fixed_amount'
  discount_value?: number
  created_by?: string | null
  notes?: string | null
  metadata?: Record<string, any>
}

export interface CouponUpdate {
  name?: string | null
  description?: string | null
  is_active?: boolean
  usage_limit?: number | null
  expires_at?: string | null
  discount_value?: number
  deactivated_by?: string | null
  deactivated_at?: string | null
  deactivation_reason?: string | null
  notes?: string | null
  metadata?: Record<string, any>
}

// =====================================================
// COUPON USAGE TYPES
// =====================================================

export interface CouponUsage {
  id: string
  created_at: string
  coupon_id: string
  school_id: string
  used_at: string
  used_by: string
  usage_type: 'school_registration' | 'subscription_renewal' | 'upgrade'
  original_discount_type: string
  original_discount_value: number
  applied_discount_amount: number
  user_agent: string | null
  ip_address: string | null
  metadata: Record<string, any>
}

export interface CouponUsageInsert {
  coupon_id: string
  school_id: string
  used_by: string
  usage_type?: 'school_registration' | 'subscription_renewal' | 'upgrade'
  original_discount_type: string
  original_discount_value: number
  applied_discount_amount: number
  user_agent?: string | null
  ip_address?: string | null
  metadata?: Record<string, any>
}

// =====================================================
// VALIDATION SCHEMAS
// =====================================================

export const SchoolCodeSchema = z
  .string()
  .min(3, 'School code must be at least 3 characters')
  .max(20, 'School code must be at most 20 characters')
  .regex(/^[a-zA-Z0-9]+$/, 'School code can only contain letters and numbers')

export const SchoolNameSchema = z
  .string()
  .min(3, 'School name must be at least 3 characters')
  .max(100, 'School name must be at most 100 characters')

export const SchoolRegistrationSchema = z.object({
  name: SchoolNameSchema,
  code: SchoolCodeSchema,
  description: z.string().optional(),
  contact_email: z.string().email().optional(),
  contact_phone: z.string().optional(),
  address: z.string().optional(),
})

export const CouponCodeSchema = z
  .string()
  .min(3, 'Coupon code must be at least 3 characters')
  .max(20, 'Coupon code must be at most 20 characters')
  .regex(/^[A-Z0-9]+$/, 'Coupon code can only contain uppercase letters and numbers')

export const CouponValidationSchema = z.object({
  code: CouponCodeSchema,
})

// =====================================================
// UTILITY TYPES
// =====================================================

export interface SchoolContext {
  school: School | null
  membership: SchoolMembership | null
  isLoading: boolean
  error: string | null
}

export interface UserSchoolAccess {
  schools: School[]
  memberships: SchoolMembership[]
  currentSchool: School | null
  currentMembership: SchoolMembership | null
}

export interface CouponValidationResult {
  isValid: boolean
  coupon: Coupon | null
  error: string | null
  canUse: boolean
  usageRemaining: number | null
}

// =====================================================
// FORM TYPES
// =====================================================

export type SchoolRegistrationForm = z.infer<typeof SchoolRegistrationSchema>
export type CouponValidationForm = z.infer<typeof CouponValidationSchema>

// =====================================================
// API RESPONSE TYPES
// =====================================================

export interface SchoolRegistrationResponse {
  success: boolean
  school?: School
  membership?: SchoolMembership
  error?: string
  schoolUrl?: string
}

export interface CouponValidationResponse {
  success: boolean
  isValid: boolean
  coupon?: Coupon
  error?: string
  message?: string
}

// =====================================================
// SUBDOMAIN TYPES
// =====================================================

export interface SubdomainInfo {
  isSchoolSubdomain: boolean
  schoolCode: string | null
  isMainDomain: boolean
  subdomain: string | null
}

export interface RouteContext {
  isLandingPage: boolean
  isAdminDashboard: boolean
  isSchoolApp: boolean
  isSchoolAdmin?: boolean
  schoolCode: string | null
  requiredAuth: boolean
  requiredRole?: 'admin' | 'supervisor' | 'teacher' | 'school_admin'
}
