<template>
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
        School Administration
      </h1>
      <p class="mt-2 text-gray-600 dark:text-gray-300">
        Manage school-specific settings and configurations for {{ schoolInfo.name }}
      </p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Teachers -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UiBaseIcon name="heroicons:users" class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Teachers</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.totalTeachers }}</p>
          </div>
        </div>
      </div>

      <!-- Active Lesson Plans -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UiBaseIcon name="heroicons:document-text" class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Lesson Plans</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.lessonPlans }}</p>
          </div>
        </div>
      </div>

      <!-- Classes -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UiBaseIcon name="heroicons:building-office-2" class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Classes</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.classes }}</p>
          </div>
        </div>
      </div>

      <!-- Subjects -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UiBaseIcon name="heroicons:book-open" class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Subjects</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.subjects }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- School Settings -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">School Settings</h2>
        </div>
        <div class="p-6 space-y-6">
          <!-- Academic Year -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Academic Year
            </label>
            <select 
              v-model="settings.academicYear"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="2024">2024</option>
              <option value="2025">2025</option>
            </select>
          </div>

          <!-- School Hours -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                School Start Time
              </label>
              <input 
                v-model="settings.startTime"
                type="time"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                School End Time
              </label>
              <input 
                v-model="settings.endTime"
                type="time"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <!-- Notification Settings -->
          <div class="space-y-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Notifications</h3>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="settings.emailNotifications"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Email notifications</span>
              </label>
              <label class="flex items-center">
                <input 
                  v-model="settings.weeklyReports"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Weekly reports</span>
              </label>
            </div>
          </div>

          <button 
            @click="saveSettings"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium"
          >
            Save Settings
          </button>
        </div>
      </div>

      <!-- Teacher Management -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Teacher Management</h2>
        </div>
        <div class="p-6">
          <!-- Invite Teacher -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Invite New Teacher</h3>
            <div class="space-y-3">
              <input 
                v-model="inviteForm.email"
                type="email"
                placeholder="<EMAIL>"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <button 
                @click="inviteTeacher"
                :disabled="!inviteForm.email"
                class="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium"
              >
                Send Invitation
              </button>
            </div>
          </div>

          <!-- Recent Teachers -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Recent Teachers</h3>
            <div class="space-y-3">
              <div v-for="teacher in recentTeachers" :key="teacher.id" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <p class="font-medium text-gray-900 dark:text-white">{{ teacher.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{{ teacher.email }}</p>
                </div>
                <span :class="teacher.status === 'active' ? 'text-green-600' : 'text-yellow-600'" class="text-sm font-medium">
                  {{ teacher.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Quick Actions</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <UiBaseIcon name="heroicons:document-plus" class="h-6 w-6 text-blue-600 mr-2" />
            <span class="text-gray-700 dark:text-gray-300">Create Template</span>
          </button>
          
          <button class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <UiBaseIcon name="heroicons:chart-bar" class="h-6 w-6 text-green-600 mr-2" />
            <span class="text-gray-700 dark:text-gray-300">View Reports</span>
          </button>
          
          <button class="flex items-center justify-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <UiBaseIcon name="heroicons:cog-6-tooth" class="h-6 w-6 text-purple-600 mr-2" />
            <span class="text-gray-700 dark:text-gray-300">System Settings</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Get school code from route
const route = useRoute()
const schoolCode = route.params.school as string

// Mock data (replace with real API calls)
const schoolInfo = ref({
  name: 'Demo High School',
  code: schoolCode
})

const stats = ref({
  totalTeachers: 12,
  lessonPlans: 145,
  classes: 8,
  subjects: 15
})

const settings = ref({
  academicYear: '2024',
  startTime: '08:00',
  endTime: '15:30',
  emailNotifications: true,
  weeklyReports: false
})

const inviteForm = ref({
  email: ''
})

const recentTeachers = ref([
  { id: 1, name: 'Sarah Johnson', email: '<EMAIL>', status: 'active' },
  { id: 2, name: 'Mike Chen', email: '<EMAIL>', status: 'active' },
  { id: 3, name: 'Lisa Wong', email: '<EMAIL>', status: 'pending' }
])

// Methods
const saveSettings = () => {
  // TODO: Implement save settings API call
  console.log('Saving settings:', settings.value)
  // Show success message
}

const inviteTeacher = () => {
  if (!inviteForm.value.email) return
  
  // TODO: Implement invite teacher API call
  console.log('Inviting teacher:', inviteForm.value.email)
  
  // Reset form
  inviteForm.value.email = ''
  
  // Show success message
}

// Set page head
useHead({
  title: `${schoolInfo.value.name} - Admin - RPHMate`,
  meta: [
    {
      name: 'description',
      content: `School administration page for ${schoolInfo.value.name} on RPHMate.`
    }
  ]
})
</script>

<style scoped>
/* Custom styles for school admin page */
</style>
