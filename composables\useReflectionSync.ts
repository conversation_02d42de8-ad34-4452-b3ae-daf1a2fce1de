import { ref, computed } from 'vue';
import { useSupabaseClient, useSupabaseUser } from '#imports';
import { useReflectionPeriods } from './useReflectionPeriods';
import { useReflectionScheduleOptions } from './useReflectionScheduleOptions';
import type { PeriodReflectionData, LessonPlanDetailedReflectionSingle } from './useReflectionPeriods';

// State
const loading = ref(false);
const error = ref<string | null>(null);

export const useReflectionSync = () => {
  const supabase = useSupabaseClient();
  const user = useSupabaseUser();
  const { 
    createPeriodKey, 
    createDefaultReflectionData, 
    fetchDetailedReflections,
    clearReflectionCache 
  } = useReflectionPeriods();
  const { getValidCombinations, getStudentCountForClassSubject } = useReflectionScheduleOptions();

  // Create initial reflections for a lesson plan (single row with all periods)
  const createDetailedReflection = async (
    lessonPlanId: string,
    lessonPlan?: any
  ): Promise<LessonPlanDetailedReflectionSingle | null> => {
    if (!user.value) return null;

    try {
      loading.value = true;
      error.value = null;

      // Check if reflection already exists
      const existingReflection = await fetchDetailedReflections(lessonPlanId);
      if (existingReflection) {
        return existingReflection;
      }

      // Get valid class-subject and day combinations from the lesson plan's timetable
      const validCombinations = await getValidCombinations(lessonPlanId);

      if (validCombinations.length === 0) {
        console.warn(`No valid timetable combinations found for lesson plan ${lessonPlanId}`);
      }

      // Create reflections object with default data for each valid combination
      const reflections: Record<string, PeriodReflectionData> = {};

      for (const combination of validCombinations) {
        const periodKey = createPeriodKey(combination.classSubjectId, combination.day);
        
        // Get student count for this class-subject combination
        const studentCount = await getStudentCountForClassSubject(combination.classSubjectId);
        
        // Create default reflection data
        reflections[periodKey] = createDefaultReflectionData(studentCount);
      }

      // If no valid combinations found, still create the record but with empty reflections
      if (Object.keys(reflections).length === 0) {
        console.warn(`No matching timetable entries found for lesson plan ${lessonPlanId}`);
      }

      const { data, error: createError } = await (supabase as any)
        .from('lesson_plan_detailed_reflections')
        .insert({
          lesson_plan_id: lessonPlanId,
          user_id: user.value.id,
          reflections: reflections
        })
        .select()
        .single();

      if (createError) throw createError;

      if (data) {
        const reflection = data as any as LessonPlanDetailedReflectionSingle;
        // Clear cache to force refresh
        clearReflectionCache(lessonPlanId);
        return reflection;
      }

      return null;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create detailed reflection';
      console.error('Error creating detailed reflection:', err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Sync reflections with lesson plan changes (auto-sync)
  const syncReflectionsWithLessonPlan = async (
    lessonPlanId: string,
    forceSync: boolean = false
  ): Promise<LessonPlanDetailedReflectionSingle | null> => {
    if (!user.value) return null;

    try {
      loading.value = true;
      error.value = null;

      // Get current reflections
      const currentReflections = await fetchDetailedReflections(lessonPlanId);
      
      // Get current valid combinations from timetable
      const validCombinations = await getValidCombinations(lessonPlanId);
      const currentValidKeys = new Set(
        validCombinations.map(combo => createPeriodKey(combo.classSubjectId, combo.day))
      );

      // If no reflections exist, create them
      if (!currentReflections) {
        return await createDetailedReflection(lessonPlanId);
      }

      // Check if sync is needed
      const existingKeys = new Set(Object.keys(currentReflections.reflections));
      const needsSync = forceSync || 
        existingKeys.size !== currentValidKeys.size ||
        ![...existingKeys].every(key => currentValidKeys.has(key));

      if (!needsSync) {
        return currentReflections; // No sync needed
      }

      // Perform sync
      const updatedReflections: Record<string, PeriodReflectionData> = {};

      // Keep existing reflections for valid combinations
      for (const validKey of currentValidKeys) {
        if (currentReflections.reflections[validKey]) {
          updatedReflections[validKey] = currentReflections.reflections[validKey];
        } else {
          // Create new reflection for new combination
          const combination = validCombinations.find(combo => 
            createPeriodKey(combo.classSubjectId, combo.day) === validKey
          );
          if (combination) {
            const studentCount = await getStudentCountForClassSubject(combination.classSubjectId);
            updatedReflections[validKey] = createDefaultReflectionData(studentCount);
          }
        }
      }

      // Update the database
      const { data, error: updateError } = await (supabase as any)
        .from('lesson_plan_detailed_reflections')
        .update({ reflections: updatedReflections })
        .eq('lesson_plan_id', lessonPlanId)
        .eq('user_id', user.value.id)
        .select()
        .single();

      if (updateError) throw updateError;

      if (data) {
        const syncedReflection = data as any as LessonPlanDetailedReflectionSingle;
        // Clear cache to force refresh
        clearReflectionCache(lessonPlanId);
        return syncedReflection;
      }

      return null;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to sync reflections';
      console.error('Error syncing reflections:', err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Delete reflections for a lesson plan
  const deleteReflectionsForLessonPlan = async (lessonPlanId: string): Promise<boolean> => {
    if (!user.value) return false;

    try {
      loading.value = true;
      error.value = null;

      const { error: deleteError } = await supabase
        .from('lesson_plan_detailed_reflections')
        .delete()
        .eq('lesson_plan_id', lessonPlanId)
        .eq('user_id', user.value.id);

      if (deleteError) throw deleteError;

      // Clear cache
      clearReflectionCache(lessonPlanId);
      return true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete reflections';
      console.error('Error deleting reflections:', err);
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Bulk update reflections (for bulk operations)
  const bulkUpdateReflections = async (
    lessonPlanId: string,
    updates: Record<string, Partial<PeriodReflectionData>>
  ): Promise<LessonPlanDetailedReflectionSingle | null> => {
    if (!user.value) return null;

    try {
      loading.value = true;
      error.value = null;

      // Get current reflections
      const currentReflections = await fetchDetailedReflections(lessonPlanId);
      if (!currentReflections) return null;

      // Apply updates
      const updatedReflections = { ...currentReflections.reflections };
      
      Object.entries(updates).forEach(([periodKey, updateData]) => {
        if (updatedReflections[periodKey]) {
          updatedReflections[periodKey] = {
            ...updatedReflections[periodKey],
            ...updateData
          };
        }
      });

      // Update the database
      const { data, error: updateError } = await (supabase as any)
        .from('lesson_plan_detailed_reflections')
        .update({ reflections: updatedReflections })
        .eq('lesson_plan_id', lessonPlanId)
        .eq('user_id', user.value.id)
        .select()
        .single();

      if (updateError) throw updateError;

      if (data) {
        const updatedReflection = data as any as LessonPlanDetailedReflectionSingle;
        // Clear cache to force refresh
        clearReflectionCache(lessonPlanId);
        return updatedReflection;
      }

      return null;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to bulk update reflections';
      console.error('Error bulk updating reflections:', err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Methods
    createDetailedReflection,
    syncReflectionsWithLessonPlan,
    deleteReflectionsForLessonPlan,
    bulkUpdateReflections
  };
};
