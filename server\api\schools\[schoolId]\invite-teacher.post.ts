// Teacher invitation API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'
import { randomBytes } from 'crypto'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')
    const schoolId = getRouterParam(event, 'schoolId')

    // Get request body
    const body = await readBody(event)
    const { email, role = 'teacher', notes } = body

    // Validate input
    if (!email || !schoolId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email and school ID are required'
      })
    }

    if (!['teacher', 'supervisor'].includes(role)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid role. Must be teacher or supervisor'
      })
    }

    // Initialize Supabase client with service role for admin operations
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Check if the requesting user has admin/supervisor access to this school
    const { data: requesterMembership, error: requesterError } = await supabase
      .from('school_memberships')
      .select('role')
      .eq('user_id', user.id)
      .eq('school_id', schoolId)
      .eq('status', 'active')
      .single()

    // Also check if user is the school admin
    const { data: school, error: schoolError } = await supabase
      .from('schools')
      .select('admin_user_id')
      .eq('id', schoolId)
      .single()

    if (schoolError || !school) {
      throw createError({
        statusCode: 404,
        statusMessage: 'School not found'
      })
    }

    const isSchoolAdmin = school.admin_user_id === user.id
    const hasPermission = isSchoolAdmin || 
      (requesterMembership && ['admin', 'supervisor'].includes(requesterMembership.role))

    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to invite teachers'
      })
    }

    // Check if user with this email already exists
    let invitedUserId = null
    const { data: existingUser, error: userError } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 1
    })
    
    // For now, we'll skip the existing user check and always create pending invitations
    // This can be enhanced later with proper user lookup
    // if (existingUser.user) {
    //   invitedUserId = existingUser.user.id

      // TODO: Check if user already has membership in this school
      // This will be implemented when we have proper user lookup
    // }

    // Generate invitation token
    const invitationToken = randomBytes(32).toString('hex')
    const invitationExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    // Create membership with pending status
    const { data: newMembership, error: membershipError } = await supabase
      .from('school_memberships')
      .insert({
        user_id: invitedUserId, // Will be null if user doesn't exist yet
        school_id: schoolId,
        role,
        status: 'pending',
        invited_by: user.id,
        invitation_token: invitationToken,
        invitation_expires_at: invitationExpiresAt.toISOString(),
        notes
      })
      .select()
      .single()

    if (membershipError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to create invitation: ${membershipError.message}`
      })
    }

    // TODO: Send invitation email
    // This would typically involve sending an email with the invitation link
    // For now, we'll just return the invitation token

    // Get school code for invitation URL
    const { data: schoolData, error: schoolDataError } = await supabase
      .from('schools')
      .select('code')
      .eq('id', schoolId)
      .single()

    const config = useRuntimeConfig()
    const schoolCode = schoolData?.code || 'unknown'
    const invitationUrl = `https://${schoolCode}.${config.public.baseDomain}/auth/accept-invitation?token=${invitationToken}`

    return {
      success: true,
      membership: newMembership,
      invitationUrl,
      message: invitedUserId 
        ? 'Invitation sent to existing user' 
        : 'Invitation created for new user'
    }

  } catch (error: any) {
    console.error('Teacher invitation error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during teacher invitation'
    })
  }
})
