// School-specific authentication middleware
// Created: 2025-07-13

import { navigateTo, defineNuxtRouteMiddleware } from "#app"

export default defineNuxtRouteMiddleware(async (to, from) => {
  // Enable school auth middleware for Phase 2 implementation
  // Updated for new RPHMate SaaS architecture

  // This middleware is specifically for school routes
  // It should only run on school subdomain routes

  const schoolCode = to.params.school as string

  if (!schoolCode) {
    // If no school code in params, this middleware shouldn't run
    return
  }

  const supabase = useSupabaseClient()

  try {
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      // Redirect to school-specific login
      return navigateTo(`/${schoolCode}/auth/login`)
    }

    // Check if user has access to this specific school
    try {
      const response = await $fetch('/api/schools/validate-access', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: { schoolCode }
      }) as any

      if (!response.hasAccess) {
        // User doesn't have access to this school
        return navigateTo('/admin/dashboard')
      }
    } catch (accessError) {
      console.error('Error validating school access:', accessError)
      return navigateTo(`/${schoolCode}/auth/login`)
    }

  } catch (error) {
    console.error('Error in school auth middleware:', error)
    return navigateTo(`/${schoolCode}/auth/login`)
  }
})

// Helper functions are no longer needed as we're using the API endpoints
