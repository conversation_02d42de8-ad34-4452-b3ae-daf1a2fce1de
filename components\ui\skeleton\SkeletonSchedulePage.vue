<template>
  <div class="space-y-8">
    <!-- <PERSON>er -->
    <SkeletonPageHeader :title-width="'18rem'" :subtitle-width="'50rem'" :show-actions="false" />

    <!-- Summary Stats Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div v-for="stat in 4" :key="`stat-${stat}`" class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <div class="flex items-center">
            <SkeletonBox height="2rem" width="2rem" class="rounded mr-3" />
            <div class="space-y-2">
              <SkeletonBox height="0.875rem" width="8rem" variant="light" />
              <SkeletonBox height="1.125rem" width="4rem" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Timetable -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- Timetable Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <SkeletonBox height="1.5rem" width="12rem" />
          <div class="flex items-center space-x-3">
            <SkeletonBox height="2rem" width="6rem" class="rounded-md" />
            <SkeletonBox height="2rem" width="8rem" class="rounded-md" />
          </div>
        </div>
      </div>

      <!-- Timetable Content -->
      <SkeletonTable :rows="8" :columns="7" :show-header="true" />
    </div>

    <!-- Schedule List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <SkeletonBox height="1.5rem" width="15rem" />
      </div>
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div v-for="item in 6" :key="`schedule-${item}`" class="p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <SkeletonBox height="2.5rem" width="2.5rem" class="rounded-lg" />
              <div class="space-y-2">
                <SkeletonBox height="1.125rem" width="12rem" />
                <SkeletonBox height="0.875rem" width="8rem" variant="light" />
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <SkeletonBox height="1.5rem" width="4rem" class="rounded-full" />
              <SkeletonBox height="2rem" width="2rem" class="rounded-md" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'
import SkeletonPageHeader from './SkeletonPageHeader.vue'
import SkeletonTable from './SkeletonTable.vue'
</script>
