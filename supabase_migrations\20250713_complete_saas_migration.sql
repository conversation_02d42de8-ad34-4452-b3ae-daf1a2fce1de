-- Complete SaaS Multi-tenant Migration Script
-- Created: 2025-07-13
-- Description: Complete migration to transform single-tenant app to multi-tenant SaaS
-- 
-- This script includes:
-- 1. Schools table creation
-- 2. School memberships table creation  
-- 3. Coupons and coupon usage tables
-- 4. Adding school_id to existing tables
-- 5. RLS policies implementation
-- 6. Rollback procedures

-- =====================================================
-- MIGRATION METADATA
-- =====================================================

-- Create migration tracking table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    migration_name TEXT NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT CHECK (status IN ('started', 'completed', 'failed', 'rolled_back')),
    notes TEXT
);

-- Log migration start
INSERT INTO migration_log (migration_name, status, notes) 
VALUES ('20250713_complete_saas_migration', 'started', 'Starting complete SaaS transformation');

BEGIN;

-- =====================================================
-- STEP 1: CREATE SCHOOLS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS schools (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- School basic information
    name TEXT NOT NULL,
    code TEXT NOT NULL,
    
    -- School admin (the user who registered the school)
    admin_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Subscription management
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'suspended', 'cancelled', 'expired')),
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional school metadata
    description TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    address TEXT,
    settings JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT schools_code_unique UNIQUE (code),
    CONSTRAINT schools_name_not_empty CHECK (char_length(name) > 0),
    CONSTRAINT schools_code_not_empty CHECK (char_length(code) > 0),
    CONSTRAINT schools_code_format CHECK (code ~ '^[a-zA-Z0-9]+$')
);

-- Create indexes for schools
CREATE INDEX IF NOT EXISTS idx_schools_code ON schools (LOWER(code));
CREATE INDEX IF NOT EXISTS idx_schools_admin_user_id ON schools (admin_user_id);
CREATE INDEX IF NOT EXISTS idx_schools_subscription_status ON schools (subscription_status);

-- =====================================================
-- STEP 2: CREATE SCHOOL MEMBERSHIPS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS school_memberships (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Foreign key relationships
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    
    -- Role within the school
    role TEXT NOT NULL CHECK (role IN ('admin', 'supervisor', 'teacher')),
    
    -- Membership status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
    
    -- Membership metadata
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    invitation_token TEXT,
    invitation_expires_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    permissions JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT school_memberships_user_school_unique UNIQUE (user_id, school_id),
    CONSTRAINT school_memberships_invitation_token_unique UNIQUE (invitation_token)
);

-- Create indexes for school memberships
CREATE INDEX IF NOT EXISTS idx_school_memberships_user_id ON school_memberships (user_id);
CREATE INDEX IF NOT EXISTS idx_school_memberships_school_id ON school_memberships (school_id);
CREATE INDEX IF NOT EXISTS idx_school_memberships_user_school_status ON school_memberships (user_id, school_id, status);

-- =====================================================
-- STEP 3: CREATE COUPONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS coupons (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Coupon basic information
    code TEXT NOT NULL,
    name TEXT,
    description TEXT,
    
    -- Coupon status and limits
    is_active BOOLEAN DEFAULT true,
    usage_limit INTEGER DEFAULT NULL,
    used_count INTEGER DEFAULT 0,
    
    -- Validity period
    expires_at TIMESTAMP WITH TIME ZONE,
    starts_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
    
    -- Coupon value and type
    discount_type TEXT DEFAULT 'free_registration' CHECK (discount_type IN ('free_registration', 'percentage', 'fixed_amount')),
    discount_value DECIMAL(10,2) DEFAULT 0,
    
    -- Admin management
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    deactivated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    deactivation_reason TEXT,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT coupons_code_unique UNIQUE (code),
    CONSTRAINT coupons_code_not_empty CHECK (char_length(code) > 0),
    CONSTRAINT coupons_code_format CHECK (code ~ '^[A-Z0-9]+$'),
    CONSTRAINT coupons_usage_limit_positive CHECK (usage_limit IS NULL OR usage_limit > 0),
    CONSTRAINT coupons_used_count_non_negative CHECK (used_count >= 0),
    CONSTRAINT coupons_discount_value_non_negative CHECK (discount_value >= 0)
);

-- Create indexes for coupons
CREATE UNIQUE INDEX IF NOT EXISTS idx_coupons_code_upper ON coupons (UPPER(code));
CREATE INDEX IF NOT EXISTS idx_coupons_is_active ON coupons (is_active);
CREATE INDEX IF NOT EXISTS idx_coupons_expires_at ON coupons (expires_at) WHERE expires_at IS NOT NULL;

-- =====================================================
-- STEP 4: CREATE COUPON USAGE TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS coupon_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Foreign key relationships
    coupon_id UUID NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    
    -- Usage details
    used_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    used_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Usage context
    usage_type TEXT DEFAULT 'school_registration' CHECK (usage_type IN ('school_registration', 'subscription_renewal', 'upgrade')),
    
    -- Original values at time of usage
    original_discount_type TEXT NOT NULL,
    original_discount_value DECIMAL(10,2) DEFAULT 0,
    applied_discount_amount DECIMAL(10,2) DEFAULT 0,
    
    -- Additional metadata
    user_agent TEXT,
    ip_address INET,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT coupon_usage_school_coupon_unique UNIQUE (coupon_id, school_id),
    CONSTRAINT coupon_usage_applied_discount_non_negative CHECK (applied_discount_amount >= 0)
);

-- Create indexes for coupon usage
CREATE INDEX IF NOT EXISTS idx_coupon_usage_coupon_id ON coupon_usage (coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_school_id ON coupon_usage (school_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_used_by ON coupon_usage (used_by);

COMMIT;

-- Log successful completion of table creation
UPDATE migration_log 
SET status = 'completed', notes = 'Core tables created successfully'
WHERE migration_name = '20250713_complete_saas_migration' AND status = 'started';
