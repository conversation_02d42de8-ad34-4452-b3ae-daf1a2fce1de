<template>
    <div class="relative">
        <template v-if="props.variant === 'floating'">
            <label :for="componentId"
                class="absolute text-sm duration-300 transform origin-[0] start-2.5 pointer-events-none z-10 rounded-md"
                :class="{
                    'scale-90 -translate-y-6 top-4 px-1 text-white bg-primary dark:bg-gray-800': isFloated,
                    'scale-100 translate-y-0 top-3': !isFloated, // Position at top for textarea
                    'text-primary': isFocused,
                    'text-gray-500 dark:text-gray-400': !isFocused,
                }">
                {{ placeholder }}
            </label>
        </template>
        <template v-if="props.variant === 'normal'">
            <label :for="componentId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ props.label }}
            </label>
        </template>
        <template v-else>
            <label :for="componentId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {{ props.label }}
            </label>
        </template>
        <textarea ref="textareaEl" :id="componentId" :value="props.modelValue" @input="onInput" @focus="handleFocus"
            @blur="handleBlur" :placeholder="props.variant === 'normal' ? props.placeholder : ''"
            :disabled="props.disabled" :required="props.required" :aria-label="props.placeholder"
            :aria-describedby="props.ariaDescribedby" :rows="props.rows" :class="[
                'form-input block peer textarea-input',
                props.variant === 'floating' ? 'pt-6 pb-2' : 'pt-3 pb-3',
                props.resize === 'none' ? 'resize-none' : '',
                props.resize === 'vertical' ? 'resize-y' : '',
                props.resize === 'horizontal' ? 'resize-x' : '',
                props.resize === 'both' ? 'resize' : '',
                props.autoResize ? 'auto-resize' : ''
            ]" />
        <!-- Error message display -->
        <p v-if="props.error" class="mt-1 text-sm text-alert-error-text dark:text-red-400">
            {{ props.error }}
        </p>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, useId, nextTick, onMounted, watch } from 'vue';
import FloatingLabel from './FloatingLabel.vue';

const props = withDefaults(defineProps<{
    modelValue?: string | null;
    placeholder?: string;
    disabled?: boolean;
    required?: boolean;
    ariaDescribedby?: string;
    variant?: "floating" | "normal";
    label?: string;
    rows?: number;
    resize?: "none" | "vertical" | "horizontal" | "both";
    error?: string;
    autoResize?: boolean;
}>(), {
    placeholder: '',
    disabled: false,
    required: false,
    variant: 'floating',
    rows: 3,
    resize: 'none',
    autoResize: false,
});

const emit = defineEmits(['update:modelValue']);

const componentId = useId();
const textareaEl = ref<HTMLTextAreaElement | null>(null);

const isFocused = ref(false);
const isFloated = computed(() => {
    const hasModelValue = props.modelValue !== undefined && props.modelValue !== null && props.modelValue.toString().length > 0;
    const hasElementValue = textareaEl.value ? textareaEl.value.value.length > 0 : false;
    return isFocused.value || hasModelValue || hasElementValue;
});

const onInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    const value = target.value;
    emit('update:modelValue', value);

    // Auto-resize functionality
    if (props.autoResize && textareaEl.value) {
        autoResizeTextarea();
    }
};

const handleFocus = () => {
    isFocused.value = true;
};

const handleBlur = () => {
    isFocused.value = false;
};

const autoResizeTextarea = () => {
    if (!textareaEl.value) return;

    // Reset height to auto to get the correct scrollHeight
    textareaEl.value.style.height = 'auto';

    // Set the height to the scrollHeight to fit content
    textareaEl.value.style.height = textareaEl.value.scrollHeight + 'px';
};

// Watch for modelValue changes to trigger auto-resize
watch(() => props.modelValue, () => {
    if (props.autoResize) {
        nextTick(() => {
            autoResizeTextarea();
        });
    }
});

onMounted(() => {
    nextTick(() => {
        // Initial auto-resize if content is pre-filled
        if (props.autoResize && textareaEl.value && props.modelValue) {
            autoResizeTextarea();
        }
    });
});
</script>

<style scoped>
/* Textarea-specific styling */
.textarea-input {
    /* Ensure text and placeholder start at the top */
    vertical-align: top;
}

/* Responsive placeholder text size */
.textarea-input::placeholder {
    font-size: 0.875rem;
    /* Default size for large screens */
}

/* Medium screens and below */
@media (max-width: 768px) {
    .textarea-input::placeholder {
        font-size: 0.875rem;
        /* 14px - smaller for medium screens */
    }
}

/* Small screens and below */
@media (max-width: 640px) {
    .textarea-input::placeholder {
        font-size: 0.8125rem;
        /* 13px - even smaller for small screens */
    }
}

/* Auto-resize specific styles */
.textarea-input.auto-resize {
    overflow-y: hidden;
    min-height: 3rem;
    /* Minimum height for 3 rows */
}
</style>
