import { ref } from "vue";

export const useWeekModals = () => {
  // Modal states
  const isAddWeekModalOpen = ref(false);
  const isDeleteWeekModalOpen = ref(false);
  const isManageModalOpen = ref(false);
  const isBulkDeleteModalOpen = ref(false);

  // Search state for manage modal
  const searchQuery = ref("");

  // Modal control functions
  const openAddWeekModal = () => {
    isAddWeekModalOpen.value = true;
    isManageModalOpen.value = false; // Close manage modal if open
  };

  const closeAddWeekModal = () => {
    isAddWeekModalOpen.value = false;
  };

  const openManageModal = () => {
    isManageModalOpen.value = true;
  };

  const closeManageModal = () => {
    isManageModalOpen.value = false;
    searchQuery.value = ""; // Clear search when closing
  };

  const openDeleteWeekModal = () => {
    isDeleteWeekModalOpen.value = true;
  };

  const closeDeleteWeekModal = () => {
    isDeleteWeekModalOpen.value = false;
  };

  const openBulkDeleteModal = () => {
    isBulkDeleteModalOpen.value = true;
  };

  const closeBulkDeleteModal = () => {
    isBulkDeleteModalOpen.value = false;
  };

  return {
    // States
    isAddWeekModalOpen,
    isDeleteWeekModalOpen,
    isManageModalOpen,
    isBulkDeleteModalOpen,
    searchQuery,

    // Actions
    openAddWeekModal,
    closeAddWeekModal,
    openManageModal,
    closeManageModal,
    openDeleteWeekModal,
    closeDeleteWeekModal,
    openBulkDeleteModal,
    closeBulkDeleteModal,
  };
};
