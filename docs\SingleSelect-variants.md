# SingleSelect Component Variants

The `SingleSelect` component now supports two distinct variants to accommodate different UI patterns and user preferences.

## Variants

### 1. Floating Label Variant (`variant="floating"`)

**Usage:**
```vue
<SingleSelect 
    v-model="selectedValue"
    :options="options"
    variant="floating"
    placeholder="Choose an option"
    option-label="name"
    option-value="id"
/>
```

**Features:**
- Animated floating label that moves up when focused or when value is selected
- Modern, Material Design-inspired interface
- `placeholder` prop becomes the floating label text
- No separate label prop needed
- Clean, minimal appearance

**Best for:**
- Modern web applications
- Clean, minimal designs
- When screen space is limited
- Mobile-first designs

### 2. Standard Label Variant (`variant="standard"`)

**Usage:**
```vue
<SingleSelect 
    v-model="selectedValue"
    :options="options"
    variant="standard"
    label="Select Option"
    placeholder="Choose an option"
    option-label="name"
    option-value="id"
/>
```

**Features:**
- Traditional label positioned above the input
- Separate `label` and `placeholder` props
- Clear visual hierarchy
- More familiar to users of traditional forms
- Better accessibility for screen readers

**Best for:**
- Traditional form layouts
- Applications requiring clear label hierarchy
- Better accessibility requirements
- When working with existing design systems that use standard labels

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'floating' \| 'standard'` | `'floating'` | Determines which variant to use |
| `label` | `string` | `''` | Label text (only used in standard variant) |
| `placeholder` | `string` | `'Select option'` | Placeholder text or floating label text |
| `required` | `boolean` | `false` | Shows a red asterisk (*) next to the label (standard variant only) |

## Example Implementation

```vue
<template>
    <div class="space-y-6">
        <!-- Floating Variant -->
        <SingleSelect 
            v-model="selectedFloating"
            :options="subjects"
            variant="floating"
            placeholder="Choose a subject"
            option-label="name"
            option-value="id"
            :allow-clear="true"
        />        <!-- Standard Variant -->
        <SingleSelect 
            v-model="selectedStandard"
            :options="subjects"
            variant="standard"
            label="Subject"
            placeholder="Select a subject"
            option-label="name"
            option-value="id"
            :required="true"
            :allow-clear="true"
        />
    </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedFloating = ref(null)
const selectedStandard = ref(null)

const subjects = ref([
    { id: '1', name: 'Mathematics' },
    { id: '2', name: 'Physics' },
    { id: '3', name: 'Chemistry' },
    // ... more options
])
</script>
```

## Display Value Logic

The component intelligently handles display values based on the variant:

- **Floating variant**: Shows `&nbsp;` when no value is selected to maintain proper floating label animation
- **Standard variant**: Shows the actual placeholder text when no value is selected

## Backwards Compatibility

The component maintains full backwards compatibility:
- Existing components using `SingleSelect` without the `variant` prop will continue to use the floating variant (default)
- All existing props and functionality remain unchanged
- No breaking changes to the API

## Migration Guide

To migrate existing standard label layouts to use the new standard variant:

**Before:**
```vue
<label>Subject</label>
<SingleSelect 
    v-model="selected"
    :options="options"
    placeholder="Select a subject"
/>
```

**After:**
```vue
<SingleSelect 
    v-model="selected"
    :options="options"
    variant="standard"
    label="Subject"
    placeholder="Select a subject"
/>
```

This provides a cleaner, more maintainable approach while improving accessibility and visual consistency.
