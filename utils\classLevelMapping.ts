/**
 * Utility functions for mapping class IDs to class level names
 * Used across the application for consistent class level display
 */

// Class level options mapping
const CLASS_LEVEL_MAPPING: Record<string, string> = {
  // Tahun levels
  't1': 'Tahun 1',
  't2': 'Tahun 2', 
  't3': 'Tahun 3',
  't4': 'Tahun 4',
  't5': 'Tahun 5',
  't6': 'Tahun 6',
  
  // Tingkatan levels
  'f1': 'Tingkatan 1',
  'f2': 'Tingkatan 2',
  'f3': 'Tingkatan 3', 
  'f4': 'Tingkatan 4',
  'f5': 'Tingkatan 5',
  'f6': 'Tingkatan 6',
};

/**
 * Convert class_id to class level name
 * @param classId - The class ID (e.g., 't1', 'f1')
 * @returns The class level name (e.g., 'Tahun 1', 'Tingkatan 1') or the original classId if not found
 */
export function getClassLevelName(classId: string): string {
  return CLASS_LEVEL_MAPPING[classId] || classId;
}

/**
 * Check if a class ID is a Tahun level
 * @param classId - The class ID to check
 * @returns True if it's a Tahun level (t1-t6)
 */
export function isTahunLevel(classId: string): boolean {
  return classId.startsWith('t') && classId in CLASS_LEVEL_MAPPING;
}

/**
 * Check if a class ID is a Tingkatan level
 * @param classId - The class ID to check
 * @returns True if it's a Tingkatan level (f1-f6)
 */
export function isTingkatanLevel(classId: string): boolean {
  return classId.startsWith('f') && classId in CLASS_LEVEL_MAPPING;
}

/**
 * Get the level type from class ID
 * @param classId - The class ID
 * @returns 'tahun', 'tingkatan', or null if unknown
 */
export function getClassLevelType(classId: string): 'tahun' | 'tingkatan' | null {
  if (isTahunLevel(classId)) return 'tahun';
  if (isTingkatanLevel(classId)) return 'tingkatan';
  return null;
}

/**
 * Get all available class level mappings
 * @returns Object with all class ID to name mappings
 */
export function getAllClassLevelMappings(): Record<string, string> {
  return { ...CLASS_LEVEL_MAPPING };
}
