// Quick test script to check if the lesson_plan_reflections table is accessible
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://nhgyywlfopodxomxbegx.supabase.co'
const supabaseKey = process.env.SUPABASE_KEY || 'your-anon-key-here'

const supabase = createClient(supabaseUrl, supabaseKey)

// Test 1: Check if table exists by selecting count
console.log('Testing table access...')
try {
  const { data, error } = await supabase
    .from('lesson_plan_reflections')
    .select('count', { count: 'exact', head: true })

  if (error) {
    console.error('Table access error:', error)
  } else {
    console.log('Table exists and is accessible, count:', data)
  }
} catch (err) {
  console.error('Connection error:', err)
}

// Test 2: Check table structure
try {
  const { data, error } = await supabase
    .from('lesson_plan_reflections')
    .select('*')
    .limit(0)

  if (error) {
    console.error('Structure check error:', error)
  } else {
    console.log('Table structure accessible')
  }
} catch (err) {
  console.error('Structure check failed:', err)
}
