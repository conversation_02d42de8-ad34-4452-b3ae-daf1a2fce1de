-- Timetable Entries Table
-- This table stores the detailed timetable with specific time slots
create table public.timetable_entries (
  id uuid not null default gen_random_uuid (),
  created_at timestamp with time zone not null default timezone ('utc'::text, now()),
  updated_at timestamp with time zone not null default timezone ('utc'::text, now()),
  
  -- Teacher/User
  user_id uuid not null,
  
  -- Schedule reference (optional link to teacher_schedules for auto-generation)
  teacher_schedule_id uuid null,
  
  -- Time and day
  day text not null check (day in ('ISNIN', 'SELASA', 'RABU', 'KHAMIS', 'JUMAAT', 'SABTU', 'AHAD')),
  time_slot_start time not null,
  time_slot_end time not null,
    -- Class and subject
  class_id text not null,  -- Class level code like 't1', 'f1', etc.
  subject_id uuid not null,
  
  -- Additional details
  room text null,
  notes text null,
  
  -- Metadata for display
  class_name text not null,
  subject_name text not null,
  
  constraint timetable_entries_pkey primary key (id),
  
  -- Prevent double booking: same teacher, day, and time slot
  constraint timetable_entries_user_day_time_unique unique (user_id, day, time_slot_start, time_slot_end),
  
  -- Foreign keys
  constraint timetable_entries_user_id_fkey foreign key (user_id) references auth.users (id) on delete cascade,
  constraint timetable_entries_teacher_schedule_id_fkey foreign key (teacher_schedule_id) references teacher_schedules (id) on delete set null
) tablespace pg_default;

-- Indexes for performance
create index if not exists idx_timetable_entries_user_id on public.timetable_entries using btree (user_id) tablespace pg_default;
create index if not exists idx_timetable_entries_day_time on public.timetable_entries using btree (day, time_slot_start) tablespace pg_default;
create index if not exists idx_timetable_entries_class_subject on public.timetable_entries using btree (class_id, subject_id) tablespace pg_default;

-- Updated_at trigger
create or replace function update_timetable_entries_updated_at()
returns trigger as $$
begin
  new.updated_at = timezone('utc', now());
  return new;
end;
$$ language plpgsql;

create trigger trigger_update_timetable_entries_updated_at 
  before update on timetable_entries 
  for each row 
  execute function update_timetable_entries_updated_at();

-- Row Level Security (RLS)
alter table public.timetable_entries enable row level security;

-- Policy: Users can only see/modify their own timetable entries
create policy "Users can view their own timetable entries" on public.timetable_entries
  for select using (auth.uid() = user_id);

create policy "Users can insert their own timetable entries" on public.timetable_entries
  for insert with check (auth.uid() = user_id);

create policy "Users can update their own timetable entries" on public.timetable_entries
  for update using (auth.uid() = user_id);

create policy "Users can delete their own timetable entries" on public.timetable_entries
  for delete using (auth.uid() = user_id);
