<template>
  <Listbox v-model="selectedValues" :disabled="props.disabled" multiple @update:modelValue="emitSelection">
    <div class="relative" :class="props.containerClass">
      <!-- Standard Label (top of input) -->
      <label v-if="props.variant === 'standard' && props.label" :for="listboxButtonId"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {{ props.label }}
        <span v-if="props.required" class="text-red-500 ml-1">*</span>
      </label>

      <!-- Floating Label -->
      <FloatingLabel v-if="props.variant === 'floating'" :for-input="listboxButtonId" :label="props.placeholder"
        :is-floated="isLabelFloated" :is-focused="isFocused" />
      <ListboxButton :id="listboxButtonId" class="form-input relative cursor-pointer pr-10" :class="[
        { 'cursor-not-allowed': props.disabled },
        { '!pt-3.5 !pb-3.5': props.variant === 'standard' },
        props.buttonClass
      ]" @click="toggleListbox" @focus="isFocused = true" @blur="handleBlur" ref="listboxButtonRef">
        <span class="block truncate">
          <template v-if="selectedValues.length > 0 && selectedValues.length <= MAX_DISPLAYED_CHIPS">
            <span v-for="option in selectedValues" :key="option[props.optionValue]" :class="props.tagClass">
              {{ option[props.optionLabel] }}
            </span>
          </template>
          <template v-else-if="selectedValues.length > MAX_DISPLAYED_CHIPS">
            <span class="text-sm text-gray-700 dark:text-gray-300">
              +{{ selectedValues.length }} pilihan
            </span>
          </template>
          <template v-else>
            <span v-if="props.variant === 'standard'" class="text-gray-500 dark:text-gray-400">
              {{ props.placeholder }}
            </span>
            <span v-else>
              &nbsp; <!-- Placeholder for height consistency in floating variant -->
            </span>
          </template>
        </span>
        <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronDownIcon class="h-5 w-5 text-gray-400 transition-transform duration-200 ease-in-out"
            :class="{ 'rotate-180': isOpen }" aria-hidden="true" />
        </span>
      </ListboxButton>
      <transition
        :enter-active-class="listboxAbove ? 'transition ease-out duration-100 transform' : 'transition ease-out duration-100 transform'"
        :enter-from-class="listboxAbove ? 'opacity-0 translate-y-2' : 'opacity-0 -translate-y-2'"
        :enter-to-class="listboxAbove ? 'opacity-100 translate-y-0' : 'opacity-100 translate-y-0'"
        :leave-active-class="listboxAbove ? 'transition ease-in duration-75 transform' : 'transition ease-in duration-75 transform'"
        :leave-from-class="listboxAbove ? 'opacity-100 translate-y-0' : 'opacity-100 translate-y-0'"
        :leave-to-class="listboxAbove ? 'opacity-0 translate-y-2' : 'opacity-0 -translate-y-2'">
        <ListboxOptions ref="listboxOptionsRef"
          class="absolute z-20 mt-1 w-full rounded-md bg-white dark:bg-dark-background text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm flex flex-col max-h-80"
          :class="[listboxAbove ? 'bottom-full mb-1' : 'top-full mt-1', props.optionsClass]">
          <div class="p-2 border-b border-gray-200 dark:border-gray-700">
            <label class="flex items-center cursor-pointer" :for="selectAllId">
              <Checkbox :id="selectAllId" name="selectAllMultiSelect" :modelValue="allSelected"
                :indeterminate="isIndeterminate" @update:modelValue="toggleSelectAll"
                customClass="mr-3 pointer-events-none" />
              <span class="text-sm text-gray-700 dark:text-gray-300">Pilih Semua</span>
            </label>
          </div>
          <div class="overflow-auto py-1 flex-grow">
            <ListboxOption v-for="(option, index) in props.options" :key="option[props.optionValue]" :value="option"
              as="template" v-slot="{ active, selected }">
              <li :class="['listbox-option', {
                'listbox-option--active': active,
                'listbox-option--selected': selected
              }]">
                <div class="flex items-center">
                  <Checkbox :id="`${optionCheckboxIdBase}-${option[props.optionValue] || index}`"
                    :name="`${optionCheckboxIdBase}-options`" :modelValue="selected"
                    customClass="mr-3 pointer-events-none" />
                  <span :class="['block truncate', selected ? 'font-semibold' : 'font-normal']">
                    {{ option[props.optionLabel] }}
                  </span>
                </div>
                <span v-if="selected" class="listbox-option__icon">
                  <CheckIcon class="h-5 w-5" aria-hidden="true" />
                </span>
              </li>
            </ListboxOption>
          </div>
          <div class="p-2 border-t border-gray-200 dark:border-gray-700 flex justify-start items-center">
            <Button size="sm" @click="closeDropdown">
              Selesai
            </Button>
          </div>
        </ListboxOptions>
      </transition>
    </div>
  </Listbox>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted, nextTick, useId, watchEffect } from 'vue';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/vue';
import { CheckIcon, ChevronDownIcon } from '@heroicons/vue/20/solid';
import FloatingLabel from './FloatingLabel.vue';
import Button from './Button.vue'; // Import the Button component
import Checkbox from './Checkbox.vue'; // Import the new Checkbox component

const emit = defineEmits(['update:modelValue']);

const props = withDefaults(defineProps<{
  modelValue: any[]; // Array for multiple selections
  options: Record<string, any>[];
  optionLabel?: string;
  optionValue?: string;
  placeholder?: string;
  label?: string;
  variant?: 'floating' | 'standard';
  required?: boolean;
  disabled?: boolean;
  containerClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  buttonClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  optionsClass?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
  tagClass?: string; // For styling individual selected tags
}>(), {
  optionLabel: 'label',
  optionValue: 'value',
  placeholder: 'Select options',
  variant: 'floating',
  disabled: false,
  containerClass: '',
  buttonClass: '',
  optionsClass: '',
  tagClass: 'inline-block bg-gray-200 dark:bg-gray-700 rounded-full px-2 py-0.5 text-xs font-medium text-gray-700 dark:text-gray-300 mr-1 mb-1',
});

const listboxButtonRef = ref<any | null>(null);
const listboxOptionsRef = ref<HTMLElement | null>(null);
const listboxAbove = ref(false);
const isOpen = ref(false);
const isFocused = ref(false);
const listboxButtonId = `listbox-button-${useId()}`;
const selectAllId = `multiselect-select-all-${useId()}`;
const optionCheckboxIdBase = `multiselect-option-${useId()}`;

const MAX_DISPLAYED_CHIPS = 3;

// selectedValues will store an array of full option objects from Headless UI
const selectedValues = ref<Record<string, any>[]>([]);

// This computed property will derive the array of actual values (e.g., IDs) to emit
const modelValueEquivalent = computed(() => selectedValues.value.map(opt => opt[props.optionValue]));

const isLabelFloated = computed(() => {
  return isFocused.value || isOpen.value || (selectedValues.value && selectedValues.value.length > 0);
});

const selectAllCheckboxRef = ref<HTMLInputElement | null>(null);

const allSelected = computed(() => {
  return props.options.length > 0 && selectedValues.value.length === props.options.length;
});

const isIndeterminate = computed(() => {
  return selectedValues.value.length > 0 && selectedValues.value.length < props.options.length;
});

watchEffect(() => {
  // selectAllCheckboxRef is no longer needed directly for indeterminate, 
  // as the Checkbox component handles its own indeterminate state via prop.
  // If you still need the ref for other purposes, it can be kept, but ensure it points to the new Checkbox component instance if necessary.
});

// Helper to find option objects by their values from props.modelValue (array of values)
const findOptionsByValues = (values: any[]) => {
  if (!Array.isArray(values)) return [];
  return props.options.filter(opt => values.includes(opt[props.optionValue]));
};

// Initialize selectedValues based on initial props.modelValue
watch(() => props.modelValue, (newModelValue) => {
  if (JSON.stringify(newModelValue) !== JSON.stringify(modelValueEquivalent.value)) {
    selectedValues.value = findOptionsByValues(newModelValue || []);
  }
}, { immediate: true, deep: true });


const emitSelection = (selectedItems: Record<string, any>[]) => {
  // selectedItems from Headless UI is already an array of the selected option objects
  selectedValues.value = selectedItems; // Keep selectedValues in sync
  emit('update:modelValue', selectedItems.map(item => item[props.optionValue]));
  // Keep the listbox open for multi-select unless an option to close on select is added
  // For now, it stays open. User can click outside or toggle to close.
};

const toggleSelectAll = (value?: boolean) => { // Modified to accept the emitted value from Checkbox
  if (typeof value === 'boolean') {
    if (value) {
      selectedValues.value = [...props.options];
    } else {
      selectedValues.value = [];
    }
  } else { // Fallback for direct calls if any, though direct calls might be removed
    if (allSelected.value) {
      selectedValues.value = [];
    } else {
      selectedValues.value = [...props.options];
    }
  }
  emit('update:modelValue', selectedValues.value.map(item => item[props.optionValue]));
};

const toggleListbox = () => {
  if (props.disabled) return;
  isOpen.value = !isOpen.value;

  if (isOpen.value) {
    isFocused.value = true;
    nextTick(() => {
      calculatePosition();
    });
  } else {
    const buttonComponent = listboxButtonRef.value;
    // Ensure buttonEl is correctly identified, checking for .el or the component itself if it's an HTMLElement
    const buttonEl = buttonComponent?.$el || (buttonComponent instanceof HTMLElement ? buttonComponent : null);
    if (document.activeElement !== buttonEl) {
      isFocused.value = false;
    }
  }
};

const handleBlur = () => {
  setTimeout(() => {
    // Only set focused to false if listbox is not open AND the button itself is not focused
    const buttonComponent = listboxButtonRef.value;
    const buttonEl = buttonComponent?.$el || buttonComponent;
    if (!isOpen.value && document.activeElement !== buttonEl) {
      isFocused.value = false;
    }
  }, 150); // Increased delay slightly for multi-select interactions
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Node;

  const buttonComponent = listboxButtonRef.value;
  const buttonEl = buttonComponent?.$el || (buttonComponent instanceof HTMLElement ? buttonComponent : null);

  if (buttonEl && typeof buttonEl.contains === 'function' && buttonEl.contains(target)) {
    return;
  }

  const optionsComponentInstance = listboxOptionsRef.value as any; // Type assertion to access .el
  const optionsPanelElement = optionsComponentInstance?.el as HTMLElement | null;

  if (isOpen.value && optionsPanelElement && typeof optionsPanelElement.contains === 'function' && optionsPanelElement.contains(target)) {
    return; // Click was inside the options panel (e.g., on an option, select all, or done button)
  }

  if (isOpen.value) {
    isOpen.value = false;
  }
};

onMounted(() => {
  window.addEventListener('click', handleClickOutside, true);
  window.addEventListener('resize', calculatePosition);
  window.addEventListener('scroll', calculatePosition, true);
});

onUnmounted(() => {
  window.removeEventListener('click', handleClickOutside, true);
  window.removeEventListener('resize', calculatePosition);
  window.removeEventListener('scroll', calculatePosition, true);
});

watch(isOpen, (newVal, oldVal) => {
  if (newVal) {
    isFocused.value = true;
    nextTick(calculatePosition);
  } else {
    const buttonComponent = listboxButtonRef.value;
    const buttonEl = buttonComponent?.$el || (buttonComponent instanceof HTMLElement ? buttonComponent : null);
    if (document.activeElement !== buttonEl) {
      isFocused.value = false;
    }
  }
});

const closeDropdown = () => {
  if (isOpen.value) {
    const buttonComponentInstance = listboxButtonRef.value;
    // Access the underlying HTML element of the ListboxButton
    const actualButtonElement = buttonComponentInstance?.$el || (buttonComponentInstance instanceof HTMLElement ? buttonComponentInstance : null);

    if (actualButtonElement && typeof actualButtonElement.click === 'function') {
      actualButtonElement.click(); // This should trigger toggleListbox via the @click handler on ListboxButton
    } else {
      isOpen.value = false; // Fallback, might not fully work with Headless UI
    }
  }
};


const calculatePosition = () => {
  const buttonComponent = listboxButtonRef.value;
  const buttonEl = buttonComponent?.$el || buttonComponent;
  if (buttonEl && typeof buttonEl.getBoundingClientRect === 'function' && isOpen.value) {
    const buttonRect = buttonEl.getBoundingClientRect();
    const optionsHeightEstimate = 240;
    const spaceBelow = window.innerHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;
    listboxAbove.value = spaceBelow < optionsHeightEstimate && spaceAbove > optionsHeightEstimate;
  }
};

</script>

<style scoped>
/* Styles for .form-input and .listbox-option are expected to be in global CSS (assets/css/tailwind.css) */
/* Add any component-specific styles here if needed */
.form-input {
  /* Ensure this class is defined globally or here if specific overrides are needed */
  min-height: 3.5rem;
  /* Adjust based on your form-input padding (pb-2.5 pt-5) */
  display: flex;
  align-items: center;
  /* Align tags and placeholder text */
}

.form-input>span.block.truncate {
  display: flex;
  flex-wrap: wrap;
  /* Allow tags to wrap */
  align-items: center;
  width: 100%;
}

/* Ensure listbox-option provides appropriate padding if not globally defined, e.g., py-2 px-4 */
/* .listbox-option { @apply py-2 px-4; } */
</style>
