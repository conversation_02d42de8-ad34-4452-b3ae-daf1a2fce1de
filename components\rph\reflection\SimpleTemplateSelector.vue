<template>
  <div class="template-selector">
    <select
      :value="selectedTemplateId || ''"
      @change="handleSelectionChange"
      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
    >
      <option value="">{{ placeholder }}</option>
      <option v-if="includeNoneOption" value="none">Tiada Template</option>
      
      <!-- System Templates -->
      <optgroup v-if="systemTemplates.length > 0" label="Template Sistem">
        <option
          v-for="template in systemTemplates"
          :key="template.id"
          :value="template.id"
        >
          {{ template.name }}
          <span v-if="template.description"> - {{ template.description }}</span>
        </option>
      </optgroup>
      
      <!-- User Templates -->
      <optgroup v-if="userTemplates.length > 0" label="Template Peribadi">
        <option
          v-for="template in userTemplates"
          :key="template.id"
          :value="template.id"
        >
          {{ template.name }}
          <span v-if="template.description"> - {{ template.description }}</span>
        </option>
      </optgroup>
    </select>
    
    <!-- Loading indicator -->
    <div v-if="loading" class="absolute right-8 top-1/2 transform -translate-y-1/2">
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useReflectionTemplates } from '~/composables/useReflectionTemplates';

interface Props {
  selectedTemplateId?: string | null;
  placeholder?: string;
  includeNoneOption?: boolean;
  filterCategory?: string;
}

interface Emits {
  (e: 'update:selected-template-id', value: string | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedTemplateId: null,
  placeholder: 'Pilih template',
  includeNoneOption: false,
  filterCategory: undefined
});

const emit = defineEmits<Emits>();

// Composables
const {
  templates,
  loading,
  fetchAllTemplates
} = useReflectionTemplates();

// Computed
const systemTemplates = computed(() => {
  let filtered = templates.value.filter(t => t.is_system_template);
  
  if (props.filterCategory) {
    filtered = filtered.filter(t => t.category === props.filterCategory);
  }
  
  return filtered.sort((a, b) => a.name.localeCompare(b.name));
});

const userTemplates = computed(() => {
  let filtered = templates.value.filter(t => !t.is_system_template);
  
  if (props.filterCategory) {
    filtered = filtered.filter(t => t.category === props.filterCategory);
  }
  
  return filtered.sort((a, b) => a.name.localeCompare(b.name));
});

// Methods
const handleSelectionChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  const value = target.value;
  
  if (value === '' || value === 'none') {
    emit('update:selected-template-id', null);
  } else {
    emit('update:selected-template-id', value);
  }
};

// Lifecycle
onMounted(async () => {
  try {
    await fetchAllTemplates();
  } catch (err) {
    console.error('Error loading templates:', err);
  }
});
</script>

<style scoped>
.template-selector {
  position: relative;
}
</style>
