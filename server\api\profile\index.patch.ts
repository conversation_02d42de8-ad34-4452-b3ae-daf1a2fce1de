// Update user profile API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')

    // Get request body
    const body = await readBody(event)
    const { 
      full_name, 
      avatar_url, 
      phone, 
      bio, 
      location, 
      website, 
      preferences 
    } = body

    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Validate input data
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (full_name !== undefined) {
      if (typeof full_name !== 'string' || full_name.length > 100) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Full name must be a string with maximum 100 characters'
        })
      }
      updateData.full_name = full_name.trim()
    }

    if (avatar_url !== undefined) {
      if (avatar_url !== null && (typeof avatar_url !== 'string' || avatar_url.length > 500)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Avatar URL must be a string with maximum 500 characters'
        })
      }
      updateData.avatar_url = avatar_url
    }

    if (phone !== undefined) {
      if (phone !== null && (typeof phone !== 'string' || phone.length > 20)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Phone must be a string with maximum 20 characters'
        })
      }
      updateData.phone = phone
    }

    if (bio !== undefined) {
      if (bio !== null && (typeof bio !== 'string' || bio.length > 500)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Bio must be a string with maximum 500 characters'
        })
      }
      updateData.bio = bio
    }

    if (location !== undefined) {
      if (location !== null && (typeof location !== 'string' || location.length > 100)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Location must be a string with maximum 100 characters'
        })
      }
      updateData.location = location
    }

    if (website !== undefined) {
      if (website !== null && (typeof website !== 'string' || website.length > 200)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Website must be a string with maximum 200 characters'
        })
      }
      updateData.website = website
    }

    if (preferences !== undefined) {
      if (typeof preferences !== 'object' || preferences === null) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Preferences must be an object'
        })
      }
      updateData.preferences = preferences
    }

    // Check if profile exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single()

    let updatedProfile

    if (checkError && checkError.code === 'PGRST116') {
      // Profile doesn't exist, check if creation is allowed
      const origin = getHeader(event, 'origin') || getHeader(event, 'referer') || ''
      const isMainDomain = !origin.includes('.localhost:3000') && !origin.includes('.rphmate.com')

      if (isMainDomain) {
        // For main domain, only allow profile creation for school admins
        const { data: schools } = await supabase
          .from('schools')
          .select('id')
          .eq('admin_user_id', user.id)
          .limit(1)

        if (!schools || schools.length === 0) {
          throw createError({
            statusCode: 403,
            statusMessage: 'Profile creation not allowed for non-school administrators'
          })
        }

        // Create profile with is_school_admin = true
        const { data: newProfile, error: profileCreateError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            email: user.email,
            is_school_admin: true,
            ...updateData,
            created_at: new Date().toISOString()
          })
          .select()
          .single()

        if (profileCreateError) {
          throw createError({
            statusCode: 500,
            statusMessage: `Failed to create profile: ${profileCreateError.message}`
          })
        }

        updatedProfile = newProfile
      } else {
        // School subdomain - create regular profile
        const { data: newProfile, error: profileCreateError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            email: user.email,
            is_school_admin: false,
            ...updateData,
            created_at: new Date().toISOString()
          })
          .select()
          .single()

        if (profileCreateError) {
          throw createError({
            statusCode: 500,
            statusMessage: `Failed to create profile: ${profileCreateError.message}`
          })
        }

        updatedProfile = newProfile
      }
    } else if (checkError) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Error checking profile existence'
      })
    } else {
      // Profile exists, update it
      const { data: updated, error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id)
        .select()
        .single()

      if (updateError) {
        throw createError({
          statusCode: 500,
          statusMessage: `Failed to update profile: ${updateError.message}`
        })
      }

      updatedProfile = updated
    }

    // Also update auth user metadata if full_name or avatar_url changed
    if (full_name !== undefined || avatar_url !== undefined) {
      const metadataUpdate: any = {}
      if (full_name !== undefined) metadataUpdate.full_name = full_name
      if (avatar_url !== undefined) metadataUpdate.avatar_url = avatar_url

      try {
        await supabase.auth.updateUser({
          data: metadataUpdate
        })
      } catch (metadataError) {
        console.error('Error updating user metadata:', metadataError)
        // Don't fail the request for metadata update errors
      }
    }

    return {
      success: true,
      profile: updatedProfile,
      message: 'Profile updated successfully'
    }

  } catch (error: any) {
    console.error('Update profile error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during profile update'
    })
  }
})
