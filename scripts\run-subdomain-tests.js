#!/usr/bin/env node

// Comprehensive Subdomain Testing Script
// Created: 2025-07-13
// Purpose: Run comprehensive tests for subdomain functionality

import { execSync } from 'child_process'
import { SubdomainTester } from './test-subdomains.js'
import { DevSubdomainSetup } from './setup-dev-subdomains.js'

const COLORS = {
  RED: '\u001b[0;31m',
  GREEN: '\u001b[0;32m',
  YELLOW: '\u001b[1;33m',
  BLUE: '\u001b[0;34m',
  PURPLE: '\u001b[0;35m',
  CYAN: '\u001b[0;36m',
  NC: '\u001b[0m' // No Color
}

class ComprehensiveSubdomainTester {
  constructor() {
    this.results = {
      setup: { passed: 0, failed: 0, tests: [] },
      functionality: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] },
      performance: { passed: 0, failed: 0, tests: [] }
    }
    this.startTime = Date.now()
  }

  log(message, color = COLORS.NC) {
    console.log(`${color}${message}${COLORS.NC}`)
  }

  logTest(category, name, passed, message = '') {
    const status = passed ? '✅' : '❌'
    const result = { name, passed, message, timestamp: new Date().toISOString() }
    
    this.log(`${status} ${name}${message ? ': ' + message : ''}`)
    this.results[category].tests.push(result)
    
    if (passed) {
      this.results[category].passed++
    } else {
      this.results[category].failed++
    }
  }

  async runSetupTests() {
    this.log('\n🔧 Phase 1: Setup Tests', COLORS.BLUE)
    this.log('=' .repeat(50), COLORS.BLUE)

    try {
      // Test 1: Check if development setup script exists
      const setupScript = './scripts/setup-dev-subdomains.js'
      try {
        await import(setupScript)
        this.logTest('setup', 'Setup script exists', true)
      } catch (error) {
        this.logTest('setup', 'Setup script exists', false, error.message)
      }

      // Test 2: Check if testing script exists
      const testScript = './scripts/test-subdomains.js'
      try {
        await import(testScript)
        this.logTest('setup', 'Test script exists', true)
      } catch (error) {
        this.logTest('setup', 'Test script exists', false, error.message)
      }

      // Test 3: Check Node.js version
      try {
        const nodeVersion = process.version
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
        const isSupported = majorVersion >= 16
        this.logTest('setup', 'Node.js version compatibility', isSupported, 
          `Node.js ${nodeVersion} (requires >= 16.0.0)`)
      } catch (error) {
        this.logTest('setup', 'Node.js version compatibility', false, error.message)
      }

      // Test 4: Check required dependencies
      try {
        const packageJson = JSON.parse(execSync('cat package.json', { encoding: 'utf8' }))
        const hasNuxt = packageJson.dependencies?.nuxt || packageJson.devDependencies?.nuxt
        this.logTest('setup', 'Nuxt.js dependency', !!hasNuxt)
      } catch (error) {
        this.logTest('setup', 'Nuxt.js dependency', false, error.message)
      }

      // Test 5: Check environment configuration
      try {
        const envExists = execSync('test -f .env && echo "exists" || echo "missing"', { encoding: 'utf8' }).trim()
        this.logTest('setup', 'Environment configuration', envExists === 'exists')
      } catch (error) {
        this.logTest('setup', 'Environment configuration', false, error.message)
      }

    } catch (error) {
      this.logTest('setup', 'Setup tests execution', false, error.message)
    }
  }

  async runFunctionalityTests() {
    this.log('\n🧪 Phase 2: Functionality Tests', COLORS.GREEN)
    this.log('=' .repeat(50), COLORS.GREEN)

    try {
      // Test 1: Subdomain detection composable
      try {
        const { useSubdomain } = await import('../composables/useSubdomain.ts')
        this.logTest('functionality', 'Subdomain composable import', true)
      } catch (error) {
        this.logTest('functionality', 'Subdomain composable import', false, error.message)
      }

      // Test 2: Multi-tenant composable
      try {
        const { useMultiTenant } = await import('../composables/useMultiTenant.ts')
        this.logTest('functionality', 'Multi-tenant composable import', true)
      } catch (error) {
        this.logTest('functionality', 'Multi-tenant composable import', false, error.message)
      }

      // Test 3: Subdomain plugins
      try {
        const clientPlugin = await import('../plugins/subdomain-detection.client.ts')
        this.logTest('functionality', 'Client subdomain plugin', true)
      } catch (error) {
        this.logTest('functionality', 'Client subdomain plugin', false, error.message)
      }

      try {
        const serverPlugin = await import('../plugins/subdomain-detection.server.ts')
        this.logTest('functionality', 'Server subdomain plugin', true)
      } catch (error) {
        this.logTest('functionality', 'Server subdomain plugin', false, error.message)
      }

      // Test 4: School authentication middleware
      try {
        const middleware = await import('../middleware/school-auth.ts')
        this.logTest('functionality', 'School auth middleware', true)
      } catch (error) {
        this.logTest('functionality', 'School auth middleware', false, error.message)
      }

      // Test 5: Health check API
      try {
        const healthAPI = await import('../server/api/health.get.ts')
        this.logTest('functionality', 'Health check API', true)
      } catch (error) {
        this.logTest('functionality', 'Health check API', false, error.message)
      }

    } catch (error) {
      this.logTest('functionality', 'Functionality tests execution', false, error.message)
    }
  }

  async runIntegrationTests() {
    this.log('\n🔗 Phase 3: Integration Tests', COLORS.PURPLE)
    this.log('=' .repeat(50), COLORS.PURPLE)

    try {
      // Test 1: Check if development server can start
      try {
        // This is a simplified check - in a real scenario, you'd start the server
        const packageJson = JSON.parse(execSync('cat package.json', { encoding: 'utf8' }))
        const hasDevScript = packageJson.scripts?.dev
        this.logTest('integration', 'Development server script', !!hasDevScript)
      } catch (error) {
        this.logTest('integration', 'Development server script', false, error.message)
      }

      // Test 2: TypeScript compilation
      try {
        execSync('npm run typecheck', { stdio: 'pipe' })
        this.logTest('integration', 'TypeScript compilation', true)
      } catch (error) {
        this.logTest('integration', 'TypeScript compilation', false, 'Type errors found')
      }

      // Test 3: Nuxt configuration validation
      try {
        const nuxtConfig = await import('../nuxt.config.ts')
        const hasSubdomainConfig = nuxtConfig.default?.runtimeConfig?.public?.baseDomain !== undefined
        this.logTest('integration', 'Nuxt subdomain configuration', hasSubdomainConfig)
      } catch (error) {
        this.logTest('integration', 'Nuxt subdomain configuration', false, error.message)
      }

      // Test 4: Database schema validation
      try {
        const schemaExists = execSync('test -f database/schema.sql && echo "exists" || echo "missing"', { encoding: 'utf8' }).trim()
        this.logTest('integration', 'Database schema', schemaExists === 'exists')
      } catch (error) {
        this.logTest('integration', 'Database schema', false, error.message)
      }

      // Test 5: RLS policies validation
      try {
        const rlsExists = execSync('test -f database/rls-policies.sql && echo "exists" || echo "missing"', { encoding: 'utf8' }).trim()
        this.logTest('integration', 'RLS policies', rlsExists === 'exists')
      } catch (error) {
        this.logTest('integration', 'RLS policies', false, error.message)
      }

    } catch (error) {
      this.logTest('integration', 'Integration tests execution', false, error.message)
    }
  }

  async runPerformanceTests() {
    this.log('\n⚡ Phase 4: Performance Tests', COLORS.YELLOW)
    this.log('=' .repeat(50), COLORS.YELLOW)

    try {
      // Test 1: Bundle size check
      try {
        const buildExists = execSync('test -d .nuxt && echo "exists" || echo "missing"', { encoding: 'utf8' }).trim()
        if (buildExists === 'exists') {
          this.logTest('performance', 'Build artifacts', true, 'Build directory exists')
        } else {
          this.logTest('performance', 'Build artifacts', false, 'No build found - run npm run build')
        }
      } catch (error) {
        this.logTest('performance', 'Build artifacts', false, error.message)
      }

      // Test 2: Memory usage estimation
      try {
        const memoryUsage = process.memoryUsage()
        const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024)
        const isEfficient = heapUsedMB < 100 // Less than 100MB for the test script
        this.logTest('performance', 'Memory efficiency', isEfficient, `${heapUsedMB}MB heap used`)
      } catch (error) {
        this.logTest('performance', 'Memory efficiency', false, error.message)
      }

      // Test 3: File structure optimization
      try {
        const fileCount = execSync('find . -name "*.vue" -o -name "*.ts" -o -name "*.js" | wc -l', { encoding: 'utf8' }).trim()
        const isOptimal = parseInt(fileCount) < 1000 // Reasonable file count
        this.logTest('performance', 'File structure', isOptimal, `${fileCount} source files`)
      } catch (error) {
        this.logTest('performance', 'File structure', false, error.message)
      }

      // Test 4: Dependency analysis
      try {
        const packageJson = JSON.parse(execSync('cat package.json', { encoding: 'utf8' }))
        const depCount = Object.keys(packageJson.dependencies || {}).length
        const devDepCount = Object.keys(packageJson.devDependencies || {}).length
        const totalDeps = depCount + devDepCount
        const isReasonable = totalDeps < 100 // Reasonable dependency count
        this.logTest('performance', 'Dependency count', isReasonable, `${totalDeps} total dependencies`)
      } catch (error) {
        this.logTest('performance', 'Dependency count', false, error.message)
      }

      // Test 5: Configuration complexity
      try {
        const nuxtConfigSize = execSync('wc -l nuxt.config.ts', { encoding: 'utf8' }).trim().split(' ')[0]
        const isSimple = parseInt(nuxtConfigSize) < 200 // Reasonable config size
        this.logTest('performance', 'Configuration complexity', isSimple, `${nuxtConfigSize} lines in nuxt.config.ts`)
      } catch (error) {
        this.logTest('performance', 'Configuration complexity', false, error.message)
      }

    } catch (error) {
      this.logTest('performance', 'Performance tests execution', false, error.message)
    }
  }

  generateReport() {
    const endTime = Date.now()
    const duration = Math.round((endTime - this.startTime) / 1000)

    this.log('\n📊 Test Report', COLORS.CYAN)
    this.log('=' .repeat(50), COLORS.CYAN)

    // Calculate totals
    let totalPassed = 0
    let totalFailed = 0
    let totalTests = 0

    Object.keys(this.results).forEach(category => {
      const result = this.results[category]
      totalPassed += result.passed
      totalFailed += result.failed
      totalTests += result.tests.length

      this.log(`\n${category.toUpperCase()}:`)
      this.log(`  ✅ Passed: ${result.passed}`)
      this.log(`  ❌ Failed: ${result.failed}`)
      this.log(`  📝 Total: ${result.tests.length}`)
    })

    this.log('\nOVERALL SUMMARY:', COLORS.CYAN)
    this.log(`  ✅ Total Passed: ${totalPassed}`)
    this.log(`  ❌ Total Failed: ${totalFailed}`)
    this.log(`  📝 Total Tests: ${totalTests}`)
    this.log(`  ⏱️  Duration: ${duration}s`)
    this.log(`  📊 Success Rate: ${Math.round((totalPassed / totalTests) * 100)}%`)

    // Show failed tests
    if (totalFailed > 0) {
      this.log('\n❌ Failed Tests:', COLORS.RED)
      Object.keys(this.results).forEach(category => {
        const failedTests = this.results[category].tests.filter(test => !test.passed)
        if (failedTests.length > 0) {
          this.log(`\n${category.toUpperCase()}:`)
          failedTests.forEach(test => {
            this.log(`  - ${test.name}: ${test.message}`)
          })
        }
      })

      this.log('\n💡 Recommendations:', COLORS.YELLOW)
      this.log('1. Fix failed tests before proceeding to production')
      this.log('2. Run individual test categories to isolate issues')
      this.log('3. Check documentation for setup requirements')
      this.log('4. Verify all dependencies are installed')
    } else {
      this.log('\n🎉 All tests passed! Subdomain system is ready for production.', COLORS.GREEN)
    }

    return totalFailed === 0
  }

  async runAllTests() {
    this.log('🧪 RPHMate Comprehensive Subdomain Testing', COLORS.CYAN)
    this.log('=' .repeat(60), COLORS.CYAN)
    this.log(`Started at: ${new Date().toISOString()}`)

    await this.runSetupTests()
    await this.runFunctionalityTests()
    await this.runIntegrationTests()
    await this.runPerformanceTests()

    return this.generateReport()
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)
  const tester = new ComprehensiveSubdomainTester()

  try {
    if (args.includes('--setup-only')) {
      await tester.runSetupTests()
    } else if (args.includes('--functionality-only')) {
      await tester.runFunctionalityTests()
    } else if (args.includes('--integration-only')) {
      await tester.runIntegrationTests()
    } else if (args.includes('--performance-only')) {
      await tester.runPerformanceTests()
    } else {
      const success = await tester.runAllTests()
      process.exit(success ? 0 : 1)
    }
  } catch (error) {
    console.error('❌ Testing failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { ComprehensiveSubdomainTester }
