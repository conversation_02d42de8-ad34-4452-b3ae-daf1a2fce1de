-- Add template preferences to user_preferences table
-- This migration extends the existing user_preferences table to include template-related settings

BEGIN;

-- =====================================================
-- ADD TEMPLATE PREFERENCES COLUMN
-- =====================================================

-- Add template_preferences JSONB column to user_preferences table
ALTER TABLE user_preferences 
ADD COLUMN IF NOT EXISTS template_preferences JSONB DEFAULT '{}';

-- =====================================================
-- CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to get user's template preferences with defaults
CREATE OR REPLACE FUNCTION get_user_template_preferences(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    preferences JSONB;
    default_preferences JSONB := '{
        "default_template_id": null,
        "auto_apply_default": false,
        "show_template_suggestions": true,
        "preferred_categories": [],
        "template_sort_order": "recent",
        "show_system_templates": true,
        "show_usage_stats": true,
        "auto_save_custom_templates": true,
        "template_preview_mode": "detailed"
    }';
BEGIN
    -- Get existing preferences or create default
    SELECT COALESCE(template_preferences, '{}') INTO preferences
    FROM user_preferences 
    WHERE user_id = p_user_id;
    
    -- If no preferences exist, return defaults
    IF preferences IS NULL OR preferences = '{}' THEN
        RETURN default_preferences;
    END IF;
    
    -- Merge with defaults to ensure all keys exist
    RETURN default_preferences || preferences;
END;
$$ LANGUAGE plpgsql;

-- Function to update user's template preferences
CREATE OR REPLACE FUNCTION update_user_template_preferences(
    p_user_id UUID,
    p_preferences JSONB
)
RETURNS JSONB AS $$
DECLARE
    current_prefs JSONB;
    updated_prefs JSONB;
BEGIN
    -- Get current preferences
    current_prefs := get_user_template_preferences(p_user_id);
    
    -- Merge new preferences with existing ones
    updated_prefs := current_prefs || p_preferences;
    
    -- Upsert the preferences
    INSERT INTO user_preferences (user_id, template_preferences)
    VALUES (p_user_id, updated_prefs)
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        template_preferences = updated_prefs,
        updated_at = NOW();
    
    RETURN updated_prefs;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for template preferences queries
CREATE INDEX IF NOT EXISTS idx_user_preferences_template_prefs 
ON user_preferences USING GIN (template_preferences);

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON COLUMN user_preferences.template_preferences IS 'JSONB storing template-related user preferences and settings';

COMMENT ON FUNCTION get_user_template_preferences(UUID) IS 'Returns user template preferences with defaults for missing keys';

COMMENT ON FUNCTION update_user_template_preferences(UUID, JSONB) IS 'Updates user template preferences by merging with existing preferences';

-- =====================================================
-- EXAMPLE TEMPLATE PREFERENCES STRUCTURE
-- =====================================================

/*
Template preferences JSONB structure:
{
    "default_template_id": "uuid-of-default-template" | null,
    "auto_apply_default": boolean,
    "show_template_suggestions": boolean,
    "preferred_categories": ["lesson_type", "assessment"],
    "template_sort_order": "recent" | "alphabetical" | "usage" | "category",
    "show_system_templates": boolean,
    "show_usage_stats": boolean,
    "auto_save_custom_templates": boolean,
    "template_preview_mode": "simple" | "detailed"
}
*/

-- =====================================================
-- SAMPLE DATA FOR TESTING (OPTIONAL)
-- =====================================================

-- Uncomment to insert sample preferences for testing
/*
INSERT INTO user_preferences (user_id, template_preferences)
VALUES (
    'sample-user-uuid',
    '{
        "default_template_id": null,
        "auto_apply_default": false,
        "show_template_suggestions": true,
        "preferred_categories": ["lesson_type", "assessment"],
        "template_sort_order": "recent",
        "show_system_templates": true,
        "show_usage_stats": true,
        "auto_save_custom_templates": true,
        "template_preview_mode": "detailed"
    }'
) ON CONFLICT (user_id) DO NOTHING;
*/

COMMIT;
