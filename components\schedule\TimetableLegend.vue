<template>
    <div class="p-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600">
        <!-- Subjects Section -->
        <div v-if="usedSubjects.length > 0" class="mb-3">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Subjek:</span>
            <div class="flex flex-wrap gap-3 mt-2">
                <div v-for="subject in usedSubjects" :key="subject.subject_id" class="flex items-center space-x-2">
                    <div
                        :class="['w-3 h-3 rounded-full', subject.bg_color.replace('bg-', 'bg-').replace(' dark:bg-', ' dark:bg-')]">
                    </div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">{{ subject.subject_name }}</span>
                </div>
            </div>
        </div>

        <!-- Activities Section -->
        <div v-if="usedActivities.length > 0">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Aktiviti:</span>
            <div class="flex flex-wrap gap-3 mt-2">
                <div v-for="activity in usedActivities" :key="activity.value" class="flex items-center space-x-2">
                    <Icon :name="activity.icon" :class="['w-3 h-3', `text-${activity.color}`]" />
                    <span class="text-xs text-gray-600 dark:text-gray-400">{{ activity.label }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Icon from '~/components/ui/base/Icon.vue'
import type { SubjectColor } from '~/types/timetable'
import type { ActivityTypeConfig } from '~/types/timetable'

defineProps<{
    usedSubjects: SubjectColor[]
    usedActivities: ActivityTypeConfig[]
}>()
</script>