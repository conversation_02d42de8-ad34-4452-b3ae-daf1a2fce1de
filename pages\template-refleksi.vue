<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <PageHeader title="Template Refleksi" subtitle="Urus template refleksi peribadi anda"
      icon="heroicons:document-text" />

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
          <button @click="activeTab = 'my-templates'" :class="[
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'my-templates'
              ? 'border-primary text-primary'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
          ]">
            Template Saya
          </button>
          <button @click="activeTab = 'system-templates'" :class="[
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'system-templates'
              ? 'border-primary text-primary'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
          ]">
            Template Sistem
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <!-- My Templates Tab -->
        <div v-if="activeTab === 'my-templates'">
          <CustomTemplateManager />
        </div>

        <!-- System Templates Tab -->
        <div v-if="activeTab === 'system-templates'">
          <SystemTemplateViewer />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PageHeader from '~/components/ui/composite/PageHeader.vue';
import CustomTemplateManager from '~/components/rph/reflection/CustomTemplateManager.vue';
import SystemTemplateViewer from '~/components/rph/reflection/SystemTemplateViewer.vue';

// Meta
definePageMeta({
  title: 'Template Refleksi',
  description: 'Urus template refleksi peribadi anda',
  requiresAuth: true
});

// State
const activeTab = ref<'my-templates' | 'system-templates'>('my-templates');
</script>

<style scoped>
.tab-content {
  min-height: 400px;
}
</style>
