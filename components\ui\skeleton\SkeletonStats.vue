<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <div 
      v-for="item in items" 
      :key="`stat-${item}`" 
      class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
    >
      <!-- Icon -->
      <div class="flex items-center justify-between mb-4">
        <SkeletonBox height="2rem" width="2rem" class="rounded-lg" />
        <SkeletonBox height="1rem" width="1rem" class="rounded" variant="light" />
      </div>
      
      <!-- Value -->
      <div class="mb-2">
        <SkeletonBox height="2rem" :width="getValueWidth(item)" />
      </div>
      
      <!-- Label -->
      <div class="mb-3">
        <SkeletonBox height="0.875rem" :width="getLabelWidth(item)" variant="light" />
      </div>
      
      <!-- Change indicator -->
      <div class="flex items-center space-x-2">
        <SkeletonBox height="0.75rem" width="0.75rem" class="rounded" />
        <SkeletonBox height="0.75rem" :width="getChangeWidth(item)" variant="light" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  items?: number
}

const props = withDefaults(defineProps<Props>(), {
  items: 4
})

const getValueWidth = (item: number): string => {
  const widths = ['60%', '45%', '70%', '55%', '65%']
  return widths[(item - 1) % widths.length]
}

const getLabelWidth = (item: number): string => {
  const widths = ['80%', '70%', '90%', '75%', '85%']
  return widths[(item - 1) % widths.length]
}

const getChangeWidth = (item: number): string => {
  const widths = ['40%', '50%', '35%', '45%', '55%']
  return widths[(item - 1) % widths.length]
}
</script>
