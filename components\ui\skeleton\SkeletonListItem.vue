<template>
  <div class="flex items-center space-x-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
    <!-- Avatar/Icon -->
    <SkeletonBox 
      v-if="showAvatar" 
      height="2.5rem" 
      width="2.5rem" 
      class="rounded-full flex-shrink-0" 
    />
    
    <!-- Content -->
    <div class="flex-1 space-y-2">
      <!-- Title -->
      <SkeletonBox height="1.125rem" :width="titleWidth" />
      
      <!-- Description -->
      <SkeletonBox height="0.875rem" :width="descriptionWidth" variant="light" />
      
      <!-- Meta information -->
      <div v-if="showMeta" class="flex items-center space-x-4">
        <SkeletonBox height="0.75rem" width="4rem" variant="light" />
        <SkeletonBox height="0.75rem" width="3rem" variant="light" />
        <SkeletonBox height="0.75rem" width="5rem" variant="light" />
      </div>
    </div>
    
    <!-- Actions -->
    <div class="flex items-center space-x-2 flex-shrink-0">
      <SkeletonBox height="2rem" width="2rem" class="rounded" />
      <SkeletonBox height="2rem" width="2rem" class="rounded" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import SkeletonBox from './SkeletonBox.vue'

interface Props {
  height?: string
  showAvatar?: boolean
  showMeta?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 'auto',
  showAvatar: true,
  showMeta: true
})

const titleWidth = computed(() => {
  const widths = ['60%', '75%', '50%', '80%', '65%']
  return widths[Math.floor(Math.random() * widths.length)]
})

const descriptionWidth = computed(() => {
  const widths = ['85%', '70%', '90%', '60%', '75%']
  return widths[Math.floor(Math.random() * widths.length)]
})
</script>
