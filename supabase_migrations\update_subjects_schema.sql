-- Migration: Update subjects table for Malaysian education system
-- Created: 2024-12-30
-- Description: Add categorization columns and update constraints to allow same subject names for different levels

BEGIN;

-- Step 1: Backup existing data
CREATE TABLE IF NOT EXISTS subjects_backup AS SELECT * FROM subjects;

-- Step 2: Add new columns to existing table
ALTER TABLE subjects 
ADD COLUMN IF NOT EXISTS category TEXT,
ADD COLUMN IF NOT EXISTS level_type TEXT CHECK (level_type IN ('tahun', 'tingkatan', 'both')),
ADD COLUMN IF NOT EXISTS sub_level TEXT, -- 'all', 'lower' (1-3), 'upper' (4-5)
ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Step 3: Drop the problematic unique constraints
DROP INDEX IF EXISTS idx_subjects_global_unique_name;
DROP INDEX IF EXISTS idx_subjects_global_unique_code;

-- Step 4: Create new unique constraints that allow same names for different levels/categories
-- This allows "Bahasa Melayu" to exist for both Tahun and Tingkatan levels
CREATE UNIQUE INDEX idx_subjects_global_unique_name_level 
ON subjects (name, level_type, sub_level, category) 
WHERE user_id IS NULL;

-- For codes, we'll make them unique per level to avoid conflicts
CREATE UNIQUE INDEX idx_subjects_global_unique_code_level 
ON subjects (code, level_type, sub_level) 
WHERE user_id IS NULL;

-- Step 5: Clear existing global subjects (keep user custom subjects)
DELETE FROM subjects WHERE user_id IS NULL;

-- Step 6: Insert new structured global subjects
-- Tahun (Primary) subjects - Teras
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Bahasa Melayu', 'BM', 'teras', 'tahun', 'all', 1, NULL, FALSE, TRUE),
('Bahasa Inggeris', 'BI', 'teras', 'tahun', 'all', 2, NULL, FALSE, TRUE),
('Matematik', 'MAT', 'teras', 'tahun', 'all', 3, NULL, FALSE, TRUE),
('Sains', 'SAI', 'teras', 'tahun', 'all', 4, NULL, FALSE, TRUE),
('Sejarah', 'SEJ', 'teras', 'tahun', 'all', 5, NULL, FALSE, TRUE),
('Pendidikan Islam', 'PI', 'teras', 'tahun', 'all', 6, NULL, FALSE, TRUE),
('Pendidikan Moral', 'PM', 'teras', 'tahun', 'all', 7, NULL, FALSE, TRUE),
('Pendidikan Jasmani dan Kesihatan', 'PJK', 'teras', 'tahun', 'all', 8, NULL, FALSE, TRUE);

-- Tahun (Primary) subjects - Tambahan
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Reka Bentuk dan Teknologi', 'RBT', 'tambahan', 'tahun', 'all', 9, NULL, FALSE, TRUE),
('Pendidikan Kesihatan', 'PK', 'tambahan', 'tahun', 'all', 10, NULL, FALSE, TRUE),
('Bahasa Arab', 'BA', 'tambahan', 'tahun', 'all', 11, NULL, FALSE, TRUE),
('Bahasa Kadazandusun', 'BKD', 'tambahan', 'tahun', 'all', 12, NULL, FALSE, TRUE),
('Bahasa Semai', 'BS', 'tambahan', 'tahun', 'all', 13, NULL, FALSE, TRUE),
('Bahasa Iban', 'BIB', 'tambahan', 'tahun', 'all', 14, NULL, FALSE, TRUE),
('Bahasa Cina', 'BC', 'tambahan', 'tahun', 'all', 15, NULL, FALSE, TRUE),
('Bahasa Tamil', 'BT', 'tambahan', 'tahun', 'all', 16, NULL, FALSE, TRUE);

-- Tingkatan 1-3 subjects - Teras
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Bahasa Melayu', 'BM_L', 'teras', 'tingkatan', 'lower', 1, NULL, FALSE, TRUE),
('Bahasa Inggeris', 'BI_L', 'teras', 'tingkatan', 'lower', 2, NULL, FALSE, TRUE),
('Matematik', 'MAT_L', 'teras', 'tingkatan', 'lower', 3, NULL, FALSE, TRUE),
('Sains', 'SAI_L', 'teras', 'tingkatan', 'lower', 4, NULL, FALSE, TRUE),
('Sejarah', 'SEJ_L', 'teras', 'tingkatan', 'lower', 5, NULL, FALSE, TRUE),
('Pendidikan Islam', 'PI_L', 'teras', 'tingkatan', 'lower', 6, NULL, FALSE, TRUE),
('Pendidikan Moral', 'PM_L', 'teras', 'tingkatan', 'lower', 7, NULL, FALSE, TRUE),
('Reka Bentuk dan Teknologi', 'RBT_L', 'teras', 'tingkatan', 'lower', 8, NULL, FALSE, TRUE),
('Pendidikan Jasmani dan Kesihatan', 'PJK_L', 'teras', 'tingkatan', 'lower', 9, NULL, FALSE, TRUE);

-- Tingkatan 1-3 subjects - Pilihan
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Pendidikan Seni Visual', 'PSV_L', 'pilihan', 'tingkatan', 'lower', 10, NULL, FALSE, TRUE),
('Pendidikan Muzik', 'PMZ_L', 'pilihan', 'tingkatan', 'lower', 11, NULL, FALSE, TRUE),
('Asas Sains Komputer', 'ASK_L', 'pilihan', 'tingkatan', 'lower', 12, NULL, FALSE, TRUE),
('Teknologi Maklumat dan Komunikasi', 'TMK_L', 'pilihan', 'tingkatan', 'lower', 13, NULL, FALSE, TRUE),
('Bahasa Arab', 'BA_L', 'pilihan', 'tingkatan', 'lower', 14, NULL, FALSE, TRUE),
('Bahasa Kadazandusun', 'BKD_L', 'pilihan', 'tingkatan', 'lower', 15, NULL, FALSE, TRUE),
('Bahasa Semai', 'BS_L', 'pilihan', 'tingkatan', 'lower', 16, NULL, FALSE, TRUE),
('Bahasa Iban', 'BIB_L', 'pilihan', 'tingkatan', 'lower', 17, NULL, FALSE, TRUE),
('Bahasa Cina', 'BC_L', 'pilihan', 'tingkatan', 'lower', 18, NULL, FALSE, TRUE),
('Bahasa Tamil', 'BT_L', 'pilihan', 'tingkatan', 'lower', 19, NULL, FALSE, TRUE);

-- Tingkatan 4-5 subjects - Teras
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Bahasa Melayu', 'BM_U', 'teras', 'tingkatan', 'upper', 1, NULL, FALSE, TRUE),
('Bahasa Inggeris', 'BI_U', 'teras', 'tingkatan', 'upper', 2, NULL, FALSE, TRUE),
('Matematik', 'MAT_U', 'teras', 'tingkatan', 'upper', 3, NULL, FALSE, TRUE),
('Sejarah', 'SEJ_U', 'teras', 'tingkatan', 'upper', 4, NULL, FALSE, TRUE),
('Pendidikan Islam', 'PI_U', 'teras', 'tingkatan', 'upper', 5, NULL, FALSE, TRUE),
('Pendidikan Moral', 'PM_U', 'teras', 'tingkatan', 'upper', 6, NULL, FALSE, TRUE),
('Pendidikan Jasmani dan Kesihatan', 'PJK_U', 'teras', 'tingkatan', 'upper', 7, NULL, FALSE, TRUE);

-- Tingkatan 4-5 subjects - Elektif Sains
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Fizik', 'FIZ_U', 'elektif_sains', 'tingkatan', 'upper', 8, NULL, FALSE, TRUE),
('Kimia', 'KIM_U', 'elektif_sains', 'tingkatan', 'upper', 9, NULL, FALSE, TRUE),
('Biologi', 'BIO_U', 'elektif_sains', 'tingkatan', 'upper', 10, NULL, FALSE, TRUE),
('Matematik Tambahan', 'MAT_TAM', 'elektif_sains', 'tingkatan', 'upper', 11, NULL, FALSE, TRUE);

-- Tingkatan 4-5 subjects - Elektif Sastera
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Geografi', 'GEO_U', 'elektif_sastera', 'tingkatan', 'upper', 12, NULL, FALSE, TRUE),
('Pendidikan Seni Visual', 'PSV_U', 'elektif_sastera', 'tingkatan', 'upper', 13, NULL, FALSE, TRUE),
('Kesusasteraan Melayu', 'KM_U', 'elektif_sastera', 'tingkatan', 'upper', 14, NULL, FALSE, TRUE),
('Kesusasteraan Inggeris', 'KI_U', 'elektif_sastera', 'tingkatan', 'upper', 15, NULL, FALSE, TRUE),
('Ekonomi', 'EKO_U', 'elektif_sastera', 'tingkatan', 'upper', 16, NULL, FALSE, TRUE),
('Perniagaan', 'PRN_U', 'elektif_sastera', 'tingkatan', 'upper', 17, NULL, FALSE, TRUE),
('Prinsip Perakaunan', 'PP_U', 'elektif_sastera', 'tingkatan', 'upper', 18, NULL, FALSE, TRUE);

-- Tingkatan 4-5 subjects - Tambahan
INSERT INTO subjects (name, code, category, level_type, sub_level, sort_order, user_id, is_custom, is_active) VALUES
('Bahasa Arab', 'BA_U', 'tambahan', 'tingkatan', 'upper', 19, NULL, FALSE, TRUE),
('Bahasa Kadazandusun', 'BKD_U', 'tambahan', 'tingkatan', 'upper', 20, NULL, FALSE, TRUE),
('Bahasa Semai', 'BS_U', 'tambahan', 'tingkatan', 'upper', 21, NULL, FALSE, TRUE),
('Bahasa Iban', 'BIB_U', 'tambahan', 'tingkatan', 'upper', 22, NULL, FALSE, TRUE),
('Bahasa Cina', 'BC_U', 'tambahan', 'tingkatan', 'upper', 23, NULL, FALSE, TRUE),
('Bahasa Tamil', 'BT_U', 'tambahan', 'tingkatan', 'upper', 24, NULL, FALSE, TRUE);

-- Step 7: Create performance indexes
CREATE INDEX IF NOT EXISTS idx_subjects_level_category ON subjects (level_type, category, sub_level);
CREATE INDEX IF NOT EXISTS idx_subjects_active_sort ON subjects (is_active, sort_order);

-- Step 8: Add column comments for documentation
COMMENT ON COLUMN subjects.category IS 'Subject category: teras, tambahan, pilihan, elektif_sains, elektif_sastera';
COMMENT ON COLUMN subjects.level_type IS 'Education level: tahun, tingkatan, both';
COMMENT ON COLUMN subjects.sub_level IS 'Sub-level: all, lower (1-3), upper (4-5)';
COMMENT ON COLUMN subjects.sort_order IS 'Display order within category';
COMMENT ON COLUMN subjects.is_active IS 'Whether subject is currently active/available';

COMMIT;

-- Show results
SELECT 'Migration completed successfully!' as status;
SELECT 
    level_type, 
    sub_level, 
    category, 
    COUNT(*) as subject_count 
FROM subjects 
WHERE is_active = TRUE AND user_id IS NULL
GROUP BY level_type, sub_level, category 
ORDER BY level_type, sub_level, category;
