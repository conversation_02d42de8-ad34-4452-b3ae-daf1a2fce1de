<template>
  <div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard - {{ schoolName }}
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Selamat datang ke sistem pengurusan pendidikan anda
          </p>
          <div class="flex items-center space-x-4 mt-2">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              📍 {{ schoolCode.toUpperCase() }}
            </span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Aktif
            </span>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-500 dark:text-gray-400">
            {{ currentDate }}
          </span>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Lesson Plans -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:document-text" class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Rancangan Pengajaran</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.lessonPlans }}</p>
          </div>
        </div>
      </div>

      <!-- This Week's Classes -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:calendar-days" class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Kelas Minggu Ini</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.weeklyClasses }}</p>
          </div>
        </div>
      </div>

      <!-- Pending Tasks -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:clipboard-document-list" class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tugas Tertunda</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.pendingTasks }}</p>
          </div>
        </div>
      </div>

      <!-- Reflections -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:light-bulb" class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Refleksi</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.reflections }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tindakan Pantas</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <NuxtLink to="/rph" class="flex flex-col items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <Icon name="heroicons:document-text" class="h-8 w-8 text-blue-600 mb-2" />
          <span class="text-sm font-medium text-gray-900 dark:text-white">RPH</span>
        </NuxtLink>
        
        <NuxtLink to="/jadual-mengajar" class="flex flex-col items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <Icon name="heroicons:calendar-days" class="h-8 w-8 text-green-600 mb-2" />
          <span class="text-sm font-medium text-gray-900 dark:text-white">Jadual Mengajar</span>
        </NuxtLink>
        
        <NuxtLink to="/refleksi" class="flex flex-col items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <Icon name="heroicons:light-bulb" class="h-8 w-8 text-purple-600 mb-2" />
          <span class="text-sm font-medium text-gray-900 dark:text-white">Refleksi</span>
        </NuxtLink>
        
        <NuxtLink to="/maklumat-guru" class="flex flex-col items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <Icon name="heroicons:user" class="h-8 w-8 text-indigo-600 mb-2" />
          <span class="text-sm font-medium text-gray-900 dark:text-white">Profil</span>
        </NuxtLink>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Aktiviti Terkini</h2>
      <div v-if="isLoadingActivity" class="space-y-3">
        <div v-for="i in 3" :key="i" class="animate-pulse flex space-x-4">
          <div class="rounded-full bg-gray-300 h-10 w-10"></div>
          <div class="flex-1 space-y-2 py-1">
            <div class="h-4 bg-gray-300 rounded w-3/4"></div>
            <div class="h-4 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      </div>
      <div v-else-if="recentActivity.length === 0" class="text-center py-8">
        <Icon name="heroicons:document-text" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-500 dark:text-gray-400">Tiada aktiviti terkini</p>
      </div>
      <div v-else class="space-y-4">
        <div v-for="activity in recentActivity" :key="activity.id" class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <Icon :name="activity.icon" class="h-6 w-6 text-gray-400" />
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {{ activity.title }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              {{ activity.description }}
            </p>
            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
              {{ activity.time }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Use school layout with authentication
definePageMeta({
  layout: 'school' as any
})

// Authentication check - redirect to login if not authenticated
const user = useSupabaseUser()

// Redirect to login if not authenticated
if (!user.value) {
  await navigateTo('/auth/login')
}

// Get school context
const route = useRoute()
const schoolCode = computed(() => route.params.school as string)
const schoolName = computed(() => `${schoolCode.value.toUpperCase()} School`)

// State
const stats = ref({
  lessonPlans: 0,
  weeklyClasses: 0,
  pendingTasks: 0,
  reflections: 0
})

const recentActivity = ref<any[]>([])
const isLoadingActivity = ref(true)

const currentDate = computed(() => {
  return new Date().toLocaleDateString('ms-MY', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Load dashboard data
const loadDashboardData = async () => {
  try {
    // Simulate loading stats
    stats.value = {
      lessonPlans: 24,
      weeklyClasses: 18,
      pendingTasks: 3,
      reflections: 12
    }

    // Simulate recent activity
    recentActivity.value = [
      {
        id: 1,
        icon: 'heroicons:document-text',
        title: 'RPH Matematik Tingkatan 1',
        description: 'Rancangan pengajaran baru telah disimpan',
        time: '2 jam yang lalu'
      },
      {
        id: 2,
        icon: 'heroicons:light-bulb',
        title: 'Refleksi Pengajaran',
        description: 'Refleksi untuk kelas Sains telah dikemaskini',
        time: '1 hari yang lalu'
      },
      {
        id: 3,
        icon: 'heroicons:calendar-days',
        title: 'Jadual Mengajar',
        description: 'Jadual minggu depan telah ditetapkan',
        time: '2 hari yang lalu'
      }
    ]
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    isLoadingActivity.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})

// Set page head
useHead({
  title: `Dashboard - ${schoolName.value}`,
  meta: [
    {
      name: 'description',
      content: `Dashboard untuk ${schoolName.value} - Sistem pengurusan pendidikan`
    }
  ]
})
</script>
