<template>
  <div :class="classes" :style="styles"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  height?: string
  width?: string
  rounded?: boolean
  variant?: 'light' | 'medium' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  height: '1rem',
  width: '100%',
  rounded: true,
  variant: 'medium'
})

const classes = computed(() => [
  'skeleton-shimmer',
  {
    'rounded': props.rounded,
    'bg-gray-100 dark:bg-gray-800': props.variant === 'light',
    'bg-gray-200 dark:bg-gray-700': props.variant === 'medium',
    'bg-gray-300 dark:bg-gray-600': props.variant === 'dark'
  }
])

const styles = computed(() => ({
  height: props.height,
  width: props.width
}))
</script>

<style scoped>
.skeleton-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

@keyframes skeleton-shimmer {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

.dark .skeleton-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}
</style>
