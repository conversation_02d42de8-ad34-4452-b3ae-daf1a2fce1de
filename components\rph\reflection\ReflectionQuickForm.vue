<template>
    <!-- Quick Mode Form -->
    <form @submit.prevent="$emit('submit')" class="space-y-6">
        <!-- General <PERSON>rror Message -->
        <div v-if="errors?.general"
            class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
                <Icon name="mdi:alert-circle" class="h-5 w-5 text-red-400 mr-2 mt-0.5" />
                <div class="text-sm text-red-800 dark:text-red-200">
                    {{ errors.general[0] }}
                </div>
            </div>
        </div> <!-- Quick Fields Component -->
        <ReflectionQuickFields :model-value="{
            overall_rating: formData.overall_rating,
            objectives_achieved: formData.objectives_achieved,
            challenges_faced: formData.challenges_faced
        }" @update:model-value="updateQuickFields" :errors="errors" />

        <!-- Form Actions -->
        <div
            class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button type="button" @click="$emit('cancel')" variant="outline">
                Batal
            </Button>
            <Button type="submit" variant="primary" :disabled="loading">
                <Icon v-if="loading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                {{ isEditing ? 'Kemaskini' : 'Simpan' }} Refleksi
            </Button>
        </div>
    </form>
</template>

<script setup lang="ts">
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import ReflectionQuickFields from '~/components/rph/ReflectionQuickFields.vue'
import type { ReflectionFormData } from '~/types/reflections'

interface Props {
    formData: ReflectionFormData
    errors: Record<string, string[]> | null
    loading: boolean
    isEditing: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
    submit: []
    cancel: []
    'update:formData': [formData: ReflectionFormData]
}>()

// Handle updates from ReflectionQuickFields
const updateQuickFields = (quickFields: {
    overall_rating: number
    objectives_achieved: boolean
    challenges_faced: string
}) => {
    emit('update:formData', {
        ...props.formData,
        ...quickFields
    })
}
</script>
