-- Migration: Create coupon_usage table for tracking coupon usage
-- Created: 2025-07-13
-- Description: Track which schools have used which coupons for analytics and validation

BEGIN;

-- =====================================================
-- CREATE COUPON_USAGE TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS coupon_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Foreign key relationships
    coupon_id UUID NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    
    -- Usage details
    used_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    used_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, -- The user who used the coupon
    
    -- Usage context
    usage_type TEXT DEFAULT 'school_registration' CHECK (usage_type IN ('school_registration', 'subscription_renewal', 'upgrade')),
    
    -- Original values at time of usage (for historical tracking)
    original_discount_type TEXT NOT NULL,
    original_discount_value DECIMAL(10,2) DEFAULT 0,
    applied_discount_amount DECIMAL(10,2) DEFAULT 0, -- Actual discount applied
    
    -- Additional metadata
    user_agent TEXT, -- Browser/client information
    ip_address INET, -- IP address of the user
    metadata JSONB DEFAULT '{}'::jsonb, -- Additional usage data
    
    -- Constraints
    CONSTRAINT coupon_usage_school_coupon_unique UNIQUE (coupon_id, school_id), -- One coupon per school
    CONSTRAINT coupon_usage_applied_discount_non_negative CHECK (applied_discount_amount >= 0)
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================

-- Index for coupon lookups
CREATE INDEX IF NOT EXISTS idx_coupon_usage_coupon_id ON coupon_usage (coupon_id);

-- Index for school lookups
CREATE INDEX IF NOT EXISTS idx_coupon_usage_school_id ON coupon_usage (school_id);

-- Index for user lookups
CREATE INDEX IF NOT EXISTS idx_coupon_usage_used_by ON coupon_usage (used_by);

-- Index for time-based queries
CREATE INDEX IF NOT EXISTS idx_coupon_usage_used_at ON coupon_usage (used_at);

-- Index for usage type analytics
CREATE INDEX IF NOT EXISTS idx_coupon_usage_type ON coupon_usage (usage_type);

-- Composite index for coupon analytics
CREATE INDEX IF NOT EXISTS idx_coupon_usage_coupon_time ON coupon_usage (coupon_id, used_at);

-- GIN index for metadata JSONB column
CREATE INDEX IF NOT EXISTS idx_coupon_usage_metadata ON coupon_usage USING GIN (metadata);

-- =====================================================
-- CREATE TRIGGER TO UPDATE COUPON USED_COUNT
-- =====================================================

-- Function to increment coupon used_count when usage is recorded
CREATE OR REPLACE FUNCTION increment_coupon_used_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Increment the used_count in the coupons table
    UPDATE coupons 
    SET used_count = used_count + 1,
        updated_at = timezone('utc'::text, now())
    WHERE id = NEW.coupon_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to decrement coupon used_count when usage is deleted
CREATE OR REPLACE FUNCTION decrement_coupon_used_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Decrement the used_count in the coupons table
    UPDATE coupons 
    SET used_count = GREATEST(0, used_count - 1),
        updated_at = timezone('utc'::text, now())
    WHERE id = OLD.coupon_id;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update coupon used_count on insert
DROP TRIGGER IF EXISTS trigger_increment_coupon_used_count ON coupon_usage;
CREATE TRIGGER trigger_increment_coupon_used_count
    AFTER INSERT ON coupon_usage
    FOR EACH ROW
    EXECUTE FUNCTION increment_coupon_used_count();

-- Trigger to automatically update coupon used_count on delete
DROP TRIGGER IF EXISTS trigger_decrement_coupon_used_count ON coupon_usage;
CREATE TRIGGER trigger_decrement_coupon_used_count
    AFTER DELETE ON coupon_usage
    FOR EACH ROW
    EXECUTE FUNCTION decrement_coupon_used_count();

-- =====================================================
-- CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to record coupon usage
CREATE OR REPLACE FUNCTION record_coupon_usage(
    p_coupon_code TEXT,
    p_school_id UUID,
    p_used_by UUID,
    p_usage_type TEXT DEFAULT 'school_registration',
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_coupon_id UUID;
    v_coupon_record RECORD;
    v_usage_id UUID;
BEGIN
    -- Get coupon details
    SELECT id, discount_type, discount_value INTO v_coupon_record
    FROM coupons 
    WHERE UPPER(code) = UPPER(p_coupon_code) AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Coupon not found or inactive: %', p_coupon_code;
    END IF;
    
    v_coupon_id := v_coupon_record.id;
    
    -- Validate coupon before recording usage
    IF NOT is_coupon_valid(p_coupon_code) THEN
        RAISE EXCEPTION 'Coupon is not valid: %', p_coupon_code;
    END IF;
    
    -- Record the usage
    INSERT INTO coupon_usage (
        coupon_id,
        school_id,
        used_by,
        usage_type,
        original_discount_type,
        original_discount_value,
        applied_discount_amount,
        user_agent,
        ip_address
    ) VALUES (
        v_coupon_id,
        p_school_id,
        p_used_by,
        p_usage_type,
        v_coupon_record.discount_type,
        v_coupon_record.discount_value,
        CASE 
            WHEN v_coupon_record.discount_type = 'free_registration' THEN 100.00
            ELSE v_coupon_record.discount_value
        END,
        p_user_agent,
        p_ip_address
    ) RETURNING id INTO v_usage_id;
    
    RETURN v_usage_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE coupon_usage IS 'Track coupon usage by schools for analytics and validation';
COMMENT ON COLUMN coupon_usage.id IS 'Unique identifier for the usage record';
COMMENT ON COLUMN coupon_usage.coupon_id IS 'Reference to the coupon that was used';
COMMENT ON COLUMN coupon_usage.school_id IS 'Reference to the school that used the coupon';
COMMENT ON COLUMN coupon_usage.used_by IS 'User who applied the coupon';
COMMENT ON COLUMN coupon_usage.usage_type IS 'Context in which the coupon was used';
COMMENT ON COLUMN coupon_usage.original_discount_type IS 'Discount type at time of usage';
COMMENT ON COLUMN coupon_usage.original_discount_value IS 'Discount value at time of usage';
COMMENT ON COLUMN coupon_usage.applied_discount_amount IS 'Actual discount amount applied';
COMMENT ON COLUMN coupon_usage.metadata IS 'Additional usage data in JSON format';

COMMENT ON FUNCTION record_coupon_usage(TEXT, UUID, UUID, TEXT, TEXT, INET) IS 'Record coupon usage with validation';

-- =====================================================
-- CREATE RLS POLICIES
-- =====================================================

-- Enable RLS on coupon_usage table
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own coupon usage
CREATE POLICY "Users can view their own coupon usage" ON coupon_usage
    FOR SELECT USING (used_by = auth.uid());

-- Policy: School admins can view usage for their schools
CREATE POLICY "School admins can view school coupon usage" ON coupon_usage
    FOR SELECT USING (
        school_id IN (
            SELECT id FROM schools WHERE admin_user_id = auth.uid()
        )
    );

-- Policy: Super admins can view all usage (for analytics)
CREATE POLICY "Super admins can view all coupon usage" ON coupon_usage
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM auth.users 
            WHERE email IN ('<EMAIL>') -- Replace with actual admin emails
        )
    );

-- Policy: Only the system can insert usage records (through the function)
CREATE POLICY "System can insert coupon usage" ON coupon_usage
    FOR INSERT WITH CHECK (true); -- Will be controlled by application logic

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 'Coupon usage table created successfully' as status;

-- Show table structure
\d coupon_usage;

-- Test the usage recording function
SELECT 'Coupon usage recording function created' as status;
